import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiService } from '../api.service';
import { Local } from '@app/core/models/local';
import { TypeLocal } from '@app/core/models/TypeLocal.1';
import { Observable } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class LocalApiService extends ApiService<Local> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint('local');
  }

  uploadImages(id: string, formData: FormData): Observable<any> {
    return this.http.post(
      `${this.baseUrl}/${this.setEndpoint}/${id}/images`,
      formData
    );
  }
}

@Injectable({ providedIn: 'root' })
export class TypeLocalApiService extends ApiService<TypeLocal> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint('type-local');
  }
}
