import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiService } from '../api.service';
import { Local } from '@app/core/models/local';
import { TypeLocal } from '@app/core/models/TypeLocal.1';
import { Observable } from 'rxjs';
import { environment } from '@app/environments/environment';

@Injectable({ providedIn: 'root' })
export class LocalApiService extends ApiService<Local> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint('local');
  }

  getImageUrl(imageName: string): string {
    if (!imageName) return ''; // Handle cases where imageName might be null/undefined
    return `${environment.host}/api/Images/download/local/${imageName}`;
  }
}

@Injectable({ providedIn: 'root' })
export class TypeLocalApiService extends ApiService<TypeLocal> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint('type-local');
  }
}
