<div class="controller-servers-section">
  <div class="section-header">
    <h3 class="section-title">Contrôleurs Serveurs</h3>
    <!-- <button
      class="create-button"
      (click)="showHierarchy()"
      [disabled]="isLoading || controllerServeurs.length === 0"
      matTooltip="Afficher la hiérarchie des contrôleurs serveurs"
    >
      <mat-icon>account_tree</mat-icon>
      Hiérarchie
    </button> -->
    <button
      class="create-button"
      (click)="addNewControllerServer()"
      *ngIf="!showCreateForm && !showDetailsModal"
    >
      <i class="material-icons">add</i> Ajouter un contrôleur serveur
    </button>
  </div>

  <div class="search-container">
    <input
      type="text"
      [(ngModel)]="searchParam"
      placeholder="Rechercher par nom..."
      class="search-input"
      (keyup.enter)="searchControllerServers()"
    />
    <button class="search-button" (click)="searchControllerServers()">
      <i class="material-icons">search</i>
    </button>
    <button class="clear-button" (click)="clearSearch()" *ngIf="searchParam">
      <i class="material-icons">clear</i>
    </button>
  </div>
  <!-- Create/Edit Form Modal -->
  <div class="modal-overlay" *ngIf="showCreateForm">
    <div class="modal-container">
      <div class="modal-header">
        <h3>
          <mat-icon>{{ isEditMode ? "edit" : "add" }}</mat-icon>
          {{
            isEditMode
              ? "Modifier le Contrôleur Serveur"
              : "Ajouter un Contrôleur Serveur"
          }}
        </h3>
        <button class="close-button" (click)="onFormClosed()">
          <i class="material-icons">close</i>
        </button>
      </div>

      <div class="modal-content">
        <app-form-cs
          [controllerServer]="selectedControllerServer"
          [isEditMode]="isEditMode"
          (formClosed)="onFormClosed()"
          [licences]="licences"
          (controllerServerCreated)="onControllerServerCreated($event)"
          (controllerServerUpdated)="onControllerServerUpdated($event)"
        >
        </app-form-cs>
      </div>
    </div>
  </div>

  <!-- Details Modal -->
  <div class="modal-overlay" *ngIf="showDetailsModal">
    <div class="modal-container details-modal">
      <div class="modal-header">
        <h3>Détails du Contrôleur Serveur</h3>
        <button class="close-button" (click)="onDetailsClosed()">
          <i class="material-icons">close</i>
        </button>
      </div>

      <div class="modal-content">
        <app-details-cs
          [controllerServer]="selectedControllerServer"
          [licences]="licences"
          (detailsClosed)="onDetailsClosed()"
        >
        </app-details-cs>
      </div>
    </div>
  </div>

  <!-- Hierarchy Modal -->
  <div class="modal-overlay hierarchy-modal-overlay" *ngIf="showHierarchyModal">
    <div class="modal-container hierarchy-modal-container">
      <div class="modal-header">
        <h3>
          <mat-icon>account_tree</mat-icon>
          Hiérarchie des Contrôleurs Serveurs
        </h3>
        <button class="close-button" (click)="onHierarchyClosed()">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <div class="modal-content hierarchy-modal-content">
        <app-hierarchy [hierarchyData]="allControllerServeurs"></app-hierarchy>
      </div>
    </div>
  </div>

  <div class="loading-container" *ngIf="isLoading">
    <div class="spinner"></div>
    <p>Chargement des contrôleurs serveurs...</p>
  </div>

  <!-- Table View -->
  <div
    class="table-container"
    *ngIf="
      !isLoading &&
      controllerServeurs.length > 0 &&
      !showCreateForm &&
      !showDetailsModal
    "
  >
    <app-generic-table
      [data]="displayedControllerServeurs"
      [headers]="csHeaders"
      [keys]="csKeys"
      [headers]="csHeaders"
      [keys]="csKeys"
      [actions]="['view', 'edit', 'delete']"
      (actionTriggered)="handleTableAction($event)"
    >
    </app-generic-table>

    <!-- Table Pagination -->
    <div class="pagination-container">
      <mat-paginator
        [length]="totalControllerServeurs"
        [pageSize]="pageSize"
        [pageIndex]="currentPage"
        [pageSizeOptions]="[5, 10, 25, 50]"
        (page)="onPageChange($event)"
        aria-label="Select page"
      >
      </mat-paginator>
    </div>
  </div>

  <div
    class="no-sites-message"
    *ngIf="
      !isLoading &&
      controllerServeurs.length === 0 &&
      !showCreateForm &&
      !showDetailsModal
    "
  >
    <i class="material-icons">dns</i>
    <p>Aucun contrôleur serveur trouvé pour cette organisation</p>
  </div>
</div>
