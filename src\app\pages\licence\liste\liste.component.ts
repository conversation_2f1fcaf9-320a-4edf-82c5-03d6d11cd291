import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';
import { Client } from '@app/core/models/client';
import { Licence } from '@app/core/models/licence';
import { Option } from '@app/core/models/option';
import { LicenceOption } from '@app/core/models/licenceOption';
import { Subscription } from '@app/core/models/subscription';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { LicenceApiService } from '@app/core/services/administrative/licence.service';
import { LicenceOptionApiService } from '@app/core/services/administrative/licenceOption.service';
import { forkJoin, of } from 'rxjs';
import { catchError, map, switchMap, finalize } from 'rxjs/operators';
import { MatIcon } from '@angular/material/icon';
import { SubscriptionApiService } from '@app/core/services/administrative/subscription.service';
import {
  NgToastComponent,
  NgToastService,
  TOAST_POSITIONS,
} from 'ng-angular-popup';
import { OptionApiService } from '@app/core/services/administrative/option.service';

@Component({
  selector: 'app-liste',
  standalone: true,
  imports: [FormsModule, CommonModule, MatIcon, NgToastComponent],
  templateUrl: './liste.component.html',
  styleUrls: ['./liste.component.css'],
})
export class ListeComponent implements OnInit {
  clients: Client[] = [];
  filteredClients: Client[] = [];
  licences: (Licence & { OptionsDisplay?: string; Options?: Option[] })[] = [];
  licenceOptions: LicenceOption[] = [];
  assignedLicences: { [idClient: string]: string } = {};
  subscriptions: Subscription[] = [];
  clientSubscription: Subscription | null = null;

  isLoading = false;
  isUpdating = false;
  isDeleting = false;
  isSubmitting = false;
  isRefreshing: { [key: string]: boolean } = {};

  isModalOpen = false;
  modalMode: 'add' | 'edit' | 'view' = 'add';
  formData: any = {
    Name: '',
    Description: '',
    Options: [] as Option[],
    OriginalLicenceOptions: [] as LicenceOption[],
  };

  TOAST_POSITIONS = TOAST_POSITIONS;

  isDeleteModalOpen = false;
  itemToDelete: any = null;

  public tableHeaders: string[] = ['Nom', 'Description', 'Options'];
  public tableKeys: string[] = ['Name', 'Description', 'OptionsDisplay'];
  public tableActions: string[] = ['view', 'edit', 'delete', 'refresh'];

  constructor(
    private clientApiService: ClientApiService,
    private licenceApiService: LicenceApiService,
    private subscriptionApiService: SubscriptionApiService,
    private licenceOptionApiService: LicenceOptionApiService,
    private optionApiService: OptionApiService,
    private toast: NgToastService
  ) {}

  ngOnInit(): void {
    this.loadInitialData();
  }

  loadInitialData(): void {
    this.isLoading = true;
    forkJoin([
      this.clientApiService.getAll(),
      this.licenceApiService.getAll(),
      this.subscriptionApiService.getAll(),
    ]).subscribe({
      next: ([clients, licences, subscription]) => {
        this.clients = clients;
        this.subscriptions = subscription;
        this.filteredClients = [...clients];

        this.licences = licences.map((licence) => {
          const options = (licence.LicenceOptions || []).map((lo) => lo.Option);
          return {
            ...licence,
            Options: options,
            OptionsDisplay: this.formatOptionsForDisplay(options),
          };
        });
      },
      error: (err) => {
        console.error('Error loading data:', err);
      },
      complete: () => {
        this.isLoading = false;
      },
    });
  }

  addOption() {
    this.formData.Options.push({
      Name: '',
      Price: 0,
    });
  }
  removeOption(index: number) {
    const optionToRemove = this.formData.Options[index];

    if (this.modalMode === 'edit') {
      const currentLicenceId = this.formData.Id;

      const hasBlockingSubscription = this.subscriptions.some(
        (sub) =>
          sub.LicenceId === currentLicenceId &&
          (sub.Status === 'Payé' || sub.Status === 'En attente')
      );

      if (hasBlockingSubscription) {
        this.showError(
          'Impossible de supprimer une option d’une licence avec une souscription active ou en attente.',
          'Suppression bloquée'
        );
        return;
      }
    }

    this.formData.Options.splice(index, 1);
  }

  private showSuccess(message: string, title: string) {
    this.toast.success(message, title, 3000, false);
  }

  private showError(message: string, title: string) {
    this.toast.warning(message, title, 3000, false);
  }

  getOptionsCount(options: Option[]): string {
    if (!options || options.length === 0) return 'Aucune option';
    return `${options.length} option${options.length > 1 ? 's' : ''}`;
  }

  formatOptionsForDisplay(options: Option[]): string {
    if (!options || options.length === 0) return 'Aucune option';
    return options
      .map((opt) => {
        let base = `${opt.Name} (${opt.Price}€)`;
        if (opt.Type === 'max-controllers-server') {
          base += ` - Max: ${opt.Value}`;
        }
        if (opt.Type === 'max-controllers') {
          base += ` - Max: ${opt.Value}`;
        }

        return base;
      })
      .join(', ');
  }

  onActionTriggered(event: { action: string; row: any }) {
    switch (event.action) {
      case 'view':
        this.viewLicence(event.row);
        break;
      case 'edit':
        this.openEditModal(event.row);
        break;
      case 'delete':
        this.openDeleteModal(event.row);
        break;
      case 'refresh':
        this.refreshSingleLicence(event.row);
        break;
    }
  }

  refreshSingleLicence(licence: any) {
    this.isRefreshing[licence.Id] = true;

    this.licenceApiService.getById(licence.Id).subscribe({
      next: (updatedLicence: Licence) => {
        const options = (updatedLicence.LicenceOptions || []).map(
          (lo) => lo.Option
        );
        const index = this.licences.findIndex(
          (l) => l.Id === updatedLicence.Id
        );

        if (index !== -1) {
          this.licences[index] = {
            ...updatedLicence,
            Options: options,
            OptionsDisplay: this.formatOptionsForDisplay(options),
          };
        }
      },
      error: (error) => {
        console.error('Error refreshing licence:', error);
      },
      complete: () => {
        this.isRefreshing[licence.Id] = false;
      },
    });
  }

  viewLicence(row: any) {
    this.modalMode = 'view';
    this.formData = {
      ...row,
      Options: row.Options
        ? row.Options.map((opt: Option) => ({ ...opt }))
        : [],
    };
    this.isModalOpen = true;
  }

  openEditModal(row: any) {
    console.log('Opening edit modal for:', row);
    this.modalMode = 'edit';
    this.formData = {
      Id: row.Id,
      Name: row.Name,
      Description: row.Description,
      Options: row.Options
        ? row.Options.map((opt: any) => ({
            ...opt,
            Name: opt.Name || '',
            Price: opt.Price || 0,
          }))
        : [],
      OriginalLicenceOptions: row.LicenceOptions ? [...row.LicenceOptions] : [],
    };
    console.log('Form data set to:', this.formData);
    this.isModalOpen = true;
  }

  openDeleteModal(row: any) {
    this.itemToDelete = row;
    this.isDeleteModalOpen = true;
  }

  confirmDelete() {
    if (this.itemToDelete) {
      const hasBlockingSubscription = this.subscriptions.some(
        (sub) =>
          sub.LicenceId === this.itemToDelete.Id &&
          (sub.Status === 'Payé' || sub.Status === 'En attente')
      );

      if (hasBlockingSubscription) {
        this.showError(
          'Impossible de supprimer une licence avec une souscription active ou en attente.',
          'Suppression bloquée'
        );
        this.closeDeleteModal();
        return;
      }

      this.isDeleting = true;

      this.licenceApiService.delete(this.itemToDelete.Id).subscribe({
        next: () => {
          this.licences = this.licences.filter(
            (lic) => lic.Id !== this.itemToDelete.Id
          );
          this.closeDeleteModal();
        },
        error: (error) => {
          console.error('Error deleting licence:', error);
          this.isDeleting = false;
        },
        complete: () => {
          this.isDeleting = false;
        },
      });
    }
  }

  closeDeleteModal() {
    this.isDeleteModalOpen = false;
    this.itemToDelete = null;
  }

  refreshData() {
    this.isLoading = true;
    Promise.all([this.fetchClients(), this.fetchLicences()]).finally(() => {
      this.isLoading = false;
    });
  }

  fetchLicences(): Promise<void> {
    return new Promise((resolve) => {
      this.licenceApiService.getAll().subscribe({
        next: (data: Licence[]) => {
          this.licences = data.map((licence) => {
            const options = (licence.LicenceOptions || []).map(
              (lo) => lo.Option
            );
            return {
              ...licence,
              Options: options,
              OptionsDisplay: this.formatOptionsForDisplay(options),
            };
          });
          resolve();
        },
        error: (error) => {
          console.error('Error fetching licences:', error);
          resolve();
        },
      });
    });
  }

  fetchClients(): Promise<void> {
    return new Promise<void>((resolve) => {
      this.clientApiService.getAll().subscribe({
        next: (data: Client[]) => {
          this.clients = data;
          this.filteredClients = [...this.clients];
          resolve();
        },
        error: (error) => {
          console.error('Error fetching clients:', error);
          resolve();
        },
      });
    });
  }

  submitForm() {
    if (this.modalMode === 'add') {
      this.addLicence();
    } else if (this.modalMode === 'edit') {
      this.updateLicence();
    }
  }

  addLicence() {
    this.isSubmitting = true;

    const validOptions = this.formData.Options.filter(
      (opt: Option) => opt.Name.trim() !== '' && opt.Price > 0
    );

    const licenceOptions = validOptions.map((opt: any) => ({
      Option: opt,
    }));

    const licenceData = {
      ...this.formData,
      LicenceOptions: licenceOptions,
    };

    this.licenceApiService.create(licenceData).subscribe({
      next: (newLicence: Licence) => {
        const options = (newLicence.LicenceOptions || []).map(
          (lo) => lo.Option
        );
        this.licences.push({
          ...newLicence,
          Options: options,
          OptionsDisplay: this.formatOptionsForDisplay(options),
        });
        this.closeModal();
      },
      error: (error) => {
        console.error('Error adding licence:', error);
        this.isSubmitting = false;
      },
      complete: () => {
        this.isSubmitting = false;
      },
    });
  }

  updateLicence() {
    this.isUpdating = true;
    this.isSubmitting = true;

    const licenceId = this.formData.Id;
    const currentOptionsInForm: Option[] = this.formData.Options.filter(
      (opt: Option) => opt.Name.trim() !== '' && opt.Price >= 0
    );
    const originalLicenceOptions: LicenceOption[] =
      this.formData.OriginalLicenceOptions || [];

    const licenceOptionsToDelete = originalLicenceOptions.filter(
      (originalLO: LicenceOption) =>
        !currentOptionsInForm.some(
          (formOpt: Option) => formOpt.Id === originalLO.OptionId
        )
    );

    const deleteCalls = licenceOptionsToDelete.map((lo) =>
      this.licenceOptionApiService.delete(lo.Id!).pipe(
        catchError((err) => {
          console.error(`Error deleting LicenceOption with ID ${lo.Id}:`, err);
          return of(null);
        })
      )
    );

    const optionsToCreate = currentOptionsInForm.filter(
      (formOpt: Option) =>
        !formOpt.Id ||
        !originalLicenceOptions.some(
          (originalLO: LicenceOption) => originalLO.OptionId === formOpt.Id
        )
    );

    const createLicenceOptionCalls = currentOptionsInForm.map((opt: Option) => {
      const existingLO = originalLicenceOptions.find(
        (lo: LicenceOption) => lo.OptionId === opt.Id
      );

      console.log('📝 Updating option:', opt); // Debug: confirm Price is correct

      const payload: Option = {
        Id: opt.Id,
        Name: opt.Name,
        Price: opt.Price,
        Type: opt.Type,
        Value: opt.Value,
      };

      return this.optionApiService.update(payload).pipe(
        catchError((err) => {
          console.error(`Error updating Option with ID ${opt.Id}:`, err);
          return of(null);
        })
      );
    });

    forkJoin([...deleteCalls, ...createLicenceOptionCalls])
      .pipe(
        switchMap(() => {
          return this.licenceApiService.getById(licenceId).pipe(
            switchMap((fetchedLicence: Licence) => {
              const finalUpdatePayload = {
                Id: licenceId,
                Name: this.formData.Name,
                Description: this.formData.Description,
                LicenceOptions: (fetchedLicence.LicenceOptions || []).map(
                  (lo) => ({
                    Id: lo.Id,
                    OptionId: lo.OptionId,
                    Option: lo.Option,
                  })
                ),
              };
              return this.licenceApiService.update(finalUpdatePayload);
            })
          );
        }),
        map((updatedLicence: Licence) => {
          const options = (updatedLicence.LicenceOptions || []).map(
            (lo) => lo.Option
          );
          const index = this.licences.findIndex(
            (l) => l.Id === updatedLicence.Id
          );

          if (index !== -1) {
            this.licences = [
              ...this.licences.slice(0, index),
              {
                ...updatedLicence,
                Options: options,
                OptionsDisplay: this.formatOptionsForDisplay(options),
              },
              ...this.licences.slice(index + 1),
            ];
          }
          this.closeModal();
        }),
        catchError((error) => {
          console.error('❌ Error updating licence or options:', error);
          return of(null);
        }),
        finalize(() => {
          this.isUpdating = false;
          this.isSubmitting = false;
        })
      )
      .subscribe();
  }

  createLicenceOptions(
    options: Option[],
    licenceId: string
  ): Promise<LicenceOption[]> {
    return Promise.resolve([]);
  }

  openAddModal() {
    this.modalMode = 'add';
    this.formData = {
      Name: '',
      Description: '',
      Options: [],
      OriginalLicenceOptions: [],
    };
    this.isModalOpen = true;
  }

  closeModal() {
    this.isModalOpen = false;
    this.formData = {
      Name: '',
      Description: '',
      Options: [],
      OriginalLicenceOptions: [],
    };
  }

  isReadOnly(): boolean {
    return this.modalMode === 'view';
  }

  getModalTitle(): string {
    switch (this.modalMode) {
      case 'add':
        return 'Ajouter une licence';
      case 'edit':
        return 'Modifier une licence';
      case 'view':
        return 'Détails de la licence';
      default:
        return 'Licence';
    }
  }

  getSubmitButtonText(): string {
    switch (this.modalMode) {
      case 'add':
        return 'Ajouter';
      case 'edit':
        return 'Modifier';
      default:
        return 'OK';
    }
  }

  trackByLicence(index: number, licence: any): any {
    return licence.Id;
  }

  trackByOption(index: number, option: any): any {
    return option.Id || index;
  }

  editLicence(licence: any) {
    this.openEditModal(licence);
  }

  deleteLicence(licence: any) {
    this.openDeleteModal(licence);
  }

  refreshLicence(licence: any) {
    this.refreshSingleLicence(licence);
  }
}
