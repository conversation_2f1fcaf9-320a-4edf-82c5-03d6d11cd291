<div class="local-details-container">
  <!-- Content -->
  <div class="site-info-section" *ngIf="!isLoading && local">
    <div class="breadcrumb-nav">
      <button class="back-button" (click)="goBack()">
        <i class="material-icons">arrow_back</i>
      </button>
      <span class="breadcrumb-text">Détails du local: {{ local.Name }}</span>
    </div>

    <div class="info-section">
      <div class="site-images-container">
        <div class="logo-container">
          <ng-container *ngIf="local.ImageLocal; else noImage">
            <img 
              [src]="'data:image/jpeg;base64,' + local.ImageLocal" 
              [alt]="local.Name" 
              class="site-logo"
              (error)="onImageError($event)"
            />
          </ng-container>
          <ng-template #noImage>
            <div class="no-logo">
              <i class="material-icons">location_city</i>
            </div>
          </ng-template>
        </div>
        <div class="site-stats">
          <div class="stat-item">
            <div class="stat-value">{{ local.Floor }}</div>
            <div class="stat-label">Étage</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ local.Capacity || 0 }}</div>
            <div class="stat-label">Capacité</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ local.SensorsCount || 0 }}</div>
            <div class="stat-label">Capteurs</div>
          </div>
        </div>
      </div>

      <div class="site-info-container">
        <div class="site-header">
          <h2 class="site-name">{{ local.Name }}</h2>
          <div class="site-type">
            Type Local : {{ local.TypeLocal?.Nom || "Type non défini" }}
          </div>
        </div>

        <div class="info-grid">
          <!-- Column 1 -->
          <div class="info-column">
            <div class="info-item">
              <div class="info-label">
                <i class="material-icons">description</i> Description
              </div>
              <div class="info-value description-text">
                {{ local.Description || "Non spécifié" }}
              </div>
            </div>
          </div>

          <!-- Column 2 -->
          <div class="info-column">
            <div class="info-item">
              <div class="info-label">
                <i class="material-icons">location_on</i> Coordonnées
              </div>
              <div class="info-value">
                <div *ngIf="local.Latitude && local.Longtitude">
                  Lat: {{ local.Latitude | number : "1.4-4" }}, Long:
                  {{ local.Longtitude | number : "1.4-4" }}
                </div>
                <div *ngIf="!local.Latitude || !local.Longtitude">
                  Coordonnées GPS non spécifiées
                </div>
                <div *ngIf="currentSite?.Address" class="address-value">
                  {{ currentSite?.Address }}
                </div>
              </div>
            </div>

            <div class="info-item" *ngIf="currentSite">
              <div class="info-label">
                <i class="material-icons">business</i> Site
              </div>
              <div class="info-value">
                {{ currentSite.Name }}
                <div *ngIf="currentSite.Address">
                  {{ currentSite.Address }}
                </div>
              </div>
            </div>
          </div>

          <!-- Column 3 -->
          <div class="info-column">
            <div class="info-item">
              <div class="info-label">
                <i class="material-icons">calendar_today</i> Dates
              </div>
              <div class="info-value">
                <div *ngIf="local.CreatedAt">
                  <strong>Créé le:</strong>
                  {{ local.CreatedAt | date : "mediumDate" }}
                </div>
                <div *ngIf="local.LastUpdatedAt">
                  <strong>Modifié le:</strong>
                  {{ local.LastUpdatedAt | date : "mediumDate" }}
                </div>
              </div>
            </div>

            <div class="info-item" *ngIf="local.Architecture2DImage">
              <div class="info-label">
                <i class="material-icons">architecture</i> Plan 2D
              </div>
              <div class="architecture-image">
                <img
                  [src]="'data:image/jpeg;base64,' + local.Architecture2DImage"
                  alt="Plan 2D"
                  (error)="onImageError($event)"
                  onerror="this.src='assets/images/default-image.jpg'"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <ngx-ui-loader *ngIf="isLoading"></ngx-ui-loader>
</div>
