<div class="local-details-container">
  <div class="site-info-section" *ngIf="!isLoading && local">
    <div class="breadcrumb-nav">
      <button class="back-button" (click)="goBack()">
        <i class="material-icons">arrow_back</i>
      </button>
      <span class="breadcrumb-text">Détails du local: {{ local.Name }}</span>
    </div>

    <div class="info-section">
      <div class="site-images-container">
        <div class="logo-container">
          <ng-container *ngIf="local.ImageLocal; else noImage">
            <img [src]="'data:image/jpeg;base64,' + local.ImageLocal" [alt]="local.Name" class="site-logo"
              (error)="onImageError($event)" />
          </ng-container>
          <ng-template #noImage>
            <div class="no-logo">
              <i class="material-icons">location_city</i>
            </div>
          </ng-template>
        </div>
        <div class="site-stats">
          <div class="stat-item">
            <div class="stat-value">{{ local.Floor }}</div>
            <div class="stat-label">Étage</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ local.Capacity || 0 }}</div>
            <div class="stat-label">Capacité</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ local.SensorsCount || 0 }}</div>
            <div class="stat-label">Capteurs</div>
          </div>
        </div>
      </div>



      <div class="site-info-container">
        <div class="site-header">
          <h2 class="site-name">{{ local.Name }}</h2>
          <div class="site-type">
            Type Local : {{ local.TypeLocal?.Nom || "Type non défini" }}
          </div>
        </div>



        <div class="info-grid">
          <div class="info-column">
            <div class="info-item">
              <div class="info-label">
                <i class="material-icons">description</i> Description
              </div>
              <div class="info-value description-text">
                {{ local.Description || "Non spécifié" }}
              </div>
            </div>

            <div class="info-item">
              <div class="info-label">
                <mat-icon>sensor_occupied</mat-icon>
                Nombre de capteurs
                <button mat-icon-button (click)="toggleSensorPopup()" class="sensor-info-btn"
                  matTooltip="Afficher la liste des capteurs" [class.active]="showSensorPopup">
                  <mat-icon>info_outline</mat-icon>
                </button>
              </div>
              <div class="info-value">
                {{ capteurCount }} capteur{{ capteurCount > 1 ? 's' : '' }}
              </div>
            </div>
          </div>
          <div class="info-item" *ngIf="currentSite">
            <div class="info-label">
              <i class="material-icons">business</i> Site
            </div>
            <div class="info-value">
              {{ currentSite.Name }}
              <div *ngIf="currentSite.Address">
                {{ currentSite.Address }}
              </div>
            </div>
          </div>
        </div>

          <!-- Column 2 -->
          <div class="info-column">
            <div class="info-item">
              <div class="info-label">
                <i class="material-icons">location_on</i> Coordonnées
              </div>
              <div class="info-value">
                <div *ngIf="local.Latitude && local.Longtitude">
                  Lat: {{ local.Latitude | number : "1.4-4" }}, Long:
                  {{ local.Longtitude | number : "1.4-4" }}
                </div>
                <div *ngIf="!local.Latitude || !local.Longtitude">
                  Coordonnées GPS non spécifiées
                </div>
                <div *ngIf="currentSite?.Address" class="address-value">
                  {{ currentSite?.Address }}
                </div>
              </div>
            </div>
        <div class="info-column">
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">location_on</i> Coordonnées
            </div>

            <div class="info-value">
              <div *ngIf="local.Latitude && local.Longtitude">
                Lat: {{ local.Latitude | number:'1.4-4' }},
                Long: {{ local.Longtitude | number:'1.4-4' }}
              </div>
              <div *ngIf="!local.Latitude || !local.Longtitude">
                Coordonnées GPS non spécifiées
              </div>
              <div *ngIf="currentSite?.Address" class="address-value">
                {{ currentSite?.Address }}
              </div>
            </div>
          </div>

        </div>

        <div class="info-column">
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">calendar_today</i> Dates
            </div>
            <div class="info-value">
              <div *ngIf="local.CreatedAt">
                <strong>Créé le:</strong>
                {{ local.CreatedAt | date : "mediumDate" }}
              </div>
              <div *ngIf="local.LastUpdatedAt">
                <strong>Modifié le:</strong>
                {{ local.LastUpdatedAt | date : "mediumDate" }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="content-tabs-section">
    <mat-tab-group [(selectedIndex)]="selectedTabIndex" class="local-tabs" animationDuration="300ms"
      dynamicHeight="true">

      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon class="tab-icon">architecture</mat-icon>
          <span class="tab-label">Plan d'Architecture</span>
        </ng-template>

        <div class="tab-content">
          <div class="architecture-plan-section">
            <div class="plan-header">
              <div class="plan-title-section">
                <h3>
                  <mat-icon class="section-icon">architecture</mat-icon>
                  Plan d'Architecture 2D
                </h3>
              </div>
            </div>
            <div class="canvas-container">
              <canvas id="canvas" width="900" height="400"></canvas>
            </div>
          </div>
        </div>
      </mat-tab>

      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon class="tab-icon">rule</mat-icon>
          <span class="tab-label">Règles Appliquées</span>
        </ng-template>

        <div class="tab-content">
          <div class="applied-rules-section">
            <div class="rules-header">
            </div>
            <app-applied-rules-list [localId]="localId">
            </app-applied-rules-list>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>

<div class="loading-container" *ngIf="isLoading">
  <div class="spinner"></div>
  <p class="loading-text">Chargement des détails du local<span class="dots">...</span></p>
</div>


<!-- Beautiful Sensor Popup Overlay -->
<div class="sensor-popup-overlay" *ngIf="showSensorPopup" (click)="closeSensorPopup()">
  <div class="sensor-popup-container" (click)="$event.stopPropagation()">
    <div class="sensor-popup-header">
      <div class="popup-title">
        <mat-icon>sensors</mat-icon>
        <span>Liste des Capteurs</span>
      </div>
      <button mat-icon-button (click)="closeSensorPopup()" class="close-popup-btn">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <div class="sensor-popup-body">
      <div class="sensor-count-badge">
        <mat-icon>device_hub</mat-icon>
        <span>{{ capteurCount }} capteur{{ capteurCount > 1 ? 's' : '' }}</span>
      </div>

      <div class="sensor-list-container" *ngIf="capteurNames && capteurNames.length > 0; else noSensors">
        <div class="sensor-card" *ngFor="let name of capteurNames; let i = index">
          <div class="sensor-icon">
            <mat-icon>radio_button_checked</mat-icon>
          </div>
          <div class="sensor-details">
            <div class="sensor-name">{{ name }}</div>
            <div class="sensor-id">Capteur #{{ i + 1 }}</div>
          </div>
        </div>
      </div>

      <ng-template #noSensors>
        <div class="no-sensors-message">
          <mat-icon>sensor_off</mat-icon>
          <h3>Aucun capteur détecté</h3>
          <p>Ce local ne contient actuellement aucun capteur configuré.</p>
        </div>
      </ng-template>
    </div>
  </div>
</div>