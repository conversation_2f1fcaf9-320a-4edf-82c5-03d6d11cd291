<!-- sidebar.component.html -->
<div class="sidebar-wrapper">
  <div class="sidebar" [ngClass]="{ collapsed: isCollapsed }">
    <div class="sidebar-header">
      <div class="logo-container">
        <div class="logo-wrapper">
          <img src="assets/images/logoIOT.png" alt="Logo" class="logo" />
        </div>
        <button class="toggle-button" (click)="toggleSidebar()">
          <mat-icon>{{ isCollapsed ? "chevron_right" : "chevron_left" }}</mat-icon>
        </button>
      </div>
    </div>

    <div class="sidebar-content">
      <ng-container *ngFor="let menuItem of menuItems">
        <ng-container *ngIf="hasAuthority(menuItem.authority)">
          <!-- Expanded Sidebar -->
          <ng-container *ngIf="!isCollapsed">
            <ng-container *ngIf="menuItem.subMenus && menuItem.subMenus.length > 0; else simpleItem">
              <div class="sidebar-item parent-menu" [class.active]="activeRoute(menuItem.starts)">
                <mat-icon>{{menuItem.icon}}</mat-icon>
                <span>{{menuItem.name}}</span>
                <mat-icon class="expand-icon">expand_more</mat-icon>
              </div>
              
              <div class="expanded-submenu">
                <ng-container *ngFor="let subItem of menuItem.subMenus">
                  <a *ngIf="hasAuthority(subItem.authority)"
                     class="submenu-item" 
                     [routerLink]="subItem.url" 
                     routerLinkActive="active">
                    <mat-icon>{{subItem.icon}}</mat-icon>
                    <span>{{subItem.name}}</span>
                  </a>
                </ng-container>
              </div>
            </ng-container>

            <ng-template #simpleItem>
              <a class="sidebar-item" 
                 [routerLink]="menuItem.url" 
                 routerLinkActive="active">
                <mat-icon>{{menuItem.icon}}</mat-icon>
                <span>{{menuItem.name}}</span>
              </a>
            </ng-template>
          </ng-container>

          <!-- Collapsed Sidebar -->
          <ng-container *ngIf="isCollapsed">
            <ng-container *ngIf="menuItem.subMenus && menuItem.subMenus.length > 0; else simpleCollapsed">
              <div class="parent-menu-collapsed" [attr.data-title]="menuItem.name">
                <div class="sidebar-item">
                  <mat-icon>{{menuItem.icon}}</mat-icon>
                </div>

                <div class="collapsed-submenu">
                  <div class="submenu-header">
                    {{menuItem.name}}
                  </div>
                  <div class="submenu-content">
                    <ng-container *ngFor="let subItem of menuItem.subMenus">
                      <a *ngIf="hasAuthority(subItem.authority)"
                         class="submenu-item" 
                         [routerLink]="subItem.url" 
                         routerLinkActive="active">
                        <mat-icon>{{subItem.icon}}</mat-icon>
                        <span>{{subItem.name}}</span>
                      </a>
                    </ng-container>
                  </div>
                </div>
              </div>
            </ng-container>

            <ng-template #simpleCollapsed>
              <a class="sidebar-item" 
                 [routerLink]="menuItem.url" 
                 routerLinkActive="active"
                 [attr.data-title]="menuItem.name">
                <mat-icon>{{menuItem.icon}}</mat-icon>
              </a>
            </ng-template>
          </ng-container>
        </ng-container>
      </ng-container>
    </div>
  </div>
</div>