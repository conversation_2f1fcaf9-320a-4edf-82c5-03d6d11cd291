<div class="sidebar-wrapper">
  <!-- EXPANDED SIDEBAR -->
  <div class="sidebar" *ngIf="!isCollapsed">
    <div class="sidebar-header">
      <div class="logo-container">
        <div class="logo-wrapper">
          <img src="assets/images/logoIOT.png" alt="Logo" class="logo" />
        </div>
        <button class="toggle-button" (click)="toggleSidebar()">
          <mat-icon>chevron_left</mat-icon>
        </button>
      </div>
    </div>

    <div class="sidebar-content">
      <a class="sidebar-item" routerLink="/accueil" routerLinkActive="active">
        <mat-icon>dashboard</mat-icon>
        <span>Accueil</span>
      </a>

      <!-- Paramétrage Menu - Expanded Version -->
      <div class="sidebar-item parent-menu" (click)="toggleParametrage()">
        <mat-icon>settings</mat-icon>
        <span>Paramétrage</span>
        <mat-icon class="expand-icon">
          {{ showParametrage ? 'expand_less' : 'expand_more' }}
        </mat-icon>
      </div>

      <!-- Submenu for expanded state - hierarchical dropdown -->
      <div class="expanded-submenu" *ngIf="showParametrage">
        <!-- Client item with nested structure -->
        <!-- Only the expanded version of Paramétrage was revisited -->
        <div class="expanded-submenu" *ngIf="showParametrage">
          <!-- Level 1: Client -->
          <div class="sidebar-item submenu-item parent-submenu">
            <div class="item-content" (click)="navigateTo('/organisation-management')">
              <mat-icon>business</mat-icon>
              <span>Client</span>
            </div>
            <mat-icon class="expand-icon submenu-expand-icon" (click)="toggleClient()">
              {{ showClient ? 'expand_less' : 'expand_more' }}
            </mat-icon>
          </div>

          <!-- Level 2: Sites under Client -->
          <div class="nested-submenu" *ngIf="showClient">
            <div class="sidebar-item nested-item parent-nested">
              <div class="item-content" (click)="navigateTo('/site-management')">
                <mat-icon>location_on</mat-icon>
                <span>Sites</span>
              </div>
              <mat-icon class="expand-icon nested-expand-icon" (click)="toggleSites()">
                {{ showSites ? 'expand_less' : 'expand_more' }}
              </mat-icon>
            </div>

            <!-- Level 3: Locaux under Sites -->
            <div class="deep-nested-submenu" *ngIf="showSites">
              <div class="sidebar-item deep-nested-item" (click)="navigateTo('/local-management')">
                <mat-icon>meeting_room</mat-icon>
                <span>Locaux</span>
              </div>
            </div>
        </div>

      </div>
      </div>

      <a class="sidebar-item" routerLink="/energy-report" routerLinkActive="active">
        <mat-icon>assessment</mat-icon>
        <span>Rapports Énergétiques</span>
      </a>

      <a class="sidebar-item" routerLink="/devices" routerLinkActive="active">
        <mat-icon>network_check</mat-icon>
        <span>Network Monitoring</span>
      </a>

      <a class="sidebar-item" routerLink="/subscriptions" routerLinkActive="active">
        <mat-icon>card_membership</mat-icon>
        <span>Abonnement</span>
      </a>

      <a class="sidebar-item" routerLink="/iot" routerLinkActive="active">
        <mat-icon>devices</mat-icon>
        <span>IoT</span>
      </a>

      <a class="sidebar-item" routerLink="/generator-regles-ia" routerLinkActive="active">
        <mat-icon>rule</mat-icon>
        <span>Gestion des Règles</span>
      </a>

      <a class="sidebar-item" routerLink="/reglesIA" routerLinkActive="active">
        <mat-icon>rule</mat-icon>
        <span>Règles en IA</span>
      </a>

      <a class="sidebar-item" routerLink="/accounts" routerLinkActive="active" *ngIf="isAdmin || isSuperAdmin">
        <mat-icon>people</mat-icon>
        <span>Accounts</span>
      </a>
    </div>
  </div>

  <!-- COLLAPSED SIDEBAR -->
  <div class="sidebar collapsed" *ngIf="isCollapsed">
    <div class="sidebar-header">
      <div class="logo-container">
        <div class="logo-wrapper">
          <img src="assets/images/logoIOT.png" alt="Logo" class="logo" />
        </div>
        <button class="toggle-button" (click)="toggleSidebar()">
          <mat-icon>chevron_left</mat-icon>
        </button>
      </div>
    </div>

    <div class="sidebar-content">
      <a class="sidebar-item" routerLink="/accueil" routerLinkActive="active" [attr.data-title]="'Accueil'">
        <mat-icon>dashboard</mat-icon>
      </a>

      <!-- Paramétrage Menu - Collapsed Version -->
      <div class="sidebar-item parent-menu" (click)="toggleParametrage()" [attr.data-title]="'Paramétrage'">
        <mat-icon>settings</mat-icon>

        <!-- Submenu for collapsed state -->
        <div class="collapsed-submenu" *ngIf="showParametrage">
          <!-- Client item -->
          <div class="sidebar-item submenu-item" (click)="navigateTo('/organisation-management')" [attr.data-title]="'Client'">
            <mat-icon>business</mat-icon>
          </div>

          <!-- Sites item -->
          <div class="sidebar-item submenu-item" (click)="navigateTo('/site-management')" [attr.data-title]="'Sites'">
            <mat-icon>location_on</mat-icon>
          </div>

          <!-- Locaux item -->
          <div class="sidebar-item submenu-item" (click)="navigateTo('/local-management')" [attr.data-title]="'Locaux'">
            <mat-icon>meeting_room</mat-icon>
          </div>
        </div>
      </div>

      <a class="sidebar-item" routerLink="/energy-report" routerLinkActive="active" [attr.data-title]="'Rapports Énergétiques'">
        <mat-icon>assessment</mat-icon>
      </a>

      <a class="sidebar-item" routerLink="/devices" routerLinkActive="active" [attr.data-title]="'Network Monitoring'">
        <mat-icon>network_check</mat-icon>
      </a>

      <a class="sidebar-item" routerLink="/subscriptions" routerLinkActive="active" [attr.data-title]="'Abonnement'">
        <mat-icon>card_membership</mat-icon>
      </a>

      <a class="sidebar-item" routerLink="/iot" routerLinkActive="active" [attr.data-title]="'IoT'">
        <mat-icon>devices</mat-icon>
      </a>

      <a class="sidebar-item" routerLink="/generator-regles-ia" routerLinkActive="active" [attr.data-title]="'Gestion des Règles'">
        <mat-icon>rule</mat-icon>
      </a>

      <a class="sidebar-item" routerLink="/reglesIA" routerLinkActive="active" [attr.data-title]="'Règles en IA'">
        <mat-icon>rule</mat-icon>
      </a>

      <a class="sidebar-item" routerLink="/accounts" routerLinkActive="active" *ngIf="isAdmin || isSuperAdmin" [attr.data-title]="'Accounts'">
        <mat-icon>people</mat-icon>
      </a>
    </div>
  </div>
</div>
