<div class="sidebar-wrapper">
  <div class="sidebar" [ngClass]="{'collapsed': isCollapsed}">
    <div class="sidebar-header">
      <div class="logo-container">
        <div class="logo-wrapper">
          <img src="assets/images/logoIOT.png" alt="Logo" class="logo" />
        </div>
        <button class="toggle-button" (click)="toggleSidebar()">
          <mat-icon>{{ isCollapsed ? 'chevron_right' : 'chevron_left' }}</mat-icon>
        </button>
      </div>
    </div>

    <div class="sidebar-content" *ngIf="!isCollapsed">
      <a class="sidebar-item" routerLink="/accueil" routerLinkActive="active">
        <mat-icon>dashboard</mat-icon>
        <span>Accueil</span>
      </a>

      <!-- PARENT: PARAMÉTRAGE -->
      <div class="sidebar-item parent-menu" (click)="toggleParametrage($event)">
        <mat-icon>settings</mat-icon>
        <span>Paramétrage</span>
        <mat-icon class="expand-icon" (click)="toggleParametrage($event); $event.stopPropagation()">
          {{ showParametrage ? 'expand_less' : 'expand_more' }}
        </mat-icon>
      </div>

      <!-- SOUS-MENU PARAMÉTRAGE -->
      <div class="expanded-submenu" *ngIf="showParametrage">

        <!-- CLIENT -->
        <div class="sidebar-item submenu-item parent-submenu">
          <div class="item-content" (click)="navigateTo('/organisation-management')">
            <mat-icon>business</mat-icon>
            <span>Client</span>
          </div>
          <mat-icon class="expand-icon submenu-expand-icon" (click)="toggleClient(); $event.stopPropagation()">
            {{ showClient ? 'expand_less' : 'expand_more' }}
          </mat-icon>
        </div>

        <!-- CLIENT → SOUS-MENU SITES -->
        <div class="nested-submenu" *ngIf="showClient">
          <div class="sidebar-item nested-item parent-nested">
            <div class="item-content" (click)="navigateTo('/site-management')">
              <mat-icon>location_on</mat-icon>
              <span>Sites</span>
            </div>
            <mat-icon class="expand-icon nested-expand-icon" (click)="toggleSites(); $event.stopPropagation()">
              {{ showSites ? 'expand_less' : 'expand_more' }}
            </mat-icon>
          </div>

          <!-- CLIENT → SITES → LOCAUX -->
          <div class="deep-nested-submenu" *ngIf="showSites">
            <a class="sidebar-item deep-nested-item" (click)="navigateTo('/local-management')" routerLinkActive="active">
              <mat-icon>meeting_room</mat-icon>
              <span>Locaux</span>
            </a>
          </div>
        </div>
      </div>

      <a class="sidebar-item" routerLink="/energy-report" routerLinkActive="active">
        <mat-icon>assessment</mat-icon>
        <span>Rapports Énergétiques</span>
      </a>

      <a class="sidebar-item" routerLink="/devices" routerLinkActive="active">
        <mat-icon>network_check</mat-icon>
        <span>Network Monitoring</span>
      </a>

      <a class="sidebar-item" routerLink="/licence" routerLinkActive="active">
        <mat-icon>assignment</mat-icon>
        <span>Licence</span>
      </a>

      <a class="sidebar-item" routerLink="/iot" routerLinkActive="active">
        <mat-icon>devices</mat-icon>
        <span>IoT</span>
      </a>

      <a class="sidebar-item" routerLink="/generator-regles-ia" routerLinkActive="active">
        <mat-icon>rule</mat-icon>
        <span>Gestion des Règles</span>
      </a>

      <a class="sidebar-item" routerLink="/reglesIA" routerLinkActive="active">
        <mat-icon>rule</mat-icon>
        <span>Règles en IA</span>
      </a>

      <a class="sidebar-item" routerLink="/accounts" routerLinkActive="active" *ngIf="isAdmin || isSuperAdmin">
        <mat-icon>people</mat-icon>
        <span>Accounts</span>
      </a>
    </div>

    <!-- Collapsed sidebar content -->
    <div class="sidebar-content" *ngIf="isCollapsed">
      <a class="sidebar-item" routerLink="/accueil" routerLinkActive="active" [attr.data-title]="'Accueil'">
        <mat-icon>dashboard</mat-icon>
      </a>

      <div class="parent-menu-collapsed"
       [class.expanded]="showParametrage || parametragePinned"
       (mouseenter)="showParametrage = true"
       (mouseleave)="!parametragePinned && (showParametrage = false)"
       [attr.data-title]="'Paramétrage'">
    <div class="sidebar-item" (click)="toggleParametrageCollapse($event)">
      <mat-icon>settings</mat-icon>
    </div>

    <!-- Floating submenu -->
    <div class="collapsed-submenu" 
         *ngIf="showParametrage || parametragePinned"
         [@dropdownAnimation]
         (mouseenter)="showParametrage = true"
         (mouseleave)="!parametragePinned && (showParametrage = false)">
      <div class="submenu-header">
        Paramétrage
        <button class="pin-button" (click)="toggleParametrageCollapse($event)" matTooltip="Pin menu">
          <mat-icon>{{ parametragePinned ? 'push_pin' : 'unfold_more' }}</mat-icon>
        </button>
      </div>
      <div class="submenu-content">
        <!-- Client -->
        <a class="submenu-item" 
           routerLink="/organisation-management" 
           routerLinkActive="active">
          <mat-icon>business</mat-icon>
          <span>Client</span>
        </a>
        <!-- Sites -->
        <a class="submenu-item" 
           routerLink="/site-management" 
           routerLinkActive="active">
          <mat-icon>location_on</mat-icon>
          <span>Sites</span>
        </a>
        <!-- Locaux -->
        <a class="submenu-item" 
           routerLink="/local-management" 
           routerLinkActive="active">
          <mat-icon>meeting_room</mat-icon>
          <span>Locaux</span>
        </a>
      </div>
    </div>
  </div>

      <a class="sidebar-item" routerLink="/energy-report" routerLinkActive="active" [attr.data-title]="'Rapports Énergétiques'">
        <mat-icon>assessment</mat-icon>
      </a>

      <a class="sidebar-item" routerLink="/devices" routerLinkActive="active" [attr.data-title]="'Network Monitoring'">
        <mat-icon>network_check</mat-icon>
      </a>

      <a class="sidebar-item" routerLink="/licence" routerLinkActive="active" [attr.data-title]="'Licence'">
        <mat-icon>assignment</mat-icon>
      </a>

      <a class="sidebar-item" routerLink="/iot" routerLinkActive="active" [attr.data-title]="'IoT'">
        <mat-icon>devices</mat-icon>
      </a>

      <a class="sidebar-item" routerLink="/generator-regles-ia" routerLinkActive="active" [attr.data-title]="'Gestion des Règles'">
        <mat-icon>rule</mat-icon>
      </a>

      <a class="sidebar-item" routerLink="/reglesIA" routerLinkActive="active" [attr.data-title]="'Règles en IA'">
        <mat-icon>rule</mat-icon>
      </a>

      <a class="sidebar-item" routerLink="/accounts" routerLinkActive="active"
         *ngIf="isAdmin || isSuperAdmin"
         [attr.data-title]="'Accounts'">
        <mat-icon>people</mat-icon>
      </a>
    </div>
  </div>
</div>