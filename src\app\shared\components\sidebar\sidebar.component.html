<!-- Modern Sidebar Component -->
<div class="sidebar-wrapper">
  <div class="sidebar" [ngClass]="{ collapsed: isCollapsed }">
    <div class="sidebar-header">
      <div class="logo-container">
        <div class="logo-wrapper">
          <img src="assets/images/logoIOT.png" alt="Logo" class="logo" />
        </div>
        <button class="toggle-button" (click)="toggleSidebar()" 
                [attr.aria-label]="isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'">
          <mat-icon>{{ isCollapsed ? "chevron_right" : "chevron_left" }}</mat-icon>
        </button>
      </div>
    </div>

    <div class="sidebar-content">
      <ng-container *ngFor="let menuItem of menuItems; trackBy: trackByFn">
        <ng-container *ngIf="hasAuthority(menuItem.authority)">
          
          <!-- Expanded Sidebar -->
          <ng-container *ngIf="!isCollapsed">
            <ng-container *ngIf="menuItem.subMenus && menuItem.subMenus.length > 0; else simpleItem">
              <button type="button"
                      class="sidebar-item parent-menu"
                      [class.active]="activeRoute(menuItem.starts)"
                      [class.expanded]="isSubmenuExpanded(menuItem.name || '')"
                      (click)="toggleSubmenu(menuItem.name || '')"
                      (keydown)="onSubmenuKeydown($event, menuItem.name || '')"
                      [attr.aria-expanded]="isSubmenuExpanded(menuItem.name || '')"
                      [attr.aria-label]="'Toggle ' + menuItem.name + ' submenu'">
                <mat-icon>{{menuItem.icon}}</mat-icon>
                <span>{{menuItem.name}}</span>
                <mat-icon class="expand-icon"
                          [class.rotated]="isSubmenuExpanded(menuItem.name || '')">
                  expand_more
                </mat-icon>
              </button>

              <div class="expanded-submenu"
                   *ngIf="isSubmenuExpanded(menuItem.name || '')"
                   [@dropdownAnimation]
                   role="menu"
                   [attr.aria-label]="menuItem.name + ' submenu'">
                <ng-container *ngFor="let subItem of menuItem.subMenus; trackBy: trackBySubFn">
                  <a *ngIf="hasAuthority(subItem.authority)"
                     class="submenu-item"
                     [routerLink]="subItem.url"
                     routerLinkActive="active"
                     role="menuitem"
                     [attr.aria-label]="subItem.name">
                    <mat-icon>{{subItem.icon}}</mat-icon>
                    <span>{{subItem.name}}</span>
                  </a>
                </ng-container>
              </div>
            </ng-container>

            <ng-template #simpleItem>
              <a class="sidebar-item" 
                 [routerLink]="menuItem.url" 
                 routerLinkActive="active"
                 [attr.aria-label]="menuItem.name">
                <mat-icon>{{menuItem.icon}}</mat-icon>
                <span>{{menuItem.name}}</span>
                <span class="menu-badge" *ngIf="menuItem.badgeName">{{menuItem.badgeName}}</span>
              </a>
            </ng-template>
          </ng-container>

          <!-- Collapsed Sidebar -->
          <ng-container *ngIf="isCollapsed">
            <ng-container *ngIf="menuItem.subMenus && menuItem.subMenus.length > 0; else simpleCollapsed">
                <div class="parent-menu-collapsed" 
                    [attr.title]="menuItem.name"
                    [attr.aria-label]="menuItem.name">
                  <div class="sidebar-item parent-menu"
                      [class.active]="activeRoute(menuItem.starts)"
                      (click)="onCollapsedMenuClick($event, menuItem.name ?? '')">
                    <mat-icon>{{menuItem.icon}}</mat-icon>
                  </div>

                  <div class="collapsed-submenu"
                      *ngIf="openCollapsedMenu === menuItem.name"
                      role="menu"
                      [attr.aria-label]="menuItem.name + ' submenu'">
                    <div class="submenu-header">
                      {{menuItem.name}}
                    </div>
                    <div class="submenu-content">
                      <ng-container *ngFor="let subItem of menuItem.subMenus; trackBy: trackBySubFn">
                        <a *ngIf="hasAuthority(subItem.authority)"
                          class="submenu-item" 
                          [routerLink]="subItem.url" 
                          routerLinkActive="active"
                          role="menuitem"
                          [attr.aria-label]="subItem.name"
                          (click)="openCollapsedMenu = null">
                          <mat-icon>{{subItem.icon}}</mat-icon>
                          <span>{{subItem.name}}</span>
                        </a>
                      </ng-container>
                    </div>
                  </div>
                </div>
              </ng-container>

            <ng-template #simpleCollapsed>
              <a class="sidebar-item" 
                 [routerLink]="menuItem.url" 
                 routerLinkActive="active"
                 [attr.title]="menuItem.name"
                 [attr.aria-label]="menuItem.name">
                <mat-icon>{{menuItem.icon}}</mat-icon>
                <span class="menu-badge" *ngIf="menuItem.badgeName">{{menuItem.badgeName}}</span>
              </a>
            </ng-template>
          </ng-container>
        </ng-container>
      </ng-container>
    </div>
  </div>
</div>