export class Page<T>{
    Content?: T[];
    Lister?: Lister;
}

export class Lister {
    pagination?: Pagination;
    sortPage?: SortPage;
    filterParams?: FilterParam[];
}

export class Pagination {
    currentPage?: number;
    pageCount?: number;
    pageSize?: number;
    isLast?: boolean;
    isFirst?: boolean;
    startIndex?: number;
    totalElement?: number;
}

export class SortPage {
    Column?: string;
    Sort?: string;
}

export class FilterParam {
    column?: string;
    value?: string | number | boolean; 
    op?: string; // "eq, gt, lt, gte, lte, in, btw : string",
    andOr?: string; //  "null if first param else AND | OR : string"
}
