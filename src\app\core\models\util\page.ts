export class Page<T>{
    Content?: T[];
    Lister?: Lister;
}
export class Lister {
    Pagination?: Pagination;
    SortPage?: SortPage;
    FilterParams?: FilterParam[];
}

export class Pagination {
    CurrentPage?: number;
    PageCount?: number;
    PageSize?: number;
    IsLast?: boolean;
    IsFirst?: boolean;
    StartIndex?: number;
    totalElement?: number;
}
export class SortPage {
    Column?: string;
    Sort?: string;
}

export class FilterParam {
    Column?: string;
    Value?: string | number | boolean; 
    Op?: string; // "eq, gt, lt, gte, lte, in, btw : string",
    AndOr?: string; //  "null if first param else AND | OR : string"
}
