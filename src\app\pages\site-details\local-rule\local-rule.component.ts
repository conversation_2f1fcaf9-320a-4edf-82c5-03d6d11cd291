// local-rule.component.ts - Enhanced with proper edit mode handling
import { Component, OnInit, Input, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { forkJoin } from 'rxjs';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';

// Import the services
import { RulesApiService } from '@app/core/services/administrative/rules.service';
import { TransactionApiService } from '@app/core/services/administrative/transaction.service';
import { ControllerApiService } from '@app/core/services/administrative/controller.service';
import { CapteurApiService } from '@app/core/services/administrative/capteur.service';
import { RuleTransactionApiService } from '@app/core/services/administrative/ruletransaction.service';
import { TypeCapteurApiService } from '@app/core/services/administrative/typecapteur.service';
import { VariablesApiService } from '@app/core/services/administrative/variables.service';

// Import the backend models
import { Controller } from '@app/core/models/controller';
import { Transaction } from '@app/core/models/transaction';
import { Capteur } from '@app/core/models/capteur';
import { Rules } from '@app/core/models/rules';
import { TypeCapteur } from '@app/shared/models/typeCapteur';
import { Variables } from '@app/core/models/variables';
import { RuleDto } from '@app/shared/models/RuleDto';
import { PublishAction } from '@app/shared/models/PublishAction';
import { SensorDataCondition } from '@app/shared/models/SensorDataCondition';
import { TimeCondition } from '@app/shared/models/TimeCondition';
import { DeviceTypes } from '@app/shared/models/DeviceTypes';
import { PayloadOption } from '@app/shared/models/PayloadOption';

import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { PublishRuleService } from '@app/core/services/publish-rule.service';
import { DeviceMapping } from '../../../shared/models/DeviceMapping';
import { ActionMapping } from '../../../shared/models/ActionMapping';
import { StepState } from '../../../shared/models/StepState';
import { RuleTransaction } from '@app/core/models/ruleTransaction';

export type Condition = SensorDataCondition | TimeCondition;

@Component({
  selector: 'app-local-rule',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    NgxUiLoaderModule
  ],
  templateUrl: './local-rule.component.html',
  styleUrls: ['./local-rule.component.css']
})
export class LocalRuleComponent implements OnInit {
  @Input() editMode: boolean = false;
  @Input() ruleTransactionToEdit?: RuleTransaction;

  // Edit mode tracking
  private originalTransactionIds: string[] = [];
  private originalControllerId?: string;
  private originalLocalId?: string;
  private originallyMappedCapteurs: Capteur[] = []; // Track originally mapped capteurs for edit mode

  selectedRuleId?: string;
  ruleList: Rules[] = [];
  baseRule: RuleDto | null = null;
  selectedRuleSummary?: string;
  controllers: Controller[] = [];
  selectedControllerId?: string;
  availableCapteurs: Capteur[] = [];
  typeCapteurs: TypeCapteur[] = [];
  variables: Variables[] = [];
  availableCapteursForMapping: Capteur[] = [];
  deviceTypes: DeviceTypes = { sensors: {}, actuators: {} };
  deviceMappings: DeviceMapping[] = [];
  actionMappings: ActionMapping[] = [];

  steps: { [key: string]: StepState } = {
    ruleSelection: { completed: false, valid: false, animating: false, accessible: true, collapsed: false },
    controllerSelection: { completed: false, valid: false, animating: false, accessible: false, collapsed: true },
    deviceMapping: { completed: false, valid: false, animating: false, accessible: false, collapsed: true },
    actionMapping: { completed: false, valid: false, animating: false, accessible: false, collapsed: true },
    preview: { completed: false, valid: false, animating: false, accessible: false, collapsed: true }
  };

  currentStep: number = 1;
  isTransitioning: boolean = false;
  
  // Final rule for preview
  transformedRule: RuleDto = {
    rule_name: '',
    topic_pattern: [],
    conditions: {
      operator: 'AND',
      groups: [
        {
          operator: 'AND',
          conditions: []
        }
      ]
    },
    actions: [],
    schedule_config: { enabled: false },
    enabled: true,
    priority: 1
  };

  // Loading states
  isLoading = true;
  loadingError: string | null = null;
  isSaving = false;
  
  // UI state
  priorities = [1, 2, 3, 4, 5];

  constructor(
    public dialogRef: MatDialogRef<LocalRuleComponent>,
    @Inject(MAT_DIALOG_DATA) public data: {
      idLocal: string,
      editMode?: boolean,
      ruleTransaction?: RuleTransaction
    },
    private rulesService: RulesApiService,
    private transactionService: TransactionApiService,
    private controllerService: ControllerApiService,
    private capteurService: CapteurApiService,
    private ruleTransactionService: RuleTransactionApiService,
    private typeCapteurService: TypeCapteurApiService,
    private variablesService: VariablesApiService,
    private dialog: MatDialog,
    private ngxUiLoaderService: NgxUiLoaderService,
    private publishRuleService: PublishRuleService
  ) {
    this.editMode = data.editMode ?? false;
    this.ruleTransactionToEdit = data.ruleTransaction ?? undefined;
  }

  ngOnInit(): void {
    this.loadInitialData();
  }

  // === GETTERS ===

  get mappedDeviceCount(): number {
    return this.deviceMappings.filter(m => m.selectedCapteurId).length;
  }
  
  get mappedActionCount(): number {
    return this.actionMappings.filter(m => m.selectedCapteurId).length;
  }

  get isRuleSelected(): boolean {
    return !!this.selectedRuleId && !!this.baseRule;
  }

  getSelectedControllerModel(): string {
    const selectedController = this.controllers.find(c => c.Id === this.selectedControllerId);
    return selectedController ? selectedController.Model || 'N/A' : 'N/A';
  }

  get isBusy(): boolean {
    return this.isSaving || this.isTransitioning;
  }

  // === STEP MANAGEMENT ===

  private updateStepState(stepKey: string, completed: boolean, valid: boolean): void {
    if (this.steps[stepKey]) {
      const wasCompleted = this.steps[stepKey].completed;
      this.steps[stepKey].completed = completed;
      this.steps[stepKey].valid = valid;
      
      if (completed && valid && !wasCompleted) {
        this.animateStepCompletion(stepKey);
        this.updateStepAccessibility();
      }
    }
  }

  private animateStepCompletion(stepKey: string): void {
    if (this.steps[stepKey]) {
      this.steps[stepKey].animating = true;
      setTimeout(() => {
        if (this.steps[stepKey]) {
          this.steps[stepKey].animating = false;
        }
      }, 1000);
    }
  }

  private updateStepAccessibility(): void {
    const stepKeys = ['ruleSelection', 'controllerSelection', 'deviceMapping', 'actionMapping', 'preview'];
    
    this.steps['ruleSelection'].accessible = true;
    
    for (let i = 1; i < stepKeys.length; i++) {
      const currentStepKey = stepKeys[i];
      const previousStepKey = stepKeys[i - 1];
      
      this.steps[currentStepKey].accessible = this.steps[previousStepKey].completed;
      
      if (currentStepKey === 'deviceMapping' && this.deviceMappings.length === 0) {
        this.steps[currentStepKey].completed = true;
        this.steps[currentStepKey].valid = true;
        this.steps[currentStepKey].accessible = this.steps[previousStepKey].completed;
      }
      
      if (currentStepKey === 'actionMapping' && this.actionMappings.length === 0) {
        this.steps[currentStepKey].completed = true;
        this.steps[currentStepKey].valid = true;
        this.steps[currentStepKey].accessible = this.steps[previousStepKey].completed;
      }
    }
  }

  private advanceToNextStep(): void {
    if (this.currentStep < 5 && !this.isTransitioning) {
      this.isTransitioning = true;
      
      const currentStepKey = this.getStepKey(this.currentStep);
      if (currentStepKey && this.steps[currentStepKey].completed) {
        this.steps[currentStepKey].collapsed = true;
      }
      
      setTimeout(() => {
        this.currentStep++;
        const nextStepKey = this.getStepKey(this.currentStep);
        if (nextStepKey) {
          this.steps[nextStepKey].collapsed = false;
        }
        this.isTransitioning = false;
      }, 300);
    }
  }

  goToStep(stepNumber: number): void {
    if (this.isTransitioning || this.isBusy) return;
    
    const stepKey = this.getStepKey(stepNumber);
    if (!stepKey || !this.steps[stepKey].accessible) return;
    
    if (stepNumber === this.currentStep) return;
    
    this.isTransitioning = true;
    
    Object.keys(this.steps).forEach(key => {
      this.steps[key].collapsed = true;
    });
    
    setTimeout(() => {
      this.currentStep = stepNumber;
      const targetStepKey = this.getStepKey(stepNumber);
      if (targetStepKey) {
        this.steps[targetStepKey].collapsed = false;
      }
      this.isTransitioning = false;
    }, 200);
  }

  private getStepKey(stepNumber: number): string | null {
    const stepKeys = ['ruleSelection', 'controllerSelection', 'deviceMapping', 'actionMapping', 'preview'];
    return stepKeys[stepNumber - 1] || null;
  }

  isStepCompleted(stepNumber: number): boolean {
    const stepKey = this.getStepKey(stepNumber);
    return stepKey ? this.steps[stepKey].completed : false;
  }

  isStepAccessible(stepNumber: number): boolean {
    const stepKey = this.getStepKey(stepNumber);
    return stepKey ? this.steps[stepKey].accessible : false;
  }

  isStepCollapsed(stepNumber: number): boolean {
    const stepKey = this.getStepKey(stepNumber);
    return stepKey ? this.steps[stepKey].collapsed : true;
  }

  getStepLabel(step: number): string {
    switch (step) {
      case 1: return 'Règle';
      case 2: return 'Contrôleur';
      case 3: return 'Capteurs';
      case 4: return 'Actionneurs';
      case 5: return 'Aperçu';
      default: return '';
    }
  }

  // === DATA LOADING ===

  private loadInitialData(): void {
    forkJoin({
      rules: this.rulesService.getAll(),
      controllers: this.controllerService.getAll(),
      capteurs: this.capteurService.getAll(),
      typeCapteurs: this.typeCapteurService.getAll(),
      variables: this.variablesService.getAll()
    }).subscribe({
      next: (data) => {
        this.ruleList = data.rules;
        this.controllers = data.controllers;
        this.availableCapteurs = data.capteurs;
        this.typeCapteurs = data.typeCapteurs;
        this.variables = data.variables;

        this.availableCapteurs.forEach(capteur => {
          capteur.TypeCapteur = this.typeCapteurs.find(tc => tc.Id === capteur.IdTypeCapteur);
        });

        this.processBackendData();
        this.isLoading = false;

        if (this.editMode && this.ruleTransactionToEdit) {
          this.loadRuleTransactionForEdit();
        }
      },
      error: (error) => {
        this.loadingError = 'Erreur lors du chargement des données initiales.';
        this.isLoading = false;
      }
    });
  }

  private async loadRuleTransactionForEdit(): Promise<void> {
    const raw = this.ruleTransactionToEdit?.RawData;
    if (!raw) return;

    const parsed = this.parseAndValidateRule(raw);
    if (!parsed) {
      this.showErrorDialog("La règle à éditer est invalide.");
      return;
    }

    // Store original data for comparison
    this.selectedRuleId = this.ruleTransactionToEdit!.IdRule;
    
    // Load all RuleTransactions for this rule to get original transaction IDs
    this.ruleTransactionService.getAll().subscribe({
      next: async (allRuleTransactions) => {
        const ruleTransactionsForThisRule = allRuleTransactions.filter(rt => rt.IdRule === this.selectedRuleId);
        this.originalTransactionIds = ruleTransactionsForThisRule.map(rt => rt.IdTransaction);
        
        // Load the transaction to get controller and local info
        try {
          const transaction = await this.transactionService.getById(this.ruleTransactionToEdit!.IdTransaction).toPromise();
          if (transaction) {
            this.originalControllerId = transaction.IdController;
            this.originalLocalId = transaction.IdLocal;
            this.selectedControllerId = transaction.IdController;
            this.data.idLocal = transaction.IdLocal;
            
            // Load originally mapped capteurs for edit mode
            await this.loadOriginallyMappedCapteurs(ruleTransactionsForThisRule);
            this.filterCapteursByController();
          }
        } catch (error) {
          console.error('Error loading transaction:', error);
        }

        // Set up the rule data
        this.baseRule = parsed;
        this.transformedRule = JSON.parse(JSON.stringify(parsed));
        this.transformedRule.rule_name += ' (Éditée)';

        this.initializeDeviceMappingsForEdit();

        // Mark all steps as completed and accessible for edit mode
        Object.keys(this.steps).forEach((key) => {
          this.steps[key].completed = true;
          this.steps[key].valid = true;
          this.steps[key].accessible = true;
          this.steps[key].collapsed = false;
        });

        this.currentStep = 5;
      },
      error: (error) => {
        this.showErrorDialog('Erreur lors du chargement des données de la règle.');
        console.error('RuleTransaction fetch error:', error);
      }
    });
  }

  // === RULE AND DEVICE PROCESSING ===

  private processBackendData(): void {
    this.deviceTypes = { sensors: {}, actuators: {} };

    this.typeCapteurs.forEach(typeCapteur => {
      const relatedVariables = this.variables.filter(v => v.IdTypeCapteur === typeCapteur.Id);
      
      const deviceCategory: 'sensors' | 'actuators' = 
        typeCapteur.DeviceType?.toLowerCase() === 'actuator' ? 'actuators' : 'sensors';
      
      const deviceKey = this.normalizeDeviceKey(typeCapteur.Nom);
      
      if (deviceCategory === 'sensors') {
        this.deviceTypes.sensors[deviceKey] = {
          topic: typeCapteur.Topic,
          device_name: typeCapteur.Nom,
          DisplayName: typeCapteur.DisplayName || typeCapteur.Nom,
          properties: relatedVariables.map(variable => ({
            key: variable.Key,
            type: this.mapVariableType(variable.Type),
            values: this.parseVariableValues(variable.Actions)
          }))
        };
      } else {
        this.deviceTypes.actuators[deviceKey] = {
          topic: typeCapteur.Topic,
          device_name: typeCapteur.Nom,
          DisplayName: typeCapteur.DisplayName || typeCapteur.Nom,
          actions: relatedVariables.map(variable => ({
            type: variable.Key,
            payload: {},
            options: this.parseActionOptions(variable.Actions)
          }))
        };
      }
    });
  }

  private normalizeDeviceKey(name: string): string {
    return name.toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^a-z0-9_]/g, '')
      .replace(/^sensor_|^actuator_/, '');
  }

  private mapVariableType(backendType: string): string {
    const typeMapping: { [key: string]: string } = {
      'String': 'string',
      'Integer': 'number',
      'Float': 'number',
      'Double': 'number',
      'Boolean': 'boolean',
      'Bool': 'boolean'
    };
    return typeMapping[backendType] || 'string';
  }

  private parseVariableValues(actions: string[] | string | null): (string | number | boolean)[] {
    if (!actions) return [];
    
    if (Array.isArray(actions)) {
      return actions;
    }
    
    if (typeof actions === 'string') {
      try {
        const parsed = JSON.parse(actions);
        if (Array.isArray(parsed)) {
          return parsed;
        }
      } catch {
        const values = actions.split(/[,;|]/).map(v => v.trim()).filter(v => v);
        return values;
      }
    }
    
    return [];
  }

  private parseActionOptions(actions: string[] | string | null): PayloadOption[] {
    if (!actions) return [];
    
    if (Array.isArray(actions)) {
      return actions.map((item: any) => {
        if (typeof item === 'object' && item !== null) {
          return {
            display: item.display || item.label || item.name || String(item.value || item),
            value: String(item.value || item)
          };
        } else {
          return {
            display: String(item),
            value: String(item)
          };
        }
      });
    }
    
    if (typeof actions === 'string') {
      try {
        const parsed = JSON.parse(actions);
        if (Array.isArray(parsed)) {
          return parsed.map((item: any) => ({
            display: item.display || item.label || item.name || String(item.value || item),
            value: String(item.value || item)
          }));
        }
      } catch {
        const values = actions.split(/[,;|]/).map(v => v.trim()).filter(v => v);
        return values.map(value => ({
          display: value,
          value: value
        }));
      }
    }
    
    return [];
  }

  // === RULE SELECTION ===

  onRuleSelectionChange(): void {
    if (this.selectedRuleId) {
      this.loadSelectedRule();
    } else {
      this.resetRule();
      this.updateStepState('ruleSelection', false, false);
    }
  }

  private loadSelectedRule(): void {
    const selectedRule = this.ruleList.find(rule => rule.Id === this.selectedRuleId);
    
    if (selectedRule && selectedRule.RawData) {
      try {
        const parsedRule = this.parseAndValidateRule(selectedRule.RawData);
        if (!parsedRule) {
          throw new Error('Failed to parse rule data');
        }
        
        this.baseRule = parsedRule;
        this.selectedRuleSummary = selectedRule.Summary || '';
        
        this.transformedRule = JSON.parse(JSON.stringify(this.baseRule));
        this.transformedRule.rule_name = `${this.baseRule.rule_name} - Transformée`;
        
        this.initializeDeviceMappings();
        
        this.updateStepState('ruleSelection', true, true);
        this.advanceToNextStep();
        
      } catch (error) {
        console.error('Error loading rule:', error);
        this.selectedRuleId = undefined;
        this.baseRule = null;
        this.updateStepState('ruleSelection', false, false);
      }
    } else {
      this.showErrorDialog('La règle sélectionnée ne contient pas de données valides.');
      this.selectedRuleId = undefined;
      this.baseRule = null;
      this.selectedRuleSummary = '';
      this.updateStepState('ruleSelection', false, false);
    }
  }

  private parseAndValidateRule(rawData: string): RuleDto | null {
    try {
      const parsedRule = JSON.parse(rawData);
      
      const ruleDto: RuleDto = {
        rule_name: parsedRule.rule_name || 'Unnamed Rule',
        topic_pattern: Array.isArray(parsedRule.topic_pattern) ? parsedRule.topic_pattern : [],
        conditions: {
          operator: parsedRule.conditions?.operator || 'AND',
          groups: []
        },
        actions: [],
        schedule_config: parsedRule.schedule_config || { enabled: false },
        enabled: parsedRule.enabled !== undefined ? parsedRule.enabled : true,
        priority: parsedRule.priority || 1
      };

      // Handle conditions
      if (parsedRule.conditions) {
        if (parsedRule.conditions.groups && Array.isArray(parsedRule.conditions.groups)) {
          ruleDto.conditions.groups = parsedRule.conditions.groups.map((group: any) => ({
            operator: group.operator || 'AND',
            conditions: Array.isArray(group.conditions) ? group.conditions.map((condition: any) => this.normalizeCondition(condition)) : []
          }));
        } else if (Array.isArray(parsedRule.conditions)) {
          ruleDto.conditions.groups = [{
            operator: 'AND',
            conditions: parsedRule.conditions.map((condition: any) => this.normalizeCondition(condition))
          }];
        } else {
          ruleDto.conditions.groups = [{
            operator: 'AND',
            conditions: []
          }];
        }
      }

      // Handle actions
      if (Array.isArray(parsedRule.actions)) {
        ruleDto.actions = parsedRule.actions.map((action: any) => this.normalizeAction(action));
      }

      return ruleDto;
      
    } catch (error) {
      console.error('Failed to parse rule:', error);
      return null;
    }
  }

  private normalizeCondition(condition: any): Condition {
    if (condition.type === 'time') {
      return {
        type: 'time',
        start_time: condition.start_time || '',
        end_time: condition.end_time || ''
      } as TimeCondition;
    } else {
      return {
        type: 'payload',
        device: condition.device || '',
        key: condition.key || '',
        operator: condition.operator || '==',
        value: condition.value || ''
      } as SensorDataCondition;
    }
  }

  private normalizeAction(action: any): PublishAction {
    return {
      type: action.type || '',
      topic: action.topic || '',
      payload: action.payload || {},
      message: action.message || undefined
    } as PublishAction;
  }

  private resetRule(): void {
    this.baseRule = null;
    this.transformedRule = {
      rule_name: '',
      topic_pattern: [],
      conditions: { 
        operator: 'AND',
        groups: [{ operator: 'AND', conditions: [] }] 
      },
      actions: [],
      schedule_config: { enabled: false },
      enabled: true,
      priority: 1
    };
    this.deviceMappings = [];
    this.actionMappings = [];
    this.selectedControllerId = undefined;
    this.availableCapteursForMapping = [];
    
    Object.keys(this.steps).forEach((key, index) => {
      this.steps[key] = { 
        completed: false, 
        valid: false, 
        animating: false, 
        accessible: index === 0,
        collapsed: index !== 0
      };
    });
    this.currentStep = 1;
  }

  getRuleNameFromRawData(rawData: string): string {
    if (!rawData) return 'Unnamed Rule';
    try {
      const rule = JSON.parse(rawData);
      return rule.rule_name?.trim() ? rule.rule_name : 'Unnamed Rule';
    } catch (e) {
      return 'Invalid Rule Data';
    }
  }

  // === CONTROLLER SELECTION ===

  onControllerSelectionChange(): void {
    if (this.selectedControllerId) {
      this.filterCapteursByController();
      this.updateStepState('controllerSelection', true, true);
      this.advanceToNextStep();
    } else {
      this.availableCapteursForMapping = [];
      this.updateStepState('controllerSelection', false, false);
    }
  }

  private filterCapteursByController(): void {
    if (!this.selectedControllerId) return;

    // Use the most optimized endpoint to get transactions for this local and controller combination
    this.transactionService.getByLocalAndController(this.data.idLocal, this.selectedControllerId).subscribe({
      next: (controllerTransactions: Transaction[]) => {
        // Filter only transactions that have capteurs
        const relevantTransactions = controllerTransactions.filter(t => t.IdCapteur);

        const uniqueCapteurIds = new Set(relevantTransactions.map(t => t.IdCapteur).filter((id): id is string => id !== undefined));
        
        this.availableCapteursForMapping = this.availableCapteurs.filter(capteur => 
          capteur.Id !== undefined && uniqueCapteurIds.has(capteur.Id)
        );

        // In edit mode, also include originally mapped capteurs even if they're not in current controller
        if (this.editMode && this.originallyMappedCapteurs.length > 0) {
          // Get all local transactions to check if originally mapped capteurs are still available in this local
          this.transactionService.getByLocalId(this.data.idLocal).subscribe({
            next: (localTransactions: Transaction[]) => {
              this.originallyMappedCapteurs.forEach(originalCapteur => {
                if (!this.availableCapteursForMapping.find(c => c.Id === originalCapteur.Id)) {
                  // Only add if it's available in the local (might be from different controller)
                  const isInLocal = localTransactions.some(t => 
                    t.IdCapteur === originalCapteur.Id
                  );
                  
                  if (isInLocal) {
                    this.availableCapteursForMapping.push(originalCapteur);
                  }
                }
              });
              
              console.log('Available Capteurs for mapping (with edit mode additions):', this.availableCapteursForMapping.length, this.availableCapteursForMapping);
            },
            error: (error) => {
              console.error('Error loading local transactions for edit mode:', error);
            }
          });
        } else {
          console.log('Available Capteurs for mapping:', this.availableCapteursForMapping.length, this.availableCapteursForMapping);
        }
      },
      error: (error) => {
        console.error('Error filtering capteurs by controller:', error);
        this.availableCapteursForMapping = [];
      }
    });
  }

  // === DEVICE MAPPING ===

  // === EDIT MODE HELPERS ===

  private async loadOriginallyMappedCapteurs(ruleTransactionsForThisRule: RuleTransaction[]): Promise<void> {
    try {
      // Get transactions for this local using the optimized endpoint
      const localTransactions = await this.transactionService.getByLocalId(this.data.idLocal).toPromise() || [];
      const transactionIds = ruleTransactionsForThisRule.map(rt => rt.IdTransaction);
      const relevantTransactions = localTransactions.filter(t => transactionIds.includes(t.Id!));
      
      // Get unique capteur IDs from these transactions
      const capteurIds = new Set(relevantTransactions
        .map(t => t.IdCapteur)
        .filter((id): id is string => id !== undefined));
      
      // Get the capteurs that were originally mapped
      this.originallyMappedCapteurs = this.availableCapteurs.filter(capteur => 
        capteur.Id !== undefined && capteurIds.has(capteur.Id)
      );
      
      console.log('Originally mapped capteurs loaded:', this.originallyMappedCapteurs);
    } catch (error) {
      console.error('Error loading originally mapped capteurs:', error);
      this.originallyMappedCapteurs = [];
    }
  }

  private initializeDeviceMappingsForEdit(): void {
    if (!this.baseRule) return;

    this.deviceMappings = [];
    
    if (this.baseRule.conditions && this.baseRule.conditions.groups) {
      this.baseRule.conditions.groups.forEach((group) => {
        if (group.conditions && Array.isArray(group.conditions)) {
          group.conditions.forEach((condition) => {
            if (condition.type === 'payload') {
              const sensorCondition = condition as SensorDataCondition;
              
              if (sensorCondition.device && !this.deviceMappings.find(m => m.originalDevice === sensorCondition.device)) {
                // Try to find the originally mapped capteur for this device
                const originalCapteur = this.findOriginalCapteurForDevice(sensorCondition.device);
                
                const deviceMapping: DeviceMapping = {
                  originalDevice: sensorCondition.device,
                  originalDeviceType: 'sensor',
                  originalDeviceTypeName: sensorCondition.device,
                  selectedCapteurId: originalCapteur?.Id || '',
                  selectedCapteur: originalCapteur,
                  availableProperties: []
                };

                // If we found an original capteur, set up its properties
                if (originalCapteur) {
                  deviceMapping.selectedTypeCapteur = originalCapteur.TypeCapteur;
                  const deviceKey = this.normalizeDeviceKey(originalCapteur.TypeCapteur?.Nom || '');
                  const deviceData = this.deviceTypes.sensors[deviceKey];
                  deviceMapping.availableProperties = deviceData ? deviceData.properties || [] : [];
                  deviceMapping.originalDeviceTypeName = originalCapteur.TypeCapteur?.DisplayName || originalCapteur.TypeCapteur?.Nom || sensorCondition.device;
                }

                this.deviceMappings.push(deviceMapping);
              }
            }
          });
        }
      });
    }

    this.actionMappings = [];
    
    if (this.baseRule.actions && Array.isArray(this.baseRule.actions)) {
      this.baseRule.actions.forEach((action) => {
        if (action.topic && action.type !== 'log' && !this.actionMappings.find(m => m.originalTopic === action.topic)) {
          // Try to find the originally mapped capteur for this action topic
          const originalCapteur = this.findOriginalCapteurForActionTopic(action.topic);
          
          const actionMapping: ActionMapping = {
            originalTopic: action.topic,
            originalDeviceType: 'actuator',
            originalDeviceTypeName: action.topic,
            originalTopicForFiltering: action.topic,
            selectedCapteurId: originalCapteur?.Id || '',
            selectedCapteur: originalCapteur,
            availableActions: []
          };

          // If we found an original capteur, set up its actions
          if (originalCapteur) {
            actionMapping.selectedTypeCapteur = originalCapteur.TypeCapteur;
            const deviceKey = this.normalizeDeviceKey(originalCapteur.TypeCapteur?.Nom || '');
            const deviceData = this.deviceTypes.actuators[deviceKey];
            actionMapping.availableActions = deviceData ? deviceData.actions || [] : [];
            actionMapping.originalDeviceTypeName = originalCapteur.TypeCapteur?.DisplayName || originalCapteur.TypeCapteur?.Nom || action.topic;
            // Update the filtering topic to match the original capteur's topic
            actionMapping.originalTopicForFiltering = originalCapteur.TypeCapteur?.Topic || action.topic;
          }

          this.actionMappings.push(actionMapping);
        }
      });
    }
    
    console.log('Device mappings initialized for edit mode:', this.deviceMappings);
    console.log('Action mappings initialized for edit mode:', this.actionMappings);
    
    this.updateStepAccessibility();
  }

  private findOriginalCapteurForDevice(deviceName: string): Capteur | undefined {
    // Try to find a capteur that matches the device name in the originally mapped capteurs
    return this.originallyMappedCapteurs.find(capteur => {
      const typeCapteurNom = capteur.TypeCapteur?.Nom;
      const typeCapteurDisplayName = capteur.TypeCapteur?.DisplayName;
      
      return typeCapteurNom === deviceName || 
             typeCapteurDisplayName === deviceName ||
             this.normalizeDeviceKey(typeCapteurNom || '') === this.normalizeDeviceKey(deviceName);
    });
  }

  private findOriginalCapteurForActionTopic(actionTopic: string): Capteur | undefined {
    // Try to find a capteur that matches the action topic in the originally mapped capteurs
    return this.originallyMappedCapteurs.find(capteur => {
      const capteurTopic = capteur.TypeCapteur?.Topic;
      const capteurTopicWithSet = capteurTopic + '/set';
      
      return capteurTopic === actionTopic || 
             capteurTopicWithSet === actionTopic ||
             capteur.Topic === actionTopic;
    });
  }

  private initializeDeviceMappings(): void {
    if (this.editMode) {
      this.initializeDeviceMappingsForEdit();
      return;
    }

    if (!this.baseRule) return;

    this.deviceMappings = [];
    
    if (this.baseRule.conditions && this.baseRule.conditions.groups) {
      this.baseRule.conditions.groups.forEach((group) => {
        if (group.conditions && Array.isArray(group.conditions)) {
          group.conditions.forEach((condition) => {
            if (condition.type === 'payload') {
              const sensorCondition = condition as SensorDataCondition;
              const normalizedDeviceName = this.normalizeDeviceKey(sensorCondition.device);
              const deviceData = this.deviceTypes.sensors[normalizedDeviceName];

              if (sensorCondition.device && !this.deviceMappings.find(m => m.originalDevice === sensorCondition.device)) {
                this.deviceMappings.push({
                  originalDevice: sensorCondition.device,
                  originalDeviceType: 'sensor',
                  originalDeviceTypeName: deviceData?.DisplayName || sensorCondition.device,
                  selectedCapteurId: '',
                  selectedCapteur: undefined,
                  availableProperties: []
                });
              }
            }
          });
        }
      });
    }

    this.actionMappings = [];
    
    if (this.baseRule.actions && Array.isArray(this.baseRule.actions)) {
      this.baseRule.actions.forEach((action) => {
        if (action.topic && action.type !== 'log' && !this.actionMappings.find(m => m.originalTopic === action.topic)) {
          const inferredActuatorType = this.typeCapteurs.find(tc => tc.Topic === action.topic && tc.DeviceType?.toLowerCase() === 'actuator');

          this.actionMappings.push({
            originalTopic: action.topic,
            originalDeviceType: 'actuator',
            originalDeviceTypeName: inferredActuatorType?.DisplayName || inferredActuatorType?.Nom || action.topic,
            originalTopicForFiltering: action.topic,
            selectedCapteurId: '',
            selectedCapteur: undefined,
            availableActions: []
          });
        }
      });
    }
    
    this.updateStepAccessibility();
  }

  onDeviceMappingChange(mapping: DeviceMapping): void {
    if (mapping.selectedCapteurId) {
      const selectedCapteur = this.availableCapteursForMapping.find(c => c.Id === mapping.selectedCapteurId);
      if (selectedCapteur) {
        mapping.selectedCapteur = selectedCapteur;
        mapping.selectedTypeCapteur = selectedCapteur.TypeCapteur;
        
        const deviceKey = this.normalizeDeviceKey(selectedCapteur.TypeCapteur?.Nom || '');
        const deviceData = this.deviceTypes.sensors[deviceKey];
        mapping.availableProperties = deviceData ? deviceData.properties || [] : [];
      }
    } else {
      mapping.selectedCapteur = undefined;
      mapping.selectedTypeCapteur = undefined;
      mapping.availableProperties = [];
    }
    
    this.updateTransformedRule();
    this.checkDeviceMappingCompletion();
  }

  onActionMappingChange(mapping: ActionMapping): void {
    if (mapping.selectedCapteurId) {
      const selectedCapteur = this.availableCapteursForMapping.find(c => c.Id === mapping.selectedCapteurId);
      if (selectedCapteur) {
        mapping.selectedCapteur = selectedCapteur;
        mapping.selectedTypeCapteur = selectedCapteur.TypeCapteur;
        
        const deviceKey = this.normalizeDeviceKey(selectedCapteur.TypeCapteur?.Nom || '');
        const deviceData = this.deviceTypes.actuators[deviceKey];
        mapping.availableActions = deviceData ? deviceData.actions || [] : [];
      }
    } else {
      mapping.selectedCapteur = undefined;
      mapping.selectedTypeCapteur = undefined;
      mapping.availableActions = [];
    }
    
    this.updateTransformedRule();
    this.checkActionMappingCompletion();
  }

  private checkDeviceMappingCompletion(): void {
    const allMapped = this.deviceMappings.length === 0 || this.deviceMappings.every(m => m.selectedCapteurId);
    if (allMapped) {
      this.updateStepState('deviceMapping', true, true);
      if (this.currentStep === 3 && this.deviceMappings.length > 0) {
        this.advanceToNextStep();
      }
    } else {
      this.updateStepState('deviceMapping', false, allMapped);
    }
  }

  private checkActionMappingCompletion(): void {
    const allMapped = this.actionMappings.length === 0 || this.actionMappings.every(m => m.selectedCapteurId);
    if (allMapped) {
      this.updateStepState('actionMapping', true, true);
      if (this.currentStep === 4 && this.actionMappings.length > 0) {
        this.advanceToNextStep();
      }
    } else {
      this.updateStepState('actionMapping', false, allMapped);
    }
    
    this.checkPreviewCompletion();
  }

  checkPreviewCompletion(): void {
    const deviceMappingComplete = this.deviceMappings.length === 0 || this.deviceMappings.every(m => m.selectedCapteurId);
    const actionMappingComplete = this.actionMappings.length === 0 || this.actionMappings.every(m => m.selectedCapteurId);
    const ruleNameValid = this.transformedRule.rule_name && this.transformedRule.rule_name.trim();
    
    if (deviceMappingComplete && actionMappingComplete && ruleNameValid) {
      this.updateStepState('preview', true, true);
    } else {
      this.updateStepState('preview', false, false);
    }
  }

  // === HELPER METHODS FOR DROPDOWNS ===

  getFilteredCapteursForDeviceMapping(mapping: DeviceMapping): Capteur[] {
    if (!this.selectedControllerId) {
      return [];
    }

    let filteredCapteurs = this.availableCapteursForMapping.filter(c => 
      c.TypeCapteur?.DeviceType?.toLowerCase() === 'sensor'
    );

    // In edit mode, always include the originally selected capteur if it exists
    if (this.editMode && mapping.selectedCapteur) {
      const originalCapteur = mapping.selectedCapteur;
      if (!filteredCapteurs.find(c => c.Id === originalCapteur.Id)) {
        // Check if the original capteur is in our available capteurs (even if not in current controller)
        const originalInAvailable = this.availableCapteurs.find(c => c.Id === originalCapteur.Id);
        if (originalInAvailable) {
          filteredCapteurs.push(originalInAvailable);
        }
      }
    }

    // Try to match by device type when not in edit mode, or as additional options in edit mode
    if (!this.editMode || filteredCapteurs.length <= 1) {
      const deviceTypeMatches = this.availableCapteursForMapping.filter(c => 
        c.TypeCapteur?.DeviceType?.toLowerCase() === 'sensor' &&
        this.normalizeDeviceKey(c.TypeCapteur?.Nom || '') === this.normalizeDeviceKey(mapping.originalDevice || '') 
      );
      
      // Add device type matches that aren't already included
      deviceTypeMatches.forEach(capteur => {
        if (!filteredCapteurs.find(c => c.Id === capteur.Id)) {
          filteredCapteurs.push(capteur);
        }
      });
    }

    return filteredCapteurs;
  }

  getFilteredCapteursForActionMapping(mapping: ActionMapping): Capteur[] {
    if (!this.selectedControllerId) {
      return [];
    }

    let filteredCapteurs = this.availableCapteursForMapping.filter(c => 
      c.TypeCapteur?.DeviceType?.toLowerCase() === 'actuator'
    );

    // In edit mode, always include the originally selected capteur if it exists
    if (this.editMode && mapping.selectedCapteur) {
      const originalCapteur = mapping.selectedCapteur;
      if (!filteredCapteurs.find(c => c.Id === originalCapteur.Id)) {
        // Check if the original capteur is in our available capteurs (even if not in current controller)
        const originalInAvailable = this.availableCapteurs.find(c => c.Id === originalCapteur.Id);
        if (originalInAvailable) {
          filteredCapteurs.push(originalInAvailable);
        }
      }
    }

    // Try to match by topic when not in edit mode, or as additional options in edit mode
    if (!this.editMode || filteredCapteurs.length <= 1) {
      const topicMatches = this.availableCapteursForMapping.filter(c => 
        c.TypeCapteur?.DeviceType?.toLowerCase() === 'actuator' &&
        c.TypeCapteur?.Topic === mapping.originalTopicForFiltering
      );
      
      // Add topic matches that aren't already included
      topicMatches.forEach(capteur => {
        if (!filteredCapteurs.find(c => c.Id === capteur.Id)) {
          filteredCapteurs.push(capteur);
        }
      });
    }

    return filteredCapteurs;
  }

  // === RULE TRANSFORMATION ===

  private updateTransformedRule(): void {
    if (!this.baseRule) return;

    this.transformedRule = JSON.parse(JSON.stringify(this.baseRule));

    // Transform conditions
    if (this.transformedRule.conditions && this.transformedRule.conditions.groups) {
      this.transformedRule.conditions.groups.forEach((group) => {
        if (group.conditions && Array.isArray(group.conditions)) {
          group.conditions.forEach((condition) => {
            if (condition.type === 'payload') {
              const sensorCondition = condition as SensorDataCondition;
              const mapping = this.deviceMappings.find(m => m.originalDevice === sensorCondition.device);
              if (mapping && mapping.selectedTypeCapteur) {
                sensorCondition.device = mapping.selectedTypeCapteur.Nom;

                // Convert boolean-like string values to uppercase
                if (typeof sensorCondition.value === 'string' && 
                    (sensorCondition.value.toLowerCase() === 'true' || sensorCondition.value.toLowerCase() === 'false')) {
                  sensorCondition.value = sensorCondition.value.toUpperCase();
                }
              }
            }
          });
        }
      });
    }

    // Transform actions
    if (this.transformedRule.actions && Array.isArray(this.transformedRule.actions)) {
      this.transformedRule.actions.forEach((action) => {
        if (action.type !== 'log' && action.topic) {
          const mapping = this.actionMappings.find(m => m.originalTopic === action.topic);
          if (mapping && mapping.selectedCapteur) {
            let topic = mapping.selectedCapteur.Topic || '';
            if (!topic.endsWith('/set')) {
              topic += '/set';
            }
            action.topic = topic;
          }
        }
      });
    }

    this.updateTopicPattern();
    this.checkPreviewCompletion();
  }

  private updateTopicPattern(): void {
    const topics: string[] = [];

    this.deviceMappings.forEach(mapping => {
      if (mapping.selectedCapteur && mapping.selectedCapteur.Topic) {
        if (!topics.includes(mapping.selectedCapteur.Topic)) {
          topics.push(mapping.selectedCapteur.Topic);
        }
      }
    });

    this.transformedRule.topic_pattern = topics;
  }

  // === VALIDATION ===

  private validateMappings(): { valid: boolean; message?: string } {
    if (!this.baseRule || !this.selectedRuleId) {
      return {
        valid: false,
        message: 'Veuillez sélectionner une règle de base valide.'
      };
    }

    if (!this.selectedControllerId) {
      return {
        valid: false,
        message: 'Veuillez sélectionner un contrôleur.'
      };
    }

    const incompleteMappings = this.deviceMappings.filter(m => !m.selectedCapteurId);
    if (incompleteMappings.length > 0) {
      return {
        valid: false,
        message: `Veuillez sélectionner des capteurs pour: ${incompleteMappings.map(m => m.originalDevice).join(', ')}`
      };
    }

    const incompleteActions = this.actionMappings.filter(m => !m.selectedCapteurId);
    if (incompleteActions.length > 0) {
      return {
        valid: false,
        message: `Veuillez sélectionner des actionneurs pour: ${incompleteActions.map(m => m.originalTopic).join(', ')}`
      };
    }

    if (!this.transformedRule.rule_name || !this.transformedRule.rule_name.trim()) {
      return {
        valid: false,
        message: 'Veuillez fournir un nom pour la règle transformée.'
      };
    }

    return { valid: true };
  }

  // === SAVE FUNCTIONALITY ===

  onSave(): void {
    if (!this.baseRule || !this.selectedRuleId) {
      this.showErrorDialog('Veuillez sélectionner une règle de base.');
      return;
    }

    const validation = this.validateMappings();
    if (!validation.valid) {
      this.showErrorDialog(validation.message!);
      return;
    }

    if (this.editMode && this.ruleTransactionToEdit) {
      this.performEditSave();
    } else {
      this.performNewSave();
    }
  }

  private async performEditSave(): Promise<void> {
    this.isSaving = true;
    this.ngxUiLoaderService.start();

    try {
      // Get current transaction IDs based on new mappings
      const currentTransactionIds = await this.getCurrentTransactionIds();
      
      // Check if transactions have changed
      const transactionsChanged = this.hasTransactionsChanged(currentTransactionIds);
      
      if (transactionsChanged) {
        console.log('Transactions changed, creating new RuleTransactions and deleting old ones');
        await this.handleTransactionChanges(currentTransactionIds);
      } else {
        console.log('Only RawData changed, updating existing RuleTransaction');
        await this.updateExistingRuleTransaction();
      }

      // Publish the rule
      if (this.selectedControllerId && this.data.idLocal && this.transformedRule) {
        this.publishRuleService.publishRule(
          this.selectedRuleId!,
          this.data.idLocal,
          this.selectedControllerId,
          this.transformedRule
        );
      }

      this.ngxUiLoaderService.stop();
      this.dialogRef.close({
        action: 'updated',
        transformedRule: this.transformedRule
      });

    } catch (error) {
      console.error('Error during edit save:', error);
      this.handleSaveError(error);
    }
  }

  private async getCurrentTransactionIds(): Promise<string[]> {
    const capteurIdsInRule = new Set<string>();
    this.deviceMappings.forEach(m => m.selectedCapteurId && capteurIdsInRule.add(m.selectedCapteurId));
    this.actionMappings.forEach(m => m.selectedCapteurId && capteurIdsInRule.add(m.selectedCapteurId));

    // Use the most optimized endpoint to get transactions for this local and controller combination
    const controllerTransactions = await this.transactionService.getByLocalAndController(this.data.idLocal, this.selectedControllerId!).toPromise() || [];
    const transactionsByCapteur = new Map<string, Transaction>();

    capteurIdsInRule.forEach(capteurId => {
      const match = controllerTransactions.find(t => t.IdCapteur === capteurId);
      if (match) transactionsByCapteur.set(capteurId, match);
    });

    const uniqueTransactions = new Map<string, Transaction>();
    transactionsByCapteur.forEach(t => {
      if (t.Id && !uniqueTransactions.has(t.Id)) {
        uniqueTransactions.set(t.Id, t);
      }
    });

    return Array.from(uniqueTransactions.keys());
  }

  private hasTransactionsChanged(currentTransactionIds: string[]): boolean {
    // Sort both arrays for comparison
    const sortedCurrent = [...currentTransactionIds].sort();
    const sortedOriginal = [...this.originalTransactionIds].sort();

    // Check if controller changed
    if (this.selectedControllerId !== this.originalControllerId) {
      return true;
    }

    // Check if transaction IDs are different
    if (sortedCurrent.length !== sortedOriginal.length) {
      return true;
    }

    return !sortedCurrent.every((id, index) => id === sortedOriginal[index]);
  }

  private async handleTransactionChanges(currentTransactionIds: string[]): Promise<void> {
    // Delete old RuleTransactions
    const allRuleTransactions = await this.ruleTransactionService.getAll().toPromise() || [];
    const oldRuleTransactions = allRuleTransactions.filter(rt => 
      rt.IdRule === this.selectedRuleId && 
      this.originalTransactionIds.includes(rt.IdTransaction)
    );

    const deletePromises = oldRuleTransactions.map(rt => 
      this.ruleTransactionService.delete(rt.Id!).toPromise()
    );
    
    await Promise.all(deletePromises);

    // Create new RuleTransactions
    const newRuleTransactions = currentTransactionIds.map(transactionId => ({
      IdRule: this.selectedRuleId!,
      IdTransaction: transactionId,
      RawData: JSON.stringify(this.transformedRule)
    }));

    const createPromises = newRuleTransactions.map(rt =>
      this.ruleTransactionService.create(rt).toPromise()
    );

    await Promise.all(createPromises);
  }

  private async updateExistingRuleTransaction(): Promise<void> {
    const updated: RuleTransaction = {
      ...this.ruleTransactionToEdit!,
      RawData: JSON.stringify(this.transformedRule)
    };

    await this.ruleTransactionService.update(updated).toPromise();
  }

  private performNewSave(): void {
    this.isSaving = true;
    this.ngxUiLoaderService.start();

    const capteurIdsInRule = new Set<string>();
    this.deviceMappings.forEach(m => m.selectedCapteurId && capteurIdsInRule.add(m.selectedCapteurId));
    this.actionMappings.forEach(m => m.selectedCapteurId && capteurIdsInRule.add(m.selectedCapteurId));

    // Use the most optimized endpoint to get transactions for this local and controller combination
    this.transactionService.getByLocalAndController(this.data.idLocal, this.selectedControllerId!).subscribe({
      next: (controllerTransactions: Transaction[]) => {
        const transactionsByCapteur = new Map<string, Transaction>();

        capteurIdsInRule.forEach(capteurId => {
          const match = controllerTransactions.find(t => t.IdCapteur === capteurId);
          if (match) transactionsByCapteur.set(capteurId, match);
        });

        if (transactionsByCapteur.size === 0) {
          this.showErrorDialog('Aucune transaction pertinente trouvée pour les capteurs utilisés.');
          this.isSaving = false;
          this.ngxUiLoaderService.stop();
          return;
        }

        const uniqueTransactions = new Map<string, Transaction>();
        transactionsByCapteur.forEach(t => {
          if (t.Id && !uniqueTransactions.has(t.Id)) {
            uniqueTransactions.set(t.Id, t);
          }
        });

        const ruleTransactions = Array.from(uniqueTransactions.values()).map(transaction => ({
          IdRule: this.selectedRuleId!,
          IdTransaction: transaction.Id!,
          RawData: JSON.stringify(this.transformedRule)
        }));

        const creationRequests = ruleTransactions.map(rt =>
          this.ruleTransactionService.create(rt)
        );

        forkJoin(creationRequests).subscribe({
          next: (createdRuleTransactions) => {
            this.handleSaveSuccess(createdRuleTransactions);
          },
          error: (error) => {
            this.handleSaveError(error);
          }
        });
      },
      error: (error) => {
        this.showErrorDialog('Erreur lors de la récupération des transactions pour la sauvegarde.');
        this.isSaving = false;
        this.ngxUiLoaderService.stop();
      }
    });
  }

  private handleSaveSuccess(createdRuleTransactions: any[]): void {
    this.isSaving = false;
    this.ngxUiLoaderService.stop();

    // Publish the rule
    if (this.selectedControllerId && this.data.idLocal && this.transformedRule) {
      this.publishRuleService.publishRule(
        this.selectedRuleId!,
        this.data.idLocal,
        this.selectedControllerId,
        this.transformedRule
      );
    }

    this.dialogRef.close({
      action: 'saved',
      ruleTransactions: createdRuleTransactions,
      transformedRule: this.transformedRule,
      transactionCount: createdRuleTransactions.length
    });
  }

  private handleSaveError(error: any): void {
    this.isSaving = false;
    this.ngxUiLoaderService.stop();
    this.showErrorDialog('Erreur lors de la sauvegarde des RuleTransactions.');
  }

  // === UI STATE METHODS ===

  isFieldDisabled(): boolean {
    return this.isSaving || this.isLoading || this.isTransitioning;
  }

  // === JSON GENERATION ===

  generateJSON(): string {
    try {
      return JSON.stringify(this.transformedRule, null, 2);
    } catch (error) {
      console.error('Error generating JSON:', error);
      return '{\n  "error": "Unable to generate JSON preview"\n}';
    }
  }

  downloadJSON(): void {
    if (this.isBusy) {
      this.showErrorDialog('Impossible de télécharger pendant la sauvegarde.');
      return;
    }
    
    try {
      const filename = `${this.transformedRule.rule_name || 'transformed-rule'}.json`;
      const json = this.generateJSON();
      const blob = new Blob([json], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading JSON:', error);
      this.showErrorDialog('Erreur lors du téléchargement du fichier JSON.');
    }
  }

  // === UTILITY METHODS ===

  onCancel(): void {
    if (this.isSaving) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: 'Sauvegarde en cours',
          message: 'Une sauvegarde est en cours. Voulez-vous vraiment annuler ?',
          icon: 'warning'
        },
        disableClose: true,
        panelClass: 'confirmation-dialog-panel'
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.dialogRef.close();
        }
      });
      return;
    }

    this.dialogRef.close();
  }

  retryLoadData(): void {
    this.loadInitialData();
  }

  private showErrorDialog(message: string): void {
    this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Erreur',
        message: message,
        icon: 'error',
        confirmText: 'OK'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    });
  }
}