// local-rule.component.ts - Enhanced with improved step navigation and animations
import { Component, OnInit, Input, Inject, untracked } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { forkJoin } from 'rxjs';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';

// Import the services
import { RulesApiService } from '@app/core/services/administrative/rules.service';
import { TransactionApiService } from '@app/core/services/administrative/transaction.service';
import { ControllerApiService } from '@app/core/services/administrative/controller.service';
import { CapteurApiService } from '@app/core/services/administrative/capteur.service';
import { RuleTransactionApiService } from '@app/core/services/administrative/ruletransaction.service';
import { TypeCapteurApiService } from '@app/core/services/administrative/typecapteur.service';
import { VariablesApiService } from '@app/core/services/administrative/variables.service';

// Import the backend models
import { Controller } from '@app/core/models/controller';
import { Transaction } from '@app/core/models/transaction';
import { Capteur } from '@app/core/models/capteur';
import { Rules } from '@app/core/models/rules';
import { TypeCapteur } from '@app/shared/models/typeCapteur';
import { Variables } from '@app/core/models/variables';
import { RuleDto } from '@app/shared/models/RuleDto';
import { PublishAction } from '@app/shared/models/PublishAction';
import { SensorDataCondition } from '@app/shared/models/SensorDataCondition';
import { TimeCondition } from '@app/shared/models/TimeCondition';
import { DeviceTypes } from '@app/shared/models/DeviceTypes';
import { PayloadOption } from '@app/shared/models/PayloadOption';

import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { PublishRuleService } from '@app/core/services/publish-rule.service';
import { DeviceMapping } from '../../../shared/models/DeviceMapping';
import { ActionMapping } from '../../../shared/models/ActionMapping';
import { StepState } from '../../../shared/models/StepState';
import { RuleTransaction } from '@app/core/models/ruleTransaction';
export type Condition = SensorDataCondition | TimeCondition;

@Component({
  selector: 'app-local-rule',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    NgxUiLoaderModule
  ],
  templateUrl: './local-rule.component.html',
  styleUrls: ['./local-rule.component.css']
})
export class LocalRuleComponent implements OnInit {
  // Rule selection
  selectedRuleId?: string;
  ruleList: Rules[] = [];
  baseRule: RuleDto | null = null;
  selectedRuleSummary?: string;
  
  // Controller and transaction selection
  controllers: Controller[] = [];
  selectedControllerId?: string;
  
  // Device data from backend
  availableCapteurs: Capteur[] = [];
  typeCapteurs: TypeCapteur[] = [];
  variables: Variables[] = [];
  
  // Capteurs available for mapping based on selected transactions
  availableCapteursForMapping: Capteur[] = [];

  // Processed device data structure
  deviceTypes: DeviceTypes = {
    sensors: {},
    actuators: {}
  };
  
  // Device mappings for rule transformation
  deviceMappings: DeviceMapping[] = [];
  actionMappings: ActionMapping[] = [];
  
  // Enhanced step management
  steps: { [key: string]: StepState } = {
    ruleSelection: { completed: false, valid: false, animating: false, accessible: true, collapsed: false },
    controllerSelection: { completed: false, valid: false, animating: false, accessible: false, collapsed: true },
    deviceMapping: { completed: false, valid: false, animating: false, accessible: false, collapsed: true },
    actionMapping: { completed: false, valid: false, animating: false, accessible: false, collapsed: true },
    preview: { completed: false, valid: false, animating: false, accessible: false, collapsed: true }
  };

  currentStep: number = 1;
  isTransitioning: boolean = false; // New property to handle step transitions
  
  // Final rule for preview
  transformedRule: RuleDto = {
    rule_name: '',
    topic_pattern: [],
    conditions: {
      operator: 'AND',
      groups: [
        {
          operator: 'AND',
          conditions: []
        }
      ]
    },
    actions: [],
    schedule_config: { enabled: false },
    enabled: true,
    priority: 1
  };

  // Loading states
  isLoading = true;
  loadingError: string | null = null;
  isSaving = false;
  
  // UI state
  priorities = [1, 2, 3, 4, 5];
  operators = [
    { label: '=', value: '==' },
    { label: '≠', value: '!=' },
    { label: '>', value: '>' },
    { label: '<', value: '<' },
    { label: '≥', value: '>=' },
    { label: '≤', value: '<=' }
  ];

  constructor(
    public dialogRef: MatDialogRef<LocalRuleComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { 
      idLocal: string;
    },
    private rulesService: RulesApiService,
    private transactionService: TransactionApiService,
    private controllerService: ControllerApiService,
    private capteurService: CapteurApiService,
    private ruleTransactionService: RuleTransactionApiService,
    private typeCapteurService: TypeCapteurApiService,
    private variablesService: VariablesApiService,
    private dialog: MatDialog,
    private ngxUiLoaderService: NgxUiLoaderService, 
     private publishRuleService: PublishRuleService
  ) {
    console.log('LocalRuleComponent initialized with data:', this.data);
  }

  ngOnInit(): void {
    this.loadInitialData();
  }

  get mappedDeviceCount(): number {
    return this.deviceMappings.filter(m => m.selectedCapteurId).length;
  }
  
  get mappedActionCount(): number {
    return this.actionMappings.filter(m => m.selectedCapteurId).length;
  }

  get isRuleSelected(): boolean {
    const result = !!this.selectedRuleId && !!this.baseRule;
    console.log('isRuleSelected:', result, { selectedRuleId: this.selectedRuleId, baseRule: !!this.baseRule });
    return result;
  }

  // New getter to get the selected controller's model
  getSelectedControllerModel(): string {
    const selectedController = this.controllers.find(c => c.Id === this.selectedControllerId);
    return selectedController ? selectedController.Model || 'N/A' : 'N/A';
  }


  
  // Enhanced step management methods
  private updateStepState(stepKey: string, completed: boolean, valid: boolean): void {
    if (this.steps[stepKey]) {
      const wasCompleted = this.steps[stepKey].completed;
      this.steps[stepKey].completed = completed;
      this.steps[stepKey].valid = valid;
      
      if (completed && valid && !wasCompleted) {
        this.animateStepCompletion(stepKey);
        this.updateStepAccessibility();
      }
    }
  }

  private animateStepCompletion(stepKey: string): void {
    if (this.steps[stepKey]) {
      this.steps[stepKey].animating = true;
      
      // Reset animation after delay
      setTimeout(() => {
        if (this.steps[stepKey]) {
          this.steps[stepKey].animating = false;
        }
      }, 1000);
    }
  }

  private updateStepAccessibility(): void {
    const stepKeys = ['ruleSelection', 'controllerSelection', 'deviceMapping', 'actionMapping', 'preview'];
    
    // First step is always accessible
    this.steps['ruleSelection'].accessible = true;
    
    // Other steps become accessible based on previous step completion
    for (let i = 1; i < stepKeys.length; i++) {
      const currentStepKey = stepKeys[i];
      const previousStepKey = stepKeys[i - 1];
      
      // Step becomes accessible if previous step is completed
      this.steps[currentStepKey].accessible = this.steps[previousStepKey].completed;
      
      // Special logic for optional steps
      if (currentStepKey === 'deviceMapping' && this.deviceMappings.length === 0) {
        this.steps[currentStepKey].completed = true;
        this.steps[currentStepKey].valid = true;
        this.steps[currentStepKey].accessible = this.steps[previousStepKey].completed;
      }
      
      if (currentStepKey === 'actionMapping' && this.actionMappings.length === 0) {
        this.steps[currentStepKey].completed = true;
        this.steps[currentStepKey].valid = true;
        this.steps[currentStepKey].accessible = this.steps[previousStepKey].completed;
      }
    }
  }

  private advanceToNextStep(): void {
    if (this.currentStep < 5 && !this.isTransitioning) {
      this.isTransitioning = true;
      
      // Collapse current step if it's completed
      const currentStepKey = this.getStepKey(this.currentStep);
      if (currentStepKey && this.steps[currentStepKey].completed) {
        this.steps[currentStepKey].collapsed = true;
      }
      
      setTimeout(() => {
        this.currentStep++;
        const nextStepKey = this.getStepKey(this.currentStep);
        if (nextStepKey) {
          this.steps[nextStepKey].collapsed = false;
        }
        this.isTransitioning = false;
      }, 300);
    }
  }

  // New method to handle step click navigation
  goToStep(stepNumber: number): void {
    if (this.isTransitioning || this.isBusy) return;
    
    const stepKey = this.getStepKey(stepNumber);
    if (!stepKey || !this.steps[stepKey].accessible) return;
    
    if (stepNumber === this.currentStep) return; // Already on this step
    
    this.isTransitioning = true;
    
    // Collapse all steps first
    Object.keys(this.steps).forEach(key => {
      this.steps[key].collapsed = true;
    });
    
    setTimeout(() => {
      this.currentStep = stepNumber;
      const targetStepKey = this.getStepKey(stepNumber);
      if (targetStepKey) {
        this.steps[targetStepKey].collapsed = false;
      }
      this.isTransitioning = false;
    }, 200);
  }

  // Enhanced navigation methods
  goToPreviousStep(): void {
    if (this.currentStep > 1 && !this.isTransitioning) {
      this.goToStep(this.currentStep - 1);
    }
  }

  goToNextStep(): void {
    if (this.currentStep < 5 && !this.isTransitioning) {
      const nextStepKey = this.getStepKey(this.currentStep + 1);
      if (nextStepKey && this.steps[nextStepKey].accessible) {
        this.goToStep(this.currentStep + 1);
      }
    }
  }

  private getStepKey(stepNumber: number): string | null {
    const stepKeys = ['ruleSelection', 'controllerSelection', 'deviceMapping', 'actionMapping', 'preview'];
    return stepKeys[stepNumber - 1] || null;
  }

  isStepCompleted(stepNumber: number): boolean {
    const stepKey = this.getStepKey(stepNumber);
    return stepKey ? this.steps[stepKey].completed : false;
  }

  isStepValid(stepNumber: number): boolean {
    const stepKey = this.getStepKey(stepNumber);
    return stepKey ? this.steps[stepKey].valid : false;
  }

  isStepAnimating(stepNumber: number): boolean {
    const stepKey = this.getStepKey(stepNumber);
    return stepKey ? this.steps[stepKey].animating : false;
  }

  isStepAccessible(stepNumber: number): boolean {
    const stepKey = this.getStepKey(stepNumber);
    return stepKey ? this.steps[stepKey].accessible : false;
  }

  isStepCollapsed(stepNumber: number): boolean {
    const stepKey = this.getStepKey(stepNumber);
    return stepKey ? this.steps[stepKey].collapsed : true;
  }

  // === DATA LOADING ===

  private loadInitialData(): void {
    console.log('Loading initial data...');
    this.isLoading = true;
    this.loadingError = null;

    forkJoin({
      rules: this.rulesService.getAll(),
      controllers: this.controllerService.getAll(),
      capteurs: this.capteurService.getAll(),
      typeCapteurs: this.typeCapteurService.getAll(),
      variables: this.variablesService.getAll()
    }).subscribe({
      next: (data) => {
        console.log('Initial data loaded:', data);
        this.ruleList = data.rules;
        this.controllers = data.controllers;
        this.availableCapteurs = data.capteurs;
        this.typeCapteurs = data.typeCapteurs;
        this.variables = data.variables;
        
        // Hydrate TypeCapteur on Capteur objects
        this.availableCapteurs.forEach(capteur => {
          if (capteur.IdTypeCapteur) {
            capteur.TypeCapteur = this.typeCapteurs.find(tc => tc.Id === capteur.IdTypeCapteur);
          }
        });
        console.log('Capteurs hydrated with TypeCapteur data:', this.availableCapteurs);

        // Process backend data
        this.processBackendData();
        
        console.log('Rules loaded:', this.ruleList.length);
        console.log('Controllers loaded:', this.controllers.length);
        console.log('Capteurs loaded:', this.availableCapteurs.length);
        console.log('Device types processed:', this.deviceTypes);
        
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading initial data:', error);
        this.loadingError = 'Erreur lors du chargement des données initiales.';
        this.isLoading = false;
      }
    });
  }

  // Process backend data like in rule-form component
  private processBackendData(): void {
    this.deviceTypes = { sensors: {}, actuators: {} };

    this.typeCapteurs.forEach(typeCapteur => {
      const relatedVariables = this.variables.filter(v => v.IdTypeCapteur === typeCapteur.Id);
      
      const deviceCategory: 'sensors' | 'actuators' = 
        typeCapteur.DeviceType?.toLowerCase() === 'actuator' ? 'actuators' : 'sensors';
      
      const deviceKey = this.normalizeDeviceKey(typeCapteur.Nom);
      
      if (deviceCategory === 'sensors') {
        this.deviceTypes.sensors[deviceKey] = {
          topic: typeCapteur.Topic,
          device_name: typeCapteur.Nom,
          DisplayName: typeCapteur.DisplayName || typeCapteur.Nom,
          properties: relatedVariables.map(variable => ({
            key: variable.Key,
            type: this.mapVariableType(variable.Type),
            values: this.parseVariableValues(variable.Actions)
          }))
        };
      } else {
        this.deviceTypes.actuators[deviceKey] = {
          topic: typeCapteur.Topic,
          device_name: typeCapteur.Nom,
          DisplayName: typeCapteur.DisplayName || typeCapteur.Nom,
          actions: relatedVariables.map(variable => ({
            type: variable.Key,
            payload: {},
            options: this.parseActionOptions(variable.Actions)
          }))
        };
      }
    });

    console.log('Processed device types:', this.deviceTypes);
  }

  private normalizeDeviceKey(name: string): string {
    return name.toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^a-z0-9_]/g, '')
      .replace(/^sensor_|^actuator_/, '');
  }

  private mapVariableType(backendType: string): string {
    const typeMapping: { [key: string]: string } = {
      'String': 'string',
      'Integer': 'number',
      'Float': 'number',
      'Double': 'number',
      'Boolean': 'boolean',
      'Bool': 'boolean'
    };
    return typeMapping[backendType] || 'string';
  }

  private parseVariableValues(actions: string[] | string | null): (string | number | boolean)[] {
    if (!actions) return [];
    
    if (Array.isArray(actions)) {
      return actions;
    }
    
    if (typeof actions === 'string') {
      try {
        const parsed = JSON.parse(actions);
        if (Array.isArray(parsed)) {
          return parsed;
        }
      } catch {
        const values = actions.split(/[,;|]/).map(v => v.trim()).filter(v => v);
        return values;
      }
    }
    
    return [];
  }

  private parseActionOptions(actions: string[] | string | null): PayloadOption[] {
    if (!actions) return [];
    
    if (Array.isArray(actions)) {
      return actions.map((item: any) => {
        if (typeof item === 'object' && item !== null) {
          return {
            display: item.display || item.label || item.name || String(item.value || item),
            value: String(item.value || item)
          };
        } else {
          return {
            display: String(item),
            value: String(item)
          };
        }
      });
    }
    
    if (typeof actions === 'string') {
      try {
        const parsed = JSON.parse(actions);
        if (Array.isArray(parsed)) {
          return parsed.map((item: any) => ({
            display: item.display || item.label || item.name || String(item.value || item),
            value: String(item.value || item)
          }));
        }
      } catch {
        const values = actions.split(/[,;|]/).map(v => v.trim()).filter(v => v);
        return values.map(value => ({
          display: value,
          value: value
        }));
      }
    }
    
    return [];
  }

  // === RULE SELECTION ===

  onRuleSelectionChange(): void {
    console.log('Rule selection changed:', this.selectedRuleId);
    if (this.selectedRuleId) {
      this.loadSelectedRule();
    } else {
      this.resetRule();
      this.updateStepState('ruleSelection', false, false);
    }
  }

  private loadSelectedRule(): void {
    const selectedRule = this.ruleList.find(rule => rule.Id === this.selectedRuleId);
    console.log('Loading selected rule:', selectedRule);
    
    if (selectedRule && selectedRule.RawData) {
      try {
        const parsedRule = this.parseAndValidateRule(selectedRule.RawData);
        if (!parsedRule) {
          throw new Error('Failed to parse rule data');
        }
        
        this.baseRule = parsedRule;
        this.selectedRuleSummary = selectedRule.Summary || '';
        
        this.transformedRule = JSON.parse(JSON.stringify(this.baseRule));
        
        this.transformedRule.rule_name = `${this.baseRule.rule_name} - Transformée`;
        
        this.initializeDeviceMappings();
        
        // Update step state and advance
        this.updateStepState('ruleSelection', true, true);
        this.advanceToNextStep();
        
        console.log('Base rule loaded successfully:', this.baseRule);
        console.log('Device mappings initialized:', this.deviceMappings);
        console.log('Action mappings initialized:', this.actionMappings);
      } catch (error) {
        console.error('Error loading rule:', error, 'Raw data:', selectedRule.RawData);
        this.selectedRuleId = undefined;
        this.baseRule = null;
        this.updateStepState('ruleSelection', false, false);
      }
    } else {
      console.error('Selected rule has no RawData:', selectedRule);
      this.showErrorDialog('La règle sélectionnée ne contient pas de données valides.');
      this.selectedRuleId = undefined;
      this.baseRule = null;
      this.selectedRuleSummary = '';
      this.updateStepState('ruleSelection', false, false);
    }
  }

  private parseAndValidateRule(rawData: string): RuleDto | null {
    try {
      console.log('Parsing raw rule data:', rawData);
      const parsedRule = JSON.parse(rawData);
      console.log('Initial parsed rule:', parsedRule);
      
      const ruleDto: RuleDto = {
        rule_name: parsedRule.rule_name || 'Unnamed Rule',
        topic_pattern: Array.isArray(parsedRule.topic_pattern) ? parsedRule.topic_pattern : [],
        conditions: {
          operator: parsedRule.conditions?.operator || 'AND',
          groups: []
        },
        actions: [],
        schedule_config: parsedRule.schedule_config || { enabled: false },
        enabled: parsedRule.enabled !== undefined ? parsedRule.enabled : true,
        priority: parsedRule.priority || 1
      };

      // Handle conditions
      if (parsedRule.conditions) {
        if (parsedRule.conditions.groups && Array.isArray(parsedRule.conditions.groups)) {
          ruleDto.conditions.groups = parsedRule.conditions.groups.map((group: any) => ({
            operator: group.operator || 'AND',
            conditions: Array.isArray(group.conditions) ? group.conditions.map((condition: any) => this.normalizeCondition(condition)) : []
          }));
        } else if (Array.isArray(parsedRule.conditions)) {
          ruleDto.conditions.groups = [{
            operator: 'AND',
            conditions: parsedRule.conditions.map((condition: any) => this.normalizeCondition(condition))
          }];
        } else {
          ruleDto.conditions.groups = [{
            operator: 'AND',
            conditions: []
          }];
        }
      }

      // Handle actions
      if (Array.isArray(parsedRule.actions)) {
        ruleDto.actions = parsedRule.actions.map((action: any) => this.normalizeAction(action));
      }

      console.log('Normalized rule DTO:', ruleDto);
      return ruleDto;
      
    } catch (error) {
      console.error('Failed to parse rule:', error);
      return null;
    }
  }

  private normalizeCondition(condition: any): Condition {
    if (condition.type === 'time') {
      return {
        type: 'time',
        start_time: condition.start_time || '',
        end_time: condition.end_time || ''
      } as TimeCondition;
    } else {
      return {
        type: 'payload',
        device: condition.device || '',
        key: condition.key || '',
        operator: condition.operator || '==',
        value: condition.value || ''
      } as SensorDataCondition;
    }
  }

  private normalizeAction(action: any): PublishAction {
    return {
      type: action.type || '',
      topic: action.topic || '',
      payload: action.payload || {},
      message: action.message || undefined
    } as PublishAction;
  }

  private resetRule(): void {
    console.log('Resetting rule data');
    this.baseRule = null;
    this.transformedRule = {
      rule_name: '',
      topic_pattern: [],
      conditions: { 
        operator: 'AND',
        groups: [{ operator: 'AND', conditions: [] }] 
      },
      actions: [],
      schedule_config: { enabled: false },
      enabled: true,
      priority: 1
    };
    this.deviceMappings = [];
    this.actionMappings = [];
    this.selectedControllerId = undefined;
    this.availableCapteursForMapping = [];
    
    // Reset all steps
    Object.keys(this.steps).forEach((key, index) => {
      this.steps[key] = { 
        completed: false, 
        valid: false, 
        animating: false, 
        accessible: index === 0, // Only first step accessible initially
        collapsed: index !== 0    // Only first step expanded initially
      };
    });
    this.currentStep = 1;
  }

  getRuleNameFromRawData(rawData: string): string {
    if (!rawData) return 'Unnamed Rule';
    try {
      const rule = JSON.parse(rawData);
      return rule.rule_name?.trim() ? rule.rule_name : 'Unnamed Rule';
    } catch (e) {
      return 'Invalid Rule Data';
    }
  }

  // === CONTROLLER AND TRANSACTION SELECTION ===

  onControllerSelectionChange(): void {
    console.log('Controller selection changed:', this.selectedControllerId);
    if (this.selectedControllerId) {
      this.filterCapteursByController();
      this.updateStepState('controllerSelection', true, true);
      this.advanceToNextStep();
    } else {
      this.availableCapteursForMapping = [];
      this.updateStepState('controllerSelection', false, false);
    }
  }

  private filterCapteursByController(): void {
    if (!this.selectedControllerId) return;

    console.log('Filtering capteurs for controller:', this.selectedControllerId);
    this.transactionService.getAll().subscribe({
      next: (transactions: Transaction[]) => {
        const relevantTransactions = transactions.filter(t => 
          t.IdLocal === this.data.idLocal && t.IdController === this.selectedControllerId && t.IdCapteur
        );
        console.log('Relevant transactions for controller:', relevantTransactions.length, relevantTransactions);

        const uniqueCapteurIds = new Set(relevantTransactions.map(t => t.IdCapteur).filter((id): id is string => id !== undefined));
        
        this.availableCapteursForMapping = this.availableCapteurs.filter(capteur => 
          capteur.Id !== undefined && uniqueCapteurIds.has(capteur.Id)
        );
        console.log('Available Capteurs for mapping based on controller transactions:', this.availableCapteursForMapping.length, this.availableCapteursForMapping);
      },
      error: (error) => {
        console.error('Error filtering capteurs by controller:', error);
        this.availableCapteursForMapping = [];
      }
    });
  }

  // === DEVICE MAPPING INITIALIZATION ===

  private initializeDeviceMappings(): void {
    if (!this.baseRule) {
      console.log('No base rule available for mapping initialization');
      return;
    }

    console.log('Initializing device mappings from base rule:', this.baseRule);

    this.deviceMappings = [];
    
    if (this.baseRule.conditions && this.baseRule.conditions.groups) {
      this.baseRule.conditions.groups.forEach((group, groupIndex) => {
        console.log(`Processing condition group ${groupIndex}:`, group);
        
        if (group.conditions && Array.isArray(group.conditions)) {
          group.conditions.forEach((condition, conditionIndex) => {
            console.log(`Processing condition ${groupIndex}.${conditionIndex}:`, condition);
            
            if (condition.type === 'payload') {
              const sensorCondition = condition as SensorDataCondition;
              const normalizedDeviceName = this.normalizeDeviceKey(sensorCondition.device);
              const deviceData = this.deviceTypes.sensors[normalizedDeviceName];

              if (sensorCondition.device && !this.deviceMappings.find(m => m.originalDevice === sensorCondition.device)) {
                this.deviceMappings.push({
                  originalDevice: sensorCondition.device,
                  originalDeviceType: 'sensor',
                  originalDeviceTypeName: deviceData?.DisplayName || sensorCondition.device,
                  selectedCapteurId: '',
                  selectedCapteur: undefined,
                  availableProperties: []
                });
                console.log('Added device mapping for:', sensorCondition.device);
              }
            }
          });
        } else {
          console.warn(`Group ${groupIndex} has no conditions array:`, group);
        }
      });
    } else {
      console.warn('Base rule has no conditions.groups:', this.baseRule.conditions);
    }

    this.actionMappings = [];
    
    if (this.baseRule.actions && Array.isArray(this.baseRule.actions)) {
      this.baseRule.actions.forEach((action, actionIndex) => {
        console.log(`Processing action ${actionIndex}:`, action);
        
        if (action.topic && action.type !== 'log' && !this.actionMappings.find(m => m.originalTopic === action.topic)) {
          const inferredActuatorType = this.typeCapteurs.find(tc => tc.Topic === action.topic && tc.DeviceType?.toLowerCase() === 'actuator');

          this.actionMappings.push({
            originalTopic: action.topic,
            originalDeviceType: 'actuator',
            originalDeviceTypeName: inferredActuatorType?.DisplayName || inferredActuatorType?.Nom || action.topic,
            originalTopicForFiltering: action.topic,
            selectedCapteurId: '',
            selectedCapteur: undefined,
            availableActions: []
          });
          console.log('Added action mapping for:', action.topic);
        }
      });
    } else {
      console.warn('Base rule has no actions array:', this.baseRule.actions);
    }

    console.log('Final device mappings:', this.deviceMappings);
    console.log('Final action mappings:', this.actionMappings);
    
    // Update step accessibility after initialization
    this.updateStepAccessibility();
  }

  // === DEVICE MAPPING HANDLERS ===

  onDeviceMappingChange(mapping: DeviceMapping): void {
    console.log('Device mapping changed:', mapping);
    if (mapping.selectedCapteurId) {
      const selectedCapteur = this.availableCapteursForMapping.find(c => c.Id === mapping.selectedCapteurId);
      if (selectedCapteur) {
        mapping.selectedCapteur = selectedCapteur;
        mapping.selectedTypeCapteur = selectedCapteur.TypeCapteur;
        
        const deviceKey = this.normalizeDeviceKey(selectedCapteur.TypeCapteur?.Nom || '');
        const deviceData = this.deviceTypes.sensors[deviceKey];
        mapping.availableProperties = deviceData ? deviceData.properties || [] : [];
        
        console.log('Mapped device to Capteur:', mapping.selectedCapteur);
        console.log('Available properties:', mapping.availableProperties);
      }
    } else {
      mapping.selectedCapteur = undefined;
      mapping.selectedTypeCapteur = undefined;
      mapping.availableProperties = [];
    }
    
    this.updateTransformedRule();
    this.checkDeviceMappingCompletion();
  }

  onActionMappingChange(mapping: ActionMapping): void {
    console.log('Action mapping changed:', mapping);
    if (mapping.selectedCapteurId) {
      const selectedCapteur = this.availableCapteursForMapping.find(c => c.Id === mapping.selectedCapteurId);
      if (selectedCapteur) {
        mapping.selectedCapteur = selectedCapteur;
        mapping.selectedTypeCapteur = selectedCapteur.TypeCapteur;
        
        const deviceKey = this.normalizeDeviceKey(selectedCapteur.TypeCapteur?.Nom || '');
        const deviceData = this.deviceTypes.actuators[deviceKey];
        mapping.availableActions = deviceData ? deviceData.actions || [] : [];
        
        console.log('Mapped action to Capteur:', mapping.selectedCapteur);
        console.log('Available actions:', mapping.availableActions);
      }
    } else {
      mapping.selectedCapteur = undefined;
      mapping.selectedTypeCapteur = undefined;
      mapping.availableActions = [];
    }
    
    this.updateTransformedRule();
    this.checkActionMappingCompletion();
  }

  private checkDeviceMappingCompletion(): void {
    const allMapped = this.deviceMappings.length === 0 || this.deviceMappings.every(m => m.selectedCapteurId);
    if (allMapped) {
      this.updateStepState('deviceMapping', true, true);
      if (this.currentStep === 3 && this.deviceMappings.length > 0) {
        this.advanceToNextStep();
      }
    } else {
      this.updateStepState('deviceMapping', false, allMapped);
    }
  }

  private checkActionMappingCompletion(): void {
    const allMapped = this.actionMappings.length === 0 || this.actionMappings.every(m => m.selectedCapteurId);
    if (allMapped) {
      this.updateStepState('actionMapping', true, true);
      if (this.currentStep === 4 && this.actionMappings.length > 0) {
        this.advanceToNextStep();
      }
    } else {
      this.updateStepState('actionMapping', false, allMapped);
    }
    
    // Update preview step if both mappings are complete
    this.checkPreviewCompletion();
  }

  checkPreviewCompletion(): void {
    const deviceMappingComplete = this.deviceMappings.length === 0 || this.deviceMappings.every(m => m.selectedCapteurId);
    const actionMappingComplete = this.actionMappings.length === 0 || this.actionMappings.every(m => m.selectedCapteurId);
    const ruleNameValid = this.transformedRule.rule_name && this.transformedRule.rule_name.trim();
    
    if (deviceMappingComplete && actionMappingComplete && ruleNameValid) {
      this.updateStepState('preview', true, true);
    } else {
      this.updateStepState('preview', false, false);
    }
  }

  // === HELPER METHODS FOR DROPDOWNS ===

  getFilteredCapteursForDeviceMapping(mapping: DeviceMapping): Capteur[] {
    if (!this.selectedControllerId) {
      return [];
    }
    return this.availableCapteursForMapping.filter(c => 
      c.TypeCapteur?.DeviceType?.toLowerCase() === 'sensor' &&
      this.normalizeDeviceKey(c.TypeCapteur?.Nom || '') === this.normalizeDeviceKey(mapping.originalDevice || '') 
    );
  }

  getFilteredCapteursForActionMapping(mapping: ActionMapping): Capteur[] {
    if (!this.selectedControllerId) {
      return [];
    }
    return this.availableCapteursForMapping.filter(c => 
      c.TypeCapteur?.DeviceType?.toLowerCase() === 'actuator' &&
      c.TypeCapteur?.Topic === mapping.originalTopicForFiltering
    );
  }

  // === RULE TRANSFORMATION ===

  private updateTransformedRule(): void {
    if (!this.baseRule) {
      console.log('No base rule available for transformation');
      return;
    }

    console.log('Updating transformed rule...');
    
    this.transformedRule = JSON.parse(JSON.stringify(this.baseRule));

    // Transform conditions - handle multiple groups
    if (this.transformedRule.conditions && this.transformedRule.conditions.groups) {
      this.transformedRule.conditions.groups.forEach((group, groupIndex) => {
        if (group.conditions && Array.isArray(group.conditions)) {
          group.conditions.forEach((condition, conditionIndex) => {
            if (condition.type === 'payload') {
              const sensorCondition = condition as SensorDataCondition;
             const mapping = this.deviceMappings.find(m => m.originalDevice === sensorCondition.device);
               if (mapping && mapping.selectedTypeCapteur) {
              sensorCondition.device = mapping.selectedTypeCapteur.Nom;

            // ✅ Convert boolean-like string values to uppercase
            if (typeof sensorCondition.value === 'string' && 
            (sensorCondition.value.toLowerCase() === 'true' || sensorCondition.value.toLowerCase() === 'false')) {
            sensorCondition.value = sensorCondition.value.toUpperCase();
             }

             console.log(`Transformed device in group ${groupIndex}, condition ${conditionIndex}: ${sensorCondition.device}`);
            }
            }
          });
        }
      });
    }

    // Transform actions
    if (this.transformedRule.actions && Array.isArray(this.transformedRule.actions)) {
      this.transformedRule.actions.forEach((action, actionIndex) => {
       if (action.type !== 'log' && action.topic) {
  const mapping = this.actionMappings.find(m => m.originalTopic === action.topic);
  if (mapping && mapping.selectedCapteur) {
    const oldTopic = action.topic;

    // Ensure topic ends with /set
    let topic = mapping.selectedCapteur.Topic || '';
    if (!topic.endsWith('/set')) {
      topic += '/set';
    }

    action.topic = topic;
    console.log(`Transformed action ${actionIndex} topic: ${oldTopic} -> ${action.topic}`);
  }
}

      });
    }

    this.updateTopicPattern();
    this.checkPreviewCompletion();
    
    console.log('Transformed rule updated:', this.transformedRule);
  }

  private updateTopicPattern(): void {
    const topics: string[] = [];

    this.deviceMappings.forEach(mapping => {
      if (mapping.selectedCapteur && mapping.selectedCapteur.Topic) {
        if (!topics.includes(mapping.selectedCapteur.Topic)) {
          topics.push(mapping.selectedCapteur.Topic);
        }
      }
    });

    // this.actionMappings.forEach(mapping => {
    //   if (mapping.selectedCapteur && mapping.selectedCapteur.Topic) {
    //     if (!topics.includes(mapping.selectedCapteur.Topic)) {
    //       topics.push(mapping.selectedCapteur.Topic);
    //     }
    //   }
    // });

    this.transformedRule.topic_pattern = topics;
    console.log('Updated topic pattern:', topics);
  }

  // === VALIDATION ===

  private validateMappings(): { valid: boolean; message?: string } {
    if (!this.baseRule || !this.selectedRuleId) {
      return {
        valid: false,
        message: 'Veuillez sélectionner une règle de base valide.'
      };
    }

    if (!this.selectedControllerId) {
      return {
        valid: false,
        message: 'Veuillez sélectionner un contrôleur.'
      };
    }

    const incompleteMappings = this.deviceMappings.filter(m => !m.selectedCapteurId);
    if (incompleteMappings.length > 0) {
      return {
        valid: false,
        message: `Veuillez sélectionner des capteurs pour: ${incompleteMappings.map(m => m.originalDevice).join(', ')}`
      };
    }

    const incompleteActions = this.actionMappings.filter(m => !m.selectedCapteurId);
    if (incompleteActions.length > 0) {
      return {
        valid: false,
        message: `Veuillez sélectionner des actionneurs pour: ${incompleteActions.map(m => m.originalTopic).join(', ')}`
      };
    }

    if (!this.transformedRule.rule_name || !this.transformedRule.rule_name.trim()) {
      return {
        valid: false,
        message: 'Veuillez fournir un nom pour la règle transformée.'
      };
    }

    return { valid: true };
  }

  // === SAVE FUNCTIONALITY ===

  onSave(): void {
    if (!this.baseRule || !this.selectedRuleId) {
      this.showErrorDialog('Veuillez sélectionner une règle de base.');
      return;
    }

    const validation = this.validateMappings();
    if (!validation.valid) {
      this.showErrorDialog(validation.message!);
      return;
    }

    this.performSave();
  }

private performSave(): void {
  console.log('Starting save operation...');
  this.isSaving = true;
  this.ngxUiLoaderService.start();

  const capteurIdsInRule = new Set<string>();
  this.deviceMappings.forEach(m => m.selectedCapteurId && capteurIdsInRule.add(m.selectedCapteurId));
  this.actionMappings.forEach(m => m.selectedCapteurId && capteurIdsInRule.add(m.selectedCapteurId));

  this.transactionService.getAll().subscribe({
    next: (allTransactions: Transaction[]) => {
      const transactionsByCapteur = new Map<string, Transaction>();

      capteurIdsInRule.forEach(capteurId => {
        const match = allTransactions.find(t =>
          t.IdCapteur === capteurId &&
          t.IdLocal === this.data.idLocal &&
          t.IdController === this.selectedControllerId
        );
        if (match) transactionsByCapteur.set(capteurId, match);
      });

      if (transactionsByCapteur.size === 0) {
        console.warn('No matching transactions found');
        this.showErrorDialog('Aucune transaction pertinente trouvée pour les capteurs utilisés.');
        this.isSaving = false;
        this.ngxUiLoaderService.stop();
        return;
      }

const uniqueTransactions = new Map<string, Transaction>();
transactionsByCapteur.forEach(t => {
  if (t.Id && !uniqueTransactions.has(t.Id)) {
    uniqueTransactions.set(t.Id, t);
  }
});

const ruleTransactions = Array.from(uniqueTransactions.values()).map(transaction => ({
  IdRule: this.selectedRuleId!,
  IdTransaction: transaction.Id!,
  RawData: JSON.stringify(this.transformedRule)
}));


      const creationRequests = ruleTransactions.map(rt =>
        this.ruleTransactionService.create(rt)
      );

      forkJoin(creationRequests).subscribe({
        next: (createdRuleTransactions) => {
          console.log('RuleTransactions created successfully:', createdRuleTransactions);
          this.handleSaveSuccess(createdRuleTransactions); // <-- this is fine to keep
        },
        error: (error) => {
          console.error('Error creating RuleTransactions:', error);
          this.handleSaveError(error);
        }
      });
    },
    error: (error) => {
      console.error('Error fetching transactions:', error);
      this.showErrorDialog('Erreur lors de la récupération des transactions pour la sauvegarde.');
      this.isSaving = false;
      this.ngxUiLoaderService.stop();
    }
  });
}

  

private handleSaveSuccess(createdRuleTransactions: any[]): void {
  this.isSaving = false;
  this.ngxUiLoaderService.stop();

  // Publish the rule
  if (this.selectedControllerId && this.data.idLocal && this.transformedRule) {
    this.publishRuleService.publishRule(
      this.selectedRuleId!,
      this.data.idLocal,
      this.selectedControllerId,
      this.transformedRule
    );
  }

  // Feedback or dialog closing
  this.dialogRef.close({
    action: 'saved',
    ruleTransactions: createdRuleTransactions,
    transformedRule: this.transformedRule,
    transactionCount: createdRuleTransactions.length
  });

  console.log('✅ Rule saved and published successfully.');
}





  private handleSaveError(error: any): void {
    this.isSaving = false;
    this.ngxUiLoaderService.stop();
    this.showErrorDialog('Erreur lors de la sauvegarde des RuleTransactions.');
  }

  // === UI STATE METHODS ===

  isFieldDisabled(): boolean {
    return this.isSaving || this.isLoading || this.isTransitioning;
  }

  get isBusy(): boolean {
    return this.isSaving || this.isTransitioning;
  }

  get statusMessage(): string {
    if (this.isSaving) return 'Sauvegarde en cours...';
    return '';
  }

  get operationProgress(): number {
    return this.isSaving ? 70 : 0;
  }

  // === JSON GENERATION ===

  generateJSON(): string {
    try {
      return JSON.stringify(this.transformedRule, null, 2);
    } catch (error) {
      console.error('Error generating JSON:', error);
      return '{\n  "error": "Unable to generate JSON preview"\n}';
    }
  }

  downloadJSON(): void {
    if (this.isBusy) {
      this.showErrorDialog('Impossible de télécharger pendant la sauvegarde.');
      return;
    }
    
    try {
      const filename = `${this.transformedRule.rule_name || 'transformed-rule'}.json`;
      const json = this.generateJSON();
      const blob = new Blob([json], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading JSON:', error);
      this.showErrorDialog('Erreur lors du téléchargement du fichier JSON.');
    }
  }

  // === UTILITY METHODS ===

  onCancel(): void {
    if (this.isSaving) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: 'Sauvegarde en cours',
          message: 'Une sauvegarde est en cours. Voulez-vous vraiment annuler ?',
          icon: 'warning'
        },
        disableClose: true,
        panelClass: 'confirmation-dialog-panel'
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.dialogRef.close();
        }
      });
      return;
    }

    this.dialogRef.close();
  }

  retryLoadData(): void {
    this.loadInitialData();
  }
// Inside your LocalRuleComponent class

getStepLabel(step: number): string {
  switch (step) {
    case 1:
      return 'Règle';
    case 2:
      return 'Contrôleur';
    case 3:
      return 'Capteurs';
    case 4:
      return 'Actionneurs';
    case 5:
      return 'Aperçu';
    default:
      return '';
  }
}
  private showErrorDialog(message: string): void {
    this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Erreur',
        message: message,
        icon: 'error',
        confirmText: 'OK'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    });
  }
}