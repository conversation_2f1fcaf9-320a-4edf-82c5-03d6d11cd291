// local-rule.component.ts - Complete fix with robust rule parsing
import { Component, OnInit, Input, Inject, untracked } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { forkJoin } from 'rxjs';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';

// Import the services
import { RulesApiService } from '@app/core/services/administrative/rules.service';
import { TransactionApiService } from '@app/core/services/administrative/transaction.service';
import { ControllerApiService } from '@app/core/services/administrative/controller.service';
import { CapteurApiService } from '@app/core/services/administrative/capteur.service';
import { RuleTransactionApiService } from '@app/core/services/administrative/ruletransaction.service';
import { TypeCapteurApiService } from '@app/core/services/administrative/typecapteur.service';
import { VariablesApiService } from '@app/core/services/administrative/variables.service';

// Import the backend models
import { Controller } from '@app/core/models/controller';
import { Transaction } from '@app/core/models/transaction';
import { Capteur } from '@app/core/models/capteur'; // Assuming Capteur now has FriendlyName and Topic
import { Rules } from '@app/core/models/rules';
import { RuleTransaction } from '@app/core/models/ruleTransaction';
import { TypeCapteur } from '@app/shared/models/typeCapteur';
import { Variables } from '@app/core/models/variables';
import { RuleDto } from '@app/shared/models/RuleDto';
import { ConditionGroup } from '@app/shared/models/ConditionGroup';
import { PublishAction } from '@app/shared/models/PublishAction';
import { SensorDataCondition } from '@app/shared/models/SensorDataCondition';
import { TimeCondition } from '@app/shared/models/TimeCondition';
import { DeviceTypes } from '@app/shared/models/DeviceTypes';
import { DeviceProperty } from '@app/shared/models/DeviceProperty';
import { DeviceAction } from '@app/shared/models/DeviceAction';
import { PayloadOption } from '@app/shared/models/PayloadOption';

import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';

export type Condition = SensorDataCondition | TimeCondition;

interface DeviceMapping {
  originalDevice: string;
  originalDeviceType?: string; // e.g., 'sensor'
  originalDeviceTypeName?: string; // e.g., 'Motion Sensor'
  selectedCapteurId: string;
  selectedCapteur?: Capteur; // Added: Store the selected Capteur object
  selectedTypeCapteur?: TypeCapteur;
  availableProperties?: DeviceProperty[];
}

interface ActionMapping {
  originalTopic: string;
  originalDeviceType?: string; // e.g., 'actuator'
  originalDeviceTypeName?: string; // e.g., 'Roller Shade'
  originalTopicForFiltering: string; // New field to directly match against TypeCapteur.Topic
  selectedCapteurId: string;
  selectedCapteur?: Capteur; // Added: Store the selected Capteur object
  selectedTypeCapteur?: TypeCapteur;
  availableActions?: DeviceAction[];
}

@Component({
  selector: 'app-local-rule',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    NgxUiLoaderModule
  ],
  templateUrl: './local-rule.component.html',
  styleUrls: ['./local-rule.component.css']
})
export class LocalRuleComponent implements OnInit {
  // Rule selection
  selectedRuleId?: string;
  ruleList: Rules[] = [];
  baseRule: RuleDto | null = null;
  
  // Controller and transaction selection
  controllers: Controller[] = [];
  selectedControllerId?: string;
  
  // Device data from backend
  availableCapteurs: Capteur[] = [];
  typeCapteurs: TypeCapteur[] = [];
  variables: Variables[] = [];
  
  // Capteurs available for mapping based on selected transactions
  availableCapteursForMapping: Capteur[] = [];

  // Processed device data structure
  deviceTypes: DeviceTypes = {
    sensors: {},
    actuators: {}
  };
  
  // Device mappings for rule transformation
  deviceMappings: DeviceMapping[] = [];
  actionMappings: ActionMapping[] = [];
  
  // Final rule for preview
  transformedRule: RuleDto = {
    rule_name: '',
    topic_pattern: [],
    conditions: {
      groups: [
        {
          operator: 'AND',
          conditions: []
        }
      ]
    },
    actions: [],
    schedule_config: { enabled: false },
    enabled: true,
    priority: 1
  };

  // Loading states
  isLoading = true;
  loadingError: string | null = null;
  isSaving = false;
  
  // UI state
  priorities = [1, 2, 3, 4, 5];
  operators = [
    { label: '=', value: '==' },
    { label: '≠', value: '!=' },
    { label: '>', value: '>' },
    { label: '<', value: '<' },
    { label: '≥', value: '>=' },
    { label: '≤', value: '<=' }
  ];

  constructor(
    public dialogRef: MatDialogRef<LocalRuleComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { 
      idLocal: string;
    },
    private rulesService: RulesApiService,
    private transactionService: TransactionApiService,
    private controllerService: ControllerApiService,
    private capteurService: CapteurApiService,
    private ruleTransactionService: RuleTransactionApiService,
    private typeCapteurService: TypeCapteurApiService,
    private variablesService: VariablesApiService,
    private dialog: MatDialog,
    private ngxUiLoaderService: NgxUiLoaderService
  ) {
    console.log('LocalRuleComponent initialized with data:', this.data);
  }

  ngOnInit(): void {
    this.loadInitialData();
  }

  get mappedDeviceCount(): number {
    return this.deviceMappings.filter(m => m.selectedCapteurId).length;
  }
  
  get mappedActionCount(): number {
    return this.actionMappings.filter(m => m.selectedCapteurId).length;
  }

  get isRuleSelected(): boolean {
    const result = !!this.selectedRuleId && !!this.baseRule;
    console.log('isRuleSelected:', result, { selectedRuleId: this.selectedRuleId, baseRule: !!this.baseRule });
    return result;
  }

  // New getter to get the selected controller's model
  getSelectedControllerModel(): string {
    const selectedController = this.controllers.find(c => c.Id === this.selectedControllerId);
    return selectedController ? selectedController.Model || 'N/A' : 'N/A';
  }

  // === DATA LOADING ===

  private loadInitialData(): void {
    console.log('Loading initial data...');
    this.isLoading = true;
    this.loadingError = null;

    forkJoin({
      rules: this.rulesService.getAll(),
      controllers: this.controllerService.getAll(),
      capteurs: this.capteurService.getAll(),
      typeCapteurs: this.typeCapteurService.getAll(),
      variables: this.variablesService.getAll()
    }).subscribe({
      next: (data) => {
        console.log('Initial data loaded:', data);
        this.ruleList = data.rules;
        this.controllers = data.controllers;
        this.availableCapteurs = data.capteurs;
        this.typeCapteurs = data.typeCapteurs;
        this.variables = data.variables;
        
        // --- IMPORTANT: Hydrate TypeCapteur on Capteur objects ---
        this.availableCapteurs.forEach(capteur => {
          if (capteur.IdTypeCapteur) {
            capteur.TypeCapteur = this.typeCapteurs.find(tc => tc.Id === capteur.IdTypeCapteur);
          }
        });
        console.log('Capteurs hydrated with TypeCapteur data:', this.availableCapteurs);
        // --------------------------------------------------------

        // Process backend data (relies on hydrated capteurs and typeCapteurs)
        this.processBackendData();
        
        console.log('Rules loaded:', this.ruleList.length);
        console.log('Controllers loaded:', this.controllers.length);
        console.log('Capteurs loaded:', this.availableCapteurs.length);
        console.log('Device types processed:', this.deviceTypes);
        
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading initial data:', error);
        this.loadingError = 'Erreur lors du chargement des données initiales.';
        this.isLoading = false;
      }
    });
  }

  // Process backend data like in rule-form component
  private processBackendData(): void {
    this.deviceTypes = { sensors: {}, actuators: {} };

    this.typeCapteurs.forEach(typeCapteur => {
      const relatedVariables = this.variables.filter(v => v.IdTypeCapteur === typeCapteur.Id);
      
      const deviceCategory: 'sensors' | 'actuators' = 
        typeCapteur.DeviceType?.toLowerCase() === 'actuator' ? 'actuators' : 'sensors';
      
      const deviceKey = this.normalizeDeviceKey(typeCapteur.Nom);
      
      if (deviceCategory === 'sensors') {
        this.deviceTypes.sensors[deviceKey] = {
          topic: typeCapteur.Topic,
          device_name: typeCapteur.Nom,
          DisplayName: typeCapteur.DisplayName || typeCapteur.Nom,
          properties: relatedVariables.map(variable => ({
            key: variable.Key,
            type: this.mapVariableType(variable.Type),
            values: this.parseVariableValues(variable.Actions)
          }))
        };
      } else {
        this.deviceTypes.actuators[deviceKey] = {
          topic: typeCapteur.Topic,
          device_name: typeCapteur.Nom,
          DisplayName: typeCapteur.DisplayName || typeCapteur.Nom,
          actions: relatedVariables.map(variable => ({
            type: variable.Key,
            payload: {},
            options: this.parseActionOptions(variable.Actions)
          }))
        };
      }
    });

    console.log('Processed device types:', this.deviceTypes);
  }

  private normalizeDeviceKey(name: string): string {
    return name.toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^a-z0-9_]/g, '')
      .replace(/^sensor_|^actuator_/, '');
  }

  private mapVariableType(backendType: string): string {
    const typeMapping: { [key: string]: string } = {
      'String': 'string',
      'Integer': 'number',
      'Float': 'number',
      'Double': 'number',
      'Boolean': 'boolean',
      'Bool': 'boolean'
    };
    return typeMapping[backendType] || 'string';
  }

  private parseVariableValues(actions: string[] | string | null): (string | number | boolean)[] {
    if (!actions) return [];
    
    if (Array.isArray(actions)) {
      return actions;
    }
    
    if (typeof actions === 'string') {
      try {
        const parsed = JSON.parse(actions);
        if (Array.isArray(parsed)) {
          return parsed;
        }
      } catch {
        const values = actions.split(/[,;|]/).map(v => v.trim()).filter(v => v);
        return values;
      }
    }
    
    return [];
  }

  private parseActionOptions(actions: string[] | string | null): PayloadOption[] {
    if (!actions) return [];
    
    if (Array.isArray(actions)) {
      return actions.map((item: any) => {
        if (typeof item === 'object' && item !== null) {
          return {
            display: item.display || item.label || item.name || String(item.value || item),
            value: String(item.value || item)
          };
        } else {
          return {
            display: String(item),
            value: String(item)
          };
        }
      });
    }
    
    if (typeof actions === 'string') {
      try {
        const parsed = JSON.parse(actions);
        if (Array.isArray(parsed)) {
          return parsed.map((item: any) => ({
            display: item.display || item.label || item.name || String(item.value || item),
            value: String(item.value || item)
          }));
        }
      } catch {
        const values = actions.split(/[,;|]/).map(v => v.trim()).filter(v => v);
        return values.map(value => ({
          display: value,
          value: value
        }));
      }
    }
    
    return [];
  }

  // === RULE SELECTION ===

  onRuleSelectionChange(): void {
    console.log('Rule selection changed:', this.selectedRuleId);
    if (this.selectedRuleId) {
      this.loadSelectedRule();
    } else {
      this.resetRule();
    }
  }


  private loadSelectedRule(): void {
    const selectedRule = this.ruleList.find(rule => rule.Id === this.selectedRuleId);
    console.log('Loading selected rule:', selectedRule);
    
    if (selectedRule && selectedRule.RawData) {
      try {
        // Parse and validate the rule
        const parsedRule = this.parseAndValidateRule(selectedRule.RawData);
        if (!parsedRule) {
          throw new Error('Failed to parse rule data');
        }
        
        this.baseRule = parsedRule;
        this.transformedRule = JSON.parse(JSON.stringify(this.baseRule));
        
        // Initialize rule name for transformation
        this.transformedRule.rule_name = `${this.baseRule.rule_name} - Transformée`;
        
        this.initializeDeviceMappings();
        console.log('Base rule loaded successfully:', this.baseRule);
        console.log('Device mappings initialized:', this.deviceMappings);
        console.log('Action mappings initialized:', this.actionMappings);
      } catch (error) {
        console.error('Error loading rule:', error, 'Raw data:', selectedRule.RawData);
        this.selectedRuleId = undefined;
        this.baseRule = null;
      }
    } else {
      console.error('Selected rule has no RawData:', selectedRule);
      this.showErrorDialog('La règle sélectionnée ne contient pas de données valides.');
      this.selectedRuleId = undefined;
      this.baseRule = null;
    }
  }

  /**
   * Parse and validate rule with robust error handling
   */
  private parseAndValidateRule(rawData: string): RuleDto | null {
    try {
      console.log('Parsing raw rule data:', rawData);
      const parsedRule = JSON.parse(rawData);
      console.log('Initial parsed rule:', parsedRule);
      
      // Create a clean RuleDto structure
      const ruleDto: RuleDto = {
        rule_name: parsedRule.rule_name || 'Unnamed Rule',
        topic_pattern: Array.isArray(parsedRule.topic_pattern) ? parsedRule.topic_pattern : [],
        conditions: {
          groups: []
        },
        actions: [],
        schedule_config: parsedRule.schedule_config || { enabled: false },
        enabled: parsedRule.enabled !== undefined ? parsedRule.enabled : true,
        priority: parsedRule.priority || 1
      };

      // Handle conditions
      if (parsedRule.conditions) {
        if (parsedRule.conditions.groups && Array.isArray(parsedRule.conditions.groups)) {
          // Already has groups structure
          ruleDto.conditions.groups = parsedRule.conditions.groups.map((group: any) => ({
            operator: group.operator || 'AND',
            conditions: Array.isArray(group.conditions) ? group.conditions.map((condition: any) => this.normalizeCondition(condition)) : []
          }));
        } else if (Array.isArray(parsedRule.conditions)) {
          // Legacy format - conditions as direct array
          ruleDto.conditions.groups = [{
            operator: 'AND',
            conditions: parsedRule.conditions.map((condition: any) => this.normalizeCondition(condition))
          }];
        } else {
          // Single group fallback
          ruleDto.conditions.groups = [{
            operator: 'AND',
            conditions: []
          }];
        }
      }

      // Handle actions
      if (Array.isArray(parsedRule.actions)) {
        ruleDto.actions = parsedRule.actions.map((action: any) => this.normalizeAction(action));
      }

      console.log('Normalized rule DTO:', ruleDto);
      return ruleDto;
      
    } catch (error) {
      console.error('Failed to parse rule:', error);
      return null;
    }
  }

  /**
   * Normalize condition to ensure proper structure
   */
  private normalizeCondition(condition: any): Condition {
    if (condition.type === 'time') {
      return {
        type: 'time',
        start_time: condition.start_time || '',
        end_time: condition.end_time || ''
      } as TimeCondition;
    } else {
      // Default to sensor_data
      return {
        type: 'sensor_data',
        device: condition.device || '',
        key: condition.key || '',
        operator: condition.operator || '==',
        value: condition.value || ''
      } as SensorDataCondition;
    }
  }

  /**
   * Normalize action to ensure proper structure
   */
  private normalizeAction(action: any): PublishAction {
    return {
      type: action.type || '',
      topic: action.topic || '',
      payload: action.payload || {},
      message: action.message || undefined
    } as PublishAction;
  }

  private resetRule(): void {
    console.log('Resetting rule data');
    this.baseRule = null;
    this.transformedRule = {
      rule_name: '',
      topic_pattern: [],
      conditions: { groups: [{ operator: 'AND', conditions: [] }] },
      actions: [],
      schedule_config: { enabled: false },
      enabled: true,
      priority: 1
    };
    this.deviceMappings = [];
    this.actionMappings = [];
    this.selectedControllerId = undefined;
    this.availableCapteursForMapping = []; // Clear mapped capteurs
  }

  getRuleNameFromRawData(rawData: string): string {
    if (!rawData) return 'Unnamed Rule';
    try {
      const rule = JSON.parse(rawData);
      return rule.rule_name?.trim() ? rule.rule_name : 'Unnamed Rule';
    } catch (e) {
      return 'Invalid Rule Data';
    }
  }

  // === CONTROLLER AND TRANSACTION SELECTION ===

  onControllerSelectionChange(): void {
    console.log('Controller selection changed:', this.selectedControllerId);
    if (this.selectedControllerId) {
      this.filterCapteursByController();
    } else {
      this.availableCapteursForMapping = []; // Clear mapped capteurs
    }
  }

  private filterCapteursByController(): void {
    if (!this.selectedControllerId) return;

    console.log('Filtering capteurs for controller:', this.selectedControllerId);
    // Get all transactions for the local (rule) and filter by selected controller
    this.transactionService.getAll().subscribe({
      next: (transactions: Transaction[]) => {
        const relevantTransactions = transactions.filter(t => 
          t.IdLocal === this.data.idLocal && t.IdController === this.selectedControllerId && t.IdCapteur
        );
        console.log('Relevant transactions for controller:', relevantTransactions.length, relevantTransactions);

        // Extract unique Capteur IDs from these relevant transactions
        const uniqueCapteurIds = new Set(relevantTransactions.map(t => t.IdCapteur).filter((id): id is string => id !== undefined));
        
        // Filter availableCapteurs based on these unique IDs
        this.availableCapteursForMapping = this.availableCapteurs.filter(capteur => 
          capteur.Id !== undefined && uniqueCapteurIds.has(capteur.Id)
        );
        console.log('Available Capteurs for mapping based on controller transactions:', this.availableCapteursForMapping.length, this.availableCapteursForMapping);
      },
      error: (error) => {
        console.error('Error filtering capteurs by controller:', error);
        this.availableCapteursForMapping = [];
      }
    });
  }

  // === DEVICE MAPPING INITIALIZATION ===

  private initializeDeviceMappings(): void {
    if (!this.baseRule) {
      console.log('No base rule available for mapping initialization');
      return;
    }

    console.log('Initializing device mappings from base rule:', this.baseRule);

    // Initialize device mappings from conditions
    this.deviceMappings = [];
    
    if (this.baseRule.conditions && this.baseRule.conditions.groups) {
      this.baseRule.conditions.groups.forEach((group, groupIndex) => {
        console.log(`Processing condition group ${groupIndex}:`, group);
        
        if (group.conditions && Array.isArray(group.conditions)) {
          group.conditions.forEach((condition, conditionIndex) => {
            console.log(`Processing condition ${groupIndex}.${conditionIndex}:`, condition);
            
            if (condition.type === 'sensor_data') {
              const sensorCondition = condition as SensorDataCondition;
              // Find the original device type information from the processed deviceTypes
              const normalizedDeviceName = this.normalizeDeviceKey(sensorCondition.device);
              const deviceData = this.deviceTypes.sensors[normalizedDeviceName];

              if (sensorCondition.device && !this.deviceMappings.find(m => m.originalDevice === sensorCondition.device)) {
                this.deviceMappings.push({
                  originalDevice: sensorCondition.device,
                  originalDeviceType: 'sensor',
                  originalDeviceTypeName: deviceData?.DisplayName || sensorCondition.device, // Use DisplayName if available
                  selectedCapteurId: '',
                  selectedCapteur: undefined, // Initialize
                  availableProperties: []
                });
                console.log('Added device mapping for:', sensorCondition.device);
              }
            }
          });
        } else {
          console.warn(`Group ${groupIndex} has no conditions array:`, group);
        }
      });
    } else {
      console.warn('Base rule has no conditions.groups:', this.baseRule.conditions);
    }

    // Initialize action mappings
    this.actionMappings = [];
    
    if (this.baseRule.actions && Array.isArray(this.baseRule.actions)) {
      this.baseRule.actions.forEach((action, actionIndex) => {
        console.log(`Processing action ${actionIndex}:`, action);
        
        if (action.topic && action.type !== 'log' && !this.actionMappings.find(m => m.originalTopic === action.topic)) {
          // Try to infer actuator type from topic if possible, or leave undefined
          const inferredActuatorType = this.typeCapteurs.find(tc => tc.Topic === action.topic && tc.DeviceType?.toLowerCase() === 'actuator');

          this.actionMappings.push({
            originalTopic: action.topic,
            originalDeviceType: 'actuator', // Assuming actions typically relate to actuators
            originalDeviceTypeName: inferredActuatorType?.DisplayName || inferredActuatorType?.Nom || action.topic,
            originalTopicForFiltering: action.topic, // Store the original topic for direct filtering
            selectedCapteurId: '',
            selectedCapteur: undefined, // Initialize
            availableActions: []
          });
          console.log('Added action mapping for:', action.topic);
        }
      });
    } else {
      console.warn('Base rule has no actions array:', this.baseRule.actions);
    }

    console.log('Final device mappings:', this.deviceMappings);
    console.log('Final action mappings:', this.actionMappings);
  }

  // === DEVICE MAPPING HANDLERS ===

  onDeviceMappingChange(mapping: DeviceMapping): void {
    console.log('Device mapping changed:', mapping);
    if (mapping.selectedCapteurId) {
      // Find the selected capteur from the ones available for mapping
      const selectedCapteur = this.availableCapteursForMapping.find(c => c.Id === mapping.selectedCapteurId);
      if (selectedCapteur) { // Removed check for selectedCapteur.TypeCapteur here as it's hydrated earlier
        mapping.selectedCapteur = selectedCapteur; // Store the full Capteur object
        mapping.selectedTypeCapteur = selectedCapteur.TypeCapteur; // Keep TypeCapteur for other needs
        
        // Get device key for properties
        const deviceKey = this.normalizeDeviceKey(selectedCapteur.TypeCapteur?.Nom || '');
        const deviceData = this.deviceTypes.sensors[deviceKey];
        mapping.availableProperties = deviceData ? deviceData.properties || [] : [];
        
        console.log('Mapped device to Capteur:', mapping.selectedCapteur);
        console.log('Available properties:', mapping.availableProperties);
      }
    } else {
      mapping.selectedCapteur = undefined; // Clear Capteur object
      mapping.selectedTypeCapteur = undefined;
      mapping.availableProperties = [];
    }
    this.updateTransformedRule();
  }

  onActionMappingChange(mapping: ActionMapping): void {
    console.log('Action mapping changed:', mapping);
    if (mapping.selectedCapteurId) {
      // Find the selected capteur from the ones available for mapping
      const selectedCapteur = this.availableCapteursForMapping.find(c => c.Id === mapping.selectedCapteurId);
      if (selectedCapteur) { // Removed check for selectedCapteur.TypeCapteur here as it's hydrated earlier
        mapping.selectedCapteur = selectedCapteur; // Store the full Capteur object
        mapping.selectedTypeCapteur = selectedCapteur.TypeCapteur; // Keep TypeCapteur for other needs
        
        // Get device key for actions
        const deviceKey = this.normalizeDeviceKey(selectedCapteur.TypeCapteur?.Nom || '');
        const deviceData = this.deviceTypes.actuators[deviceKey];
        mapping.availableActions = deviceData ? deviceData.actions || [] : [];
        
        console.log('Mapped action to Capteur:', mapping.selectedCapteur);
        console.log('Available actions:', mapping.availableActions);
      }
    } else {
      mapping.selectedCapteur = undefined; // Clear Capteur object
      mapping.selectedTypeCapteur = undefined;
      mapping.availableActions = [];
    }
    this.updateTransformedRule();
  }

  // === HELPER METHODS FOR DROPDOWNS ===

  // New method to get filtered capteurs for sensor mapping
  getFilteredCapteursForDeviceMapping(mapping: DeviceMapping): Capteur[] {
    if (!this.selectedControllerId) {
      return [];
    }
    return this.availableCapteursForMapping.filter(c => 
      c.TypeCapteur?.DeviceType?.toLowerCase() === 'sensor' &&
      // Match by normalized device name from the original rule with the TypeCapteur's Nom
      this.normalizeDeviceKey(c.TypeCapteur?.Nom || '') === this.normalizeDeviceKey(mapping.originalDevice || '') 
    );
  }

  // New method to get filtered capteurs for actuator mapping
  getFilteredCapteursForActionMapping(mapping: ActionMapping): Capteur[] {
    if (!this.selectedControllerId) {
      return [];
    }
    return this.availableCapteursForMapping.filter(c => 
      c.TypeCapteur?.DeviceType?.toLowerCase() === 'actuator' &&
      // Directly match the TypeCapteur's Topic with the original action's topic
      c.TypeCapteur?.Topic === mapping.originalTopicForFiltering
    );
  }

  // === RULE TRANSFORMATION ===

  private updateTransformedRule(): void {
    if (!this.baseRule) {
      console.log('No base rule available for transformation');
      return;
    }

    console.log('Updating transformed rule...');
    
    // Clone the base rule
    this.transformedRule = JSON.parse(JSON.stringify(this.baseRule));

    // Transform conditions - handle multiple groups
    if (this.transformedRule.conditions && this.transformedRule.conditions.groups) {
      this.transformedRule.conditions.groups.forEach((group, groupIndex) => {
        if (group.conditions && Array.isArray(group.conditions)) {
          group.conditions.forEach((condition, conditionIndex) => {
            if (condition.type === 'sensor_data') {
              const sensorCondition = condition as SensorDataCondition;
              const mapping = this.deviceMappings.find(m => m.originalDevice === sensorCondition.device);
              if (mapping && mapping.selectedTypeCapteur) {
                const oldDevice = sensorCondition.device;
                // Update device reference to new TypeCapteur name (Nom)
                sensorCondition.device = mapping.selectedTypeCapteur.Nom;
                console.log(`Transformed device in group ${groupIndex}, condition ${conditionIndex}: ${oldDevice} -> ${sensorCondition.device}`);
              }
            }
          });
        }
      });
    }

    // Transform actions
    if (this.transformedRule.actions && Array.isArray(this.transformedRule.actions)) {
      this.transformedRule.actions.forEach((action, actionIndex) => {
        if (action.type !== 'log' && action.topic) {
          const mapping = this.actionMappings.find(m => m.originalTopic === action.topic);
          if (mapping && mapping.selectedCapteur) { // Check for selectedCapteur here
            const oldTopic = action.topic;
            // Update the action's topic to the topic of the SELECTED real Capteur
            action.topic = mapping.selectedCapteur.Topic || ''; // Use Capteur.Topic
            console.log(`Transformed action ${actionIndex} topic: ${oldTopic} -> ${action.topic}`);
          }
        }
      });
    }

    // Update topic pattern
    this.updateTopicPattern();
    
    console.log('Transformed rule updated:', this.transformedRule);
  }

  private updateTopicPattern(): void {
    const topics: string[] = [];

    // Add topics from mapped devices (sensors)
    this.deviceMappings.forEach(mapping => {
      if (mapping.selectedCapteur && mapping.selectedCapteur.Topic) { // Use selectedCapteur.Topic
        if (!topics.includes(mapping.selectedCapteur.Topic)) {
          topics.push(mapping.selectedCapteur.Topic);
        }
      }
    });

    // Add topics from mapped actions (actuators)
    this.actionMappings.forEach(mapping => {
      if (mapping.selectedCapteur && mapping.selectedCapteur.Topic) { // Use selectedCapteur.Topic
        if (!topics.includes(mapping.selectedCapteur.Topic)) {
          topics.push(mapping.selectedCapteur.Topic);
        }
      }
    });

    this.transformedRule.topic_pattern = topics;
    console.log('Updated topic pattern:', topics);
  }

  // === VALIDATION ===

  private validateMappings(): { valid: boolean; message?: string } {
    // Check if rule is selected and valid
    if (!this.baseRule || !this.selectedRuleId) {
      return {
        valid: false,
        message: 'Veuillez sélectionner une règle de base valide.'
      };
    }

    // Check if a controller is selected
    if (!this.selectedControllerId) {
      return {
        valid: false,
        message: 'Veuillez sélectionner un contrôleur.'
      };
    }

    // Check if all device mappings are complete
    const incompleteMappings = this.deviceMappings.filter(m => !m.selectedCapteurId);
    if (incompleteMappings.length > 0) {
      return {
        valid: false,
        message: `Veuillez sélectionner des capteurs pour: ${incompleteMappings.map(m => m.originalDevice).join(', ')}`
      };
    }

    // Check if all action mappings are complete
    const incompleteActions = this.actionMappings.filter(m => !m.selectedCapteurId);
    if (incompleteActions.length > 0) {
      return {
        valid: false,
        message: `Veuillez sélectionner des actionneurs pour: ${incompleteActions.map(m => m.originalTopic).join(', ')}`
      };
    }

    // Check if rule name is provided
    if (!this.transformedRule.rule_name || !this.transformedRule.rule_name.trim()) {
      return {
        valid: false,
        message: 'Veuillez fournir un nom pour la règle transformée.'
      };
    }

    return { valid: true };
  }

  // === SAVE FUNCTIONALITY ===

  onSave(): void {
    if (!this.baseRule || !this.selectedRuleId) {
      this.showErrorDialog('Veuillez sélectionner une règle de base.');
      return;
    }

    const validation = this.validateMappings();
    if (!validation.valid) {
      this.showErrorDialog(validation.message!);
      return;
    }

    this.performSave();
  }

  private performSave(): void {
    console.log('Starting save operation...');
    this.isSaving = true;
    this.ngxUiLoaderService.start();

    // Collect all unique Capteur Ids from availableCapteursForMapping
    const uniqueCapteurIdsInScope = new Set(this.availableCapteursForMapping.map(c => c.Id).filter((id): id is string => id !== undefined));

    // Fetch all transactions for the current local and selected controller
    this.transactionService.getAll().subscribe({
        next: (allTransactions: Transaction[]) => {
            // Filter transactions to those associated with the selected controller and the capteurs in scope
            const transactionsForSelectedController = allTransactions.filter(t => 
                t.IdLocal === this.data.idLocal &&
                t.IdController === this.selectedControllerId && 
                t.IdCapteur && uniqueCapteurIdsInScope.has(t.IdCapteur)
            );

            if (transactionsForSelectedController.length === 0) {
                console.warn('No relevant transactions found for the selected controller and mapped capteurs. No RuleTransactions will be created.');
                this.showErrorDialog('Aucune transaction pertinente trouvée pour le contrôleur sélectionné et les capteurs mappés. Aucune RuleTransaction ne sera créée.');
                this.isSaving = false;
                this.ngxUiLoaderService.stop();
                return;
            }

            const ruleTransactions: any[] = transactionsForSelectedController.map(transaction => ({
                IdRule: this.selectedRuleId!,
                IdTransaction: transaction.Id!, // Use the ID of the filtered transaction
                RawData: JSON.stringify(this.transformedRule)
            }));

            console.log('Creating rule transactions for transactions associated with controller:', ruleTransactions);

            const creationRequests = ruleTransactions.map(rt => 
                this.ruleTransactionService.create(rt)
            );

            forkJoin(creationRequests).subscribe({
                next: (createdRuleTransactions) => {
                    console.log('RuleTransactions created successfully:', createdRuleTransactions);
                    this.handleSaveSuccess(createdRuleTransactions);
                },
                error: (error) => {
                    console.error('Error creating RuleTransactions:', error);
                    this.handleSaveError(error);
                }
            });
        },
        error: (error) => {
            console.error('Error fetching all transactions for save operation:', error);
            this.showErrorDialog('Erreur lors de la récupération des transactions pour la sauvegarde.');
            this.isSaving = false;
            this.ngxUiLoaderService.stop();
        }
    });
  }

  private handleSaveSuccess(createdRuleTransactions: any[]): void {
    this.isSaving = false;
    this.ngxUiLoaderService.stop();
    this.dialogRef.close({ 
      action: 'saved', 
      ruleTransactions: createdRuleTransactions,
      transformedRule: this.transformedRule
    });
  }

  private handleSaveError(error: any): void {
    this.isSaving = false;
    this.ngxUiLoaderService.stop();
    this.showErrorDialog('Erreur lors de la sauvegarde des RuleTransactions.');
  }

  // === UI STATE METHODS ===

  isFieldDisabled(): boolean {
    return this.isSaving || this.isLoading;
  }

  get isBusy(): boolean {
    return this.isSaving;
  }

  get statusMessage(): string {
    if (this.isSaving) return 'Création des RuleTransactions en cours...';
    return '';
  }

  get operationProgress(): number {
    return this.isSaving ? 70 : 0;
  }

  // === JSON GENERATION ===

  generateJSON(): string {
    try {
      return JSON.stringify(this.transformedRule, null, 2);
    } catch (error) {
      console.error('Error generating JSON:', error);
      return '{\n  "error": "Unable to generate JSON preview"\n}';
    }
  }

  downloadJSON(): void {
    if (this.isBusy) {
      this.showErrorDialog('Impossible de télécharger pendant la sauvegarde.');
      return;
    }
    
    try {
      const filename = `${this.transformedRule.rule_name || 'transformed-rule'}.json`;
      const json = this.generateJSON();
      const blob = new Blob([json], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading JSON:', error);
      this.showErrorDialog('Erreur lors du téléchargement du fichier JSON.');
    }
  }

  // === UTILITY METHODS ===

  onCancel(): void {
    if (this.isSaving) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: 'Sauvegarde en cours',
          message: 'Une sauvegarde est en cours. Voulez-vous vraiment annuler ?',
          icon: 'warning'
        },
        disableClose: true,
        panelClass: 'confirmation-dialog-panel'
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.dialogRef.close();
        }
      });
      return;
    }

    this.dialogRef.close();
  }

  retryLoadData(): void {
    this.loadInitialData();
  }

  private showErrorDialog(message: string): void {
    this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Erreur',
        message: message,
        icon: 'error',
        confirmText: 'OK'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    });
  }
}