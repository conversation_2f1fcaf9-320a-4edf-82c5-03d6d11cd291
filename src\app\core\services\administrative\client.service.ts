import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { ApiService } from "../api.service";
import { Client } from "../../models/client";
import { ClientLicenceControllerView } from "@app/shared/models/clientLicenceControllerView";
import { Observable } from "rxjs";

@Injectable({
  providedIn: 'root' // It's a good practice to provide services at root unless you have a specific reason not to
})
export class ClientApiService extends ApiService<Client> {
  // Add the 'override' keyword here
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint("client");
  }
  
  getClientControllers(clientId: string): Observable<ClientLicenceControllerView[]> {
    const url = `${this.baseUrl}client/controllers/${clientId}`;
    return this.http.get<ClientLicenceControllerView[]>(url);
  }
}
