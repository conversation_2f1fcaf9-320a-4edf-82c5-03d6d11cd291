import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { Client } from '../../models/client';
import { ClientLicenceControllerView } from '@app/shared/models/clientLicenceControllerView';
import { Observable } from 'rxjs';
import { Lister, Page } from '@app/core/models/util/page';
import { Controller } from '@app/core/models/controller';
import { ClientWithSiteStatusView } from '@app/shared/models/clientWithSiteStatusView';

@Injectable({
  providedIn: 'root', // It's a good practice to provide services at root unless you have a specific reason not to
})
export class ClientApiService extends ApiService<Client> {
  // Add the 'override' keyword here
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint('client');
  }

  getClientControllers(
    clientId: string
  ): Observable<ClientLicenceControllerView[]> {
    const url = `${this.baseUrl}client/controllers/${clientId}`;
    return this.http.get<ClientLicenceControllerView[]>(url);
  }

  getClientControllersForChoice(clientId: string): Observable<Controller[]> {
    const url = `${this.baseUrl}client/controllers/${clientId}`;
    return this.http.get<Controller[]>(url);
  }

  uploadClientImage(clientId: string, imageData: string): Observable<any> {
    const requestBody = {
      id: clientId,
      imageData: imageData,
    };

    return this.http.post(`${this.baseUrl}Images/upload/client`, requestBody, {
      headers: {
        'Content-Type': 'application/json-patch+json',
      },
    });
  }

  downloadClientImage(clientId: string): Observable<any> {
    return this.http.post(`${this.baseUrl}Images/download/client`, null, {
      params: {
        Id: clientId,
      },
      headers: {
        'Content-Type': 'application/json-patch+json',
      },
    });
  }

  getAllSiteStatus(clientId: string): Observable<ClientWithSiteStatusView[]> {
    const url = `${this.baseUrl}client/sitestatus/${clientId}`;
    return this.http.get<ClientWithSiteStatusView[]>(url);
  }

  getPageSiteStatus(lister: Lister): Observable<ClientWithSiteStatusView[]> {
    const url = `${this.baseUrl}client/sitestatus`;
    return this.http.post<ClientWithSiteStatusView[]>(url, lister);
  }

  changeClientStatus(clientId: string): Observable<Client[]> {
    const url = `${this.baseUrl}client/client-activation`;
    return this.http.post<Client[]>(url, { Id: clientId });
  }

  changeClientStatusInactif(clientId: string): Observable<Client[]> {
    const url = `${this.baseUrl}client/client-desactivation`;
    return this.http.post<Client[]>(url, { Id: clientId });
  }
}
