/* General container styling */
.organisations-container {
  max-width: 1300px;
  margin: 30px auto;
  padding: 25px;
  background: linear-gradient(145deg, #ffffff, #f9f9f9);
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

/* Header section */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  animation: slideIn 0.5s ease-out;
}

.page-title {
  margin: 0;
}

.title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.reset-filter-btn {
  background: #f56565;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-left: 15px;
  transition: all 0.2s ease;
}

.reset-filter-btn:hover {
  background: #e53e3e;
  transform: translateY(-1px);
}

.reset-filter-btn i {
  font-size: 16px;
}

.title-icon {
  font-size: 30px;
  color: #49b38d;
  background: linear-gradient(45deg, #49b38d, rgba(129, 199, 132, 0.747));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  font-size: 16px;
  color: #718096;
  margin: 0;
  font-style: italic;
}

.actions {
  display: flex;
  gap: 15px;
}

.create-button {
  background: linear-gradient(45deg, #4CAF50, #81C784);
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.create-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(76, 175, 80, 0.5);
  background: linear-gradient(45deg, #81C784, #4CAF50);
}

.view-toggle {
  border: 1px solid #49b38d;
  color: #49b38d;
  padding: 8px 16px;
  border-radius: 8px;
  background: transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.view-toggle:hover {
  background-color: #e8f5e9;
  transform: translateY(-2px);
}

.action-icon {
  font-size: 20px;
  color: #49b38d;
}

/* Create form card */
/* .create-form-card {
  margin-bottom: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.4s ease-in 0.3s;
  background: white;
  padding: 25px;
}

.form-title {
  margin: 0 0 20px;
  font-size: 22px;
  font-weight: 600;
  color: #2E7D32;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.form-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.form-group label {
  font-size: 14px;
  color: #4a5568;
  margin-bottom: 6px;
  font-weight: 500;
}

.form-control {
  height: 40px;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #4CAF50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  outline: none;
}

.form-error {
  font-size: 12px;
  color: #e53e3e;
  margin-top: 4px;
} */
 .create-form-card {
  margin-bottom: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.4s ease-in 0.3s;
  background: white;
  padding: 25px;
}

.create-form-card form {
  width: 100%;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
  align-items: end;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
}

.form-group input,
.form-group select {
  height: 42px;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  color: #4a5568;
  background-color: white;
  transition: all 0.2s ease;
  box-sizing: border-box;
  width: 100%; 
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #4CAF50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-group input[type="file"] {
  padding: 8px;
  height: auto;
  border: 2px dashed #e2e8f0;
  background-color: #f8fafc;
  cursor: pointer;
}

.form-group input[type="file"]:hover {
  border-color: #4CAF50;
  background-color: #f0fff4;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}
/* Required field indicator */
.form-group label[required]:after,
.form-group label.required:after {
  content: "*";
  color: #ef4444;
  margin-left: 4px;
}


.file-input-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 6px;
}

.file-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background-color: #edf2f7;
  color: #4a5568;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
}

.file-button:hover {
  background-color: #e2e8f0;
}

.file-info {
  font-size: 14px;
  color: #718096;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.btn-cancel {
  padding: 10px 20px;
  background: transparent;
  border: 1px solid #cbd5e0;
  color: #718096;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.btn-cancel:hover {
  background-color: #f7fafc;
}

.btn-submit {
  padding: 10px 20px;
  background: linear-gradient(45deg, #4CAF50, #81C784);
  color: white;
  border: none;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

.btn-submit:hover {
  background: linear-gradient(45deg, #81C784, #4CAF50);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.btn-submit:disabled {
  background: #cbd5e0;
  transform: none;
  box-shadow: none;
  cursor: not-allowed;
}

/* Search bar */
.search-bar {
  margin-bottom: 30px;
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.search-container {
  display: flex;
  align-items: center;
  max-width: 450px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  /* Remove padding so button is not inside the input background */
  padding: 0;
  transition: all 0.3s ease;
  flex: 1;
}

.search-input {
  border: none;
  background: transparent;
  padding: 8px 12px;
  font-size: 15px;
  color: #4a5568;
  outline: none;
  width: 100%;
  border-radius: 8px 0 0 8px;
}

.search-button {
  background: #4CAF50;
  color: white;
  border: none;
  border-radius:8px;
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  margin-left: 0;
  height: 35px;
  width: 70px;
}

.search-button .material-icons {
  font-size: 18px;
}



.search-field {
  max-width: 450px;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  padding: 10px 15px;
  display: flex;
  align-items: center;
}

.search-field:focus-within {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border-color: #4CAF50;
}

.search-icon {
  color: #718096;
  font-size: 20px;
  margin-right: 10px;
}

.search-field input {
  border: none;
  background: transparent;
  width: 100%;
  padding: 0;
  font-size: 15px;
  color: #4a5568;
}

.search-field input:focus {
  outline: none;
}

/* organisations.component.css */
.active-filters {
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.active-filter {
  display: flex;
  align-items: center;
  background: linear-gradient(90deg, #e8f5e9 80%, #c8e6c9 100%);
  color: #388e3c;
  padding: 0.4rem 1.2rem 0.4rem 1rem;
  border-radius: 24px;
  font-size: 1rem;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.07);
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  gap: 0.5rem;
}

.clear-filter {
  background: none;
  border: none;
  margin-left: 0.5rem;
  cursor: pointer;
  color: #388e3c;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s, color 0.2s;
}

.clear-filter:hover {
  background: #c8e6c9;
  color: #1b5e20;
}

.clear-filter .material-icons {
  font-size: 20px;
  line-height: 1;
}

/* Loading container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 250px;
  animation: fadeIn 0.5s ease-out;
}

.spinner {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid rgba(76, 175, 80, 0.1);
  border-top-color: #4CAF50;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-container p {
  color: #718096;
  font-size: 16px;
  font-style: italic;
}

/* No data message */
.no-data {
  text-align: center;
  padding: 30px;
  color: #718096;
  animation: fadeIn 0.5s ease-out;
}

.no-data .material-icons {
  font-size: 60px;
  color: #4CAF50;
  margin-bottom: 15px;
}

/* Cards container */
.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

/* Table container */
.table-container {
  overflow-x: auto;
  margin-bottom: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  background: #ffffff;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: linear-gradient(45deg, #f5f5f5, #eceff1);
  padding: 15px;
  text-align: left;
  font-weight: 600;
  color: #2d3748;
  border-bottom: 2px solid #e2e8f0;
}

.data-table td {
  padding: 15px;
  border-bottom: 1px solid #edf2f7;
  color: #4a5568;
}

.actions-cell {
  display: flex;
  gap: 8px;
}

.action-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
  background: transparent;
}

.action-button .material-icons {
  font-size: 18px;
}

.action-button.view {
  color: #4CAF50;
}

.action-button.view:hover {
  background-color: #e8f5e9;
  transform: scale(1.1);
}

.action-button.edit {
  color: #2196F3;
}

.action-button.edit:hover {
  background-color: #e3f2fd;
  transform: scale(1.1);
}

.action-button.delete {
  color: #ef5350;
}

.action-button.delete:hover {
  background-color: #ffebee;
  transform: scale(1.1);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .actions {
    width: 100%;
    flex-direction: column;
    gap: 10px;
  }

  .create-button,
  .view-toggle {
    width: 100%;
    padding: 12px;
    justify-content: center;
  }

  .cards-container {
    grid-template-columns: 1fr;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 24px;
  }

  .title-icon {
    font-size: 26px;
  }

  .subtitle {
    font-size: 14px;
  }

  .search-field {
    max-width: 100%;
  }
}


/* Add these styles to your existing CSS */
.show-more-container {
  display: flex;
  justify-content: center;
  margin: 1rem 0 2rem;
}

.show-more-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: 2px solid #4CAF50;
  border-radius: 8px;
  background: transparent;
  color: #4CAF50;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.show-more-button:hover {
  background: #e8f5e9;
  transform: translateY(-2px);
}

.show-more-button .material-icons {
  font-size: 20px;
  transition: transform 0.3s ease;
}

.show-more-button:hover .material-icons {
  transform: translateY(2px);
}

/* Add animation for the categories */
app-categories-sites {
  display: block;
  transition: all 0.3s ease-in-out;
}

/* Statistics Section */
.stats-section {
  margin-bottom: 2.5rem;
  margin-top: 1.5rem;
  animation: fadeIn 0.7s;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 2rem;
}

.stat-card {
  background: linear-gradient(120deg, #f8fafc 60%, #e8f5e9 100%);
  border-radius: 16px;
  box-shadow: 0 4px 18px rgba(76, 175, 80, 0.08);
  display: flex;
  align-items: center;
  padding: 1.5rem 1.2rem;
  gap: 1.2rem;
  transition: box-shadow 0.3s, transform 0.3s;
}

.stat-card:hover {
  box-shadow: 0 8px 32px rgba(76, 175, 80, 0.18);
  transform: translateY(-4px) scale(1.03);
}

.stat-icon {
  width: 54px;
  height: 54px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  background: #e0e7ef;
  color: #4caf50;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.08);
}

.stat-icon.energy { background: #e3f2fd; color: #1976d2; }
.stat-icon.clients { background: #fffde7; color: #fbc02d; }
.stat-icon.ongoing { background: #f3e5f5; color: #8e24aa; }
.stat-icon.execution { background: #e8f5e9; color: #388e3c; }
.stat-icon.satisfaction { background: #e3fcec; color: #43a047; }
.stat-icon.impact { background: #e0f7fa; color: #00897b; }
.stat-icon.partners { background: #fff3e0; color: #f57c00; }

.stat-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.2rem;
}

.stat-label {
  font-size: 1rem;
  color: #718096;
  font-weight: 500;
  letter-spacing: 0.01em;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1.2rem;
  }
  .stat-card {
    padding: 1.2rem 0.8rem;
  }
}

/* Update the pagination controls styles */
.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.page-numbers {
  display: flex;
  gap: 0.25rem;
  align-items: center;
  margin: 0 0.5rem;
}

.page-number-button {
  min-width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #4a5568;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.page-number-button:hover:not(:disabled):not(.active) {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.page-number-button.active {
  background: #49b38d;
  color: white;
  border-color: #49b38d;
}

.page-number-button.separator {
  border: none;
  background: transparent;
  cursor: default;
}

.page-number-button:disabled {
  cursor: default;
  opacity: 0.5;
}

.pagination-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-button:hover:not(:disabled) {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  margin: 0 1rem;
  color: #718096;
  font-size: 0.875rem;
}

.create-form-card {
  margin-bottom: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.4s ease-in 0.3s;
  background: white;
  padding: 25px;
}

.create-form-card form {
  width: 100%;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
}

.form-group input,
.form-group select {
  height: 42px;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  color: #4a5568;
  background-color: white;
  transition: all 0.2s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #4CAF50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-group input[type="file"] {
  padding: 8px;
  height: auto;
  border: 2px dashed #e2e8f0;
  background-color: #f8fafc;
  cursor: pointer;
}

.form-group input[type="file"]:hover {
  border-color: #4CAF50;
  background-color: #f0fff4;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}
.action-buttons button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.action-buttons button i {
  font-size: 20px;
}

.form-actions button {
  padding: 10px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.form-actions button[type="button"] {
  background-color: #fff;
  border: 1px solid #e2e8f0;
  color: #4a5568;
}

.form-actions button[type="submit"] {
  background: linear-gradient(45deg, #4CAF50, #81C784);
  border: none;
  color: white;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.2);
}

.form-actions button[type="button"]:hover {
  background-color: #f8fafc;
  border-color: #cbd5e0;
}

.form-actions button[type="submit"]:hover {
  background: linear-gradient(45deg, #81C784, #4CAF50);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(76, 175, 80, 0.3);
}

.form-actions button[type="submit"]:disabled {
  background: #e2e8f0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Pagination styles */
.card-pagination-container,
.pagination-container {

  margin-top: 20px;
  margin-bottom: 20px;
}

.table-container {
  overflow-x: auto;
  margin-bottom: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  background: #ffffff;
}

.pagination-container {
  padding: 20px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  border-radius: 0 0 12px 12px;
}

.card-pagination-container {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 10px 20px;
}