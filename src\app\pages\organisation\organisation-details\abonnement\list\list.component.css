/* Loading State */
.loading-message {
  padding: 2rem;
  text-align: center;
  font-size: 1rem;
  color: #6b7280;
  font-family: "Lato", sans-serif;
}

/* Table Container */
.table-container {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  margin-top: 20px;
  width: 95%;
}

.table-container::before {
  content: "";
  display: block;
  height: 0.5rem;
  background: linear-gradient(to right, #49b38d, rgba(52, 211, 153, 0.747));
}

/* Table Header */
.table-header {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.table-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
  font-family: "Montserrat", sans-serif;
}

/* Subscriptions Table */
.subscriptions-table {
  width: 100%;
  border-collapse: collapse;
}

.subscriptions-table th {
  padding: 1rem 1.5rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 600;
  color: #111827;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-family: "Montserrat", sans-serif;
}

.subscriptions-table td {
  padding: 1rem 1.5rem;
  font-size: 0.875rem;
  color: #4b5563;
  border-bottom: 1px solid #e5e7eb;
  font-family: "Lato", sans-serif;
  vertical-align: middle;
  text-align: center;
}

.subscriptions-table tr:last-child td {
  border-bottom: none;
}

.subscriptions-table tr:hover {
  background-color: #f9fafb;
}

/* Selected row styling */
.subscriptions-table tr.selected {
  background-color: #eff6ff;
  border-left: 4px solid #49b38d;
}

.subscriptions-table tr.selected:hover {
  background-color: #dbeafe;
}

/* No Subscriptions Message */
.no-subscriptions {
  padding: 3rem;
  text-align: center;
  color: #6b7280;
  font-size: 1rem;
  font-family: "Lato", sans-serif;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  min-width: 500px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  font-family: "Montserrat", sans-serif;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s ease;
}

.close-button:hover {
  background-color: #f3f4f6;
  color: #111827;
}

.modal-content {
  padding: 1.5rem;
  overflow-y: auto;
  max-height: calc(90vh - 120px);
}

/* Dropdown styles (commented out in HTML but keeping for reference) */
.dropdown-cell {
  position: relative;
}

.dropdown-container {
  position: relative;
  display: inline-block;
}

.dots-menu {
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s ease;
}

.dots-menu:hover {
  background-color: #f3f4f6;
}

.dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  top: 100%;
  background-color: white;
  min-width: 150px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-radius: 0.375rem;
  border: 1px solid #e5e7eb;
  z-index: 1000;
}

.dropdown-content.show {
  display: block;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  color: #374151;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background-color: #f3f4f6;
}

.dropdown-item:first-child {
  border-radius: 0.375rem 0.375rem 0 0;
}

.dropdown-item:last-child {
  border-radius: 0 0 0.375rem 0.375rem;
}

/* Status badge styles */
.status-cell {
  text-align: center;
}

.status-active {
  color: #28a745;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  background-color: rgba(40, 167, 69, 0.1);
  display: inline-block;
  min-width: 60px;
}

.status-inactive {
  color: #dc3545;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  background-color: rgba(220, 53, 69, 0.1);
  display: inline-block;
  min-width: 60px;
}

.status-maintenance {
  color: #044c8f;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  background-color: rgba(45, 122, 160, 0.1);
  display: inline-block;
  min-width: 60px;
}

.status-error {
  color: #fd7e14;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  background-color: rgba(253, 126, 20, 0.1);
  display: inline-block;
  min-width: 60px;
}

.status-retire {
  color: #ff9900;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  background-color: #ffe1c4;
  display: inline-block;
  min-width: 60px;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .subscriptions-table td,
  .subscriptions-table th {
    padding: 0.75rem 1rem;
  }
}

  .table-header {
    padding: 1rem;

  .table-header {
    padding: 1rem;
  }

  .table-title {
    font-size: 1.25rem;

    .table-title {
      font-size: 1.25rem;
    }

  .modal-container {
    min-width: 90vw;
    margin: 1rem;
  }
  
  .table-container {
    width: 100%;
  }
}}

@media (max-width: 768px) {
  .table-container {
    width: 100%;
    margin-top: 10px;
  .table-container {
    width: 100%;
    margin-top: 10px;
  }
}

  .subscriptions-table td,
  .subscriptions-table th {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  .subscriptions-table td,
  .subscriptions-table th {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .table-title {
    font-size: 1.1rem;
  }
  
  .modal-header {
    padding: 1rem;
  }

  .modal-content {
    padding: 1rem;
  }}
}
