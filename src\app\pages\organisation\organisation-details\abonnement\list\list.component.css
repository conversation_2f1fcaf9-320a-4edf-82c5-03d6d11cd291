/** Loading State **/
.loading-message {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  font-size: 1rem;
  color: #6b7280;
  font-family: "Lato", sans-serif;
}

/** Table Container **/
.table-container {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  margin-top: 20px;
  width: 100%;
  margin-left: 15px;
}

/** Table Header **/
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.table-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
  font-family: "Montserrat", sans-serif;
}

/** Color bar at top of table **/
.table-container::before {
  content: "";
  display: block;
  height: 0.5rem;
  background: linear-gradient(to right, #059669, #34d399);
}

/** Subscriptions Table **/
.subscriptions-table {
  width: 100%;
  border-collapse: collapse;
}

.subscriptions-table th {
  padding: 1rem 1.5rem;
  text-align: left;
  font-size: 0.875rem;
  font-weight: 600;
  color: #111827;
  /* background-color: #f9fafb; */
  border-bottom: 1px solid #e5e7eb;
  font-family: "Montserrat", sans-serif;
}

.subscriptions-table td {
  padding: 1rem 1.5rem;
  font-size: 0.875rem;
  color: #4b5563;
  border-bottom: 1px solid #e5e7eb;
  font-family: "Lato", sans-serif;
  vertical-align: middle;
}

.subscriptions-table tr:last-child td {
  border-bottom: none;
}

.subscriptions-table tr:hover {
  background-color: #f9fafb;
}

/** Selected row styling **/
.subscriptions-table tr.selected {
  /* background-color: #ecfdf5; */
  border-left: 4px solid #059669;
}

/** Three dots menu **/
.dots-menu {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 20px;
  color: #6b7280;
  background: transparent;
  transition: background-color 0.2s ease;
}

.dots-menu:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

/** Dropdown Container **/
.dropdown-container {
  position: relative;
  display: inline-block;
}

/** Dropdown Content **/
.dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  top: 100%;
  background-color: white;
  min-width: 160px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  z-index: 1000;
  overflow: hidden;
}

.dropdown-content.show {
  display: block;
}

/** Dropdown Items **/
.dropdown-item {
  width: 100%;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  background: none;
  border: none;
  cursor: pointer;
  text-align: left;
  color: #4b5563;
  font-family: "Lato", sans-serif;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background-color: #f3f4f6;
}

/** No Subscriptions Message **/
.no-subscriptions {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  font-size: 1rem;
  color: #6b7280;
  font-family: "Lato", sans-serif;
  font-style: italic;
}

/** Modal Styles **/
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.modal-container {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 90%;
  max-height: 90%;
  width: 800px;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
  font-family: "Montserrat", sans-serif;
}

.close-button {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.close-button:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.modal-content {
  padding: 1.5rem;
  max-height: 70vh;
  overflow-y: auto;
}

/** Responsive adjustments **/
@media (max-width: 1024px) {
  .subscriptions-table td,
  .subscriptions-table th {
    padding: 0.75rem 1rem;
  }

  .table-header {
    padding: 1rem;
  }

  .table-title {
    font-size: 1.25rem;
  }

  .modal-container {
    width: 95%;
    max-width: none;
  }
}

@media (max-width: 768px) {
  .table-container {
    width: 100%;
    margin-top: 10px;
  }

  .subscriptions-table td,
  .subscriptions-table th {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }

  .table-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .modal-header {
    padding: 1rem;
  }

  .modal-content {
    padding: 1rem;
  }
}

 