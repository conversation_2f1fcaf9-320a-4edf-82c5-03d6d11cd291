import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Client } from '@app/core/models/client';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { OrganisationApiService } from '@app/core/services/administrative/organisation.service';
import { CardOrganisationComponent } from '../../../components/card-organisation/card-organisation.component';
import { GenericTableComponent } from '../../../components/generic-table/generic-table.component';
import { Organisation } from '@app/core/models/organisation';
import { CardComponent } from '../card/card.component';
import { ClientFormService } from '@app/shared/services/client-form.service';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { NgToastComponent, NgToastService, TOAST_POSITIONS } from 'ng-angular-popup';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';

type ViewMode = 'cards' | 'table';

@Component({
  selector: 'app-clients',
  standalone: true,
  imports: [
    CommonModule,
    GenericTableComponent,
    FormsModule,
    ReactiveFormsModule,
    CardComponent,
    NgToastComponent,
    NgxUiLoaderModule
  ],
  templateUrl: './clients.component.html',
  styleUrl: './clients.component.css',
})
export class ClientsComponent {
  TOAST_POSITIONS = TOAST_POSITIONS;
  showCreateForm: boolean = false;
  viewMode: ViewMode = 'cards';
  isLoading: boolean = true;
  clients: Client[] = [];
  selectedType: string = '';
  filteredClients: Client[] = [];
  searchTerm: string = '';
  organisations: Organisation[] = [];
  currentPage: number = 1;
  pageSize: number = 4;
  totalPages: number = 1;
  totalClients: number = 0;
  uploadedLogo: File | undefined;
  logoPreviewUrl: string | null = null;


  headers: string[] = [
    'Raison Sociale',
    'Email Contact',
    'Téléphone',
    'Organisation',
    'Contact Addresse',
    'RC',
    'IF',
    'Statut'
  ];

  keys: string[] = [
    'Name',
    'ContactEmail',
    'PhoneNumber',
    'Organisation.Nom',
    'ContactAddress',
    'RC',
    'IF',
    'ClientStatus'
  ];

  constructor(
    private clientApiService: ClientApiService,
    private organisationApiService: OrganisationApiService,
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private clientFormService: ClientFormService,
    private dialog: MatDialog,
    private toast: NgToastService,
    private ngxUiLoaderService: NgxUiLoaderService
  ) { }

  // onFileSelected(event: any): void {
  //   if (event.target.files.length > 0) {
  //     this.uploadedLogo = event.target.files[0];
  //     console.log('File selected:', this.uploadedLogo);
  //   } else {
  //     this.uploadedLogo = undefined;
  //   }
  // }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.uploadedLogo = file;

      const reader = new FileReader();
      reader.onload = () => {
        const base64 = reader.result as string;

        // Keep base64 only (without data:image prefix)
        const base64Data = base64.split(',')[1];
        this.logoPreviewUrl = base64Data;
      };
      reader.readAsDataURL(file);
    }
  }
  showAddOrganisationForm(): void {
    this.showCreateForm = true;
  }
  toggleViewMode(): void {
    this.viewMode = this.viewMode === 'cards' ? 'table' : 'cards';
  }

  ngOnInit(): void {
    this.loadClients();
    // this.loadOrganisations();
  }

  loadOrganisations(): void {
    this.isLoading = true;
    this.organisationApiService.getAll().subscribe({
      next: (organisations: Organisation[]) => {
        this.organisations = organisations;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading organisations:', error);
        this.isLoading = false;
      },
    });
  }

  loadClients(): void {
    this.isLoading = true;
    console.log("Loading", this.isLoading);
    this.clientApiService.getAll().subscribe({
      next: (clients) => {

        console.log("Loading", this.isLoading);
        // Transform the API response to match your interface
        this.clients = clients;
        console.log('Clients loaded:', this.clients);

        console.log("Loading", this.isLoading);
        this.filterClients();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading clients:', error);
        this.isLoading = false;
      },
    });
  }

  filterClients(): void {
    if (!this.searchTerm) {
      this.filteredClients = [...this.clients];
      return;
    }

    const lowerCaseSearchTerm = this.searchTerm.toLowerCase();
    this.filteredClients = this.clients.filter(
      (client) =>
        (client.Name &&
          client.Name.toLowerCase().includes(lowerCaseSearchTerm)) ??
        (client.PhoneNumber &&
          client.PhoneNumber.toLowerCase().includes(lowerCaseSearchTerm)) ??
        (client.Organisation?.Nom &&
          client.Organisation.Nom.toLowerCase().includes(lowerCaseSearchTerm))
    );
  }

  filterClientsByOrganisation(organisationId: string): void {
    console.log('Filtering clients by organisation ID:', organisationId);
    this.filteredClients = this.clients.filter(
      (client) => client.IdOrganisation === organisationId
    );

    // Update the selected type for display
    const organisation = this.organisations.find(org => org.Id === organisationId);
    if (organisation) {
      this.selectedType = organisation.Nom;
    }
  }

  scrollToSection(): void {
    const element = document.getElementById('section');
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  }

  resetFilter(): void {
    this.selectedType = '';
    this.searchTerm = '';
    this.filteredClients = [...this.clients];
  }

  onSearchChange(): void {
    this.filterClients();
  }


  handleAction(event: { action: string; row: Client }): void {
    switch (event.action) {
      case 'view':
        this.viewClientDetails(event.row);
        break;
      case 'edit':
        this.editClient(event.row);
        break;
      case 'delete':
        this.deleteClient(event.row);
        break;
      default:
        console.warn('Unknown action:', event.action);
    }
  }

  viewClientDetails(client: Client): void {
    console.log('Viewing client details:', client);
    // Navigate to client details - adjust route as needed
    this.router.navigate(['organisation-details/', client.Id]);
    console.log('Redirecting to /organisation-details/' + client.Id);
  }
  async editClient(client: Client) {
    console.log("edit client specific", client.Id);
    await this.clientApiService.getById(client.Id ?? "").subscribe({
      next: (response) => {
        const dialogRef = this.clientFormService.openEditClientDialog(response, this.organisations)
          .then(result => {
            if (result) {
              this.loadClients();
            }
          });
        console.log("Le Client a été modifié avec succes", "Information", response);
      },
      error: (error: any) => {
        console.log("Erreur lors de la modification de client", "Erreur");
      },
    });

    // Navigate to client edit - adjust route as needed
    // this.router.navigate(['organisations/edit/', client.id]);
    // console.log('Redirecting to /organisations/edit/' + client.id);
  }

  deleteClient(client: Client): void {

    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirmation de suppression',
        message: `Êtes-vous sûr de vouloir supprimer définitivement le client ${client.Name} ?\n\n` +
          `Cette action supprimera toutes les données associées et est irréversible.`,
        icon: 'warning'
      }
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.isLoading = true;
        this.clientApiService.delete(client.Id).subscribe({
          next: () => {
            this.loadClients();
            this.showSuccess("Le Client a été suprimé avec succes", "Information");
          },
          error: (error) => {
            console.error('Error deleting client:', error);
            this.showError("Erreur lors de la suppression de client, Essayer ultérieurement", "Erreur");
          },
        });
      }
    });
  }

  viewClientDetailsById(id: string): void {
    console.log('Viewing client details by ID:', id);
    const client = this.clients.find((c) => c.Id === id);
    if (client) this.viewClientDetails(client);
  }

  editClientById(id: string): void {
    const client = this.clients.find((c) => c.Id === id);
    if (client) this.editClient(client);
  }

  deleteClientById(id: string): void {
    const client = this.clients.find((c) => c.Id === id);
    if (client) this.deleteClient(client);
  }
  onPageChange(event: { pageIndex: number; pageSize: number } | number): void {
    if (typeof event === 'number') {
      this.currentPage = event;
    } else {
      this.currentPage = event.pageIndex + 1;
      this.pageSize = event.pageSize;
    }
    this.loadClients();
  }

  getPageNumbers(): number[] {
    if (this.totalPages <= 7) {
      return Array.from({ length: this.totalPages }, (_, i) => i + 1);
    }

    if (this.currentPage <= 4) {
      return [1, 2, 3, 4, 5, -1, this.totalPages];
    }

    if (this.currentPage >= this.totalPages - 3) {
      return [
        1,
        -1,
        this.totalPages - 4,
        this.totalPages - 3,
        this.totalPages - 2,
        this.totalPages - 1,
        this.totalPages,
      ];
    }

    return [
      1,
      -1,
      this.currentPage - 1,
      this.currentPage,
      this.currentPage + 1,
      -1,
      this.totalPages,
    ];
  }

  public showSuccess(message: string, title: string) {
    this.toast.info(message, title, 3000, false);
  }

  public showError(message: string, title: string) {
    this.toast.warning(message, title, 3000, false);
  }
}
