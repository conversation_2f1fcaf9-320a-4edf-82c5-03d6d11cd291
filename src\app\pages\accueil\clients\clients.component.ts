import { CommonModule } from '@angular/common';
import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Client } from '@app/core/models/client';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { OrganisationApiService } from '@app/core/services/administrative/organisation.service';
import { GenericTableComponent } from '../../../components/generic-table/generic-table.component';
import { Organisation } from '@app/core/models/organisation';
import { CardComponent } from '../card/card.component';
import { ClientFormService } from '@app/shared/services/client-form.service';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import {
  NgToastComponent,
  NgToastService,
  TOAST_POSITIONS,
} from 'ng-angular-popup';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';
import { FilterParam, Lister, Pagination } from '@app/core/models/util/page';
import { PageEvent, MatPaginatorModule } from '@angular/material/paginator';

type ViewMode = 'cards' | 'table';

@Component({
  selector: 'app-clients',
  standalone: true,
  imports: [
    CommonModule,
    GenericTableComponent,
    FormsModule,
    ReactiveFormsModule,
    CardComponent,
    NgToastComponent,
    NgxUiLoaderModule,
    MatPaginatorModule,
  ],
  templateUrl: './clients.component.html',
  styleUrl: './clients.component.css',
})
export class ClientsComponent implements OnChanges {
  @Input() selectedOrganisationId: string | null = null;
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedOrganisationId']) {
      this.loadClients();
    }
  }
  TOAST_POSITIONS = TOAST_POSITIONS;
  showCreateForm: boolean = false;
  viewMode: ViewMode = 'cards';
  isLoading: boolean = true;
  clients: Client[] = [];
  selectedType: string = '';
  filteredClients: Client[] = [];
  searchTerm: string = '';
  organisations: Organisation[] = [];
  currentPage: number = 0;
  pageSize: number = 5;
  totalPages: number = 1;
  totalClients: number = 0;
  uploadedLogo: File | undefined;
  logoPreviewUrl: string | null = null;
  totalCount: number = 0;
  searchOrganisationType: boolean = false;
  searchParam: string = '';
  hasSearchFilter: boolean = false;

  headers: string[] = [
    'Raison Sociale',
    'Email Contact',
    'Téléphone',
    'Organisation',
    'Contact Addresse',
    'RC',
    'IF',
    'Statut',
  ];

  keys: string[] = [
    'Name',
    'ContactEmail',
    'PhoneNumber',
    'Organisation.Nom',
    'ContactAddress',
    'RC',
    'IF',
    'ClientStatus',
  ];

  constructor(
    readonly clientApiService: ClientApiService,
    readonly organisationApiService: OrganisationApiService,
    readonly fb: FormBuilder,
    readonly router: Router,
    readonly route: ActivatedRoute,
    readonly clientFormService: ClientFormService,
    readonly dialog: MatDialog,
    readonly toast: NgToastService,
    readonly ngxUiLoaderService: NgxUiLoaderService
  ) {}

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.uploadedLogo = file;

      const reader = new FileReader();
      reader.onload = () => {
        const base64 = reader.result as string;

        // Keep base64 only (without data:image prefix)
        const base64Data = base64.split(',')[1];
        this.logoPreviewUrl = base64Data;
      };
      reader.readAsDataURL(file);
    }
  }
  showAddOrganisationForm(): void {
    this.showCreateForm = true;
  }
  toggleViewMode(): void {
    this.viewMode = this.viewMode === 'cards' ? 'table' : 'cards';
  }

  ngOnInit(): void {
    this.loadClients();
    this.loadOrganisations();
  }

  loadOrganisations(): void {
    this.isLoading = true;
    this.organisationApiService.getAll().subscribe({
      next: (organisations: Organisation[]) => {
        this.organisations = organisations;
        console.log(this.organisations, 'organisations');
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading organisations:', error);
        this.isLoading = false;
      },
    });
  }

  request: Lister = {};

  clearTypeFilter() {
    this.selectedOrganisationId = null;
    this.searchOrganisationType = false;
    this.currentPage = 0; // Reset to first page
    // Reset the request object to clear any cached filter parameters
    this.request = {
      pagination: {
        CurrentPage: 1,
        PageSize: this.pageSize,
      },
      filterParams: []
    };
    this.loadClients();
  }

  loadClients(): void {
    this.isLoading = true;
    const pagination: Pagination = {
      CurrentPage: this.currentPage + 1,
      PageSize: this.pageSize,
    };

    this.request.pagination = pagination;

    const filterParams: FilterParam[] = [];

    if (this.selectedOrganisationId != null) {
      this.searchOrganisationType = true;
      filterParams.push({
        Column: 'IdOrganisation',
        Op: 'eq',
        Value: this.selectedOrganisationId,
        AndOr: 'AND',
      });
    }

    // Add search filter if searchParam is not empty
    if (this.searchParam && this.searchParam.trim() !== '') {
      this.hasSearchFilter = true;
      filterParams.push({
        Column: 'Name',
        Value: this.searchParam.trim(),
        Op: 'contains',
        AndOr: 'AND',
      });
    } else {
      this.hasSearchFilter = false;
    }

    this.request.filterParams = filterParams;
    this.clientApiService.gatePage(this.request).subscribe({
      next: (result: any) => {
        console.log(result);
        console.log('Clients loaded:', this.clients);
        if (result?.Lister?.Pagination?.TotalElement !== undefined) {
          this.totalCount = result.Lister.Pagination.TotalElement;
        } else {
          this.totalCount = Math.max(8, result.Content.length);
        }
        console.log('Loading', this.isLoading);
        this.clients = result.Content;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading clients:', error);
        this.isLoading = false;
      },
    });
  }

  onSearchKeyup( event: KeyboardEvent): void{
    if (event.key === 'Enter') {
      this.searchClients();
    } else if (event.key === 'Backspace' && this.searchParam === '') {
      this.loadClients();
    }
  }
  filterClientsByOrganisation(organisationId: string): void {
    console.log('Filtering clients by organisation ID:', organisationId);
    this.filteredClients = this.clients.filter(
      (client) => client.IdOrganisation === organisationId
    );

    // Update the selected type for display
    const organisation = this.organisations.find(
      (org) => org.Id === organisationId
    );
    if (organisation) {
      this.selectedType = organisation.Nom;
    }
  }

  scrollToSection(): void {
    const element = document.getElementById('section');
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  }

  resetFilter(): void {
    this.selectedType = '';
    this.searchTerm = '';
    this.filteredClients = [...this.clients];
  }

  handleAction(event: { action: string; row: Client }): void {
    switch (event.action) {
      case 'view':
        this.viewClientDetails(event.row);
        break;
      case 'edit':
        this.editClient(event.row);
        break;
      case 'delete':
        this.deleteClient(event.row);
        break;
      default:
        console.warn('Unknown action:', event.action);
    }
  }

  viewClientDetails(client: Client): void {
    console.log('Viewing client details:', client);
    // Navigate to client details - adjust route as needed
    this.router.navigate(['organisation-details/', client.Id]);
    console.log('Redirecting to /organisation-details/' + client.Id);
  }

  async editClient(client: Client) {
    console.log('edit client specific', client.Id);
    await this.clientApiService.getById(client.Id ?? '').subscribe({
      next: (response) => {
        const dialogRef = this.clientFormService
          .openEditClientDialog(response, this.organisations)
          .then((result) => {
            if (result) {
              this.loadClients();
            }
          });
        console.log(
          'Le Client a été modifié avec succes',
          'Information',
          response
        );
      },
      error: (error: any) => {
        console.log('Erreur lors de la modification de client', 'Erreur');
      },
    });
  }

  deleteClient(client: Client): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirmation de suppression',
        message:
          `Êtes-vous sûr de vouloir supprimer définitivement le client ${client.Name} ?\n\n` +
          `Cette action supprimera toutes les données associées et est irréversible.`,
        icon: 'warning',
      },
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.isLoading = true;
        this.clientApiService.delete(client.Id).subscribe({
          next: () => {
            this.loadClients();
            this.showSuccess(
              'Le Client a été suprimé avec succes',
              'Information'
            );
          },
          error: (error) => {
            console.error('Error deleting client:', error);
            this.showError(
              'Erreur lors de la suppression de client, Essayer ultérieurement',
              'Erreur'
            );
          },
        });
      }
    });
  }

  viewClientDetailsById(id: string): void {
    console.log('Viewing client details by ID:', id);
    const client = this.clients.find((c) => c.Id === id);
    if (client) this.viewClientDetails(client);
  }

  editClientById(id: string): void {
    const client = this.clients.find((c) => c.Id === id);
    if (client) this.editClient(client);
  }

  deleteClientById(id: string): void {
    const client = this.clients.find((c) => c.Id === id);
    if (client) this.deleteClient(client);
  }

  public showSuccess(message: string, title: string) {
    this.toast.info(message, title, 3000, false);
  }

  public showError(message: string, title: string) {
    this.toast.warning(message, title, 3000, false);
  }

  searchClients(): void {
    this.currentPage = 0; // Reset to first page when searching
    this.loadClients();
  }

  clearSearch(): void {
    this.searchParam = '';
    this.hasSearchFilter = false;
    this.currentPage = 0; // Reset to first page when clearing search
    this.loadClients();
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;

    console.log(
      'Updated pagination - Page:',
      this.currentPage,
      'Size:',
      this.pageSize
    );
    // Reload clients with new pagination
    this.loadClients();
  }
}
