<div class="org-card" (click)="onDetailsClick()">
  <div class="card-image-container">
    <img [src]="'data:image/jpeg;base64,' + client.ClientLogo" [alt]="client.Name" class="card-image" *ngIf="client.ClientLogo" />
    <div *ngIf="!client.ClientLogo" class="no-logo">
      <i class="material-icons">location_city</i>
    </div>
    <div class="card-overlay"></div>
  </div>
  <div class="card-header">
    <h3 class="org-title">{{ client.Name }}</h3>
    <p class="org-type">{{ client.LegalForm }} - {{ client.CompanySize }}</p>
    <p class="org-location">
      <i class="material-icons icon-small">location_on</i>
      {{ client.City }}, {{ client.Country }}
    </p>
  </div>

  <div class="card-content">
    <div class="info-row">
      <div class="info-item">
        <i class="material-icons icon-small">event</i>
        C<PERSON>é en {{ client.CompanyCreationDate | date:'yyyy' }}
      </div>
    </div>

    <div class="primary-contact">
      <h4>PRIMARY CONTACT</h4>
      <p class="contact-name">{{ client.ContactName }}</p>
      <p class="contact-email">
        <i class="material-icons icon-small">email</i>
        {{ client.ContactEmail }}
      </p>
      <p class="contact-phone">
        <i class="material-icons icon-small">phone</i>
        {{ client.PhoneNumber }}
      </p>
    </div>
  </div>
    <!-- <div class="info-row">
      <div class="info-item full-width">
        <i class="material-icons icon-small">home</i>
        {{ 55 }} locaux
      </div>
    </div> -->


  <div class="card-actions">
    <button class="btn btn-primary" (click)="onDetailsClick()">
      <i class="material-icons">visibility</i>
    </button>
    <button class="btn btn-accent" (click)="onEditClick($event)">
      <i class="material-icons">edit</i>
    </button>
    <button class="btn btn-danger" (click)="onDeleteClick($event)">
      <i class="material-icons">delete</i>
    </button>
  </div>
</div>