import {
  Component,
  OnInit,
  AfterViewInit,
  ViewChild,
  ElementRef,
  OnDestroy,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { Chart, ChartConfiguration, registerables } from 'chart.js';
import L from 'leaflet';

Chart.register(...registerables);

interface SalesData {
  title: string;
  amount: string;
  icon: string;
  iconColor: string;
  progress: number;
  color: string;
}

@Component({
  selector: 'app-dashboard-client',
  standalone: true,
  imports: [CommonModule, MatIconModule],
  templateUrl: './dashboard-client.component.html',
  styleUrls: ['./dashboard-client.component.css'],
})
export class DashboardClientComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  @ViewChild('chartCanvas', { static: false })
  chartCanvas!: ElementRef<HTMLCanvasElement>;
  @ViewChild('mapContainer', { static: false }) mapContainerRef!: ElementRef;

  private consommationChart?: Chart;
  private depenseEnergyChart?: Chart;
  private expenseChart?: Chart;
  private map?: L.Map;

  locations = [
    { id: 1, name: 'Thiruvananthapuram', latitude: 8.5241, longitude: 76.9366 },
    { id: 2, name: 'Kochi', latitude: 9.9312, longitude: 76.2673 },
    { id: 3, name: 'Kozhikode', latitude: 11.2588, longitude: 75.7804 },
    { id: 4, name: 'Thrissur', latitude: 10.5276, longitude: 76.2144 },
    { id: 5, name: 'Alappuzha', latitude: 9.4981, longitude: 76.3388 },
    { id: 6, name: 'Kollam', latitude: 8.8932, longitude: 76.6141 },
  ];

  salesData: SalesData[] = [
    {
      title: 'Consommation moyenne par mois en euro',
      amount: '€ 2,859.95',
      icon: 'fa-solid fa-money-bill',
      iconColor: '#28a745',
      progress: 85,
      color: '#28a745',
    },
    {
      title: "Nombre d'équipement",
      amount: '2,500',
      icon: 'fa-solid fa-toolbox',
      iconColor: '#00c9a7',
      progress: 100,
      color: '#00c9a7',
    },
    {
      title: 'Économisations en euro',
      amount: '€ 8,638.32',
      icon: 'fa-solid fa-piggy-bank',
      iconColor: '#ffc107',
      progress: 92,
      color: '#ffc107',
    },
    {
      title: "Nombre d'employées",
      amount: '1,475',
      icon: 'fa-solid fa-users',
      iconColor: '#17a2b8',
      progress: 100,
      color: '#17a2b8',
    },
    {
      title: "Économies d'Énergie Totales",
      amount: '€ 1,400.84',
      icon: 'fa-solid fa-leaf',
      iconColor: '#28a745',
      progress: 78,
      color: '#28a745',
    },
    {
      title: 'Réduction en Pourcentage',
      amount: '33%',
      icon: 'fa-solid fa-chart-line',
      iconColor: '#dc3545',
      progress: 33,
      color: '#dc3545',
    },
    {
      title: 'Consommation Moyenne Mensuelle',
      amount: '1,250 kWh',
      icon: 'fa-solid fa-bolt',
      iconColor: '#fd7e14',
      progress: 65,
      color: '#fd7e14',
    },
    {
      title: "Taux de Réalisation de l'Objectif",
      amount: '99%',
      icon: 'fa-solid fa-chart-line',
      iconColor: '#198754',
      progress: 99,
      color: '#198754',
    },
    {
      title: 'Variation Mensuelle Moyenne',
      amount: '148.526 kWh/mois',
      icon: 'fa-solid fa-chart-area',
      iconColor: '#6f42c1',
      progress: 72,
      color: '#6f42c1',
    },
    {
      title: 'Taux de Réduction par Mois',
      amount: '51.684%',
      icon: 'fa-solid fa-arrow-trend-down',
      iconColor: '#20c997',
      progress: 52,
      color: '#20c997',
    },
    {
      title: 'Réduction Énergétique Cumulée',
      amount: '3,247 kWh',
      icon: 'fa-solid fa-battery-quarter',
      iconColor: '#e83e8c',
      progress: 68,
      color: '#e83e8c',
    },
    {
      title: "Nombre de Mois où l'Objectif est Atteint",
      amount: '8/12',
      icon: 'fa-solid fa-calendar-check',
      iconColor: '#6610f2',
      progress: 67,
      color: '#6610f2',
    },
  ];

  ngOnInit() {}

  ngAfterViewInit() {
    this.createConsommationChart();
    this.createDepenseEnergyChart();
    this.createExpenseChart();
    this.initMap();
  }
  private initMap(): void {
    const defaultIcon = L.icon({
      iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
      shadowUrl:
        'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
      iconSize: [25, 41],
      iconAnchor: [12, 41],
      popupAnchor: [1, -34],
    });

    L.Marker.prototype.options.icon = defaultIcon;

    this.map = L.map('map').setView(
      [this.locations[3].latitude, this.locations[3].longitude],
      7
    );

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution:
        '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>',
    }).addTo(this.map);

    this.locations.forEach((location) => {
      L.marker([location.latitude, location.longitude])
        .addTo(this.map!)
        .bindPopup(`<b>${location.name}</b>`);
    });
  }

  private createConsommationChart() {
    const ctx = this.chartCanvas.nativeElement.getContext('2d');
    if (!ctx) return;

    const labels = [
      'Jan',
      'Fév',
      'Mar',
      'Avr',
      'Mai',
      'Jun',
      'Jul',
      'Aoû',
      'Sep',
      'Oct',
      'Nov',
      'Déc',
    ];
    const beforeOpti = [
      400,
      500,
      600,
      700,
      800,
      900,
      null,
      null,
      null,
      null,
      null,
      null,
    ];
    const afterOpti = [
      420,
      544,
      596,
      600,
      453,
      800,
      976,
      null,
      null,
      null,
      null,
      null,
    ];
    const objectifs = [
      540,
      896,
      452,
      600,
      785,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
    ];

    const chartData = {
      labels,
      datasets: [
        {
          label: 'Conso avant opti (kWh)',
          data: beforeOpti,
          borderColor: '#dc3545',
          fill: false,
          tension: 0.4,
          spanGaps: true,
          borderWidth: 3,
        },
        {
          label: 'Conso après opti (kWh)',
          data: afterOpti,
          borderColor: '#28a745',
          fill: false,
          tension: 0.4,
          spanGaps: true,
          borderWidth: 3,
        },
        {
          label: 'Objectif (kWh)',
          data: objectifs,
          borderColor: '#ffc107',
          fill: false,
          tension: 0,
          spanGaps: true,
          borderDash: [10, 5],
          borderWidth: 2,
        },
      ],
    };

    new Chart(ctx, {
      type: 'line',
      data: chartData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: 'Consommation Énergétique',
          },
          legend: { position: 'top' },
        },
        scales: {
          y: {
            beginAtZero: true,
            suggestedMin: 0,
            title: { display: true, text: 'kWh' },
          },
          x: {
            title: { display: true, text: 'Mois' },
          },
        },
      },
    });
  }

  private createDepenseEnergyChart() {
    const depenseCanvas = document.getElementById(
      'energyLineChart'
    ) as HTMLCanvasElement;
    if (!depenseCanvas) return;
    const ctx = depenseCanvas.getContext('2d');
    if (!ctx) return;

    const data = {
      labels: [
        'Jan',
        'Fév',
        'Mar',
        'Avr',
        'Mai',
        'Jun',
        'Jul',
        'Aoû',
        'Sep',
        'Oct',
        'Nov',
        'Déc',
      ],
      datasets: [
        {
          label: 'Dépense Énergétique (kWh)',
          data: [300, 450, 500, 550, 600, 580, 600, 610, 630, 640, 650, 670],
          borderColor: '#007bff',
          backgroundColor: 'rgba(0, 123, 255, 0.1)',
          fill: true,
          tension: 0.3,
          borderWidth: 3,
          pointStyle: 'circle',
          pointRadius: 5,
          pointHoverRadius: 7,
        },
      ],
    };

    const config: ChartConfiguration = {
      type: 'line',
      data,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: 'Dépense Énergétique',
            font: { size: 18, weight: 'bold' },
          },
          legend: {
            display: true,
            position: 'top',
          },
        },
        scales: {
          y: {
            beginAtZero: true,
            title: { display: true, text: 'Dépense (kWh)' },
            grid: { color: 'rgba(0, 0, 0, 0.1)' },
          },
          x: {
            title: { display: true, text: 'Mois' },
            grid: { color: 'rgba(0, 0, 0, 0.1)' },
          },
        },
      },
    };

    this.depenseEnergyChart = new Chart(ctx, config);
  }

  private createExpenseChart() {
    const expenseCanvas = document.getElementById(
      'expenseChart'
    ) as HTMLCanvasElement;
    if (!expenseCanvas) return;
    const ctx = expenseCanvas.getContext('2d');
    if (!ctx) return;

    const data = {
      labels: ['Électricité', 'Gaz', 'Eau'],
      datasets: [
        {
          data: [65, 25, 10],
          backgroundColor: ['#ffc107', '#17a2b8', '#28a745'],
          borderWidth: 3,
          borderColor: '#fff',
        },
      ],
    };

    const config: ChartConfiguration = {
      type: 'doughnut',
      data,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: 'Répartition des Dépenses Énergétiques',
            font: { size: 18, weight: 'bold' },
          },
          legend: {
            display: true,
            position: 'bottom',
          },
        },
      },
    };

    this.expenseChart = new Chart(ctx, config);
  }

  ngOnDestroy() {
    this.consommationChart?.destroy();
    this.depenseEnergyChart?.destroy();
    this.expenseChart?.destroy();
    this.map?.remove();
  }
}
