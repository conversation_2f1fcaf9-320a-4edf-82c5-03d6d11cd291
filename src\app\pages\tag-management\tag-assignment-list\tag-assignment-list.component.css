/* Tag Assignment List Container */
.tag-assignment-list-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* Header Card Container */
.header-card-container {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
  animation: slideIn 0.5s ease-out;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: #2E7D32;
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 0 5px 0;
}

.title-icon {
  font-size: 28px !important;
  width: 28px !important;
  height: 28px !important;
  color: #2E7D32;
}

.subtitle {
  color: #666;
  margin: 0;
  font-size: 14px;
}

/* Content Container */
.content-container {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 25px;
}

/* Table Cards */
.table-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e2e8f0;
}

.table-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2E7D32;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Table Styles */
.table-stats {
  display: flex;
  gap: 10px;
}

.stats-badge {
  background: linear-gradient(45deg, #2E7D32, #81C784);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.data-table th {
  background: #f8f9fa;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #4a5568;
  border-bottom: 1px solid #e2e8f0;
  font-size: 14px;
}

.data-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #f1f5f9;
  font-size: 14px;
  color: #2d3748;
}

.data-table tr:hover {
  background-color: #f8f9fa;
}

.tag-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tag-icon {
  font-size: 18px !important;
  width: 18px !important;
  height: 18px !important;
  color: #2E7D32;
}

.target-type-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.target-type-badge.type-client {
  background: #e3f2fd;
  color: #1976d2;
}

.target-type-badge.type-site {
  background: #f3e5f5;
  color: #7b1fa2;
}

.target-type-badge.type-local {
  background: #e8f5e8;
  color: #388e3c;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-action {
  padding: 6px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-delete {
  background: #ffebee;
  color: #d32f2f;
}

.btn-delete:hover {
  background: #ffcdd2;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.empty-icon {
  font-size: 48px !important;
  width: 48px !important;
  height: 48px !important;
  color: #cbd5e0;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #4a5568;
}

.empty-state p {
  margin: 0 0 20px 0;
  color: #718096;
}

.btn-primary {
  padding: 10px 20px;
  background: linear-gradient(45deg, #2E7D32, #81C784);
  color: white;
  border: none;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(46, 125, 50, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
}

.btn-primary:hover {
  background: linear-gradient(45deg, #81C784, #2E7D32);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(46, 125, 50, 0.4);
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  background: white;
  padding: 30px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.spinning {
  animation: spin 1s linear infinite;
  font-size: 32px !important;
  color: #2E7D32;
  margin-bottom: 10px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .tag-assignment-list-container {
    padding: 15px;
  }
  
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .table-container {
    font-size: 12px;
  }
  
  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
