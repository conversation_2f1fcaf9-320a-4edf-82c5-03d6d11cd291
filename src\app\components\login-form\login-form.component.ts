import { Component } from '@angular/core';
import {FormsModule, NgForm} from '@angular/forms';

@Component({
  selector: 'app-login-form',
  imports: [
    FormsModule
  ],
  templateUrl: './login-form.component.html',
  styleUrl: './login-form.component.css'
})
export class LoginFormComponent {

  onSubmit(form: NgForm) {
    if (form.valid) {
      const { email, password, remember } = form.value;
      alert(`Login submitted!\nEmail: ${email}\nRemember Me: ${remember}`);
      // You can also call an auth service here
    }
  }

}
