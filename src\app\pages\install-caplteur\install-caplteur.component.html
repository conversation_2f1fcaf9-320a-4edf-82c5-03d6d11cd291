<div class="plan-editor">
  <!-- 🎯 Header avec sélecteur de type de plan -->
  <header class="editor-header">
    <div class="header-left">
      <h1 class="editor-title">
        <span class="title-icon"></span>
        Install Capteur
      </h1>



      <!-- Sélecteurs de contexte -->
      <div class="context-selectors">
        <!-- Sélecteur Client -->
        <div class="selector-group">
          <label class="selector-label">Client</label>
          <div class="dropdown" [class.open]="isClientDropdownOpen">
            <button class="dropdown-trigger" (click)="toggleClientDropdown()">
              <span class="selected-text">
                {{ selectedClient ? selectedClient.Name : 'Sélectionner un client' }}
              </span>
              <span class="dropdown-icon">{{ isClientDropdownOpen ? '▲' : '▼' }}</span>
            </button>
            <div class="dropdown-menu" *ngIf="isClientDropdownOpen">
              <div class="search-box">
                <input type="text" placeholder="Rechercher un client..." [(ngModel)]="clientSearchTerm"
                  class="search-input" (click)="$event.stopPropagation()">
              </div>
              <div class="dropdown-options">
                <div *ngFor="let client of filteredClients" class="dropdown-option" (click)="selectClient(client)">
                  <div class="option-main">
                    <span class="option-name">{{ client.Name }}</span>

                  </div>
                  <div class="option-meta">{{ client.BusinessSector }}</div>
                </div>
                <div *ngIf="filteredClients.length === 0" class="no-results">
                  Aucun client trouvé
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sélecteur Site -->
        <div class="selector-group">
          <label class="selector-label">Site</label>
          <div class="dropdown" [class.open]="isSiteDropdownOpen" [class.disabled]="!selectedClient">
            <button class="dropdown-trigger" (click)="toggleSiteDropdown()" [disabled]="!selectedClient">
              <span class="selected-text">
                {{ selectedSite ? selectedSite.Name : (selectedClient ? 'Sélectionner un site' : 'Choisir un client
                d\'abord') }}
              </span>
              <span class="dropdown-icon">{{ isSiteDropdownOpen ? '▲' : '▼' }}</span>
            </button>
            <div class="dropdown-menu" *ngIf="isSiteDropdownOpen && selectedClient">
              <div class="search-box">
                <input type="text" placeholder="Rechercher un site..." [(ngModel)]="siteSearchTerm" class="search-input"
                  (click)="$event.stopPropagation()">
              </div>
              <div class="dropdown-options">
                <div *ngFor="let site of sites" class="dropdown-option" (click)="selectSite(site)">
                  <div class="option-main">
                    <span class="option-name">{{ site.Name }}</span>

                  </div>
                  <div class="option-meta">{{ site.Address }}</div>
                </div>
                <div *ngIf="filteredSites.length === 0" class="no-results">
                  Aucun site trouvé
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sélecteur Local -->
        <div class="selector-group">
          <label class="selector-label">Local</label>
          <div class="dropdown" [class.open]="isLocalDropdownOpen" [class.disabled]="!selectedSite">
            <button class="dropdown-trigger" (click)="toggleLocalDropdown()" [disabled]="!selectedSite">
              <span class="selected-text">
                {{ selectedLocal ? selectedLocal.Name : (selectedSite ? 'Sélectionner un local' : 'Choisir un site
                d\'abord') }}
              </span>
              <span class="dropdown-icon">{{ isLocalDropdownOpen ? '▲' : '▼' }}</span>
            </button>
            <div class="dropdown-menu" *ngIf="isLocalDropdownOpen && selectedSite">
              <div class="search-box">
                <input type="text" placeholder="Rechercher un local..." [(ngModel)]="localSearchTerm"
                  class="search-input" (click)="$event.stopPropagation()">
              </div>
              <div class="dropdown-options">
                <div *ngFor="let local of locals" class="dropdown-option" (click)="selectLocal(local)">
                  <div class="option-main">
                    <span class="option-name">{{ local.Name }}</span>
                    <span class="option-floor">{{ local.floor }}</span>
                  </div>
                  <div class="option-meta">
                    <span class="option-area">{{ local.area }}m²</span>
                    <span class="option-type">{{ local.type }}</span>
                  </div>
                </div>
                <div *ngIf="filteredLocals.length === 0" class="no-results">
                  Aucun local trouvé
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sélecteur Controleur -->
        <div class="selector-group">
          <label class="selector-label">Controleur</label>
          <div class="dropdown" [class.open]="isControlerDropdownOpen" [class.disabled]="!selectedLocal">
            <button class="dropdown-trigger" (click)="toggleControlerDropdown()" [disabled]="!selectedLocal">
              <span class="selected-text">
                {{ selectedControler ? selectedControler.Model : (selectedLocal ? 'Sélectionner un Controleur' :
                'Choisir un site d\'abord') }}
              </span>
              <span class="dropdown-icon">{{ isLocalDropdownOpen ? '▲' : '▼' }}</span>
            </button>
            <div class="dropdown-menu" *ngIf="isControlerDropdownOpen && selectedLocal">
              <div class="search-box">
                <input type="text" placeholder="Rechercher un local..." [(ngModel)]="ControlerSearchTerm"
                  class="search-input" (click)="$event.stopPropagation()">
              </div>
              <div class="dropdown-options">
                <div *ngFor="let Controler of Controlers" class="dropdown-option" (click)="selectControler(Controler)">
                  <div class="option-main">
                    <span class="option-name">{{ Controler.Model }}</span>

                  </div>
                  <div class="option-meta">
                    <span class="option-area">{{ Controler.HostName }}</span>

                  </div>
                </div>
                <div *ngIf="filteredLocals.length === 0" class="no-results">
                  Aucun local trouvé
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>

      <div class="export-section">
        <button class="export-btn primary" (click)="addPlanToLocal()">
          <span class="btn-icon">💾</span>
          Enregistrer
        </button>
        <button class="export-btn" [class.primary]="!pairingMode" [class.warn]="pairingMode"
          (click)="togglePermitJoin()">
          <mat-icon>
            {{ pairingMode ? 'stop' : 'settings_backup_restore' }}
          </mat-icon>
          {{ pairingMode ? 'Arrêter l\'appairage' : 'Autoriser l\'appairage' }}
        </button>

        <div class="pairing-container">


          <!-- Affichage du compteur quand l'appairage est actif -->
          <div class="pairing-countdown" *ngIf="pairingMode && pairingTimeLeft > 0">
            <mat-icon>timer</mat-icon>
            <span>Temps restant: {{ getFormattedTimeLeft() }}</span>
            <div class="progress-bar">
              <div class="progress-fill" [style.width.%]="(pairingTimeLeft / 240) * 100">
              </div>
            </div>
          </div>
        </div>


      </div>

    </div>


  </header>

  <div class="editor-body">
    <!-- 🎨 Palette d'outils -->
    <aside class="tools-palette">
      <!-- Section Structure -->
      <div class="palette-section">
        <h3 class="section-title">
          <span class="section-icon">🏗️</span>
          Les Capteur
        </h3>
        <div class="tool-group">
          <button class="tool-btn" [class.selected]="selectedTool === element.FriendlyName"
            *ngFor="let element of Capteurs" (click)="addShape(element.Type)" [title]="element.Model">
            <mat-icon>water_drop</mat-icon>
            <span class="tool-name">{{ element.FriendlyName }}</span>
          </button>
        </div>
      </div>

      <div class="palette-section">
        <h3 class="section-title">
          <span class="section-icon">🏗️</span>
          Les Nouveaux Capteurs
        </h3>
        <div class="tool-group">
          <button class="tool-btn" [class.selected]="selectedTool === element.FriendlyName"
            *ngFor="let element of peredCapteurs" (click)="addShape(element.FriendlyName)" [title]="element.Model">
            <mat-icon>water_drop</mat-icon>
            <span class="tool-name">{{ element.FriendlyName }}</span>
          </button>
        </div>
      </div>







    </aside>

    <!-- 🖼️ Zone de dessin -->
    <main class="canvas-area">
      <div class="canvas-header">
        <div class="canvas-info">
          <span class="canvas-title">Plan 2D</span>

        </div>

      </div>

      <div class="canvas-wrapper">
        <canvas id="canvas" width="1000" height="700"></canvas>
        <div class="canvas-overlay" [class.selecting]="selectedTool">
          <div class="selection-hint" *ngIf="selectedTool">
            Cliquez pour placer: {{ getSelectedToolName() }}
          </div>
        </div>
      </div>
    </main>
  </div>


</div>