<ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>
  <!-- Edit Form Popup -->
  <app-form-site 
    *ngIf="showEditForm && selectedSite"
    [selectedSite]="selectedSite"
    [editSiteForm]="editSiteForm"
    [showOptionalFields]="showOptionalFields"
    (formSubmit)="submitEditForm()"
    (closeForm)="hideEditForm()"
    (toggleOptionalFields)="toggleOptionalFields($event)"
    (removeExistingImage)="removeExistingImage()"
    (editImagesSelected)="onEditImagesSelected($event)">
  </app-form-site>
<div class="site-management-container">
  <div class="header-section">
    <div class="page-title">
      <h1 class="title">
        <mat-icon class="title-icon">location_on</mat-icon> Gestion des Sites
      </h1>
    </div>
    <div class="actions"></div>
  </div>



  <div class="search-bar">
    <input type="text" [(ngModel)]="searchTerm" placeholder="Rechercher un site" (keyup.enter)="filterSites()" />
    <button class="search-button" (click)="filterSites()">
      <mat-icon>search</mat-icon>
    </button>
  </div>

  <div class="table-view" *ngIf="viewMode === 'table'">
    <app-generic-table
      [data]="filteredSites"
      [headers]="headers"
      [keys]="keys"
      [actions]="['edit', 'view', 'delete']"
      (actionTriggered)="handleAction($event)">
    </app-generic-table>

    <div class="pagination-container">
      <mat-paginator [length]="totalCount" [pageSize]="pageSize" [pageIndex]="currentPage"
        [pageSizeOptions]="[5, 10, 25, 50]" (page)="onPageChange($event)" aria-label="Select page"></mat-paginator>
    </div>
  </div>

  <ngx-ui-loader></ngx-ui-loader>
</div>