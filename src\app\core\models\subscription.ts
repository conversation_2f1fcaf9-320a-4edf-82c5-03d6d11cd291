import { ClientForRule } from "@app/shared/models/rule/ClientForRule";
import { Facture } from "./facture";
import { Licence } from "./licence";
import { AuditModel } from "./models-audit/audit-model";
import { Client } from "./rules";


export class Subscription extends AuditModel {
    DateDebut?: Date | null;
    DateFin?: Date | null;
    Status!: string;
    ClientId!: string;
    Client?: Client;
    LicenceId!: string;
    Licence?: Licence;
    Price?: number | null;
    PaymentFrequency?: string | null;
    Factures?: Facture[];
}
