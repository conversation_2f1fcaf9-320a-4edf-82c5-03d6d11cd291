<app-success></app-success>
<ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>
<div class="organisation-management-container">
  <div class="header-section">
    <div class="page-title">
      <h1 class="title">
        <i class="material-icons title-icon">people</i> Liste des Clients 
        <!-- ({{
        filteredClients.length
        }}) -->
      </h1>
    </div>
    <div class="actions">
      <button class="create-button" (click)="showClientForm()">
        <i class="material-icons action-icon">add</i> Créer Client
      </button>
    </div>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="loading-spinner"></div>
    <p>Chargement des clients...</p>
  </div>

  <div class="search-bar-container">
    <div class="search-bar">
      <input type="text" [(ngModel)]="searchTerm" placeholder="Rechercher" class="search-input"
        (keyup.enter)="filterClients()" />
      <button class="clear-btn" (click)="clearSearch()" *ngIf="searchTerm">
        <mat-icon style="font-size: 22px;">close</mat-icon>
      </button>
    </div>
    <span class="search-button" (click)="filterClients()">
      <mat-icon>search</mat-icon>
    </span>
  </div>

  <!-- Clients table -->
  <div class="table-section" *ngIf="!isLoading" style="width: 100%;">
    <!-- <div class="table-header">
      <h3>Liste des Clients ({{ filteredClients.length }})</h3>
    </div> -->

    <app-generic-table [data]="clients" [headers]="[
        'Raison Sociale',
        'Email Contact',
        'Téléphone',
        'Organisation',
        'Contact Addresse',
        'RC',
        'IF',
        'Statut'
      ]" [keys]="[
        'Name',
        'ContactEmail',
        'PhoneNumber',
        'Organisation.Nom',
        'ContactAddress',
        'RC',
        'IF',
        'ClientStatus'
      ]" [actions]="['edit', 'view', 'delete']" (actionTriggered)="handleAction($event)"></app-generic-table>
    <div class="pagination-container">
      <mat-paginator [length]="totalClients" [pageSize]="pageSize" [pageIndex]="currentPage"
        [pageSizeOptions]="[5, 10, 25, 50]" (page)="onPageChange($event)" aria-label="Select page">
      </mat-paginator>
    </div>
  </div>
</div>