// rule-form.component.ts - Updated with Summary Generation
import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { DragDropModule, CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { forkJoin } from 'rxjs';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';

// Import the services
import { TypeCapteurApiService } from '@app/core/services/administrative/typecapteur.service';
import { VariablesApiService } from '@app/core/services/administrative/variables.service';
import { RulesApiService } from '@app/core/services/administrative/rules.service';

// Import the backend models
import { TypeCapteur } from '@app/shared/models/typeCapteur';
import { Variables } from '@app/core/models/variables';
import { PayloadOption } from '../../shared/models/PayloadOption';
import { DeviceProperty } from '../../shared/models/DeviceProperty';
import { DeviceAction } from '../../shared/models/DeviceAction';
import { DeviceTypes } from '../../shared/models/DeviceTypes';
import { SensorDataCondition } from '../../shared/models/SensorDataCondition';
import { TimeCondition } from '../../shared/models/TimeCondition';
import { ConditionGroup } from '../../shared/models/ConditionGroup';
import { PublishAction } from '../../shared/models/PublishAction';
import { RuleDto } from '../../shared/models/RuleDto';

import { ConfirmationDialogComponent} from '../../components/confirmation-dialog/confirmation-dialog.component';

@Component({
  selector: 'app-rule-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    DragDropModule,
    NgxUiLoaderModule
  ],
  templateUrl: './rule-form.component.html',
  styleUrls: ['./rule-form.component.css']
})
export class RuleFormComponent implements OnInit {
  rule: RuleDto = {
    rule_name: '',
    topic_pattern: [],
    conditions: {
      operator: 'AND', // Default operator for the top-level group
      groups: [
        {
          operator: 'AND',
          conditions: []
        }
      ]
    },
    actions: [],
    schedule_config: { enabled: false },
    enabled: true,
    priority: 1
  };

  priorities = [1, 2, 3, 4, 5];
  operators = [
    { label: '=', value: '==' },
    { label: '≠', value: '!=' },
    { label: '>', value: '>' },
    { label: '<', value: '<' },
    { label: '≥', value: '>=' },
    { label: '≤', value: '<=' }
  ];
  operator = ['AND', 'OR'];
  selectedActionTypes: string[] = [];
  selectedDevices: string[] = [];

  // Backend data
  typeCapteurs: TypeCapteur[] = [];
  variables: Variables[] = [];
  
  // Processed device data structure
  deviceTypes: DeviceTypes = {
    sensors: {},
    actuators: {}
  };

  // Loading states
  isLoading = true;
  loadingError: string | null = null;
  isSaving = false;
  isGeneratingSummary = false;

  constructor(
    public dialogRef: MatDialogRef<RuleFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { rule: RuleDto | null },
    private typeCapteurService: TypeCapteurApiService,
    private variablesService: VariablesApiService,
    private rulesService: RulesApiService,
    private dialog: MatDialog,
    private ngxUiLoaderService: NgxUiLoaderService

  ) {}

  ngOnInit(): void {
    this.loadBackendData();
    // Ensure the global operator is set
    if (!this.rule.conditions.operator) {
      this.rule.conditions.operator = 'AND';
    }
  }

  // === ENHANCED DRAG & DROP METHODS ===
  
  /**
   * Handle dropping condition groups to reorder them
   * This handles moving entire condition groups within the groups array
   */
onGroupDrop(event: CdkDragDrop<ConditionGroup[]>): void {
  if (event.previousContainer === event.container) {
    if (event.previousIndex !== event.currentIndex) {
      moveItemInArray(
        this.rule.conditions.groups,
        event.previousIndex,
        event.currentIndex
      );
      this.updateTopicPattern();
    }
  }
}

  /**
   * Handle dropping conditions within a specific group
   * This handles moving conditions within a group or between groups
   */
 onConditionDrop(event: CdkDragDrop<Condition[]>, targetGroupIndex: number): void {
  if (event.previousContainer === event.container) {
    // Moving within the same group
    if (event.previousIndex !== event.currentIndex) {
      moveItemInArray(
        this.rule.conditions.groups[targetGroupIndex].conditions,
        event.previousIndex,
        event.currentIndex
      );
      this.updateTopicPattern();
    }
  } else {
    // Moving between different groups
    const sourceGroupIndex = this.findGroupIndexByContainer(event.previousContainer.data);
    if (sourceGroupIndex !== -1 && sourceGroupIndex !== targetGroupIndex) {
      transferArrayItem(
        this.rule.conditions.groups[sourceGroupIndex].conditions,
        this.rule.conditions.groups[targetGroupIndex].conditions,
        event.previousIndex,
        event.currentIndex
      );
      this.updateTopicPattern();
    }
  }
}

  /**
   * Handle dropping actions to reorder them
   */
 onActionDrop(event: CdkDragDrop<PublishAction[]>): void {
  if (event.previousContainer === event.container) {
    if (event.previousIndex !== event.currentIndex) {
      moveItemInArray(
        this.rule.actions,
        event.previousIndex,
        event.currentIndex
      );
      this.updateTopicPattern();
    }
  }
}

  /**
   * Helper method to find group index by container data
   */
  private findGroupIndexByContainer(containerData: Condition[]): number {
    for (let i = 0; i < this.rule.conditions.groups.length; i++) {
      if (this.rule.conditions.groups[i].conditions === containerData) {
        return i;
      }
    }
    return -1;
  }

  /**
   * Generate unique tracking ID for condition groups
   * Enhanced to ensure uniqueness and stability
   */
  trackByGroupIndex(index: number, group: ConditionGroup): string {
    const conditionCount = group.conditions.length;
    const hasTimeCondition = group.conditions.some(c => c.type === 'time');
    const hasSensorCondition = group.conditions.some(c => c.type === 'payload');
    
    return `group-${index}-${group.operator}-${conditionCount}-${hasTimeCondition ? 'time' : ''}-${hasSensorCondition ? 'sensor' : ''}`;
  }

  /**
   * Generate unique tracking ID for conditions
   * Enhanced to handle different condition types properly
   */
  trackByConditionIndex(index: number, condition: Condition): string {
    if (condition.type === 'payload') {
      const sensorCondition = condition as SensorDataCondition;
      return `condition-${index}-sensor-${sensorCondition.device || 'no-device'}-${sensorCondition.key || 'no-key'}-${sensorCondition.operator || 'no-op'}-${sensorCondition.value || 'no-value'}`;
    } else if (condition.type === 'time') {
      const timeCondition = condition as TimeCondition;
      return `condition-${index}-time-${timeCondition.start_time || 'no-start'}-${timeCondition.end_time || 'no-end'}`;
    }
    return `condition-${index}-unknown-${(condition as any)?.type ?? 'unknown'}`;
  }

  /**
   * Generate unique tracking ID for actions
   * Enhanced to handle different action types properly
   */
  trackByActionIndex(index: number, action: PublishAction): string {
    if (action.type === 'log') {
      return `action-${index}-log-${action.message || 'no-message'}`;
    } else {
      const payloadKeys = action.payload ? Object.keys(action.payload).join('-') : 'no-payload';
      return `action-${index}-${action.type || 'no-type'}-${action.topic || 'no-topic'}-${payloadKeys}`;
    }
  }

  /**
   * Predicate to determine which condition groups are connected
   * This helps CDK understand the drag and drop boundaries
   */
  groupConnectedTo(index: number): string[] {
    const connectedGroups: string[] = [];
    for (let i = 0; i < this.rule.conditions.groups.length; i++) {
      if (i !== index) {
        connectedGroups.push(`condition-group-${i}`);
      }
    }
    return connectedGroups;
  }

  /**
   * Get the CDK drop list ID for a condition group
   */
  getConditionGroupId(index: number): string {
    return `condition-group-${index}`;
  }

  // === CONFIRMATION DIALOG METHODS ===

  /**
   * Show confirmation dialog before canceling/closing
   */
 onCancel(): void {
  // Don't allow canceling during critical operations
  if (this.isSaving) {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Sauvegarde en cours',
        message: 'Une sauvegarde est en cours. Voulez-vous vraiment annuler ? Cela pourrait corrompre les données.',
        icon: 'warning'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.forceCancel();
      }
    });
    return;
  }

  // Allow canceling during summary generation
  if (this.isGeneratingSummary) {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Génération IA en cours',
        message: 'La génération du résumé IA est en cours. Voulez-vous vraiment fermer ? Le résumé ne sera pas généré.',
        icon: 'warning'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.dialogRef.close();
      }
    });
    return;
  }

  // Only show confirmation if there are unsaved changes
  if (this.hasUnsavedChanges()) {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Annuler les modifications',
        message: 'Vous avez des modifications non sauvegardées. Êtes-vous sûr de vouloir fermer sans sauvegarder ?',
        icon: 'warning'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.dialogRef.close();
      }
    });
  } else {
    // No changes: close immediately
    this.dialogRef.close();
  }
}
private forceCancel(): void {
  // Reset all loading states
  this.isSaving = false;
  this.isGeneratingSummary = false;
  this.dialogRef.close({ action: 'cancelled', forced: true });
}

  /**
   * Show confirmation dialog before removing condition group
   */
  removeConditionGroup(index: number): void {
    const group = this.rule.conditions.groups[index];
    const hasConditions = group.conditions.length > 0;
    
    if (hasConditions) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: 'Supprimer le groupe de conditions',
          message: `Ce groupe contient ${group.conditions.length} condition(s). Êtes-vous sûr de vouloir le supprimer définitivement ?`,
          icon: 'delete_forever'
        },
        disableClose: true,
        panelClass: 'confirmation-dialog-panel'
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.rule.conditions.groups.splice(index, 1);
          this.updateTopicPattern();
        }
      });
    } else {
      this.rule.conditions.groups.splice(index, 1);
      this.updateTopicPattern();
    }
  }

  /**
   * Show confirmation dialog before removing condition
   */
  removeCondition(groupIndex: number, conditionIndex: number): void {
    const condition = this.rule.conditions.groups[groupIndex].conditions[conditionIndex];
    let conditionDescription = '';
    
    if (condition.type === 'payload') {
      const sensorCondition = condition as SensorDataCondition;
      conditionDescription = sensorCondition.device ? 
        `la condition sur ${sensorCondition.device}` : 
        'cette condition de capteur';
    } else if (condition.type === 'time') {
      conditionDescription = 'cette condition horaire';
    } else {
      conditionDescription = 'cette condition';
    }

    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Supprimer la condition',
        message: `Êtes-vous sûr de vouloir supprimer ${conditionDescription} ?`,
        icon: 'delete'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.rule.conditions.groups[groupIndex].conditions.splice(conditionIndex, 1);
        this.updateTopicPattern();
      }
    });
  }

  /**
   * Show confirmation dialog before removing action
   */
  removeAction(index: number): void {
    const action = this.rule.actions[index];
    let actionDescription = '';
    
    if (action.type === 'log') {
      actionDescription = 'cette action de log';
    } else if (action.topic) {
      const deviceNameKey = this.getDeviceNameKeyByTopic(action.topic, 'actuators');
      if (deviceNameKey) {
        const device = this.deviceTypes.actuators[deviceNameKey];
        actionDescription = `l'action sur ${device.DisplayName || device.device_name}`;
      } else {
        actionDescription = 'cette action';
      }
    } else {
      actionDescription = 'cette action';
    }

    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Supprimer l\'action',
        message: `Êtes-vous sûr de vouloir supprimer ${actionDescription} ?`,
        icon: 'delete'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.rule.actions.splice(index, 1);
        this.updateTopicPattern();
      }
    });
  }

  /**
   * Show confirmation dialog before saving and handle summary generation
   */
  onSave(): void {
  if (!this.rule.rule_name.trim() || this.isBusy) {
    return;
  }

  const cleanRule = this.generateCleanRule();
  const hasValidConditions = cleanRule.conditions.groups.some(group => group.conditions.length > 0);
  const hasValidActions = cleanRule.actions.length > 0;

  if (!hasValidConditions || !hasValidActions) {
    const missingParts = [];
    if (!hasValidConditions) missingParts.push('conditions valides');
    if (!hasValidActions) missingParts.push('actions valides');
    
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Règle incomplète',
        message: `Cette règle n'a pas de ${missingParts.join(' et ')}. Voulez-vous vraiment la sauvegarder ?`,
        icon: 'warning'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.performSaveWithSummary();
      }
    });
  } else {
    const isEdit = this.data.rule && ('isEdit' in this.data.rule || 'id' in this.data.rule);
    
    if (isEdit) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: 'Mettre à jour la règle',
          message: `Êtes-vous sûr de vouloir mettre à jour la règle "${this.rule.rule_name}" ?`,
          icon: 'edit'
        },
        disableClose: true,
        panelClass: 'confirmation-dialog-panel'
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.performSaveWithSummary();
        }
      });
    } else {
      this.performSaveWithSummary();
    }
  }
}
  /**
   * Generate summary and then save the rule
   */
  private performSaveWithSummary(): void {
  // Set initial loading state
  this.isSaving = true;
  this.isGeneratingSummary = false;

  const cleanRule = this.generateCleanRule();
  
  // Generate summary first
  this.isGeneratingSummary = true;
  this.rulesService.summarizeRule(cleanRule).subscribe({
    next: (summaryResponse: any) => {
      this.isGeneratingSummary = false;
      
      // Extract the actual summary text
      let summaryText: string;
      if (summaryResponse && typeof summaryResponse === 'object' && summaryResponse.summary) {
        summaryText = summaryResponse.summary;
      } else {
        summaryText = String(summaryResponse);
      }
      
      console.log('Summary generated successfully:', summaryText);
      
      // Now save the rule with the summary
      this.saveRuleWithSummary(cleanRule, summaryText);
    },
    error: (error) => {
      this.isGeneratingSummary = false;
      console.error('Error generating summary:', error);
      
      // Save without summary if generation fails
      this.saveRuleWithSummary(cleanRule, null);
    }
  });
}

  /**
   * Save the rule with or without summary
   */
  private saveRuleWithSummary(cleanRule: RuleDto, summary: string | null): void {
  // Ensure we're still in saving state
  if (!this.isSaving) {
    console.warn('Save operation was cancelled or interrupted');
    return;
  }

  const ruleToSave: { RawData: string; summary?: string; id?: string } = {
    RawData: JSON.stringify(cleanRule)
  };

  // Add summary if available
  if (summary) {
    ruleToSave.summary = summary;
  }

  const isEdit = this.data.rule && ('isEdit' in this.data.rule || 'id' in this.data.rule);
  
  if (isEdit && this.data!.rule!.id != null) {
    ruleToSave.id = this.data!.rule!.id;
    this.rulesService.update(ruleToSave).subscribe({
      next: (savedRule) => {
        this.handleSaveSuccess(savedRule, cleanRule, summary, 'updated');
      },
      error: (error) => {
        this.handleSaveError(error, 'Erreur lors de la mise à jour de la règle. Veuillez réessayer.');
      }
    });
  } else {
    this.rulesService.create(ruleToSave).subscribe({
      next: (savedRule) => {
        this.handleSaveSuccess(savedRule, cleanRule, summary, 'created');
      },
      error: (error) => {
        this.handleSaveError(error, 'Erreur lors de la création de la règle. Veuillez réessayer.');
      }
    });
  }
}
private handleSaveSuccess(savedRule: any, cleanRule: RuleDto, summary: string | null, action: 'created' | 'updated'): void {
  this.isSaving = false;
  
  const actionText = action === 'created' ? 'créée' : 'mise à jour';
  console.log(`Rule ${actionText} successfully:`, savedRule);
  
  this.dialogRef.close({ 
    action: 'saved', 
    rule: {
      ...savedRule,
      ...cleanRule,
      summary: summary
    },
    operation: action
  });
}
private handleSaveError(error: any, message: string): void {
  this.isSaving = false;
  this.isGeneratingSummary = false;
  
  console.error('Save error:', error);
  
  // Show detailed error if available
  let errorMessage = message;
  if (error?.error?.message) {
    errorMessage += `\n\nDétails: ${error.error.message}`;
  } else if (error?.message) {
    errorMessage += `\n\nDétails: ${error.message}`;
  }
  
  this.showErrorDialog(errorMessage);
}

  /**
   * Check if there are unsaved changes
   */
  private hasUnsavedChanges(): boolean {
    if (!this.data.rule) {
      return this.rule.rule_name.trim() !== '' ||
             this.rule.conditions.groups.some(group => 
               group.conditions.some(condition => {
                 if (condition.type === 'payload') {
                   const sc = condition as SensorDataCondition;
                   return sc.device || sc.key || sc.value;
                 } else if (condition.type === 'time') {
                   const tc = condition as TimeCondition;
                   return tc.start_time || tc.end_time;
                 }
                 return false;
               })
             ) ||
             this.rule.actions.some(action => 
               action.topic || action.type || action.message ||
               (action.payload && Object.keys(action.payload).length > 0)
             );
    }
    
    const originalRule = this.data.rule;
    return JSON.stringify(this.rule) !== JSON.stringify(originalRule);
  }

  /**
   * Show error dialog
   */
  private showErrorDialog(message: string, showRetry: boolean = false): void {
  const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
    data: {
      title: 'Erreur',
      message: message,
      icon: 'error',
      showCancel: showRetry,
      confirmText: showRetry ? 'Réessayer' : 'OK',
      cancelText: 'Fermer'
    },
    disableClose: true,
    panelClass: 'confirmation-dialog-panel'
  });

  if (showRetry) {
    dialogRef.afterClosed().subscribe(result => {
      if (result && this.loadingError) {
        this.retryLoadData();
      }
    });
  }
}

  // === BACKEND DATA LOADING ===

  private loadBackendData(): void {
    this.isLoading = true;
    this.loadingError = null;

    forkJoin({
      typeCapteurs: this.typeCapteurService.getAll(),
      variables: this.variablesService.getAll()
    }).subscribe({
      next: (data) => {
        this.typeCapteurs = data.typeCapteurs;
        this.variables = data.variables;
        this.processBackendData();
        this.initializeRule();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading backend data:', error);
        this.loadingError = 'Erreur lors du chargement des données. Veuillez réessayer.';
        this.isLoading = false;
        this.initializeRule();
      }
    });
  }

  private processBackendData(): void {
    this.deviceTypes = { sensors: {}, actuators: {} };

    this.typeCapteurs.forEach(typeCapteur => {
      const relatedVariables = this.variables.filter(v => v.IdTypeCapteur === typeCapteur.Id);
      
      const deviceCategory: 'sensors' | 'actuators' = 
        typeCapteur.DeviceType?.toLowerCase() === 'actuator' ? 'actuators' : 'sensors';
      
      const deviceKey = this.normalizeDeviceKey(typeCapteur.Nom);
      
      if (deviceCategory === 'sensors') {
        this.deviceTypes.sensors[deviceKey] = {
          topic: typeCapteur.Topic,
          device_name: typeCapteur.Nom,
          DisplayName: typeCapteur.DisplayName || typeCapteur.Nom,
          properties: relatedVariables.map(variable => ({
            key: variable.Key,
            type: this.mapVariableType(variable.Type),
            values: this.parseVariableValues(variable.Actions)
          }))
        };
      } else {
        this.deviceTypes.actuators[deviceKey] = {
          topic: typeCapteur.Topic,
          device_name: typeCapteur.Nom,
          DisplayName: typeCapteur.DisplayName || typeCapteur.Nom,
          actions: relatedVariables.map(variable => ({
            type: variable.Key,
            payload: {},
            options: this.parseActionOptions(variable.Actions)
          }))
        };
      }
    });
  }

  private normalizeDeviceKey(name: string): string {
    return name.toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^a-z0-9_]/g, '')
      .replace(/^sensor_|^actuator_/, '');
  }

  private mapVariableType(backendType: string): string {
    const typeMapping: { [key: string]: string } = {
      'String': 'string',
      'Integer': 'number',
      'Float': 'number',
      'Double': 'number',
      'Boolean': 'boolean',
      'Bool': 'boolean'
    };
    return typeMapping[backendType] || 'string';
  }

  private parseVariableValues(actions: string[] | string | null): (string | number | boolean)[] {
  if (!actions) return [];
  
  // Handle case where actions is already an array
  if (Array.isArray(actions)) {
    return actions.map(item => {
      // Convert string representations of numbers and booleans to actual types
      if (item === 'true') return true;
      if (item === 'false') return false;
      
      // Try to convert to number if it's a valid number string
      const numValue = Number(item);
      if (!isNaN(numValue) && isFinite(numValue)) {
        return numValue;
      }
      
      // Return as string for everything else
      return String(item);
    });
  }
  
  // Handle case where actions is a JSON string
  if (typeof actions === 'string') {
    try {
      const parsed = JSON.parse(actions);
      if (Array.isArray(parsed)) {
        return parsed.map(item => {
          // Convert string representations of numbers and booleans to actual types
          if (item === 'true') return true;
          if (item === 'false') return false;
          
          // Try to convert to number if it's a valid number string
          const numValue = Number(item);
          if (!isNaN(numValue) && isFinite(numValue)) {
            return numValue;
          }
          
          // Return as string for everything else
          return String(item);
        });
      } else {
        // If it's not an array, treat it as a single value
        return [String(parsed)];
      }
    } catch (parseError) {
      console.warn('Failed to parse actions as JSON:', actions, parseError);
      
      // Fallback: split by common delimiters
      const values = actions.split(/[,;|]/).map(v => v.trim()).filter(v => v);
      return values.map(item => {
        if (item === 'true') return true;
        if (item === 'false') return false;
        
        const numValue = Number(item);
        if (!isNaN(numValue) && isFinite(numValue)) {
          return numValue;
        }
        
        return item;
      });
    }
  }
  
  return [];
}

  private parseActionOptions(actions: string[] | string | null): PayloadOption[] {
  if (!actions) return [];
  
  // Handle case where actions is already an array
  if (Array.isArray(actions)) {
    return actions.map((item: any) => {
      if (typeof item === 'object' && item !== null) {
        return {
          display: item.display || item.label || item.name || String(item.value || item),
          value: String(item.value || item)
        };
      } else {
        const stringValue = String(item);
        return {
          display: stringValue,
          value: stringValue
        };
      }
    });
  }
  
  // Handle case where actions is a JSON string
  if (typeof actions === 'string') {
    try {
      const parsed = JSON.parse(actions);
      if (Array.isArray(parsed)) {
        return parsed.map((item: any) => {
          if (typeof item === 'object' && item !== null) {
            return {
              display: item.display || item.label || item.name || String(item.value || item),
              value: String(item.value || item)
            };
          } else {
            const stringValue = String(item);
            return {
              display: stringValue,
              value: stringValue
            };
          }
        });
      } else {
        // Single value
        const stringValue = String(parsed);
        return [{
          display: stringValue,
          value: stringValue
        }];
      }
    } catch (parseError) {
      console.warn('Failed to parse actions as JSON for actuator:', actions, parseError);
      
      // Fallback: split by common delimiters
      const values = actions.split(/[,;|]/).map(v => v.trim()).filter(v => v);
      return values.map(value => ({
        display: value,
        value: value
      }));
    }
  }
  
  return [];
}


  private initializeRule(): void {
    if (this.data.rule) {
      if (typeof this.data.rule === 'object' && 'RawData' in this.data.rule) {
        try {
          this.rule = JSON.parse((this.data.rule as any).RawData);
        } catch (error) {
          console.error('Error parsing rule RawData:', error);
          this.rule = JSON.parse(JSON.stringify(this.data.rule));
        }
      } else {
        this.rule = JSON.parse(JSON.stringify(this.data.rule));
      }
    } else {
      if (this.rule.conditions.groups.length === 0) {
        this.addConditionGroup();
      }
      if (this.rule.conditions.groups[0].conditions.length === 0) {
        this.addCondition(0);
      }
    }
    // Ensure the global operator is set
    if (!this.rule.conditions.operator) {
      this.rule.conditions.operator = 'AND';
    }
    this.updateTopicPattern();
  }

  // === CONDITION MANAGEMENT ===

  addConditionGroup(): void {
    this.rule.conditions.groups.push({
      operator: 'AND',
      conditions: [{ type: 'payload', device: '', key: '', operator: '==', value: '' } as SensorDataCondition]
    });
    this.updateTopicPattern();
  }

  addCondition(groupIndex: number): void {
    this.rule.conditions.groups[groupIndex].conditions.push({
      type: 'payload',
      device: '',
      key: '',
      operator: '==',
      value: ''
    } as SensorDataCondition);
    this.updateTopicPattern();
  }

  addTimeCondition(groupIndex: number): void {
    this.rule.conditions.groups[groupIndex].conditions.push({
      type: 'time',
      start_time: '',
      end_time: ''
    } as TimeCondition);
    this.updateTopicPattern();
  }

  onConditionDeviceChange(condition: SensorDataCondition, deviceType: 'sensors' | 'actuators', deviceName: string): void {
    condition.device = deviceName;
    condition.key = '';
    condition.value = '';
    this.updateTopicPattern();
  }

  onConditionKeyChange(condition: SensorDataCondition): void {
    condition.value = '';
    this.updateTopicPattern();
  }

  onConditionValueChange(condition: SensorDataCondition): void {
    this.updateTopicPattern();
  }

  onConditionOperatorChange(condition: SensorDataCondition): void {
    this.updateTopicPattern();
  }

  onConditionTypeChange(condition: Condition): void {
    if (condition.type === 'payload') {
      (condition as SensorDataCondition).device = '';
      (condition as SensorDataCondition).key = '';
      (condition as SensorDataCondition).operator = '==';
      (condition as SensorDataCondition).value = '';
      delete (condition as unknown as Partial<TimeCondition>).start_time;
      delete (condition as unknown as Partial<TimeCondition>).end_time;
    } else if (condition.type === 'time') {
      (condition as TimeCondition).start_time = '';
      (condition as TimeCondition).end_time = '';
      delete (condition as unknown as Partial<SensorDataCondition>).device;
      delete (condition as unknown as Partial<SensorDataCondition>).key;
      delete (condition as unknown as Partial<SensorDataCondition>).operator;
      delete (condition as unknown as Partial<SensorDataCondition>).value;
    }
    this.updateTopicPattern();
  }

  onTimeConditionChange(condition: TimeCondition): void {
    this.updateTopicPattern();
  }

  // === ACTION MANAGEMENT ===

  addAction(): void {
    this.rule.actions.push({
      type: '',
      payload: {},
      topic: ''
    } as PublishAction);
    this.updateTopicPattern();
  }

  addLogAction(): void {
    this.rule.actions.push({
      type: 'log',
      topic: '',
      payload: {},
      message: ''
    } as PublishAction);
    this.updateTopicPattern();
  }

  onActionDeviceChange(action: PublishAction, selectedTopic: string): void {
    console.log('onActionDeviceChange called with:', selectedTopic);
    
    let deviceNameKey: string | undefined;
    for (const key in this.deviceTypes.actuators) {
      if (this.deviceTypes.actuators[key].topic === selectedTopic) {
        deviceNameKey = key;
        break;
      }
    }

    console.log('Found device key:', deviceNameKey);

    if (deviceNameKey && selectedTopic) {
      const selectedDevice = this.deviceTypes.actuators[deviceNameKey];
      if (selectedDevice) {
        action.topic = selectedDevice.topic;
        action.type = '';
        action.payload = {};
        console.log('Updated action:', action);
        this.updateTopicPattern();
      }
    } else {
      action.topic = '';
      action.type = '';
      action.payload = {};
      this.updateTopicPattern();
    }
  }

  onActionTypeChange(action: PublishAction, selectedActionType: string): void {
    console.log('onActionTypeChange called with:', selectedActionType);
    
    action.type = selectedActionType;
    
    if (selectedActionType && selectedActionType !== 'log') {
      action.payload = action.payload || {};
      if (!(selectedActionType in action.payload)) {
        action.payload[selectedActionType] = '';
      }
    } else if (selectedActionType === 'log') {
      action.payload = {};
      if (!action.message) {
        action.message = '';
      }
    } else {
      action.payload = {};
    }
    
    console.log('Updated action after type change:', action);
    this.updateTopicPattern();
  }

  onActionPayloadChange(action: PublishAction): void {
    this.updateTopicPattern();
  }

  onActionMessageChange(action: PublishAction): void {
    this.updateTopicPattern();
  }

  // === HELPER FUNCTIONS FOR DYNAMIC DROPDOWNS ===

  getSensorDeviceNames(): string[] {
    return Object.keys(this.deviceTypes.sensors);
  }

  getActuatorDeviceNames(): string[] {
    return Object.keys(this.deviceTypes.actuators);
  }

  getPropertiesForDevice(deviceType: 'sensors' | 'actuators', deviceName: string): DeviceProperty[] {
    if (!deviceName) return [];
    const device = this.deviceTypes[deviceType][deviceName];
    return device ? device.properties || [] : [];
  }

  getActionsForDevice(deviceType: 'sensors' | 'actuators', deviceName: string): DeviceAction[] {
    if (!deviceName) return [];
    const device = this.deviceTypes[deviceType][deviceName];
    return device ? device.actions || [] : [];
  }

  getDeviceNameKeyByTopic(topic: string, deviceType: 'sensors' | 'actuators'): string | undefined {
    if (!topic) return undefined;
    for (const key in this.deviceTypes[deviceType]) {
      if (this.deviceTypes[deviceType][key].topic === topic) {
        return key;
      }
    }
    return undefined;
  }

  getSelectedProperty(condition: SensorDataCondition): DeviceProperty | undefined {
    if (!condition.device || !condition.key) {
      return undefined;
    }
    const device = this.deviceTypes.sensors[condition.device];
    if (device && device.properties) {
      return device.properties.find(p => p.key === condition.key);
    }
    return undefined;
  }

  getSelectedActionType(action: PublishAction): DeviceAction | undefined {
    if (!action.topic || !action.type) {
      return undefined;
    }
    const deviceNameKey = this.getDeviceNameKeyByTopic(action.topic, 'actuators');
    if (deviceNameKey) {
      const selectedActuator = this.deviceTypes.actuators[deviceNameKey];
      if (selectedActuator && selectedActuator.actions) {
        return selectedActuator.actions.find(a => a.type === action.type);
      }
    }
    return undefined;
  }

  // === JSON GENERATION AND DOWNLOAD ===

  generateJSON(): string {
    try {
      const ruleToExport = JSON.parse(JSON.stringify(this.rule));

      ruleToExport.conditions.groups.forEach((group: ConditionGroup) => {
        group.conditions = group.conditions.filter((condition: Condition) => {
          if (condition.type === 'payload') {
            const sensorCondition = condition as SensorDataCondition;
            return sensorCondition.device && sensorCondition.key && sensorCondition.operator && 
                   sensorCondition.value !== null && sensorCondition.value !== undefined && sensorCondition.value !== '';
          } else if (condition.type === 'time') {
            const timeCondition = condition as TimeCondition;
            return timeCondition.start_time && timeCondition.end_time;
          }
          return false;
        });
      });
      
      ruleToExport.conditions.groups = ruleToExport.conditions.groups.filter((group: ConditionGroup) => group.conditions.length > 0);

      ruleToExport.actions = ruleToExport.actions.filter((action: PublishAction) => {
        if (action.type === 'log') {
          return action.message && action.message.trim();
        }
        
        if (action.type && action.type !== 'log') {
          const hasBasicInfo = action.topic && action.type;
          
          let hasPayload = false;
          if (action.payload && typeof action.payload === 'object') {
            hasPayload = Object.keys(action.payload).some(key => {
              const value = action.payload[key];
              return value !== null && value !== undefined && value !== '';
            });
          }
          
          return hasBasicInfo && hasPayload;
        }
        
        return false;
      });

      return JSON.stringify(ruleToExport, null, 2);
    } catch (error) {
      console.error('Error generating JSON:', error);
      return '{\n  "error": "Unable to generate JSON preview"\n}';
    }
  }

  downloadJSON(): void {
  if (this.isBusy) {
    this.showErrorDialog('Impossible de télécharger pendant une opération de sauvegarde. Veuillez patienter.');
    return;
  }
  
  try {
    const filename = `${this.rule.rule_name || 'rule'}.json`;
    const json = this.generateJSON();
    const blob = new Blob([json], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading JSON:', error);
    this.showErrorDialog('Erreur lors du téléchargement du fichier JSON.');
  }
}

get shouldDisableInteractions(): boolean {
  return this.isBusy || this.isLoading;
}
get loadingStateDescription(): string {
  if (this.isLoading) {
    return 'Chargement des données...';
  }
  if (this.isSaving) {
    return 'Sauvegarde en cours...';
  }
  if (this.isGeneratingSummary) {
    return 'Génération IA en cours...';
  }
  return '';
}
  /**
   * Generates a clean rule object with only complete conditions and actions
   */
  private generateCleanRule(): RuleDto {
    const cleanRule = JSON.parse(JSON.stringify(this.rule));

    cleanRule.conditions.groups.forEach((group: ConditionGroup) => {
      group.conditions = group.conditions.filter((condition: Condition) => {
        if (condition.type === 'payload') {
          const sensorCondition = condition as SensorDataCondition;
          return sensorCondition.device && sensorCondition.key && sensorCondition.operator && 
                 sensorCondition.value !== null && sensorCondition.value !== undefined && sensorCondition.value !== '';
        } else if (condition.type === 'time') {
          const timeCondition = condition as TimeCondition;
          return timeCondition.start_time && timeCondition.end_time;
        }
        return false;
      });
    });
    
    cleanRule.conditions.groups = cleanRule.conditions.groups.filter((group: ConditionGroup) => group.conditions.length > 0);

    cleanRule.actions = cleanRule.actions.filter((action: PublishAction) => {
      if (action.type === 'log') {
        return action.message && action.message.trim();
      }
      
      if (action.type && action.type !== 'log') {
        const hasBasicInfo = action.topic && action.type;
        
        let hasPayload = false;
        if (action.payload && typeof action.payload === 'object') {
          hasPayload = Object.keys(action.payload).some(key => {
            const value = action.payload[key];
            return value !== null && value !== undefined && value !== '';
          });
        }
        
        return hasBasicInfo && hasPayload;
      }
      
      return false;
    });

    return cleanRule;
  }

  // === TOPIC PATTERN UPDATE ===
  
  updateTopicPattern(): void {
    const topics: string[] = [];

    this.rule.conditions.groups.forEach(group => {
      group.conditions.forEach(condition => {
        if (condition.type === 'payload') {
          const sensorCondition = condition as SensorDataCondition;
          if (sensorCondition.device) {
            const device = this.deviceTypes.sensors[sensorCondition.device];
            if (device && device.topic && !topics.includes(device.topic)) {
              topics.push(device.topic);
            }
          }
        }
      });
    });


    this.rule.topic_pattern = topics;
  }

  // === UTILITY METHODS ===

  capitalizeFirst(str: string): string {
    if (!str) return str;
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

 retryLoadData(): void {
  if (this.isBusy) {
    console.warn('Cannot retry while another operation is in progress');
    return;
  }
  
  this.loadingError = null;
  this.loadBackendData();
}


  // === GETTER METHODS FOR UI STATE ===

  /**
   * Check if currently saving or generating summary
   */
  get isBusy(): boolean {
    return this.isSaving || this.isGeneratingSummary;
  }

  /**
   * Get the current operation status message
   */
  get statusMessage(): string {
  if (this.isGeneratingSummary && this.isSaving) {
    return 'Sauvegarde terminée, génération du résumé IA...';
  }
  if (this.isGeneratingSummary) {
    return 'Génération du résumé IA en cours...';
  }
  if (this.isSaving) {
    return 'Sauvegarde de la règle en cours...';
  }
  return '';
}
get operationProgress(): number {
  if (this.isSaving && !this.isGeneratingSummary) {
    return 50; // Saving phase
  }
  if (this.isGeneratingSummary) {
    return 80; // Summary generation phase
  }
  if (!this.isSaving && !this.isGeneratingSummary) {
    return 100; // Complete
  }
  return 0; // Not started
}

}

export type Condition = SensorDataCondition | TimeCondition;