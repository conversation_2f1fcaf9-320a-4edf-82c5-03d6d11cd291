import { Component, AfterViewInit } from '@angular/core';
import * as fabric from 'fabric';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { InstalationService} from '@app/core/services/instalation.service';   
@Component({
  selector: 'app-plan-editor',
   imports: [ CommonModule,FormsModule],
  templateUrl: './plan-editor.component.html',
  styleUrls: ['./plan-editor.component.css']
})
export class PlanEditorComponent implements AfterViewInit {
  canvas!: fabric.Canvas;
 
  canvasInitialized = false;
  selectedTool = '';
  planType = 'local'; // 'local' ou 'site'
  isGridVisible = true;
  
  // Palette de couleurs
  selectedColor = '#3B82F6';
  selectedStrokeColor = '#374151';
  selectedStrokeWidth = 2;
  constructor(private installService: InstalationService) {}
   ngOnInit() {
      this.installService.getClients().subscribe(data => {
        this.clients = data;
        console.log(this.clients)
      });
    }
  colorPalette = [
    { name: 'Bleu', color: '#3B82F6' },
    { name: 'Vert', color: '#059669' },
    { name: 'Rouge', color: '#DC2626' },
    { name: 'Orange', color: '#D97706' },
    { name: 'Violet', color: '#7C3AED' },
    { name: 'Rose', color: '#DB2777' },
    { name: 'Gris', color: '#6B7280' },
    { name: 'Noir', color: '#111827' },
    { name: 'Marron', color: '#92400E' },
    { name: 'Cyan', color: '#0891B2' },
    { name: 'Lime', color: '#65A30D' },
    { name: 'Indigo', color: '#4338CA' }
  ];
  
  strokeColors = [
    { name: 'Gris foncé', color: '#374151' },
    { name: 'Noir', color: '#111827' },
    { name: 'Blanc', color: '#FFFFFF' },
    { name: 'Bleu', color: '#3B82F6' },
    { name: 'Rouge', color: '#DC2626' }
  ];

  // Éléments de structure
  elements = [
    { 
      type: 'capteur', 
      name: 'Capteur', 
      icon: '📡', 
      description: 'Capteur IoT',
      color: '#3B82F6',
      category: 'technique'
    },
    { 
      type: 'mur', 
      name: 'Mur', 
      icon: '🧱', 
      description: 'Mur / Cloison',
      color: '#6B7280',
      category: 'structure'
    },
    { 
      type: 'porte', 
      name: 'Porte', 
      icon: '🚪', 
      description: 'Porte / Ouverture',
      color: '#92400E',
      category: 'structure'
    },
    { 
      type: 'fenetre', 
      name: 'Fenêtre', 
      icon: '🪟', 
      description: 'Fenêtre',
      color: '#0891B2',
      category: 'structure'
    },
    { 
      type: 'escalier', 
      name: 'Escalier', 
      icon: '🪜', 
      description: 'Escalier',
      color: '#7C2D12',
      category: 'structure'
    },
    { 
      type: 'zone', 
      name: 'Zone', 
      icon: '📦', 
      description: 'Zone / Pièce',
      color: '#059669',
      category: 'structure'
    }
  ];

  // Éléments de mobilier avec SVG
  mobilierElements = [
    { 
      type: 'bureau', 
      name: 'Bureau', 
      icon: '🖥️', 
      description: 'Bureau de travail',
      color: '#8B4513',
      useSVG: true
    },
    { 
      type: 'table', 
      name: 'Table', 
      icon: '🪑', 
      description: 'Table de réunion',
      color: '#CD853F',
      useSVG: true
    },
    { 
      type: 'chaise', 
      name: 'Chaise', 
      icon: '🪑', 
      description: 'Chaise de bureau',
      color: '#2F4F4F',
      useSVG: true
    },
    { 
      type: 'armoire', 
      name: 'Armoire', 
      icon: '🗄️', 
      description: 'Armoire / Rangement',
      color: '#556B2F',
      useSVG: true
    },
    { 
      type: 'canape', 
      name: 'Canapé', 
      icon: '🛋️', 
      description: 'Canapé / Sofa',
      color: '#4682B4',
      useSVG: true
    },
    { 
      type: 'lit', 
      name: 'Lit', 
      icon: '🛏️', 
      description: 'Lit',
      color: '#BC8F8F',
      useSVG: true
    },
    { 
      type: 'evier', 
      name: 'Évier', 
      icon: '🚿', 
      description: 'Évier / Lavabo',
      color: '#20B2AA',
      useSVG: true
    },
    { 
      type: 'frigo', 
      name: 'Réfrigérateur', 
      icon: '🧊', 
      description: 'Réfrigérateur',
      color: '#F0F8FF',
      useSVG: true
    },
    { 
      type: 'cuisiniere', 
      name: 'Cuisinière', 
      icon: '🔥', 
      description: 'Cuisinière / Plaque',
      color: '#FF6347',
      useSVG: true
    },
    { 
      type: 'toilette', 
      name: 'Toilette', 
      icon: '🚽', 
      description: 'WC / Toilette',
      color: '#E6E6FA',
      useSVG: true
    },
    { 
      type: 'douche', 
      name: 'Douche', 
      icon: '🚿', 
      description: 'Cabine de douche',
      color: '#B0E0E6',
      useSVG: true
    },
    { 
      type: 'plante', 
      name: 'Plante', 
      icon: '🪴', 
      description: 'Plante décorative',
      color: '#228B22',
      useSVG: true
    }
  ];

  ngAfterViewInit(): void {
    this.initializeCanvas();
  }

  initializeCanvas(): void {
    this.canvas = new fabric.Canvas('canvas', {
      backgroundColor: '#FAFAFA',
      width: 1000,
      height: 700,
      selection: true
    });
    
    this.addGridPattern();
    this.canvasInitialized = true;
    
    // Événements du canvas
    this.canvas.on('selection:created', () => this.updateToolbar());
    this.canvas.on('selection:cleared', () => this.updateToolbar());
  }

  addGridPattern(): void {
    if (!this.isGridVisible) return;
    
    const gridSize = 20;
    const patternCanvas = document.createElement('canvas');
    patternCanvas.width = gridSize;
    patternCanvas.height = gridSize;
    const patternCtx = patternCanvas.getContext('2d')!;
    
    patternCtx.strokeStyle = '#E5E7EB';
    patternCtx.lineWidth = 1;
    patternCtx.beginPath();
    patternCtx.moveTo(0, gridSize);
    patternCtx.lineTo(gridSize, gridSize);
    patternCtx.lineTo(gridSize, 0);
    patternCtx.stroke();
    
    const pattern = new fabric.Pattern({
      source: patternCanvas,
      repeat: 'repeat'
    });
    
    this.canvas.backgroundColor = pattern;
    this.canvas.renderAll();
  }

  toggleGrid(): void {
    this.isGridVisible = !this.isGridVisible;
    if (this.isGridVisible) {
      this.addGridPattern();
    } else {
      this.canvas.backgroundColor = '#FAFAFA';
      this.canvas.renderAll();
    }
  }

  selectTool(elementType: string): void {
    this.selectedTool = elementType;
    this.canvas.defaultCursor = 'crosshair';
    
    // Désélectionner tous les objets
    this.canvas.discardActiveObject();
    this.canvas.renderAll();
  }

  getSelectedToolName(): string {
    const allElements = [...this.elements, ...this.mobilierElements];
    const selectedElement = allElements.find(e => e.type === this.selectedTool);
    return selectedElement ? selectedElement.name : '';
  }

  addShape(type: string): void {
    if (!this.canvasInitialized) return;

    const left = Math.random() * (this.canvas.getWidth() - 150) + 50;
    const top = Math.random() * (this.canvas.getHeight() - 100) + 50;

    // Vérifier si c'est un élément de mobilier avec SVG
    const mobilierElement = this.mobilierElements.find(el => el.type === type);
    if (mobilierElement && mobilierElement.useSVG) {
      this.createSVGShape(type, left, top);
      return;
    }

    // Créer les formes basiques pour les éléments de structure
    this.createBasicShape(type, left, top);
  }

  private createSVGShape(type: string, left: number, top: number): void {
    const svgString = this.getSVGDefinition(type);
    if (!svgString) {
      this.createBasicShape(type, left, top);
      return;
    }

    fabric.loadSVGFromString(svgString).then(({ objects, options }) => {
      const validObjects = objects.filter((obj): obj is fabric.Object => obj !== null);
      if (validObjects.length > 0) {
        const svgGroup = fabric.util.groupSVGElements(validObjects, options);
        svgGroup.set({ 
          left, 
          top,
          scaleX: 0.8,
          scaleY: 0.8
        });
        
        this.canvas.add(svgGroup);
        this.canvas.setActiveObject(svgGroup);
        this.canvas.renderAll();
        this.selectedTool = '';
        this.canvas.defaultCursor = 'default';
      }
    }).catch(error => {
      console.error('Erreur lors de la création du SVG:', error);
      this.createBasicShape(type, left, top);
    });
  }

  private createBasicShape(type: string, left: number, top: number): void {
    let shape;
    const fillColor = this.selectedColor;
    const strokeColor = this.selectedStrokeColor;
    const strokeWidth = this.selectedStrokeWidth;

    switch (type) {
      case 'capteur':
        shape = new fabric.Circle({
          radius: 15,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top,
          shadow: new fabric.Shadow({
            color: 'rgba(0,0,0,0.2)',
            blur: 5,
            offsetX: 2,
            offsetY: 2
          })
        });
        break;
        
      case 'mur':
        shape = new fabric.Rect({
          width: 120,
          height: 15,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top
        });
        break;
        
      case 'porte':
        shape = new fabric.Rect({
          width: 60,
          height: 8,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top,
          rx: 4,
          ry: 4
        });
        break;
        
      case 'fenetre':
        shape = new fabric.Rect({
          width: 80,
          height: 6,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top
        });
        break;
        
      case 'escalier':
        const stairs = [];
        for (let i = 0; i < 5; i++) {
          stairs.push(new fabric.Rect({
            width: 60 - (i * 8),
            height: 8,
            fill: fillColor,
            stroke: strokeColor,
            strokeWidth: 1,
            left: left + (i * 8),
            top: top + (i * 8)
          }));
        }
        shape = new fabric.Group(stairs, { left, top });
        break;
        
      case 'zone':
        shape = new fabric.Rect({
          width: 150,
          height: 100,
          fill: this.hexToRgba(fillColor, 0.1),
          stroke: fillColor,
          strokeWidth: strokeWidth,
          strokeDashArray: [5, 5],
          left,
          top
        });
        break;

      // Formes basiques pour le mobilier (fallback)
      case 'bureau':
        shape = new fabric.Rect({
          width: 120,
          height: 60,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top
        });
        break;

      case 'table':
        shape = new fabric.Ellipse({
          rx: 40,
          ry: 30,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top
        });
        break;

      case 'chaise':
        shape = new fabric.Rect({
          width: 25,
          height: 25,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top
        });
        break;

      default:
        shape = new fabric.Rect({
          width: 50,
          height: 50,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top
        });
    }

    if (shape) {
      this.canvas.add(shape);
      this.canvas.setActiveObject(shape);
      this.canvas.renderAll();
      this.selectedTool = '';
      this.canvas.defaultCursor = 'default';
    }
  }

  private getSVGDefinition(type: string): string {
    const svgTemplates: { [key: string]: string } = {
      bureau: `
       
<svg version="1.1" id="_x5F_" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 450 450" style="enable-background:new 0 0 450 450;" xml:space="preserve">

		<path style="fill:#984626;" d="M351.852,255.085c-27.091,0-49.131,22.04-49.131,49.131v39.683
			c0,27.091,22.04,49.131,49.131,49.131c27.091,0,49.131-22.04,49.131-49.131v-39.683
			C400.984,277.126,378.943,255.085,351.852,255.085z M358.723,339.492c0,3.789-3.082,6.871-6.871,6.871
			c-3.789,0-6.871-3.082-6.871-6.871v-30.869c0-3.789,3.082-6.871,6.871-6.871c3.789,0,6.871,3.082,6.871,6.871V339.492z"/>
		<path style="fill:#BF5F3D;" d="M351.852,256.719c-26.19,0-47.497,21.307-47.497,47.497v39.683
			c0,26.19,21.307,47.497,47.497,47.497c26.19,0,47.497-21.307,47.497-47.497v-39.683
			C399.35,278.027,378.042,256.719,351.852,256.719z M359.994,339.492c0,4.49-3.652,8.142-8.142,8.142
			c-4.489,0-8.142-3.652-8.142-8.142v-30.869c0-4.49,3.652-8.142,8.142-8.142c4.49,0,8.142,3.652,8.142,8.142V339.492z"/>
		<path style="fill:#984626;" d="M351.852,348.588c-3.895,0-7.36-2.468-8.65-5.918c0.996,3.941,4.404,6.871,8.65,6.871
			c4.246,0,7.655-2.93,8.65-6.871C359.213,346.12,355.748,348.588,351.852,348.588z"/>
		<path style="fill:#FFC2AD;" d="M351.852,390.031c-24.321,0-44.245-18.867-46.061-42.732c2.395,23.282,22.158,41.506,46.061,41.506
			c23.904,0,43.667-18.225,46.061-41.506C396.098,371.164,376.174,390.031,351.852,390.031z"/>
		<path style="fill:#FFC2AD;" d="M351.852,299.528c-3.895,0-7.36,2.468-8.65,5.918c0.996-3.941,4.404-6.871,8.65-6.871
			c4.246,0,7.655,2.93,8.65,6.871C359.213,301.996,355.748,299.528,351.852,299.528z"/>
		<path style="fill:#984626;" d="M351.852,258.085c-24.321,0-44.245,18.867-46.061,42.732c2.395-23.281,22.158-41.506,46.061-41.506
	</svg>

      `,
      chaise: `
        <svg viewBox="0 0 50 60" xmlns="http://www.w3.org/2000/svg">
          <rect x="5" y="0" width="40" height="35" rx="5" fill="${this.selectedColor}" stroke="${this.selectedStrokeColor}" stroke-width="${this.selectedStrokeWidth}"/>
          <rect x="0" y="30" width="50" height="30" rx="3" fill="${this.lightenColor(this.selectedColor)}" stroke="${this.selectedStrokeColor}" stroke-width="${this.selectedStrokeWidth}"/>
          <rect x="5" y="55" width="4" height="15" fill="${this.darkenColor(this.selectedColor)}"/>
          <rect x="41" y="55" width="4" height="15" fill="${this.darkenColor(this.selectedColor)}"/>
        </svg>
      `,
      lit: `
        <svg viewBox="0 0 100 140" xmlns="http://www.w3.org/2000/svg">
          <rect x="10" y="20" width="80" height="120" rx="5" fill="${this.selectedColor}" stroke="${this.selectedStrokeColor}" stroke-width="${this.selectedStrokeWidth}"/>
          <rect x="5" y="15" width="90" height="15" rx="7" fill="${this.darkenColor(this.selectedColor)}" stroke="${this.selectedStrokeColor}" stroke-width="1"/>
          <ellipse cx="30" cy="40" rx="15" ry="8" fill="${this.lightenColor(this.selectedColor)}" stroke="${this.selectedStrokeColor}" stroke-width="1"/>
          <ellipse cx="70" cy="40" rx="15" ry="8" fill="${this.lightenColor(this.selectedColor)}" stroke="${this.selectedStrokeColor}" stroke-width="1"/>
        </svg>
      `,
      table: `
        <svg viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
          <ellipse cx="60" cy="40" rx="55" ry="35" fill="${this.selectedColor}" stroke="${this.selectedStrokeColor}" stroke-width="${this.selectedStrokeWidth}"/>
          <rect x="55" y="65" width="10" height="15" fill="${this.darkenColor(this.selectedColor)}"/>
          <ellipse cx="60" cy="75" rx="25" ry="8" fill="${this.darkenColor(this.selectedColor)}"/>
        </svg>
      `
    };
    
    return svgTemplates[type] || '';
  }

  deleteSelected(): void {
    const activeObjects = this.canvas.getActiveObjects();
    if (activeObjects.length) {
      activeObjects.forEach(obj => this.canvas.remove(obj));
      this.canvas.discardActiveObject();
      this.canvas.renderAll();
    }
  }

  duplicateSelected(): void {
    const activeObject = this.canvas.getActiveObject();
    if (activeObject) {
      activeObject.clone().then((cloned: fabric.Object) => {
        cloned.set({
          left: (cloned.left || 0) + 20,
          top: (cloned.top || 0) + 20,
        });
        this.canvas.add(cloned);
        this.canvas.setActiveObject(cloned);
        this.canvas.renderAll();
      });
    }
  }

  clearCanvas(): void {
    if (confirm('Êtes-vous sûr de vouloir effacer tout le plan ?')) {
      this.canvas.clear();
      this.canvas.backgroundColor = '#FAFAFA';
      this.canvas.renderAll();
      if (this.isGridVisible) {
        this.addGridPattern();
      }
    }
  }

  bringAllToFront(): void {
    if (this.canvas && this.canvas.getObjects().length > 0) {
      this.canvas.getObjects().forEach(obj => {
        this.canvas.bringObjectToFront(obj);
      });
      this.canvas.renderAll();
    }
  }

  sendAllToBack(): void {
    if (this.canvas && this.canvas.getObjects().length > 0) {
      this.canvas.getObjects().forEach(obj => {
        this.canvas.sendObjectToBack(obj);
      });
      this.canvas.renderAll();
    }
  }

  updateToolbar(): void {
    // Mise à jour de l'interface en fonction de la sélection
  }

  setPlanType(type: 'local' | 'site'): void {
    this.planType = type;
  }

  // Méthodes pour la palette de couleurs
  selectColor(color: string): void {
    this.selectedColor = color;
    this.applyColorToSelected();
  }

  selectStrokeColor(color: string): void {
    this.selectedStrokeColor = color;
    this.applyColorToSelected();
  }

  setStrokeWidth(width: number): void {
    this.selectedStrokeWidth = width;
    this.applyColorToSelected();
  }

  onColorChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.selectColor(target.value);
  }

  onStrokeWidthChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.setStrokeWidth(parseInt(target.value, 10));
  }

  applyColorToSelected(): void {
    const activeObject = this.canvas.getActiveObject();
    if (activeObject) {
      if (activeObject.type === 'group') {
        // Pour les groupes, appliquer à tous les objets
        (activeObject as fabric.Group).getObjects().forEach(obj => {
          obj.set({
            fill: this.selectedColor,
            stroke: this.selectedStrokeColor,
            strokeWidth: this.selectedStrokeWidth
          });
        });
      } else {
        // Pour les objets simples
        activeObject.set({
          fill: activeObject.get('type') === 'zone' ? 
            this.hexToRgba(this.selectedColor, 0.1) : this.selectedColor,
          stroke: this.selectedStrokeColor,
          strokeWidth: this.selectedStrokeWidth
        });
      }
      this.canvas.renderAll();
    }
  }

  pickColorFromSelected(): void {
    const activeObject = this.canvas.getActiveObject();
    if (activeObject) {
      const fill = activeObject.get('fill') as string;
      const stroke = activeObject.get('stroke') as string;
      const strokeWidth = activeObject.get('strokeWidth') as number;
      
      if (fill && typeof fill === 'string' && fill.startsWith('#')) {
        this.selectedColor = fill;
      }
      if (stroke && typeof stroke === 'string' && stroke.startsWith('#')) {
        this.selectedStrokeColor = stroke;
      }
      if (strokeWidth) {
        this.selectedStrokeWidth = strokeWidth;
      }
    }
  }

  async importFile(event: Event): Promise<void> {
    const input = event.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) return;

    const file = input.files[0];
    const fileName = file.name.toLowerCase();

    try {
      if (fileName.endsWith('.json')) {
        const text = await file.text();
        const jsonData = JSON.parse(text);
        this.canvas.loadFromJSON(jsonData, () => {
          this.canvas.renderAll();
          console.log('✅ Plan JSON importé');
        });
      } else if (fileName.endsWith('.svg')) {
        const text = await file.text();
        const { objects, options } = await fabric.loadSVGFromString(text);
        const validObjects = objects.filter((obj): obj is fabric.Object => obj !== null);
        if (validObjects.length > 0) {
          const svgGroup = fabric.util.groupSVGElements(validObjects, options);
          svgGroup.set({ left: 100, top: 100 });
          this.canvas.add(svgGroup);
          this.canvas.renderAll();
          console.log('✅ SVG importé');
        } else {
          alert('❌ Aucun objet valide trouvé dans le SVG');
        }
      } else {
        alert('❌ Type de fichier non pris en charge');
      }
    } catch (error) {
      console.error('Erreur lors de l\'importation:', error);
      alert('❌ Erreur lors de l\'importation du fichier');
    }

    input.value = '';
  }

  exportToJson(): void {
    const json = this.canvas.toJSON();
    const exportData = {
      ...json,
      planType: this.planType,
      exportDate: new Date().toISOString(),
      version: '1.0'
    };

 
    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(exportData, null, 2));
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute("href", dataStr);
    downloadAnchorNode.setAttribute("download", `plan-${this.planType}-${new Date().toISOString().split('T')[0]}.json`);
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
  }

 addPlanToLocal(): void {
    const json =JSON.stringify( this.canvas.toJSON());
     this.selectedLocal.Architecture2DImage=json;
  
   this.selectedLocal.Site=null;
   this.selectedLocal.Transactions=null;
   //this.selectedLocal.Site.ClientId="null";

   this.selectedLocal.TypeLocal=null;
    this.installService.updateLocal(this.selectedLocal)
  .subscribe(
    (res) => {
      console.log('Mise à jour réussie', res);
    },
    (err) => {
      console.error('Erreur mise à jour', err);
    }
  );
  }
  exportToPNG(): void {
    const dataURL = this.canvas.toDataURL({
      format: 'png',
      quality: 1,
      multiplier: 2
    });
    
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute("href", dataURL);
    downloadAnchorNode.setAttribute("download", `plan-${this.planType}-${new Date().toISOString().split('T')[0]}.png`);
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
  }

  // Fonctions utilitaires pour les couleurs
  hexToRgba(hex: string, alpha: number): string {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  }

  darkenColor(color: string): string {
    const hex = color.replace('#', '');
    const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - 50);
    const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - 50);
    const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - 50);
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }

  lightenColor(color: string): string {
    const hex = color.replace('#', '');
    const r = Math.min(255, parseInt(hex.substr(0, 2), 16) + 50);
    const g = Math.min(255, parseInt(hex.substr(2, 2), 16) + 50);
    const b = Math.min(255, parseInt(hex.substr(4, 2), 16) + 50);
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }




   //Données pour les listes déroulantes avec recherche
  clients :any[]=[];

  sites : any[]=[]

  locals:any[]=[]; 

  // Sélections actuelles et filtrage
  selectedClient: any = null;
  selectedSite: any = null;
  selectedLocal: any = null;
  
  // Recherche et filtrage
  clientSearchTerm = '';
  siteSearchTerm = '';
  localSearchTerm = '';
  
  // États des dropdowns
  isClientDropdownOpen = false;
  isSiteDropdownOpen = false;
  isLocalDropdownOpen = false;

// Getters pour les listes filtrées
  get filteredClients() {
    if (!this.clientSearchTerm) return this.clients;
    return this.clients.filter(client => 
      client.name.toLowerCase().includes(this.clientSearchTerm.toLowerCase()) ||
      client.code.toLowerCase().includes(this.clientSearchTerm.toLowerCase()) ||
      client.type.toLowerCase().includes(this.clientSearchTerm.toLowerCase())
    );
  }

  get filteredSites() {
    let sites = this.selectedClient ? 
      this.sites.filter(site => site.clientId === this.selectedClient.id) : 
      [];
    
    if (!this.siteSearchTerm) return sites;
    return sites.filter(site => 
      site.name.toLowerCase().includes(this.siteSearchTerm.toLowerCase()) ||
      site.address.toLowerCase().includes(this.siteSearchTerm.toLowerCase()) ||
      site.type.toLowerCase().includes(this.siteSearchTerm.toLowerCase())
    );
  }

  get filteredLocals() {
    let locals = this.selectedSite ? 
      this.locals.filter(local => local.siteId === this.selectedSite.id) : 
      [];
    
    if (!this.localSearchTerm) return locals;
    return locals.filter(local => 
      local.name.toLowerCase().includes(this.localSearchTerm.toLowerCase()) ||
      local.floor.toLowerCase().includes(this.localSearchTerm.toLowerCase()) ||
      local.type.toLowerCase().includes(this.localSearchTerm.toLowerCase())
    );
  }

  // Méthodes pour la gestion des sélections
  selectClient(client: any): void {
    this.selectedClient = client;
    this.selectedSite = null;
    this.selectedLocal = null;
    this.isClientDropdownOpen = false;
    this.clientSearchTerm = '';
    this.siteSearchTerm = '';
    this.localSearchTerm = '';
    console.log('Client sélectionné:', client);
    this.installService.getSitesByClientId(client.Id).subscribe(
      (res: any) => {
        this.sites = res['Content']; // adapte selon la structure de la réponse
        console.log(this.sites);
      },
      err => {
        console.error(err);
      }
    );
    
  }

  selectSite(site: any): void {
    this.selectedSite = site;
    this.selectedLocal = null;
    this.isSiteDropdownOpen = false;
    this.siteSearchTerm = '';
    this.localSearchTerm = '';
    console.log('Site sélectionné:', site);
    this.installService.getLocalBySitetId(site.Id).subscribe(
      (res: any) => {
        this.locals = res['Content']; // adapte selon la structure de la réponse
        console.log("LOCAL",this.locals);
      },
      err => {
        console.error(err);
      }
    );
  }

  selectLocal(local: any): void {
    this.selectedLocal = local;
    this.isLocalDropdownOpen = false;
    this.localSearchTerm = '';
    console.log('Local sélectionné:', local);
    
    // Mettre à jour le titre du plan avec les informations sélectionnées
    this.updatePlanTitle();
  }

  // Méthodes pour gérer l'ouverture/fermeture des dropdowns
  toggleClientDropdown(): void {
    this.isClientDropdownOpen = !this.isClientDropdownOpen;
    this.isSiteDropdownOpen = false;
    this.isLocalDropdownOpen = false;
  }

  toggleSiteDropdown(): void {
    if (!this.selectedClient) return;
    this.isSiteDropdownOpen = !this.isSiteDropdownOpen;
    this.isClientDropdownOpen = false;
    this.isLocalDropdownOpen = false;
  }

  toggleLocalDropdown(): void {
    if (!this.selectedSite) return;
    this.isLocalDropdownOpen = !this.isLocalDropdownOpen;
    this.isClientDropdownOpen = false;
    this.isSiteDropdownOpen = false;
  }

  // Fermer tous les dropdowns
  closeAllDropdowns(): void {
    this.isClientDropdownOpen = false;
    this.isSiteDropdownOpen = false;
    this.isLocalDropdownOpen = false;
  }

  // Mettre à jour le titre du plan
  updatePlanTitle(): void {
    if (this.selectedClient && this.selectedSite && this.selectedLocal) {
      const planTitle = `${this.selectedClient.Name} - ${this.selectedSite.Name} - ${this.selectedLocal.Name}`;
      console.log('Titre du plan:', planTitle);
      
      // Optionnel: ajouter le titre comme texte sur le canvas
      const titleText = new fabric.Text(planTitle, {
        left: 20,
        top: 20,
        fontSize: 16,
        fontWeight: 'bold',
        fill: '#333333',
        backgroundColor: 'rgba(255,255,255,0.8)',
        padding: 10
      });
      
      // Supprimer l'ancien titre s'il existe
      const existingTitle = this.canvas.getObjects().find(obj => obj.get('isTitle'));
      if (existingTitle) {
        this.canvas.remove(existingTitle);
      }
      
      titleText.set('isTitle', true);
      this.canvas.add(titleText);
      this.canvas.renderAll();
    }
  }

  // Réinitialiser les sélections
  resetSelections(): void {
    this.selectedClient = null;
    this.selectedSite = null;
    this.selectedLocal = null;
    this.clientSearchTerm = '';
    this.siteSearchTerm = '';
    this.localSearchTerm = '';
    this.closeAllDropdowns();
  }

}