import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { Local } from '@app/core/models/local';
import { LocalApiService } from '@app/core/services/administrative/local.service';

@Component({
  selector: 'app-card-local',
  templateUrl: './card-local.component.html',
  styleUrls: ['./card-local.component.css'],
  standalone: true,
  imports: [CommonModule, MatIconModule],
})
export class CardLocalComponent {
  @Input() local!: Local;
  @Output() viewDetails = new EventEmitter<string>();
  @Output() editLocal = new EventEmitter<Local>();
  @Output() deleteLocal = new EventEmitter<string>();

  imageLocaleUrl: string = '';

  constructor(
    readonly router: Router,
    readonly localService: LocalApiService
  ) {}

  ngOnInit() {
    // Get the image URL from the service
    this.imageLocaleUrl = this.localService.getImageUrl(this.local.ImageLocal);
    
    console.log('Local data:', this.local);
    
    if (this.local.TypeLocal?.Nom) {
      const typeElement = document.querySelector('.local-type');
      if (typeElement) {
        typeElement.setAttribute('data-type', this.local.TypeLocal.Nom);
      }
    }
  }

  onView(): void {
    this.router.navigate(['/local-details', this.local.Id]);
  }

  onEdit(): void {
    this.editLocal.emit(this.local);
  }

  onDelete(): void {
    this.deleteLocal.emit(this.local.Id);
  }
}