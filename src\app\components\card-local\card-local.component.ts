import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { Local } from '@app/core/models/local';
import { LocalApiService } from '@app/core/services/administrative/local.service';

@Component({
  selector: 'app-card-local',
  templateUrl: './card-local.component.html',
  styleUrls: ['./card-local.component.css'],
  standalone: true,
  imports: [CommonModule, MatIconModule],
})
export class CardLocalComponent {
  @Input() local!: Local;
  @Output() viewDetails = new EventEmitter<string>();
  @Output() editLocal = new EventEmitter<Local>();
  @Output() deleteLocal = new EventEmitter<string>();

  imageArchitectureUrl: string = '';
  imageLocaleUrl: string = '';

  constructor(readonly router: Router, readonly localService:LocalApiService) {}
  localTypes = [
    { value: 'Salle de réunion', label: 'Salle de réunion' },
    { value: 'Bureau', label: 'Bureau' },
    { value: 'Laboratoire', label: 'Laboratoire' },
    { value: 'Amphithéâtre', label: 'Amphithéâtre' },
    { value: 'Espace détente', label: 'Espace détente' },
    { value: 'Salle de formation', label: 'Salle de formation' },
  ];

  ngOnInit() {
    this.imageLocaleUrl = `data:image/jpeg;base64,${this.local.ImageLocal}`;
    console.log('images:',this.local);
  }

  onView(): void {
    this.router.navigate(['/site-details', this.local.Id]);
  }

  getLocalTypeLabel(typeValue: string): string {
    // const type = this.localTypes.find((t) => t.value === typeValue);
    // return type ? type.label : typeValue;
    return ""
  }

  onEdit(): void {
    this.editLocal.emit(this.local);
  }

  onDelete(): void {
    this.deleteLocal.emit(this.local.Id);
  }
  }

