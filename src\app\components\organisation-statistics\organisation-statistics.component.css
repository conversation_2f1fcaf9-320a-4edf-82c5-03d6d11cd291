/* Organisation Statistics Component Styles */
.statistics-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #333;
  line-height: 1.5;
}

/* Header Section */
.statistics-header {
  background: linear-gradient(135deg, #ffffff, #f8fafc);
  border-radius: 12px;
  padding: 25px 30px;
  margin-bottom: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.header-top {
  margin-bottom: 20px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #4a5568;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.back-button:hover {
  background: #e2e8f0;
  color: #2d3748;
  transform: translateX(-2px);
}

.back-button i {
  font-size: 18px;
}

.statistics-title {
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 28px;
  color: #4CAF50;
  background: linear-gradient(45deg, #4CAF50, #81C784);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.statistics-subtitle {
  font-size: 16px;
  color: #718096;
  margin: 0;
  font-style: italic;
}

/* Statistics Grid */
.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

/* Stat Cards */
.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4CAF50, #81C784);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: #4a5568;
  line-height: 1.4;
  flex: 1;
  margin-right: 10px;
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: rgba(76, 175, 80, 0.1);
}

.card-icon i {
  font-size: 20px;
}

.card-value {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 15px;
  display: flex;
  align-items: baseline;
  gap: 6px;
}

.unit {
  font-size: 16px;
  font-weight: 500;
  color: #718096;
}

.card-footer {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.percentage {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.percentage.positive {
  color: #22c55e;
  background-color: #dcfce7;
}

.percentage.negative {
  color: #ef4444;
  background-color: #fef2f2;
}

.percentage.neutral {
  color: #6b7280;
  background-color: #f3f4f6;
}

.trend-icon {
  font-size: 14px;
}

.na-badge {
  background-color: #fef3c7;
  color: #92400e;
  font-size: 11px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
}

/* Summary Section */
.summary-section {
  margin-top: 30px;
}

.summary-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.summary-title {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.summary-title i {
  color: #4CAF50;
  font-size: 22px;
}

.summary-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f1f5f9;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-label {
  font-weight: 500;
  color: #4a5568;
}

.summary-value {
  font-weight: 600;
  color: #2d3748;
}

.status-active {
  color: #22c55e;
  background: #dcfce7;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

/* No Data State */
.no-data {
  text-align: center;
  padding: 60px 20px;
  color: #718096;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.no-data-icon {
  font-size: 64px;
  color: #cbd5e0;
  margin-bottom: 20px;
}

.no-data h3 {
  font-size: 20px;
  font-weight: 600;
  color: #4a5568;
  margin: 0 0 10px 0;
}

.no-data p {
  font-size: 16px;
  margin: 0;
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.loading-spinner {
  margin-bottom: 20px;
}

.spinning {
  font-size: 48px;
  color: #4CAF50;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  color: #718096;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .statistics-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .statistics-container {
    padding: 15px;
  }

  .statistics-header {
    padding: 20px;
  }

  .statistics-title {
    font-size: 20px;
  }

  .statistics-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .stat-card {
    padding: 16px;
  }

  .card-value {
    font-size: 24px;
  }

  .summary-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .statistics-header {
    padding: 15px;
  }

  .statistics-title {
    font-size: 18px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .card-icon {
    align-self: flex-end;
  }
}