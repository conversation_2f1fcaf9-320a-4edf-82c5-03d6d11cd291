/* Validation Summary Styles */
.validation-summary {
  background-color: #fdf2f2;
  border: 1px solid #f5c6cb;
  border-left: 4px solid #dc3545;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 24px;
  animation: slideDown 0.3s ease-out;
}

.validation-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #721c24;
  margin-bottom: 12px;
  font-size: 16px;
}

.validation-header .material-icons {
  margin-right: 8px;
  font-size: 20px;
  color: #dc3545;
}

.validation-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.validation-list li {
  display: flex;
  align-items: center;
  color: #721c24;
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.4;
}

.validation-list li:last-child {
  margin-bottom: 0;
}

.validation-list li .material-icons {
  margin-right: 8px;
  font-size: 16px;
  color: #dc3545;
  flex-shrink: 0;
}

/* Animation for validation summary */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form field error styles */
.form-group input.error,
.form-group select.error {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.error-message {
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
}

.error-message::before {
  content: "⚠";
  margin-right: 4px;
  font-size: 12px;
}

.required {
  color: #dc3545;
}
.site-management-container {
  width: 100%;
  margin: 30px auto;
  padding: 25px;
  background: linear-gradient(145deg, #ffffff, #f9f9f9);
  border-radius: 12px;
  /* box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08); */
}

/* Header Section */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  animation: slideIn 0.5s ease-out;
  width: 95%;
}

.page-title {
  margin: 0;
}

.title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.title-icon {
  font-size: 30px;
  color: #4caf50;
  background: linear-gradient(45deg, #4caf50, #81c784);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.actions {
  display: flex;
  gap: 15px;
}

.create-button {
  background: linear-gradient(45deg, #4caf50, #81c784);
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.create-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(76, 175, 80, 0.5);
  background: linear-gradient(45deg, #81c784, #4caf50);
}

/* Create and Edit Forms */
.create-form-card,
.edit-form-container {
  border-radius: 12px;
  animation: fadeIn 0.4s ease-in;
  background: white;
  padding: 25px;
  width: 95%;
  height: 100%;
  max-height: 90vh; /* prevent modal from exceeding viewport */
  overflow: hidden;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.form-container{
  overflow-y: auto;
  max-height: 60vh;
}

.create-form-card form,
.edit-form-container form {
  width: 100%;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
  align-items: end;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 100%;
  margin-bottom: 0;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
}

.form-group input,
.form-group select,
.form-group textarea {
  height: 42px;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  color: #4a5568;
  background-color: white;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-group textarea {
  height: auto;
  min-height: 100px;
  resize: vertical;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #4caf50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  color: #1a202c;
  background-color: white;
  transition: border-color 0.2s;
}

.form-group select:focus {
  border-color: #4caf50;
  outline: none;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

.form-group select:invalid {
  color: #718096;
}

.form-group select option {
  color: #1a202c;
}

.form-group input[type="file"] {
  padding: 8px;
  height: auto;
  border: 2px dashed #e2e8f0;
  background-color: #f8fafc;
  cursor: pointer;
}

.form-group input[type="file"]:hover {
  border-color: #4caf50;
  background-color: #f0fff4;
}

.form-group input[type="checkbox"] {
  width: auto;
  height: auto;
  margin-top: 5px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.form-actions button {
  padding: 10px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.form-actions button[type="button"] {
  background-color: #fff;
  border: 1px solid #e2e8f0;
  color: #4a5568;
}

.form-actions button[type="submit"] {
  background: linear-gradient(45deg, #4caf50, #81c784);
  border: none;
  color: white;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.2);
}

.form-actions button[type="button"]:hover {
  background-color: #f8fafc;
  border-color: #cbd5e0;
}

.form-actions button[type="submit"]:hover {
  background: linear-gradient(45deg, #81c784, #4caf50);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(76, 175, 80, 0.3);
}

.form-actions button[type="submit"]:disabled {
  background: #e2e8f0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Search Bar */
.search-bar {
  margin-bottom: 30px;
}

.search-bar input {
  width: 100%;
  max-width: 450px;
  padding: 12px 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #f8fafc;
  color: #4a5568;
}

.search-bar input:focus {
  outline: none;
  border-color: #4caf50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.search-bar input::placeholder {
  color: #a0aec0;
}

/* Loading Spinner */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  font-size: 18px;
  color: #4caf50;
}

/* Table View */
.table-view {
  width: 100%;
}

/* Pagination */
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.mat-paginator {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Current Images Preview */
.current-images-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
  margin-top: 8px;
}

.current-image-item {
  position: relative;
  aspect-ratio: 1;
}

.current-image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.remove-image-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0;
}

/* Required field indicator */
.required {
  color: #ef4444;
  margin-left: 4px;
}

.error-message {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .actions {
    width: 100%;
    flex-direction: column;
    gap: 10px;
  }

  .create-button {
    width: 100%;
    padding: 12px;
    justify-content: center;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 24px;
  }

  .title-icon {
    font-size: 26px;
  }

  .search-bar input {
    max-width: 100%;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions button {
    width: 100%;
  }
}

/* Add to the existing CSS file */
.form-group .mat-error {
  font-size: 12px;
  color: #ef4444;
  margin-top: 4px;
}

.form-group .mat-form-field {
  width: 100%;
}

.form-group .mat-select {
  width: 100%;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 20px;
  margin-right: 10px;
}

.multi-select-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 50%;
  margin-bottom: 10px;
}

.multi-select {
  padding: 8px;
  border-radius: 8px;
  border: 1px solid #ccc;
  font-size: 14px;
  background-color: #fff;
  transition: border-color 0.2s;
}

.multi-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.selected-items {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.selected-item {
  display: flex;
  align-items: center;
  background-color: #e6f0ff;
  border: 1px solid #007bff;
  border-radius: 20px;
  padding: 4px 10px;
  font-size: 13px;
  color: #007bff;
}

.remove-btn {
  background: transparent;
  border: none;
  color: #007bff;
  font-size: 16px;
  margin-left: 6px;
  cursor: pointer;
  line-height: 1;
}

.remove-btn:hover {
  color: #dc3545;
}

.info-message {
  font-size: 13px;
  color: #6c757d;
}

.error-message {
  font-size: 13px;
  color: #dc3545;
  margin-top: 4px;
}
