import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule, FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatPaginatorModule, MatPaginator, PageEvent } from '@angular/material/paginator';
import { AuthService, RegisterModel } from '@app/core/services/auth.service';
import { NgxUiLoaderService } from 'ngx-ui-loader';
import { NgToastService } from 'ng-angular-popup';
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';

interface UserRole {
  value: string;
  label: string;
  description: string;
}

interface User {
  id: string;
  userName: string;
  email: string;
  phoneNumber: string;
  roles: string[];
  emailConfirmed: boolean;
  lockoutEnabled: boolean;
  createdAt: string;
}

interface CreateUserRequest {
  email: string;
  userName: string;
  phoneNumber: string;
  newPassword: string;
  roles: string[];
  emailConfirmed: boolean;
  lockoutEnabled: boolean;
}

@Component({
  selector: 'app-user-management',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatOptionModule,
    MatCardModule,
    MatDialogModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatPaginatorModule,
    GenericTableComponent
  ],
  templateUrl: './user-management.component.html',
  styleUrls: ['./user-management.component.css']
})
export class UserManagementComponent implements OnInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  
  users: User[] = [];
  filteredUsers: User[] = [];
  isLoadingUsers: boolean = false;
  
  // Pagination
  pageSize: number = 10;
  currentPage: number = 0;
  totalUsers: number = 0;
  
  // Search
  searchParam: string = '';
  hasSearchFilter: boolean = false;
  
  // Form and popup
  showUserForm: boolean = false;
  isEditMode: boolean = false;
  selectedUser: User | null = null;
  showValidationErrors = false;
  validationErrorMessage = '';
  
  // Generic table configuration
  tableHeaders: string[] = ['Utilisateur', 'Email', 'Téléphone', 'Rôles', 'Date de création', 'Statut'];
  tableKeys: string[] = ['userName', 'email', 'phoneNumber', 'roles', 'createdAt', 'lockoutEnabled'];
  tableActions: string[] = ['edit', 'delete'];
  
  userRoles: UserRole[] = [
    {
      value: 'Admin',
      label: 'Administrateur',
      description: 'Accès complet à toutes les fonctionnalités'
    },
    {
      value: 'SuperAdmin',
      label: 'Super Administrateur',
      description: 'Accès système complet avec gestion des administrateurs'
    },
    {
      value: 'Integrateur',
      label: 'Intégrateur',
      description: 'Accès aux fonctionnalités d\'intégration et configuration'
    },
    {
      value: 'User',
      label: 'Utilisateur',
      description: 'Accès de base aux fonctionnalités utilisateur'
    },
    {
      value: 'Manager',
      label: 'Gestionnaire',
      description: 'Accès aux fonctionnalités de gestion et supervision'
    }
  ];

  userForm = new FormGroup({
    id: new FormControl(''),
    userName: new FormControl('', [Validators.required, Validators.minLength(3)]),
    email: new FormControl('', [Validators.required, Validators.email]),
    newPassword: new FormControl('', [Validators.required, Validators.minLength(6)]),
    confirmPassword: new FormControl('', [Validators.required]),
    phoneNumber: new FormControl('', [Validators.required, Validators.pattern(/^[\+]?[0-9\s\-\(\)]{10,}$/)]),
    roles: new FormControl<string[]>([], [Validators.required, this.rolesValidator]),
    emailConfirmed: new FormControl(true),
    lockoutEnabled: new FormControl(false)
  }/*, {
    validators: this.passwordMatchValidator
  }*/);

  constructor(
    private readonly fb: FormBuilder,
    private readonly authService: AuthService,
    private readonly toast: NgToastService,
    private readonly loader: NgxUiLoaderService,
    private readonly dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadUsers();
  }

  // Custom validator for roles
  private rolesValidator(control: FormControl): {[key: string]: any} | null {
    const roles = control.value;
    if (!roles || roles.length === 0) {
      return { 'rolesRequired': true };
    }
    return null;
  }

  private passwordMatchValidator(form: FormGroup) {
    const password = form.get('newPassword');
    const confirmPassword = form.get('confirmPassword');

    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }

    if (confirmPassword?.hasError('passwordMismatch')) {
      confirmPassword.setErrors(null);
    }

    return null;
  }

  loadUsers(): void {
    this.isLoadingUsers = true;
    this.loader.start();
    
    this.authService.getAllUsers().subscribe({
      next: (users) => {
        // Map the API response to our User interface
        this.users = users.map(user => ({
          id: user.id,
          userName: user.userName,
          email: user.email,
          phoneNumber: user.phoneNumber ?? '',
          roles: user.roles ?? [],
          emailConfirmed: user.emailConfirmed ?? true,
          lockoutEnabled: user.lockoutEnabled ?? false,
          createdAt: new Date().toLocaleDateString('fr-FR')
        }));
        
        this.totalUsers = this.users.length;
        this.applySearchFilter();
        this.isLoadingUsers = false;
        this.loader.stop();
      },
      error: (error) => {
        this.toast.warning('Erreur lors du chargement des utilisateurs', 'Erreur', 3000);
        this.isLoadingUsers = false;
        this.loader.stop();
      }
    });
  }

  private applySearchFilter(): void {
    if (!this.searchParam.trim()) {
      this.filteredUsers = [...this.users];
      this.hasSearchFilter = false;
    } else {
      this.hasSearchFilter = true;
      const term = this.searchParam.toLowerCase();
      this.filteredUsers = this.users.filter(user =>
        user.userName.toLowerCase().includes(term) ||
        user.email.toLowerCase().includes(term) ||
        user.roles.some(role => role.toLowerCase().includes(term))
      );
    }
    this.totalUsers = this.filteredUsers.length;
  }

  onSearchKeyup(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      this.searchUsers();
    } else if (event.key === 'Backspace' && this.searchParam === '') {
      this.clearSearch();
    }
  }

  searchUsers(): void {
    this.currentPage = 0; // Reset to first page when searching
    this.applySearchFilter();
  }

  clearSearch(): void {
    this.searchParam = '';
    this.hasSearchFilter = false;
    this.currentPage = 0; // Reset to first page when clearing search
    this.applySearchFilter();
  }

  get paginatedUsers(): User[] {
    const startIndex = this.currentPage * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    return this.filteredUsers.slice(startIndex, endIndex);
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
  }

  // Handle actions from generic table
  handleTableAction(event: { action: string; row: any }): void {
    const { action, row } = event;
    
    switch (action) {
      case 'edit':
        this.editUser(row);
        break;
      case 'delete':
        this.deleteUser(row.id);
        break;
      default:
        console.warn('Unknown action:', action);
    }
  }

  addNewUser(): void {
    this.isEditMode = false;
    this.selectedUser = null;
    this.userForm.reset();
    this.showValidationErrors = false;
    this.validationErrorMessage = '';
    
    // Set default values
    this.userForm.patchValue({
      emailConfirmed: true,
      lockoutEnabled: false,
      roles: []
    });
    
    // Restore password validation for create mode
    this.userForm.get('newPassword')?.setValidators([Validators.required, Validators.minLength(6)]);
    this.userForm.get('confirmPassword')?.setValidators([Validators.required]);
    this.userForm.get('newPassword')?.updateValueAndValidity();
    this.userForm.get('confirmPassword')?.updateValueAndValidity();
    
    this.showUserForm = true;
  }

  editUser(user: User): void {
    this.isEditMode = true;
    this.selectedUser = user;
    this.showValidationErrors = false;
    this.validationErrorMessage = '';

    this.userForm.patchValue({
      id: user.id,
      userName: user.userName,
      email: user.email,
      phoneNumber: user.phoneNumber,
      roles: user.roles,
      emailConfirmed: user.emailConfirmed,
      lockoutEnabled: user.lockoutEnabled,
      newPassword: '', // Don't populate password for edit
      confirmPassword: ''
    });

    // Remove password validation for edit mode
    this.userForm.get('newPassword')?.clearValidators();
    this.userForm.get('confirmPassword')?.clearValidators();
    this.userForm.get('newPassword')?.updateValueAndValidity();
    this.userForm.get('confirmPassword')?.updateValueAndValidity();

    this.showUserForm = true;
  }

  submitUserForm(): void {
    this.markFormGroupTouched(this.userForm);

    if (this.userForm.valid) {
      this.showValidationErrors = false;
      this.validationErrorMessage = '';
      
      if (this.isEditMode) {
        // Show confirmation dialog for edit mode
        const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
          width: '400px',
          data: {
            title: 'Confirmation de modification',
            message: 'Êtes-vous sûr de vouloir modifier cet utilisateur ?',
            icon: 'edit',
          },
        });

        dialogRef.afterClosed().subscribe((result) => {
          if (result) {
            this.processUserForm();
          }
        });
      } else {
        // For create mode, directly process the form
        this.processUserForm();
      }
    } else {
      // Show validation errors
      this.showValidationErrors = true;
      this.validationErrorMessage = 'Veuillez corriger les erreurs dans le formulaire avant de continuer.';

      // Scroll to top of form to show error message
      const formElement = document.querySelector('.popup-form');
      if (formElement) {
        formElement.scrollTop = 0;
      }
    }
  }

  private processUserForm(): void {
    const formValue = this.userForm.value;

    if (this.isEditMode && this.selectedUser) {
      this.loader.start();
      
      const updateData = {
        email: formValue.email ?? '',
        userName: formValue.userName ?? '',
        phoneNumber: formValue.phoneNumber ?? '',
        newPassword: formValue.newPassword || undefined, // Only include if provided
        roles: formValue.roles ?? [],
        emailConfirmed: formValue.emailConfirmed ?? true,
        lockoutEnabled: formValue.lockoutEnabled ?? false
      };

      // Remove newPassword if it's empty (not changing password)
      if (!updateData.newPassword) {
        delete updateData.newPassword;
      }

      this.authService.updateUser(this.selectedUser.id, updateData).subscribe({
        next: (updatedUser) => {
          this.toast.success('Utilisateur modifié avec succès', 'Succès', 3000, false);
          
          // Update local data with the response from the API
          const userIndex = this.users.findIndex(u => u.id === this.selectedUser!.id);
          if (userIndex !== -1) {
            this.users[userIndex] = {
              ...this.users[userIndex],
              userName: updatedUser.userName,
              email: updatedUser.email,
              phoneNumber: updatedUser.phoneNumber,
              roles: updatedUser.roles,
              emailConfirmed: updatedUser.emailConfirmed,
              lockoutEnabled: updatedUser.lockoutEnabled
            };
            this.applySearchFilter();
          }
          
          this.closeUserForm();
          this.loader.stop();
        },
        error: (error) => {
          this.toast.warning(error?.message ?? 'Erreur lors de la modification de l\'utilisateur', 'Erreur', 5000);
          this.loader.stop();
        }
      });
    } else {
      // Create mode
      this.loader.start();
      
      // Map to the RegisterModel format expected by AuthService
      const registerModel: RegisterModel = {
        email: formValue.email ?? '',
        password: formValue.newPassword ?? '',
        confirmPassword: formValue.confirmPassword ?? '',
        phoneNumber: formValue.phoneNumber ?? '',
        userRole: formValue.roles?.[0] ?? '' // Take first role for compatibility
      };

      this.authService.register(registerModel).subscribe({
        next: () => {
          this.toast.success('Utilisateur créé avec succès', 'Succès', 3000, false);
          this.closeUserForm();
          this.loadUsers();
          this.loader.stop();
        },
        error: (error) => {
          this.toast.warning(error?.message ?? 'Erreur lors de la création de l\'utilisateur', 'Erreur', 5000);
          this.loader.stop();
        }
      });
    }
  }

  closeUserForm(): void {
    this.showUserForm = false;
    this.isEditMode = false;
    this.selectedUser = null;
    this.userForm.reset();
    this.showValidationErrors = false;
    this.validationErrorMessage = '';
  }

  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  getRoleLabel(roleValue: string): string {
    const role = this.userRoles.find(r => r.value === roleValue);
    return role ? role.label : roleValue;
  }

  // Get formatted roles display for table
  getFormattedRoles(roles: string[]): string {
    if (!roles || roles.length === 0) return 'Aucun rôle';
    return roles.map(role => this.getRoleLabel(role)).join(', ');
  }

  // Get status display for table
  getStatusDisplay(user: User): string {
    return user.lockoutEnabled ? 'Bloqué' : 'Actif';
  }

  getFieldError(fieldName: string): string {
    const field = this.userForm.get(fieldName);
    if (field?.errors && field.touched) {
      const errors = field.errors;
      
      switch (fieldName) {
        case 'userName':
          if (errors['required']) return 'Le nom d\'utilisateur est obligatoire';
          if (errors['minlength']) return 'Le nom d\'utilisateur doit contenir au moins 3 caractères';
          break;
          
        case 'email':
          if (errors['required']) return 'L\'adresse email est obligatoire';
          if (errors['email']) return 'Veuillez saisir une adresse email valide (ex: <EMAIL>)';
          break;
          
        case 'newPassword':
          if (errors['required']) return 'Le mot de passe est obligatoire';
          if (errors['minlength']) return 'Le mot de passe doit contenir au moins 6 caractères';
          break;
          
        case 'confirmPassword':
          if (errors['required']) return 'La confirmation du mot de passe est obligatoire';
          if (errors['passwordMismatch']) return 'Les mots de passe ne correspondent pas';
          break;
          
        case 'phoneNumber':
          if (errors['required']) return 'Le numéro de téléphone est obligatoire';
          if (errors['pattern']) return 'Veuillez saisir un numéro de téléphone valide (ex: +33 1 23 45 67 89)';
          break;
          
        case 'roles':
          if (errors['required'] || errors['rolesRequired']) return 'Vous devez sélectionner au moins un rôle pour l\'utilisateur';
          break;
          
        default:
          if (errors['required']) return `${fieldName} est obligatoire`;
          break;
      }
    }
    return '';
  }

  deleteUser(userId: string): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirmation de suppression',
        message: 'Êtes-vous sûr de vouloir supprimer cet utilisateur ? Cette action est irréversible.',
        icon: 'warning',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.loader.start();
        this.authService.deleteUser(userId).subscribe({
          next: () => {
            this.toast.success('Utilisateur supprimé avec succès', 'Succès', 3000, false);
            this.users = this.users.filter(user => user.id !== userId);
            this.applySearchFilter();
            this.loader.stop();
          },
          error: (error) => {
            this.toast.warning('Erreur lors de la suppression de l\'utilisateur', 'Erreur', 3000);
            this.loader.stop();
          }
        });
      }
    });
  }
}