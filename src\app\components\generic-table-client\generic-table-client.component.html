<div class="subscription-table-container">
  <div class="table-color-bar"></div>
  <div class="table-wrapper">
    <table class="generic-table">
      <thead>
        <tr>
          <th *ngFor="let header of headers" [colSpan]="header.colspan">{{ header.header }}</th>
          <th *ngIf="actions.length > 0">Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngIf="data.length === 0">
            <td [colSpan]="headers.length">Aucun client trouvé</td>
        </tr>
        <tr *ngFor="let row of data" class="table-row">
          <td
            *ngFor="let key of keys"
            [ngClass]="getCellClass(key, getValue(row, key))"
          >
            <ng-container [ngSwitch]="key">
              <ng-container
                *ngIf="
                  key.split('.').length > 1 && row[key.split('.')[0]] != null;
                  else ngElse
                "
              >
                {{ row[key.split(".")[0]][key.split(".")[1]] }}
              </ng-container>

              <ng-template #ngElse>
                <ng-container *ngIf="key === 'SiteActif' || key === 'SiteEnMaintenance' || key === 'SiteInactif'; else defaultText">
                  <div>
                    <div class="status-background">
                        <span [ngClass]="getSiteStatusClassByIndex(key)">
                          {{ row[key] != null ? row[key] : "-" }}
                        </span>
                    </div>
                  </div>
                </ng-container>

                <ng-template #defaultText>
                  <span [ngClass]="getStatusClass(key, getValue(row, key))">
                    {{ row[key] != null ? row[key] : "-" }}
                  </span>
                </ng-template>
              </ng-template>
            </ng-container>
          </td>
          <td *ngIf="actions.length > 0">
            <button
              mat-icon-button
              [matMenuTriggerFor]="actionsMenu"
              class="more-actions-button"
            >
              <mat-icon>more_vert</mat-icon>
            </button>

            <mat-menu #actionsMenu="matMenu">
              <button
                mat-menu-item
                *ngFor="let action of actions"
                (click)="triggerAction(action, row)"
                class="menu-action-item"
              >
                <mat-icon [ngClass]="getActionClass(action)">{{
                  getActionIcon(action)
                }}</mat-icon>
                <span>{{ getActionLabel(action) }}</span>
              </button>
            </mat-menu>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
