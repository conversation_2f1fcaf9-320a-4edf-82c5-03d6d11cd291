import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ControllerServeurApiService } from '@app/core/services/administrative/controllerserveur.service';
import { ControllerServeur } from '@app/core/models/controllerServeur';
import { Licence } from '@app/core/models/licence';

@Component({
  selector: 'app-form-cs',
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './form-cs.component.html',
  styleUrl: './form-cs.component.css',
})
export class FormCsComponent implements OnInit, OnChanges {
  @Input() clientId: string = '';
  @Input() controllerServer: ControllerServeur | null = null;
  @Input() isEditMode: boolean = false;
  @Output() formClosed = new EventEmitter<void>();
  @Output() controllerServerCreated = new EventEmitter<ControllerServeur>();
  @Output() controllerServerUpdated = new EventEmitter<ControllerServeur>();
  @Input() licences: Licence[] = [];

  createControllerServerForm!: FormGroup;
  isSubmitting: boolean = false;

  constructor(
    private fb: FormBuilder,
    private controllerServerService: ControllerServeurApiService
  ) {}

  ngOnInit(): void {
    console.log('Form initialized with licences:', this.licences);
    this.initializeForm();
    if (this.isEditMode && this.controllerServer) {
      this.populateForm();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['licences']) {
      console.log('Licences changed:', changes['licences'].currentValue);
    }

    if (changes['controllerServer'] || changes['isEditMode']) {
      if (this.createControllerServerForm) {
        if (this.isEditMode && this.controllerServer) {
          this.populateForm();
        } else {
          this.createControllerServerForm.reset();
          this.initializeForm();
        }
      }
    }
  }

  private initializeForm(): void {
    this.createControllerServerForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      maxControllers: [0],
      maxSensors: [0],
      geographicZone: [''],
      commercialCondition: [''],
      triggerType: [''],
      actionType: [''],
      eventType: [''],
      status: ['Active', [Validators.required]],
      idlicence: ['', [Validators.required]],
    });
  }

  private populateForm(): void {
    if (this.controllerServer) {
      console.log(
        'Populating form with controller server:',
        this.controllerServer
      );
      console.log(
        'Controller server IdLicence:',
        this.controllerServer.IdLicence
      );

      // Get the licence ID - handle both direct IdLicence and nested Licence object
      let licenceId = '';
      if (this.controllerServer.IdLicence) {
        licenceId = this.controllerServer.IdLicence;
      } else if (
        this.controllerServer.Licence &&
        (this.controllerServer.Licence.Id )
      ) {
        licenceId =
          this.controllerServer.Licence.Id ||
          '';
      }

      console.log('Using licence ID:', licenceId);

      this.createControllerServerForm.patchValue({
        name: this.controllerServer.Name || '',
        maxControllers: this.controllerServer.MaxControllers || 0,
        maxSensors: this.controllerServer.MaxSensors || 0,
        geographicZone: this.controllerServer.GeographicZone || '',
        commercialCondition: this.controllerServer.CommercialCondition || '',
        triggerType: this.controllerServer.TriggerType || '',
        actionType: this.controllerServer.ActionType || '',
        eventType: this.controllerServer.EventType || '',
        status: this.controllerServer.Status || '',
        idlicence: licenceId,
      });
    }
  }

  // Helper methods for licence handling
  getLicenceId(licence: Licence): string {
    // Handle both Id and id properties consistently
    return licence.Id || '';
  }

  getLicenceName(licence: Licence): string {
    return licence.Name || `Licence ${this.getLicenceId(licence)}`;
  }

  // onSubmit(): void {
  //   console.log('Form submission started');
  //   console.log('Form valid:', this.createControllerServerForm.valid);
  //   console.log('Form value:', this.createControllerServerForm.value);

  //   if (this.createControllerServerForm.valid && !this.isSubmitting) {
  //     this.isSubmitting = true;

  //     const formData = this.createControllerServerForm.value;
  //     console.log('Form data being processed:', formData);

  //     // Prepare the data according to your API structure
  //     const controllerServerData = {
  //       Name: formData.name,
  //       MaxControllers: parseInt(formData.maxControllers) || 0,
  //       MaxSensors: parseInt(formData.maxSensors) || 0,
  //       GeographicZone: formData.geographicZone || '',
  //       CommercialCondition: formData.commercialCondition || '',
  //       TriggerType: formData.triggerType || '',
  //       ActionType: formData.actionType || '',
  //       EventType: formData.eventType || '',
  //       Status: formData.status,
  //       IdLicence: formData.idlicence && formData.idlicence !== '' ? formData.idlicence : null,
  //     };

  //     console.log('Controller server data to be sent:', controllerServerData);

  //     if (this.isEditMode && this.controllerServer) {
  //       const updateData = {
  //         ...controllerServerData,
  //         Id: this.controllerServer.Id
  //       };

  //       this.controllerServerService.update(updateData).subscribe({
  //         next: (response: any) => {
  //           console.log('Controller Server updated successfully:', response);

  //           // Ensure the response includes licence information for display
  //           const updatedControllerServer = {
  //             ...response,
  //             Licence: this.findLicenceForResponse(response, formData.idlicence)
  //           };

  //           this.controllerServerUpdated.emit(updatedControllerServer);
  //           this.resetForm();
  //         },
  //         error: (error: any) => {
  //           console.error('Error updating controller server:', error);
  //           this.isSubmitting = false;
  //           alert('Erreur lors de la mise à jour du contrôleur serveur');
  //         },
  //       });
  //     } else {
  //       this.controllerServerService.post('', controllerServerData).subscribe({
  //         next: (response: any) => {
  //           console.log('Controller Server created successfully:', response);

  //           // Ensure the response includes licence information for display
  //           const newControllerServer = {
  //             ...response,
  //             Licence: this.findLicenceForResponse(response, formData.idlicence)
  //           };

  //           this.controllerServerCreated.emit(newControllerServer);
  //           this.resetForm();
  //         },
  //         error: (error: any) => {
  //           console.error('Error creating controller server:', error);
  //           this.isSubmitting = false;
  //           alert('Erreur lors de la création du contrôleur serveur');
  //         },
  //       });
  //     }
  //   } else {
  //     console.log('Form is invalid, marking fields as touched');
  //     // Mark all fields as touched to show validation errors
  //     Object.keys(this.createControllerServerForm.controls).forEach((key) => {
  //       const control = this.createControllerServerForm.get(key);
  //       if (control && control.invalid) {
  //         console.log(`Field ${key} is invalid:`, control.errors);
  //       }
  //       control?.markAsTouched();
  //     });
  //   }
  // }

  onSubmit(): void {
    console.log('Form submission started');
    console.log('Form valid:', this.createControllerServerForm.valid);
    console.log('Form value:', this.createControllerServerForm.value);

    if (this.createControllerServerForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;

      const formData = this.createControllerServerForm.value;
      console.log('Form data being processed:', formData);

      // Prepare the data according to your API structure
      const controllerServerData = {
        Name: formData.name,
        MaxControllers: parseInt(formData.maxControllers) || 0,
        MaxSensors: parseInt(formData.maxSensors) || 0,
        GeographicZone: formData.geographicZone || '',
        CommercialCondition: formData.commercialCondition || '',
        TriggerType: formData.triggerType || '',
        ActionType: formData.actionType || '',
        EventType: formData.eventType || '',
        Status: formData.status,
        // Make sure this is not null if a licence is selected
        IdLicence:
          formData.idlicence && formData.idlicence !== ''
            ? formData.idlicence
            : null,
      };

      console.log('Controller server data to be sent:', controllerServerData);

      if (this.isEditMode && this.controllerServer) {
        const updateData = {
          ...controllerServerData,
          Id: this.controllerServer.Id,
        };

        this.controllerServerService.update(updateData).subscribe({
          next: (response: any) => {
            console.log('Controller Server updated successfully:', response);
            this.controllerServerUpdated.emit(response);
            this.resetForm();
          },
          error: (error: any) => {
            console.error('Error updating controller server:', error);
            this.isSubmitting = false;
            alert('Erreur lors de la mise à jour du contrôleur serveur');
          },
        });
      } else {
        this.controllerServerService.post('', controllerServerData).subscribe({
          next: (response: any) => {
            console.log('Controller Server created successfully:', response);
            this.controllerServerCreated.emit(response);
            this.resetForm();
          },
          error: (error: any) => {
            console.error('Error creating controller server:', error);
            this.isSubmitting = false;
            alert('Erreur lors de la création du contrôleur serveur');
          },
        });
      }
    } else {
      console.log('Form is invalid, marking fields as touched');
      // Mark all fields as touched to show validation errors
      Object.keys(this.createControllerServerForm.controls).forEach((key) => {
        const control = this.createControllerServerForm.get(key);
        if (control && control.invalid) {
          console.log(`Field ${key} is invalid:`, control.errors);
        }
        control?.markAsTouched();
      });
    }
  }

  // Helper method to find and attach licence information to the response
  private findLicenceForResponse(response: any, licenceId: string): any {
    if (response.Licence) {
      return response.Licence; // Already has licence info
    }

    // Find the licence from the available licences
    const selectedLicence = this.licences.find(
      (licence) => (licence.Id ) === licenceId
    );

    if (selectedLicence) {
      return {
        Id: selectedLicence.Id,
        Name: selectedLicence.Name,
        Description: selectedLicence.Description,
      };
    }

    return null;
  }

  onCancel(): void {
    this.resetForm();
  }

  private resetForm(): void {
    this.createControllerServerForm.reset();
    this.initializeForm();
    this.isSubmitting = false;
    this.formClosed.emit();
  }

  // Helper method to check if a field has errors
  hasError(fieldName: string): boolean {
    const field = this.createControllerServerForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  // Helper method to get error message
  getErrorMessage(fieldName: string): string {
    const field = this.createControllerServerForm.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors['required']) {
        return `${this.getFieldLabel(fieldName)} est obligatoire`;
      }
      if (field.errors['minLength']) {
        return `${this.getFieldLabel(fieldName)} doit contenir au moins ${
          field.errors['minLength'].requiredLength
        } caractères`;
      }
      if (field.errors['min']) {
        return `${this.getFieldLabel(fieldName)} doit être supérieur à ${
          field.errors['min'].min - 1
        }`;
      }
      if (field.errors['max']) {
        return `${this.getFieldLabel(fieldName)} doit être inférieur à ${
          field.errors['max'].max + 1
        }`;
      }
      if (field.errors['pattern']) {
        return `${this.getFieldLabel(fieldName)} n'est pas valide`;
      }
    }
    return '';
  }

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      name: 'Le nom',
      maxSensors: 'Le nombre maximum de capteurs',
      maxControllers: 'Le nombre maximum de contrôleurs',
      commercialCondition: 'Les conditions commerciales',
      triggerType: 'Le type de déclencheur',
      geographicZone: 'La zone géographique',
      status: 'Le statut',
      idlicence: 'La licence',
      ipAddress: "L'adresse IP",
      port: 'Le port',
    };
    return labels[fieldName] || fieldName;
  }

  get formTitle(): string {
    return this.isEditMode
      ? 'Modifier le Contrôleur Serveur'
      : 'Ajouter un Contrôleur Serveur';
  }

  get submitButtonText(): string {
    if (this.isSubmitting) {
      return this.isEditMode ? 'Mise à jour...' : 'Création...';
    }
    return 'Enregistrer';
  }

  get submitButtonIcon(): string {
    if (this.isSubmitting) {
      return 'hourglass_empty';
    }
    return this.isEditMode ? 'update' : 'save';
  }
}