<!-- dashboard-site.component.html -->
<div class="dashboard-container">
  <!-- Header -->
  <div class="org-name">
    <h1>{{ currentSite.Name }}</h1>
    <p>{{ currentSite.Address }}</p>
  </div>

  <div class="dashboard-content">
    <!-- All Cards Section -->
    <div class="all-cards-section">
      <!-- Site metrics cards -->
      <div class="card" *ngFor="let metric of siteMetrics">
        <div class="card-header">
          <div class="card-icon">
            <i [class]="metric.icon" [style.color]="metric.iconColor"></i>
          </div>
          <div class="card-title">{{ metric.title }}</div>
        </div>
        <div class="card-content">
          <div class="card-amount">
            <span class="amount-text">{{ metric.amount }}</span>
          </div>
          <div class="card-progress">
            <div class="progress-bar">
              <div
                class="progress-fill"
                [style.width.%]="metric.progress"
                [style.background-color]="metric.color"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="charts-section">
      <!-- Top row with two side-by-side charts -->
      <div class="top-charts">
        <div class="chart-container">
          <canvas id="energyLineChart"></canvas>
        </div>
        <div class="chart-container">
          <canvas id="expenseChart"></canvas>
        </div>
      </div>

      <!-- Full-width chart below -->
      <div class="chart-container consommation-chart full-width-chart">
        <canvas #chartCanvas></canvas>
      </div>
    </div>

    <!-- Map Section -->
    <div class="map-container" *ngIf="currentSite.Latitude && currentSite.Longtitude">
      <div class="map-header">Localisation du Site</div>
      <div id="map" style="height: 500px; width: 100%"></div>
    </div>
  </div>
</div>
