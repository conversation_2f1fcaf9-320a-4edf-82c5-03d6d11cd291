import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'numberSpacing'
})
export class NumberSpacingPipe implements PipeTransform {

  transform(value: number | string): string {
    if (value === null || value === undefined) return '';
    
    // Remove all non-digit characters
    const cleanValue = value.toString().replace(/\D/g, '');

    // Format number with space every 3 digits from the right
    return cleanValue.replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
  }

}
