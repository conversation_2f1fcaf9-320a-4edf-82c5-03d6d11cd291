import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'numberSpacing'
})
export class NumberSpacingPipe implements PipeTransform {

  transform(value: number | string): string {
    if (value === null || value === undefined) return '';
    
    const strValue = value.toString();
    
    // Process only the numeric parts of the string, leaving alphabets intact
    return strValue.replace(/\d+/g, (num) => {
      // Add space every 3 digits from the right for each numeric sequence
      return num.replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
    });
  }

}
