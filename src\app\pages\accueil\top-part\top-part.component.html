<div class="row" *ngIf="organisations.length > 0">
  <div class="col-md-12">
    <h1><i class="material-icons title-icon">business</i> Organisations</h1>
  </div>
</div>
<div class="organisation-list">
  <div
    onkeypress=""
    class="category-card"
    *ngFor="let org of visibleOrganisations"
    (click)="onCardClick(org)"
  >
    <button
      class="details-button"
      (click)="onDetailsClick($event, org)"
      title="Voir tous les détails"
    >
      <i class="material-icons details-icon">bar_chart</i>
    </button>

    <div
      class="icon-container"
      [style.background]="
        'linear-gradient(135deg, ' +
        getColor(org) +
        '20, ' +
        getColor(org) +
        '40)'
      "
    >
      <ng-container *ngIf="hasLogo(org); else iconBlock">
        <img
          [src]="getLogoUrl(org)"
          [alt]="org.Nom + ' logo'"
          class="organisation-logo"
        />
      </ng-container>
      <ng-template #iconBlock>
        <i class="material-icons category-icon" [style.color]="getColor(org)">
          {{ getIcon(org) }}
        </i>
      </ng-template>
    </div>

    <div class="category-info">
      <h3 class="category-title">{{ org.Nom }}</h3>
      <p class="category-count">
        {{ getClientCount(org) }} Client<span *ngIf="getClientCount(org) > 1"
          >s</span
        >
      </p>
    </div>
  </div>
</div>
<div
  class="show-more-container"
  *ngIf="organisations.length > initialDisplayCount && organisations.length > 0"
>
  <button class="show-more-button" (click)="toggleCategoriesDisplay()">
    <span>{{ showAllCategories ? "Afficher moins" : "Afficher tout" }}</span>
    <i class="material-icons">
      {{ showAllCategories ? "expand_less" : "expand_more" }}
    </i>
  </button>
</div>
