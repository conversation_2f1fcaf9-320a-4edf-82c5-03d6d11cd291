import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AppliedRulesListComponent } from './applied-rules-list.component';

describe('AppliedRulesListComponent', () => {
  let component: AppliedRulesListComponent;
  let fixture: ComponentFixture<AppliedRulesListComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AppliedRulesListComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(AppliedRulesListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
