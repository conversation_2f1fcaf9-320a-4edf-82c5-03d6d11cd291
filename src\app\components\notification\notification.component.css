

.notification {
  position: fixed;
  top: calc(60px + 1rem); /* Adjust based on your header height */
  right: 2rem;
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  max-width: 400px;
  width: 90%;
  margin: 0;
}

.notification-content {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.notification-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.notification-icon.success {
  color: #4CAF50;
}

.notification-icon.error {
  color: #F44336;
}

.notification-text {
  flex: 1;
}

.notification-text h4 {
  font-size: 1.1rem;
  margin: 0 0 0.25rem 0;
  font-weight: 700;
}

.success-notification .notification-text h4 {
  color: #065f46;
}

.error-notification .notification-text h4 {
  color: #991b1b;
}

.notification-text p {
  font-size: 0.9rem;
  color: #374151;
  margin: 0;
  line-height: 1.5;
}

.close-notification-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  margin-left: auto;
  flex-shrink: 0;
}

.success-notification .close-notification-btn {
  background: #f0fdf4;
  color: #10b981;
}

.error-notification .close-notification-btn {
  background: #fef2f2;
  color: #ef4444;
}

.success-notification .close-notification-btn:hover {
  background: #dcfce7;
  color: #059669;
}

.error-notification .close-notification-btn:hover {
  background: #b82f2f;
  color: #dc2626;
}

.success-notification {
  border-left: 4px solid #10b981;
}

.error-notification {
  border-left: 4px solid #ef4444;
}