<div
  class="local-card clickable-card"
  (click)="onView()"
  tabindex="0"
  role="button"
  (keydown.enter)="onView()"
>
    <div class="card-image-container">
    <div class="image-container">
      <ng-container *ngIf="local.ImageLocal; else noImage">
        <img
          [src]="'data:image/jpeg;base64,' + local.ImageLocal"
          [alt]="local.Name"
          class="local-image"
        />
      </ng-container>
      <ng-template #noImage>
        <div class="no-image-container">
          <i class="material-icons">location_city</i>
        </div>
      </ng-template>
    </div>
  </div>

  <div class="card-header">
    <h3 class="local-title">{{ local.Name }}</h3>
    <div 
      class="local-type" 
      [attr.data-type]="local.TypeLocal?.Nom"
      *ngIf="local.TypeLocal?.Nom"
    >
      {{ local.TypeLocal?.Nom }}
    </div>
  </div>

  <div class="card-content">
    <div class="local-details">
      <div class="detail-item">
        <i class="material-icons">layers</i>
        <span>Étage {{ local.Floor }}</span>
      </div>
      <div class="detail-item">
        <i class="material-icons">people</i>
        <span>Capacité: {{ local.Capacity }} personnes</span>
      </div>
      <div class="detail-item">
        <i class="material-icons">sensors</i>
        <span>{{ capteurCount  }} équipements</span>
      </div>
    </div>
  </div>

  <div class="card-actions">
    <button class="btn btn-primary" (click)="onView(); $event.stopPropagation()">
      <i class="material-icons">visibility</i>
    </button>
    <button class="btn btn-accent" (click)="onEdit(); $event.stopPropagation()">
      <i class="material-icons">edit</i>
    </button>
    <button class="btn btn-danger" (click)="onDelete(); $event.stopPropagation()">
      <i class="material-icons">delete</i>
    </button>
  </div>
</div>