<div
  class="local-card clickable-card"
  (click)="onView()"
  tabindex="0"
  role="button"
  (keydown.enter)="onView()"
>
  <div class="card-header">
     <h3>  {{
    local.Name.length > 25
      ? local.Name.substring(0, 25) + '...' 
      : local.Name
  }} </h3> 
  </div>
  <div class="image-section">
    <img
      [src]="local.ImageLocal ? 'data:image/jpeg;base64,' + local.ImageLocal : 'assets/images/default-image.jpg'"
      alt="local"
      class="local-image"
    />
</div>
  
  <div class="card-content">
    <div class="info-row">
      <span class="info-label">Nome:</span>
      <span class="info-value">{{local.Name}}</span>
    </div>
    <div class="info-row">
      <span class="info-label">Étage:</span>
      <span class="info-value">{{local.Floor}}</span>
    </div>

    <div class="info-row">
      <span class="info-label">Capacité:</span>
      <span class="info-value">{{local.Capacity}} personnes</span>
    </div>
    <div class="info-row">
      <span class="info-label">Nombre de equipements:</span>
      <span class="info-value">{{local.SensorsCount}} </span>
    </div>
  </div>

  <div class="tags-container">
    <!-- <span class="tag" *ngFor="let tag of local.tags">{{tag.tag}}</span> -->
  </div>

  <div class="card-actions">
    <button class="action-btn" (click)="onView(); $event.stopPropagation()">
      <mat-icon>visibility</mat-icon>
    </button>
    <button class="action-btn" (click)="onEdit(); $event.stopPropagation()">
      <mat-icon>edit</mat-icon>
    </button>
    <button class="action-btn delete" (click)="onDelete(); $event.stopPropagation()">
      <mat-icon>delete</mat-icon>
    </button>
  </div>
</div>