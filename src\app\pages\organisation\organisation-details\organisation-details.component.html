<ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>
<div class="organisation-details-container">
  <ngx-spinner
    bdColor="rgba(4,9,5,0.8)"
    size="medium"
    color="#fff"
    type="ball-beat"
    [fullScreen]="true"
    ><p style="color: white">Loading...</p></ngx-spinner
  >

  <div class="breadcrumb-nav">
    <button class="back-button" (click)="goBack()">
      <i class="material-icons">arrow_back</i>
    </button>
    <span class="breadcrumb-text">Détails de {{ client?.Name }}</span>
  </div>

  <div class="content-container" *ngIf="client">
    <div class="info-section">
      <div class="logo-container">
          <button (click)="setDashButton()">
            <mat-icon
              aria-hidden="false"
              aria-label="Go to dashboard"
              fontIcon="dashboard"
            ></mat-icon>
          </button>
          <div class="logo-container">
            <img *ngIf="client.ClientLogo"
                  [src]="imageUrl"
                  [alt]="client.Name"
                  class="org-logo"
            />
            <div *ngIf="!client.ClientLogo" class="no-logo">
              <i class="material-icons">location_city</i>
            </div>
          </div>
            <!-- <div *ngIf="!client.ClientLogo" class="no-logo">
              <i class="material-icons">business</i>
            </div> -->
      </div>
      <div class="org-info-container" >
        <h2 class="org-name">{{ client.Name}}</h2>
        <!-- <p class="info-label"><i class="material-icons">business</i> Type: </p> -->
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">business</i> Type:
            </div>
            <div class="info-value">{{ client.Organisation?.Nom }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">email</i> Email:
            </div>
            <div class="info-value">{{ client.ContactEmail }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">phone</i> Téléphone:
            </div>
            <div class="info-value">{{ client.PhoneNumber }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">location_city</i> Ville:
            </div>
            <div class="info-value">{{ client.City }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">location_on</i> Adresse:
            </div>
            <div class="info-value">{{ client.Address }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">devices</i> Équipements Actifs:
            </div>
            <div class="info-value">{{ client.ActiveEquipment }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">devices_other</i> Équipements Inactifs:
            </div>
            <div class="info-value">{{ client.InactiveEquipment }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">assignment</i> Patente:
            </div>
            <div class="info-value">{{ client.Patente }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">branding_watermark</i> Marque:
            </div>
            <div class="info-value">{{ client.Brand }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">business</i> Secteur d'activité:
            </div>
            <div class="info-value">{{ client.BusinessSector }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">verified_user</i> Statut:
            </div>
            <div class="info-value">{{ client.ClientStatus }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">event</i> Date de création:
            </div>
            <div class="info-value">
              {{ formatDate(client.CompanyCreationDate) }}
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">people</i> Taille de l'entreprise:
            </div>
            <div class="info-value">{{ client.CompanySize }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">business_center</i> Type de
              l'entreprise:
            </div>
            <div class="info-value">{{ client.CompanyType }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">contact_mail</i> Adresse du contact:
            </div>
            <div class="info-value">{{ client.ContactAddress }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">person</i> Nom du contact:
            </div>
            <div class="info-value">{{ client.ContactName }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">public</i> Pays:
            </div>
            <div class="info-value">{{ client.Country }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">corporate_fare</i> Filiale:
            </div>
            <div class="info-value">{{ client.Filiale }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">receipt</i> ICE:
            </div>
            <div class="info-value">{{ client.ICE }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">description</i> IF:
            </div>
            <div class="info-value">{{ client.IF }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">article</i> RC:
            </div>
            <div class="info-value">{{ client.RC }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">gavel</i> Forme juridique:
            </div>
            <div class="info-value">{{ client.LegalForm }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">fingerprint</i> SIREN:
            </div>
            <div class="info-value">{{ client.SIREN }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">badge</i> SIRET:
            </div>
            <div class="info-value">{{ client.SIRET }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <i class="material-icons">map</i> Région:
            </div>
            <div class="info-value">{{ client.Region }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="sites-section" *ngIf="client">
  <div class="info-section" *ngIf="!dashButton">
    <app-dashboard-client></app-dashboard-client>
  </div>

  <!-- <div class="actions">
        <button class="create-button" (click)="toggleSection()">
          {{ showSites ? "Sites" : "Contrôleurs Serveurs" }}
        </button>
      </div> -->
  <div class="tabs-container">
      <button
      class="tab-button"
      [class.active]="activeTab === 'abonnement'"
      (click)="activeTab = 'abonnement'"
    >
      Abonnement
    </button>
    <button
      class="tab-button"
      [class.active]="activeTab === 'sites'"
      (click)="activeTab = 'sites'"
    >
      Sites
    </button>

    <button
      class="tab-button"
      [class.active]="activeTab === 'controllerServeurs'"
      (click)="activeTab = 'controllerServeurs'"
    >
      Contrôleurs Serveurs
    </button>

    <button
      class="tab-button"
      [class.active]="activeTab === 'controllers'"
      (click)="activeTab = 'controllers'"
    >
      Contrôleurs
    </button>

  
  </div>

  <div class="search-section" *ngIf="showSites">
    <div class="search-container">
      <input
        type="text"
        [(ngModel)]="searchParam"
        placeholder="Rechercher par nom..."
        class="search-input"
        (keyup.enter)="searchSites()"
      />
      <button class="search-button" (click)="searchSites()">
        <i class="material-icons">search</i>
      </button>
      <button class="clear-button" (click)="clearSearch()" *ngIf="searchParam">
        <i class="material-icons">clear</i>
      </button>
    </div>
  </div>
  <div
    class="sites-section"
    *ngIf="client && activeTab === 'sites'"
  >
    <div class="section-header">
      <h3 class="section-title">Sites</h3>

      <div class="view-controls">
        <button
          class="view-toggle-btn"
          [class.active]="viewMode === 'cards'"
          (click)="viewMode = 'cards'"
        >
          <i class="material-icons">grid_view</i>
        </button>
        <button
          class="view-toggle-btn"
          [class.active]="viewMode === 'table'"
          (click)="viewMode = 'table'"
        >
          <i class="material-icons">view_list</i>
        </button>
      </div>

      <button class="btn btn-primary add-site-btn" (click)="addNewSite()">
        <i class="material-icons">add</i> Ajouter un site
      </button>
    </div>

    <div class="loading-container" *ngIf="isLoadingSites">
      <div class="spinner"></div>
      <p>Chargement des sites...</p>
    </div>

    <!-- Card View -->
    <div
      class="sites-grid"
      *ngIf="!isLoadingSites && clientSites.length > 0 && viewMode === 'cards'"
    >
      <app-card-site
        *ngFor="let site of clientSites"
        [site]="site"
        (viewDetails)="viewSiteDetails($event)"
        (edit)="editSite($event)"
        (delete)="deleteSite($event)"
      >
      </app-card-site>
    </div>

    <!-- Card View Pagination -->
    <div
      class="card-pagination-container"
      *ngIf="viewMode === 'cards' && !isLoadingSites && clientSites.length > 0"
    >
      <mat-paginator
        [length]="totalSites"
        [pageSize]="pageSize"
        [pageIndex]="currentPage"
        [pageSizeOptions]="[5, 10, 25, 50]"
        (page)="onPageChange($event)"
        aria-label="Select page"
      >
      </mat-paginator>
    </div>

    <!-- Table View -->
    <div
      class="table-container"
      *ngIf="!isLoadingSites && clientSites.length > 0 && viewMode === 'table'"
    >
      <app-generic-table
        [data]="paginatedSites"
        [headers]="siteHeaders"
        [keys]="siteKeys"
        [actions]="['edit', 'view', 'delete']"
        (actionTriggered)="handleTableAction($event)"
      >
      </app-generic-table>

      <!-- Table Pagination -->
      <div class="pagination-container">
        <mat-paginator
          [length]="totalSites"
          [pageSize]="pageSize"
          [pageIndex]="currentPage"
          [pageSizeOptions]="[5, 10, 25, 50]"
          (page)="onPageChange($event)"
          aria-label="Select page"
        >
        </mat-paginator>
      </div>
    </div>

    <div
      class="no-sites-message"
      *ngIf="!isLoadingSites && clientSites.length === 0"
    >
      <i class="material-icons">location_off</i>
      <p>Aucun site trouvé pour cette organisation</p>
      <!-- <button class="btn btn-primary" (click)="addNewSite()">
            <i class="material-icons">add</i> Ajouter un site
          </button> -->
    </div>
  </div>
  <app-controller-server
    *ngIf="activeTab === 'controllerServeurs'"
    [controllerServeurs]="controllerServeurs"
    [clientId]="urlclientId"
    [licences]="client.Licences || []"
    (controllerServerDeleted)="handleControllerServerDeleted($event)"
  >
  </app-controller-server>

  <app-list *ngIf="activeTab === 'abonnement'"></app-list>

  <app-controller
    *ngIf="activeTab === 'controllers'"
    [clientId]="urlclientId"
  >
  </app-controller>
</div>

<!-- Site Form Popup -->
<div
  onkeypress=""
  class="popup-overlay"
  *ngIf="showSiteForm"
  (click)="closeSiteForm()"
>
  <div onkeydown="" class="popup-form" (click)="$event.stopPropagation()">
    <div class="popup-header">
      <h3>
        <mat-icon>{{ isEditMode ? "edit" : "add" }}</mat-icon>
        {{ isEditMode ? "Modifier le Site" : "Ajouter un Site" }}
      </h3>
      <button class="close-btn" (click)="closeSiteForm()">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <form
      [formGroup]="siteForm"
      (ngSubmit)="submitSiteForm()"
      class="site-form"
    >
      <!-- Validation errors section -->
      <div class="validation-errors" *ngIf="showValidationErrors">
        <ul class="validation-errors-list">
          <li *ngIf="siteForm.get('name')?.invalid">
            <mat-icon>error</mat-icon>
            Le nom est requis
          </li>
          <li *ngIf="siteForm.get('adress')?.invalid">
            <mat-icon>error</mat-icon>
            L'adresse est requise
          </li>
          <li *ngIf="siteForm.get('phoneNumber')?.invalid">
            <mat-icon>error</mat-icon>
            Le téléphone est requis
          </li>
          <li *ngIf="siteForm.get('contact')?.invalid">
            <mat-icon>error</mat-icon>
            Le contact est requis
          </li>
          <li *ngIf="siteForm.get('manager')?.invalid">
            <mat-icon>error</mat-icon>
            Le responsable est requis
          </li>
          <li *ngIf="siteForm.get('email')?.invalid">
            <mat-icon>error</mat-icon>
            Un email valide est requis
          </li>
          <li *ngIf="siteForm.get('status')?.invalid">
            <mat-icon>error</mat-icon>
            Le statut est requis
          </li>
        </ul>
      </div>

      <div class="form-grid">
        <!-- Required fields (always visible) -->
        <div class="form-group">
          <label for="name">Nom <span class="required">*</span></label>
          <input id="name" type="text" formControlName="name" required />
        </div>

        <div class="form-group">
          <label for="adress">Adresse <span class="required">*</span></label>
          <input id="adress" type="text" formControlName="adress" required />
        </div>

        <div class="form-group">
          <label for="phoneNumber"
            >Téléphone <span class="required">*</span></label
          >
          <input
            id="phoneNumber"
            type="text"
            formControlName="phoneNumber"
            required
          />
        </div>

        <div class="form-group">
          <label for="contact">Contact <span class="required">*</span></label>
          <input id="contact" type="text" formControlName="contact" required />
        </div>

        <div class="form-group">
          <label for="manager"
            >Responsable <span class="required">*</span></label
          >
          <input id="manager" type="text" formControlName="manager" required />
        </div>

        <div class="form-group">
          <label for="email">Email <span class="required">*</span></label>
          <input id="email" type="email" formControlName="email" required />
        </div>
        <!-- Show more button -->
        <div class="form-group" *ngIf="!showMoreFields">
          <button
            class="show-more-buttons"
            (click)="toggleShowMore()"
            [class.expanded]="showMoreFields"
          >
            <span>{{
              showMoreFields ? "Afficher Moins" : "Afficher Plus"
            }}</span>
            <i class="material-icons">
              {{ showMoreFields ? "expand_less" : "expand_more" }}
            </i>
          </button>
        </div>

        <!-- Optional fields (hidden by default) -->
        <div class="optional-fields" [class.expanded]="showMoreFields">
          <div class="form-group">
            <label for="status">Statut <span></span></label>
            <select id="status" formControlName="status">
              <option value="">Sélectionnez un statut</option>
              <option value="Actif">Actif</option>
              <option value="Inactif">Inactif</option>
              <option value="En maintenance">En maintenance</option>
              <option value="En installation">En Installation</option>
            </select>
          </div>
          <div class="form-group">
            <label for="addressComplement">Complément d'adresse</label>
            <input
              id="addressComplement"
              type="text"
              formControlName="addressComplement"
            />
          </div>

          <div class="form-group">
            <label for="editGrade">Grade</label>
            <select id="editGrade" formControlName="grade">
              <option value="A+++">A+++</option>
              <option value="A++">A++</option>
              <option value="A+">A+</option>
              <option value="A">A</option>
              <option value="B">B</option>
              <option value="C">C</option>
              <option value="D">D</option>
            </select>
          </div>

          <div class="form-group">
            <label for="latitude">Latitude</label>
            <input
              id="latitude"
              type="number"
              formControlName="latitude"
              step="any"
            />
          </div>

          <div class="form-group">
            <label for="longtitude">Longitude</label>
            <input
              id="longtitude"
              type="number"
              formControlName="longtitude"
              step="any"
            />
          </div>

          <div class="form-group">
            <label for="surface">Surface (m²)</label>
            <input
              id="surface"
              type="number"
              formControlName="surface"
              min="0"
            />
          </div>

          <div class="form-group full-width">
            <label for="description">Description</label>
            <textarea
              id="description"
              formControlName="description"
              rows="3"
            ></textarea>
          </div>

          <!-- Replace your current image input with this -->
          <div class="form-group full-width">
            <label for="image">Image</label>

            <!-- Display existing image in edit mode -->
            <div
              *ngIf="existingImageUrl && !imagePreview"
              class="image-preview"
            >
              <img
                [src]="existingImageUrl"
                alt="Current site"
                class="preview-image"
              />
              <button
                type="button"
                class="remove-image-btn"
                (click)="removeImage()"
              >
                <mat-icon>delete</mat-icon>
              </button>
            </div>

            <!-- Display new image preview when selected -->
            <div *ngIf="imagePreview" class="image-preview">
               <button
                type="button"
                class="remove-image-btn"
                (click)="removeImage()"
              >
                <mat-icon>delete</mat-icon>
              </button>
              <img
                [src]="imagePreview"
                alt="Selected image preview"
                class="preview-image"
              />
             
            </div>

            <input
              type="file"
              id="image"
              (change)="onImagesSelected($event)"
              accept="image/*"
              class="image-upload-input"
            />

            <label for="image" class="image-upload-label">
              <mat-icon>cloud_upload</mat-icon>
              {{
                imagePreview || existingImageUrl
                  ? "Changer l'image"
                  : "Choisir une image"
              }}
            </label>
          </div>
          <div class="form-group" *ngIf="showMoreFields">
            <button
              class="show-more-buttons"
              (click)="toggleShowMore()"
              [class.expanded]="showMoreFields"
            >
              <span>{{
                showMoreFields ? "Afficher Moins" : "Afficher Plus"
              }}</span>
              <i class="material-icons">
                {{ showMoreFields ? "expand_less" : "expand_more" }}
              </i>
            </button>
          </div>
        </div>
      </div>

      <div class="form-actions">
        <button type="button" class="btn-cancel" (click)="closeSiteForm()">
          Annuler
        </button>
        <button type="submit" class="btn-submit">
          {{ isEditMode ? "Enregistrer" : "Créer" }}
        </button>
      </div>
    </form>
  </div>
</div>
