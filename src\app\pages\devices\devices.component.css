/* ═══════════════════════════════════════════════════════════════
   DEVICE CONTROL CONTAINER - CLEANED & OPTIMIZED
   ═══════════════════════════════════════════════════════════════ */

.device-control-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  background-color: #fafafa;
  min-height: 100vh;
}

/* ─────────────────────────────────────────────────────────────
   HEADER SECTION
   ───────────────────────────────────────────────────────────── */

.header-section {
  margin-bottom: 32px;
}

.header-card {
  background: #ffffff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  border-left: 4px solid var(--primary);
}

.header-title {
  color: var(--primary);
  font-size: 28px;
  font-weight: 500;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title mat-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
}

/* ─────────────────────────────────────────────────────────────
   STATISTICS GRID
   ───────────────────────────────────────────────────────────── */

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
}

.stat-card {
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.stat-info .stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #212121;
  line-height: 1;
}

.stat-info .stat-label {
  font-size: 14px;
  color: #757575;
  font-weight: 400;
}

/* ─────────────────────────────────────────────────────────────
   FILTER SECTION
   ───────────────────────────────────────────────────────────── */

.filter-card {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 24px;
  margin-top: 10px;
}

.filter-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-end;
}

.input-label {
  font-size: 14px;
  font-weight: 500;
  color: #424242;
  margin-bottom: 6px;
}

.input-group,
.select-group {
  display: flex;
  flex-direction: column;
  flex: 1 1 250px;
  min-width: 220px;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  border: 1px solid #c7c7c7;
  border-radius: 6px;
  background: #fafafa;
  transition: border-color 0.2s, box-shadow 0.2s;
  height: 40px;
}

.input-wrapper:focus-within {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
}

.custom-input {
  flex: 1;
  border: none;
  background: transparent;
  padding: 8px 36px 8px 36px;
  font-size: 14px;
  outline: none;
  height: 100%;
}

.icon-prefix {
  position: absolute;
  left: 10px;
  color: #757575;
  pointer-events: none;
}

.icon-button {
  position: absolute;
  right: 4px;
  background: none;
  border: none;
  padding: 6px;
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #757575;
}

.icon-button:hover mat-icon {
  color: #f44336;
}

.select-wrapper {
  border: 1px solid #c7c7c7;
  border-radius: 6px;
  background: #fafafa;
  padding: 0 8px;
  height: 40px;
  display: flex;
  align-items: center;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.select-wrapper:focus-within {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
}

::ng-deep .mat-mdc-select {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}

.clear-filters-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 18px;
  font-weight: 500;
  border: 1px solid var(--primary);
  border-radius: 6px;
  background: #fff;
  color: var(--primary);
  white-space: nowrap;
  transition: 0.2s background, 0.2s color, 0.2s border-color;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.clear-filters-btn:hover {
  background-color: #e8f5e9;
  border-color: #388e3c;
  color: #388e3c;
}

/* ─────────────────────────────────────────────────────────────
   TOGGLE BUTTONS
   ───────────────────────────────────────────────────────────── */

.toggle-buttons-wrapper {
  display: flex;
  gap: 16px;
  width: 100%;
  margin : 10px 0px 10px 0px;
}

.toggle-card {
  flex: 1;
  margin: 10px 0px 10px 0px;
}

.toggle-card-content {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 12px;
  font-size: 16px;
  font-weight: 500;
  color: #424242;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px 20px;
  background: #ffffff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
  width: 50%;
  margin: 0px 0px 0px 0px;
}

.toggle-card-content:hover {
  background-color: #f5f5f5;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.toggle-card-icon mat-icon {
  font-size: 24px;
  color: var(--primary);
}

/* ─────────────────────────────────────────────────────────────
   SYNC ROW
   ───────────────────────────────────────────────────────────── */

.sync-row {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.sync-date {
  display: flex;
  align-self: center;
  font-size: 14px;
  color: #555;
}

.actualiser-btn {
  display: flex;
  align-self: center;
  border: 1px solid var(--primary) !important;
  color: var(--primary) !important;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 6px;
  gap: 8px;
  background-color: #fff;
  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

/* ─────────────────────────────────────────────────────────────
   DEVICES GRID
   ───────────────────────────────────────────────────────────── */

.devices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.device-card {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
  overflow: hidden;
  border: 1px solid #e0e0e0;
}

.device-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* ─────────────────────────────────────────────────────────────
   DEVICE HEADER
   ───────────────────────────────────────────────────────────── */

.device-header {
  display: flex;
  align-items: flex-start;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  position: relative;


}
.right-part {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;                
  margin-left: auto;
  margin-right: 12px;
}


.device-icon {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  background: var(--primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.device-icon mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

.device-name {
  font-size: 16px;
  font-weight: 500;
  color: #212121;
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.device-model {
  font-size: 12px;
  color: #757575;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.actif {
  display: flex;
  margin-left: auto; 
  text-align: center;

  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: white;
}


.actif.active {
  background-color: var(--primary); /* Green */
}

.actif.inactive {
  background-color: #f44336; /* Red */
}

.actif.enveille {
  background-color: #ff9800; /* Orange */
}

.actif.never {
  background-color: #9e9e9e; /* Grey */
}

/* ─────────────────────────────────────────────────────────────
   DEVICE CONTENT & SENSORS
   ───────────────────────────────────────────────────────────── */

.sensor-grid {
  display: grid;
  margin: 5px;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.sensor-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  text-align: center;
  min-height: 80px;
  transition: background-color 0.2s ease;
}

.sensor-item:hover {
  background: #f0f0f0;
}

.sensor-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  color: #424242;
  margin-bottom: 8px;
}

.sensor-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
}

.sensor-label {
  font-size: 11px;
  color: #757575;
  font-weight: 400;
  margin-bottom: 4px;
  line-height: 1.2;
}

.sensor-value {
  font-size: 14px;
  font-weight: 500;
  color: #212121;
  line-height: 1.2;
  word-break: break-word;
}

.sensor-unit {
  font-size: 12px;
  color: #757575;
  font-weight: 400;
  margin-left: 2px;
}

/* ─────────────────────────────────────────────────────────────
   CONTROL ITEMS
   ───────────────────────────────────────────────────────────── */

.control-item {
  margin-bottom: 12px;
}

.control-item:last-child {
  margin-bottom: 0;
}

.device-content {
  padding: 16px 20px;
}

.control-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.control-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #424242;
}

.control-value {
  font-size: 12px;
  color: #757575;
  margin-left: 8px;
}

.toggle_Alimentation {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  text-align: center;
  min-height: 80px;
  transition: background-color 0.2s ease;
  box-sizing: border-box;
}

.toggle_Alimentation:hover {
  background: #f0f0f0;
}

.toggle_Alimentation .control-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  min-height: 40px;
}

.control-label mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  color: #424242;
}

.label-text {
  font-size: 11px;
  color: #757575;
  font-weight: 400;
  line-height: 1.2;
}

.toggle_Alimentation mat-slide-toggle {
  margin-top: 8px;
  align-self: center;
}

/* ─────────────────────────────────────────────────────────────
   OPEN CLOSE STOP CONTROL
   ───────────────────────────────────────────────────────────── */
.custom-select-container {
  width: 110px;
  height: 28px;
  border-bottom: 1px solid grey;
  background-color: transparent;
  display: flex;
  align-items: center;
  margin-top: 15px;
  transition: border-color 0.2s ease;
}

/* Change border color on focus */
.custom-select-container.mat-focused {
  border-bottom: 1px solid var(--primary) !important;
}

::ng-deep .custom-select {
  background-color: transparent !important;
  font-size: 0.85rem;
  padding: 0 !important;
  height: 28px;
  line-height: 1.2;
  color: #333;
}

/* Trigger styling */
::ng-deep .custom-select .mat-select-trigger {
  background-color: transparent !important;
  padding: 0 !important;
  min-height: 28px;
  display: flex;
  align-items: center;
}

/* Dropdown panel */
::ng-deep .mat-select-panel {
  background-color: transparent !important;
  box-shadow: none !important;
}

/* Options */
::ng-deep .mat-option {
  background-color: transparent !important;
  color: #333 !important;
}

/* Arrow icon default */
::ng-deep .custom-select .mat-select-arrow {
  color: grey;
  transition: color 0.2s ease;
}

/* Arrow icon on focus */
::ng-deep .custom-select.mat-focused .mat-select-arrow {
  color: var(--primary) !important;
}




/* ─────────────────────────────────────────────────────────────
   BRIGHTNESS CONTROL
   ───────────────────────────────────────────────────────────── */

.brightness-control {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.brightness-control mat-icon {
  color: #757575;
  font-size: 22px;
  width: 22px;
  height: 22px;
  flex-shrink: 0;
}

.brightness-control mat-slider {
  flex: 1;
}

.brightness-value {
  font-size: 14px;
  font-weight: 500;
  color: #424242;
  min-width: 35px;
  text-align: right;
}

/* ─────────────────────────────────────────────────────────────
   TABLE VIEW
   ───────────────────────────────────────────────────────────── */

.devices-table {
  margin-top: 20px;
}

.table-container {
  border-radius: 8px;
  overflow: hidden;
}

.device-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-device-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  color: var(--primary);
}

.table-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* ─────────────────────────────────────────────────────────────
   ADDITIONAL INFO
   ───────────────────────────────────────────────────────────── */

.additional-info {
  margin-top: 12px;
  padding: 12px;
  background: #fafafa;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 13px;
  color: #424242;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  color: #757575;
}

/* ─────────────────────────────────────────────────────────────
   NO DATA & EMPTY STATES
   ───────────────────────────────────────────────────────────── */

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  color: #757575;
  font-size: 14px;
}

.no-data mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  margin-bottom: 8px;
  color: #bdbdbd;
}

/* ─────────────────────────────────────────────────────────────
   FILTER RESULTS
   ───────────────────────────────────────────────────────────── */

.filter-results {
  display: flex;
  justify-content: center;
  margin: 10px;
}

/* ─────────────────────────────────────────────────────────────
   PROGRESS BARS
   ───────────────────────────────────────────────────────────── */

/* Method 1: Target multiple possible selectors */
::ng-deep .bar-green .mdc-linear-progress__bar-inner,
::ng-deep .bar-green .mat-mdc-progress-bar-buffer-bar,
::ng-deep .bar-green .mat-mdc-progress-bar-primary-bar,
::ng-deep .bar-green .mdc-linear-progress__primary-bar {
  background-color: var(--primary) !important;
}

::ng-deep .bar-orange .mdc-linear-progress__bar-inner,
::ng-deep .bar-orange .mat-mdc-progress-bar-buffer-bar,
::ng-deep .bar-orange .mat-mdc-progress-bar-primary-bar,
::ng-deep .bar-orange .mdc-linear-progress__primary-bar {
  background-color: #ff9800 !important;
}

::ng-deep .bar-red .mdc-linear-progress__bar-inner,
::ng-deep .bar-red .mat-mdc-progress-bar-buffer-bar,
::ng-deep .bar-red .mat-mdc-progress-bar-primary-bar,
::ng-deep .bar-red .mdc-linear-progress__primary-bar {
  background-color: #f44336 !important;
}

/* Method 2: Use CSS variables (recommended for newer Angular Material) */
::ng-deep .bar-green {
  --mdc-linear-progress-active-indicator-color: var(--primary) !important;
}

::ng-deep .bar-orange {
  --mdc-linear-progress-active-indicator-color: #ff9800 !important;
}

::ng-deep .bar-red {
  --mdc-linear-progress-active-indicator-color: #f44336 !important;
}

/* ─────────────────────────────────────────────────────────────
   MATERIAL DESIGN OVERRIDES
   ───────────────────────────────────────────────────────────── */

.mat-mdc-form-field {
  font-size: 14px;
}

.mat-mdc-slide-toggle {
  --mdc-switch-selected-track-color: var(--primary);
  --mdc-switch-selected-handle-color: #ffffff;
}

.mat-mdc-slider {
  --mdc-slider-active-track-color: var(--primary);
  --mdc-slider-handle-color: var(--primary);
}

/* ─────────────────────────────────────────────────────────────
   RESPONSIVE DESIGN
   ───────────────────────────────────────────────────────────── */

@media (max-width: 768px) {
  .device-control-container {
    padding: 16px;
  }
  
  .devices-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
  }
  
  .header-title {
    font-size: 24px;
  }
  
  .sensor-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 8px;
  }
}

@media (max-width: 600px) {
  .filter-controls {
    flex-direction: column;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .sensor-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
/* --------------------------------filters Card ------------------ */
/* Component SCSS - Matching Device Control Theme */
.filters-container {
  margin-top: 10px;
  background: #ffffff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  transition: box-shadow 0.2s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
}

.filters-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 500;
  color: var(--primary);
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
  position: relative;

  .filters-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
    color: var(--primary);
  }
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

.filter-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
}

.select-container {
  position: relative;
  border: 1px solid #c7c7c7;
  border-radius: 6px;
  background-color: #ffffff;
  height: 40px;
  display: flex;
  align-items: center;
  transition: border-color 0.2s, box-shadow 0.2s;
  padding-right: 10px;


  &:focus-within {
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
  }

  .custom-select {
    display: flex !important;
    align-items: center !important;
    width: 98% !important;
    height: 100% !important;
    padding-left: 10px !important;
    
    .mat-select-trigger {
      height: 100%;
      display: flex;
      align-items: center;
      padding: 0 8px;
    }

    .mat-select-value {
      font-size: 14px;
      color: #212121;
    }

    .mat-select-placeholder {
      color: #757575;
    }

    .mat-select-arrow {
      padding-right: 2px;
    }

    &.mat-focused {
      .mat-select-arrow {
        color: var(--primary);
      }
    }
  }
}

.filters-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;

  @media (max-width: 768px) {
    justify-content: center;
  }
}

.clear-filters-button {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 10px 18px !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease !important;
  border: 1px solid var(--primary) !important;
  color: var(--primary) !important;
  background: #fff !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08) !important;

  &:hover {
    background-color: #e8f5e9;
    border-color: #388e3c;
    color: #388e3c;
  }

  &:active {
    transform: none;
  }

  mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

/* Angular Material Theme Overrides */
::ng-deep {
  .mat-form-field-appearance-outline {
    .mat-form-field-outline {
      border-radius: 6px;
    }

    .mat-form-field-outline-thick {
      color: var(--primary);
    }

    &.mat-focused {
      .mat-form-field-outline-thick {
        color: var(--primary);
      }
    }
  }

  .mat-select-panel {
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    
    .mat-option {
      font-size: 14px;
      line-height: 1.5;
      padding: 12px 16px;
      
      &:hover {
        background-color: #f5f5f5;
      }
      
      &.mat-selected {
        background-color: #e8f5e9;
        color: #388e3c;
        
        &:hover {
          background-color: #e1f5fe;
        }
      }
    }
  }

  .mat-stroked-button {
    .mat-button-ripple {
      border-radius: 6px;
    }
  }

  .mat-mdc-select {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
  }
}
.right-section{
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
}
