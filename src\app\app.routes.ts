import { Routes } from '@angular/router';
import { AuthGuard } from './core/services/auth.guard';
import { LayoutComponent } from './shared/layout/layout.component';
import { SiteDetailsComponent } from './pages/site-details/site-details.component';
import { LocalDetailsComponent } from './components/local-details/local-details.component';
const adminRoles = ['Admin', 'SuperAdmin'];
const allRoles = ['Admin', 'SuperAdmin', 'Enterprise'];

export const routes: Routes = [
  // ✅ Public Route: Login
  {
    path: 'login',
    loadComponent: () =>
      import('./pages/auth/login/login.component').then(
        (m) => m.LoginComponent
      ),
  },

  // ✅ Protected Routes with LayoutComponent
  {
    path: '',
    component: LayoutComponent,
    // canActivate: [AuthGuard],
    children: [
      {
        path: 'organisation-management',
        loadComponent: () =>
          import(
            './pages/organisation/organisation-management/organisation-management.component'
          ).then((m) => m.OrganisationManagementComponent),
        // data: { roles: allRoles },
      },
      {
        path: 'licence',
        loadComponent: () =>
          import('./pages/licence/licence.component').then((m) => m.LicenceComponent),
      },
      {
        path: 'site-management',
        loadComponent: () =>
          import(
            './pages/sites/site-management/site-management.component'
          ).then((m) => m.SiteManagementComponent),
        // data: { roles: allRoles },
      },
      {
        path: 'local-management',
        loadComponent: () =>
          import(
            './pages/local-parametrage/local-management/local-management.component'
          ).then((m) => m.LocalManagementComponent),
        // data: { roles: allRoles },
      },
      {
        path: 'sensor-data',
        loadComponent: () =>
          import('./pages/sensor-data-table/sensor-data-table.component').then(
            (m) => m.SensorDataTableComponent
          ),
        // data: { roles: allRoles },
      },
      {
        path: 'subscriptions', // Changed from 'subscription' to 'subscriptions'
        loadComponent: () =>
          import(
            './pages/abonnement/admin-subscription-dashboard/admin-subscription-dashboard.component'
          ).then((m) => m.AdminSubscriptionDashboardComponent),
        // data: { roles: adminRoles }, // Restrict to admin roles only since this is an admin dashboard
      },
      {
        path: 'devices',
        loadComponent: () =>
          import('./pages/devices/devices.component').then(
            (m) => m.DeviceControlComponent
          ),
        // data: { roles: adminRoles },
      },

      {
        path: 'iot',
        loadComponent: () =>
          import('./pages/iot/iot.component').then((m) => m.IotComponent),
      },
      {
        path: 'projects',
        loadComponent: () =>
          import('./pages/projects-list/projects-list.component').then(
            (m) => m.ProjectsListComponent
          ),
      },
      {
        path: 'projects/add',
        loadComponent: () =>
          import('./pages/projects/project-form/project-form.component').then(
            (m) => m.ProjectFormComponent
          ),
      },
      {
        path: 'projects/update/:id',
        loadComponent: () =>
          import('./pages/projects/project-form/project-form.component').then(
            (m) => m.ProjectFormComponent
          ),
      },
      {
        path: 'projects/delete/:id',
        loadComponent: () =>
          import(
            './pages/projects/project-delete/project-delete.component'
          ).then((m) => m.ProjectDeleteComponent),
      },

      {
        path: 'generator-regles-ia',
        loadComponent: () =>
          import(
            './pages/generator-regles-ia/generator-regles-ia.component'
          ).then((m) => m.GeneratorReglesIaComponent),
        // data: { roles: adminRoles },
      },
      {
        path: 'accounts',
        loadComponent: () =>
          import(
            './pages/accounts/account-management/account-management.component'
          ).then((m) => m.AccountManagementComponent),
        // data: { roles: adminRoles },
      },
      {
        path: 'site-details/:id',
        loadComponent: () =>
          import('./pages/site-details/site-details.component').then(
            (m) => m.SiteDetailsComponent
          ),
      },
      {
        path: 'energy-report',
        loadComponent: () =>
          import(
            './pages/energy-report-generator/energy-report-generator.component'
          ).then((m) => m.EnergyReportGeneratorComponent),
      },
      {
        path: 'site-locals/:siteId', // Add siteId parameter
        loadComponent: () =>
          import(
            './pages/Locals/local-management/local-management.component'
          ).then((m) => m.LocalManagementComponent),
        // data: { roles: allRoles },
      },
      {
        path: 'register-enterprise',
        loadComponent: () =>
          import(
            './pages/auth/register-enterprise/register-enterprise.component'
          ).then((m) => m.RegisterEnterpriseComponent),
        // data: { roles: adminRoles },
      },
      {
        path: 'register-admin',
        loadComponent: () =>
          import('./pages/auth/register-admin/register-admin.component').then(
            (m) => m.RegisterAdminComponent
          ),
        // data: { roles: ['SuperAdmin'] },
      },
      {
        path: 'organisations',
        loadComponent: () =>
          import(
            './pages/organisation/organisations/organisations.component'
          ).then((m) => m.OrganisationsComponent),
      },
      {
        path: 'organisation-details/:id',
        loadComponent: () =>
          import(
            './pages/organisation/organisation-details/organisation-details.component'
          ).then((m) => m.OrganisationDetailsComponent),
      },
      {
        path: 'organisations/edit/:id',
        loadComponent: () =>
          import(
            './pages/organisation/organisation-edit/organisation-edit.component'
          ).then((m) => m.OrganisationEditComponent),
      },
      {
        path: 'reglesIA',
        loadComponent: () =>
          import('./pages/regles-ia/regles-ia.component').then(
            (m) => m.ReglesIAComponent
          ),
      },
      {
        path: 'type-organisations/:type',
        loadComponent: () =>
          import(
            './pages/organisation/clients-list/clients-list/clients-list.component'
          ).then((m) => m.ClientsListComponent),
      },
      {
        path: 'installCapteur',
        loadComponent: () =>
          import(
            './pages/install-caplteur/install-caplteur.component'
          ).then((m) => m.InstallCaplteurComponent),
      },
      {
        path: 'planeditor',
        loadComponent: () =>
          import(
            './pages/plan-editor/plan-editor.component'
          ).then((m) => m.PlanEditorComponent),
      },
      {
        path: 'accueil',
        loadComponent: () =>
          import(
            './pages/accueil/accueil.component'
          ).then((m) => m.AccueilComponent),
      },
      {
        path: 'login',
        loadComponent: () =>
          import(
            './pages/login-page/login-page.component'
            ).then((m) => m.LoginPageComponent),
        // data: { roles: allRoles },
      },
      {
        path: '',
        redirectTo: 'accueil',
        pathMatch: 'full',
      },
    ],
  },

  {
    path: '**',
    redirectTo: '/accueil',
  },
];
