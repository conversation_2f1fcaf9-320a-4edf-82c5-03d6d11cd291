import { Routes } from '@angular/router';
import { AuthGuard } from './core/services/auth.guard';
import { LayoutComponent } from './shared/layout/layout.component';
import { SiteDetailsComponent } from './pages/site-details/site-details.component';
import { LocalDetailsComponent } from './components/local-details/local-details.component';

// Define role constants for better maintainability
const ADMIN_ROLES = ['ADMINISTRATEUR', 'SUPERADMIN'];
const CLIENT_ROLES = ['CLIENT'];
const ALL_ROLES = ['ADMINISTRATEUR', 'SUPERADMIN', 'CLIENT', 'INTEGRATEUR'];
const ADMIN_ONLY = ['ADMINISTRATEUR', 'SUPERADMIN'];

export const routes: Routes = [
 {
  path: 'login',
  loadComponent: () =>
    import('./pages/auth/login/login.component').then(
      (m) => m.LoginComponent
    ),
  canActivate: [AuthGuard] // Now using the same AuthGuard
},

  {
    path: '',
    component: LayoutComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: 'accueil',
        loadComponent: () =>
          import('./pages/accueil/accueil.component').then(
            (m) => m.AccueilComponent
          ),
        data: { roles: ADMIN_ONLY } // All authenticated users can access dashboard
      },
      {
        path: 'facture',
        loadComponent: () =>
          import('./pages/facture/facture.component').then(
            (m) => m.FactureComponent
          ),
        data: { roles: ADMIN_ONLY } // Only admins can access billing
      },
      {
        path: 'licence',
        loadComponent: () =>
          import('./pages/licence/subscription-table/subscription-table.component').then(
            (m) => m.SubscriptionTableComponent
          ),
        data: { roles: ADMIN_ONLY } // Only admins can manage licenses
      },
      {
        path: 'abonement-new',
        loadComponent: () =>
          import('./pages/licence/licence-card-new/licence-card-new.component').then(
            (m) => m.LicenceCardNewComponent
          ),
        data: { roles: ADMIN_ONLY } // Only admins can create new subscriptions
      },
      {
        path: 'abonnement-modifie/:id',
        loadComponent: () =>
          import('./pages/licence/licence-cards-edit/licence-cards-edit.component').then(
            (m) => m.LicenceCardsEditComponent
          ),
        data: { roles: ADMIN_ONLY } // Only admins can edit subscriptions
      },
      // Alias for possible typo (optional)
      {
        path: 'abonement-modifie/:id',
        redirectTo: 'abonnement-modifie/:id'
      },
      {
        path: 'licence-liste',
        loadComponent: () =>
          import('./pages/licence/liste/liste.component').then(
            (m) => m.ListeComponent
          ),
        data: { roles: ADMIN_ONLY } // Only admins can view license list
      },
      // {
      //   path: 'register',
      //   loadComponent: () => import('./pages/auth/login/register/register.component')
      //   .then(m => m.RegisterComponent)
      // },
      {
        path: 'organisation-management',
        loadComponent: () =>
          import(
            './pages/organisation/organisation-management/organisation-management.component'
          ).then((m) => m.OrganisationManagementComponent),
        data: { roles: ADMIN_ONLY } // Only admins can manage organizations
      },
      {
        path: 'site-management',
        loadComponent: () =>
          import(
            './pages/sites/site-management/site-management.component'
          ).then((m) => m.SiteManagementComponent),
        data: { roles: ADMIN_ONLY } // Only admins can manage sites
      },
      {
        path: 'local-management',
        loadComponent: () =>
          import(
            './pages/local-parametrage/local-management/local-management.component'
          ).then((m) => m.LocalManagementComponent),
        data: { roles: ALL_ROLES } // All users can view local management
      },
      {
        path: 'controllers',
        loadComponent: () =>
          import(
            './pages/parametrage/controllers/c-p-list/c-p-list.component'
          ).then((m) => m.CPListComponent),
        data: { roles: ALL_ROLES } // All users can view controllers
      },
      {
        path: 'sensor-data',
        loadComponent: () =>
          import('./pages/sensor-data-table/sensor-data-table.component').then(
            (m) => m.SensorDataTableComponent
          ),
        data: { roles: ALL_ROLES } // All users can view sensor data
      },
      {
        path: 'subscriptions', // Changed from 'subscription' to 'subscriptions'
        loadComponent: () =>
          import(
            './pages/abonnement/admin-subscription-dashboard/admin-subscription-dashboard.component'
          ).then((m) => m.AdminSubscriptionDashboardComponent),
        data: { roles: ADMIN_ONLY } // Restrict to admin roles only since this is an admin dashboard
      },
      {
        path: 'devices',
        loadComponent: () =>
          import('./pages/devices/devices.component').then(
            (m) => m.DeviceControlComponent
          ),
        data: { roles: ADMIN_ONLY } // Only admins can control devices
      },

      {
        path: 'dashsite',
        loadComponent: () =>
          import(
            './pages/site-dashboard-page/site-dashboard-page.component'
          ).then((m) => m.SiteDashboardPageComponent),
        data: { roles: ALL_ROLES } // All users can view site dashboard
      },

      {
        path: 'iot',
        loadComponent: () =>
          import('./pages/iot/iot.component').then((m) => m.IotComponent),
        data: { roles: ALL_ROLES } // All users can view IoT data
      },
      // {
      //   path: 'accounts',
      //   loadComponent: () =>
      //     import(
      //       './pages/accounts/account-management/account-management.component'
      //     ).then((m) => m.AccountManagementComponent),
      //   // data: { roles: adminRoles },
      // },
      {
        path: 'local-details/:id',
        loadComponent: () =>
          import('./pages/site-details/site-details.component').then(
            (m) => m.SiteDetailsComponent
          ),
        data: { roles: ALL_ROLES } // All users can view local details
      },
      {
        path: 'energy-report',
        loadComponent: () =>
          import(
            './pages/energy-report-generator/energy-report-generator.component'
          ).then((m) => m.EnergyReportGeneratorComponent),
        data: { roles: ALL_ROLES } // All users can generate energy reports
      },
      {
        path: 'site-locals/:siteId', // Add siteId parameter
        loadComponent: () =>
          import(
            './pages/Locals/local-management/local-management.component'
          ).then((m) => m.LocalManagementComponent),
        data: { roles: ALL_ROLES } // All users can view site locals
      },
      // {
      //   path: 'register-enterprise',
      //   loadComponent: () =>
      //     import(
      //       './pages/auth/register-enterprise/register-enterprise.component'
      //     ).then((m) => m.RegisterEnterpriseComponent),

      // },
      // {
      //   path: 'register-admin',
      //   loadComponent: () =>
      //     import('./pages/auth/register-admin/register-admin.component').then(
      //       (m) => m.RegisterAdminComponent
      //     ),
      // },
      // {
      //   path: 'organisations',
      //   loadComponent: () =>
      //     import(
      //       './pages/organisation/organisations/organisations.component'
      //     ).then((m) => m.OrganisationsComponent),
      // },
      {
        path: 'organisation-details/:id',
        loadComponent: () =>
          import(
            './pages/organisation/organisation-details/organisation-details.component'
          ).then((m) => m.OrganisationDetailsComponent),
        data: { roles: ALL_ROLES } // All users can view organization details (CLIENT will see their own)
      },
      {
        path: 'organisation-statistics/:id',
        loadComponent: () =>
          import(
            './components/organisation-statistics/organisation-statistics.component'
          ).then((m) => m.OrganisationStatisticsComponent),
        data: { roles: ALL_ROLES } // All users can view organization statistics
      },
      {
        path: 'organisations/edit/:id',
        loadComponent: () =>
          import(
            './pages/organisation/organisation-edit/organisation-edit.component'
          ).then((m) => m.OrganisationEditComponent),
        data: { roles: ADMIN_ONLY } // Only admins can edit organizations
      },
      {
        path: 'reglesIA',
        loadComponent: () =>
          import('./pages/regles-ia/regles-ia.component').then(
            (m) => m.ReglesIAComponent
          ),
        data: { roles: ADMIN_ONLY } // Only admins can manage AI rules
      },
      {
        path: 'app-rule-generation',
        loadComponent: () =>
          import('./pages/rule-generation/rule-generation.component').then(
            (m) => m.RuleGenerationComponent
          ),
        data: { roles: ADMIN_ONLY } // Only admins can generate rules
      },
      {
        path: 'type-organisations/:type',
        loadComponent: () =>
          import(
            './pages/organisation/clients-list/clients-list/clients-list.component'
          ).then((m) => m.ClientsListComponent),
        data: { roles: ADMIN_ONLY } // Only admins can view client lists by type
      },
      {
        path: 'installCapteur',
        loadComponent: () =>
          import('./pages/install-caplteur/install-caplteur.component').then(
            (m) => m.InstallCaplteurComponent
          ),
        data: { roles: ALL_ROLES } // All users can install sensors
      },
      {
        path: 'planeditor',
        loadComponent: () =>
          import('./pages/plan-editor/plan-editor.component').then(
            (m) => m.PlanEditorComponent
          ),
        data: { roles: ALL_ROLES } // All users can edit plans
      },
      {
        path: 'user-management',
        loadComponent: () =>
          import('./pages/user-management/user-management.component').then(
            (m) => m.UserManagementComponent
          ),
        data: { roles: ADMIN_ONLY }
      },
      {
        path: 'tag-management',
        loadComponent: () =>
          import('./pages/tag-management/tag-management.component').then(
            (m) => m.TagManagementComponent
          ),
        data: { roles: ADMIN_ONLY } // Only admins can manage tags
      },
      {
        path: '',
        redirectTo: 'accueil',
        pathMatch: 'full',
      },
    ],
  },

  {
    path: '**',
    redirectTo: '/login',
  },
];
