.local-details-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Loading styles */
.loading-text { 
  font-size: 20px; 
  font-weight: 500; 
  letter-spacing: 0.5px; 
}

.dots { 
  animation: dotAnimation 1.4s infinite; 
  display: inline-block; 
}

@keyframes dotAnimation { 
  0% { opacity: 0; } 
  50% { opacity: 1; } 
  100% { opacity: 0; } 
}

.breadcrumb-nav {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  padding: 16px;
}

.back-button {
  background: #f5f5f5;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: #e0e0e0;
  transform: translateX(-3px);
}

.back-button .material-icons {
  color: #555;
  font-size: 24px;
}

.breadcrumb-text {
  font-size: 16px;
  color: #666;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.content-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.info-section {
  display: flex;
  margin-bottom: 30px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 25px;
  animation: fadeIn 0.5s ease-out;
}

.site-images-container {
  flex: 0 0 300px;
  margin-right: 30px;
  display: flex;
  flex-direction: column;
}

.logo-container {
  width: 300px;
  height: 280px;
  border-radius: 8px;
  overflow: hidden;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.site-logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background-color: #f5f5f5;
}

.site-logo[src$="default-image.jpg"] {
  object-fit: contain;
  padding: 20px;
  background: linear-gradient(135deg, #f0f8f0, #e8f5e8);
}

.architecture-image {
  margin-top: 10px;
  border-radius: 8px;
  overflow: hidden;
}

.architecture-image img {
  width: 100%;
  height: auto;
  object-fit: contain;
  background-color: #f5f5f5;
}

.architecture-image img[src$="default-image.jpg"] {
  max-height: 200px;
  padding: 20px;
  background: linear-gradient(135deg, #f0f8f0, #e8f5e8);
}

.no-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #f0f8f0;
}

.no-logo .material-icons {
  font-size: 80px;
  color: #9e9e9e;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.no-logo:hover .material-icons {
  opacity: 0.8;
  transform: scale(1.1);
}

/* --- Modernized Stats Section --- */
.site-stats {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px 0 rgba(76, 175, 80, 0.07), 0 1.5px 6px 0 rgba(0,0,0,0.04);
  padding: 0;
  margin-top: 0;
  overflow: hidden;
  margin-bottom: 10px;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 18px 0 12px 0;
  background: transparent;
  transition: background 0.2s;
  border-right: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-right: none;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #388e3c;
  margin-bottom: 2px;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(76,175,80,0.08);
}

.stat-label {
  font-size: 0.85rem;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
  margin-top: 2px;
  letter-spacing: 0.7px;
}

/* Add a subtle hover effect for interactivity */
.stat-item:hover {
  background: #f5fef7;
}

/* Responsive tweaks */
@media (max-width: 768px) {
  .site-stats {
    flex-direction: column;
    box-shadow: 0 1px 6px 0 rgba(76, 175, 80, 0.07), 0 1px 3px 0 rgba(0,0,0,0.04);
  }
  .stat-item {
    border-right: none;
    border-bottom: 1px solid #f0f0f0;
    padding: 14px 0 10px 0;
  }
  .stat-item:last-child {
    border-bottom: none;
  }
}

.site-info-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.site-header {
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #e2e8f0;
}

.site-name {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: #4CAF50;
  line-height: 1.2;
}

.site-type {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  background-color: #e8f5e9;
  color: #2e7d32;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  flex: 1;
  margin-top: 24px;
}

.info-column {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.info-item {
  padding: 16px 0;
  transition: all 0.3s ease;
  border-left: none;
  background: transparent;
}

.info-item:not(:last-child) {
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 16px;
}

.info-item:hover {
  background: transparent;
  transform: none;
  box-shadow: none;
}

.info-label {
  display: flex;
  align-items: center;
  color: #64748b;
  font-weight: 500;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-label mat-icon,
.info-label .material-icons {
  margin-right: 8px;
  color: #4CAF50;
  font-size: 20px;
}

.info-value {
  color: #1e293b;
  font-size: 14px;
  line-height: 1.6;
  font-weight: 400;
}

.info-value strong {
  color: #64748b;
  font-weight: 500;
}

.description-text {
  white-space: pre-line;
}

.site-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  color: #64748b;
  font-size: 0.9rem;
}

.site-info mat-icon,
.site-info .material-icons {
  color: #4CAF50;
  font-size: 20px;
  width: 20px;
  height: 20px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 1024px) {
  .info-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .info-section {
    flex-direction: column;
    padding: 25px;
  }

  .site-images-container {
    margin-right: 0;
    margin-bottom: 20px;
    align-self: center;
  }

  .logo-container {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
  }

  .site-stats {
    max-width: 400px;
    width: 100%;
  }

  .site-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .site-type {
    margin-left: 0;
  }
}

@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .local-details-container {
    padding: 12px;
  }

  .info-section {
    padding: 20px;
  }

  .site-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .site-type {
    margin-top: 10px;
    margin-left: 0;
  }

  .site-name {
    font-size: 24px;
  }

  .logo-container {
    height: 200px;
  }

  .site-images-container {
    flex: none;
  }

  .stat-value {
    font-size: 24px;
  }

  .info-item {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .site-name {
    font-size: 20px;
  }

  .breadcrumb-text {
    font-size: 14px;
  }

  .info-item {
    padding: 14px;
  }

  .logo-container {
    height: 180px;
  }

  .stat-value {
    font-size: 20px;
  }

  .stat-label {
    font-size: 10px;
  }
}

/* Action Section Styling */
.action-section {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin: 25px 0 30px 0;
  padding: 0;
}

/* Add Rule Button Styling */
.add-rule-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 14px 24px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.25), 0 2px 4px rgba(76, 175, 80, 0.15);
  letter-spacing: 0.5px;
  text-transform: none;
  min-height: 48px;
  white-space: nowrap;
}

.add-rule-btn:hover {
  background: linear-gradient(135deg, #45a049, #3d8b40);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(76, 175, 80, 0.35), 0 4px 8px rgba(76, 175, 80, 0.2);
}

.add-rule-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.25), 0 2px 4px rgba(76, 175, 80, 0.15);
}

.add-rule-btn:focus {
  outline: none;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.25), 0 2px 4px rgba(76, 175, 80, 0.15), 0 0 0 3px rgba(76, 175, 80, 0.1);
}

/* Material Icon inside button */
.add-rule-btn mat-icon,
.add-rule-btn .material-icons {
  font-size: 20px;
  width: 20px;
  height: 20px;
  margin-right: 0;
}

/* Alternative positioning - if you want it centered instead */
.action-section.centered {
  justify-content: center;
}

/* Alternative positioning - if you want it on the left */
.action-section.left-aligned {
  justify-content: flex-start;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .action-section {
    justify-content: center;
    margin: 20px 0 25px 0;
  }
  
  .add-rule-btn {
    padding: 12px 20px;
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .action-section {
    margin: 15px 0 20px 0;
  }
  
  .add-rule-btn {
    padding: 12px 18px;
    font-size: 14px;
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .add-rule-btn {
    padding: 14px 16px;
    font-size: 13px;
  }
}

/* Architecture Plan Section */
.architecture-plan-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 20px 0;
  padding: 20px;
}

.plan-header {
  margin-bottom: 16px;
}

.plan-header h3 {
  color: var(--text-color);
  font-size: 18px;
  font-weight: 500;
  margin: 0;
}

.canvas-container {
  width: 100%;
  height: 500px;
  overflow: hidden;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.12);
}

canvas {
  width: 100%;
  height: 100%;
}