/* local-details.component.css - Fixed version */
.local-details-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Loading styles */
.loading-text { 
  font-size: 20px; 
  font-weight: 500; 
  letter-spacing: 0.5px; 
}

.dots { 
  animation: dotAnimation 1.4s infinite; 
  display: inline-block; 
}

@keyframes dotAnimation { 
  0% { opacity: 0; } 
  50% { opacity: 1; } 
  100% { opacity: 0; } 
}

.breadcrumb-nav {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  padding: 16px;
}

.back-button {
  background: #f5f5f5;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: #e0e0e0;
  transform: translateX(-3px);
}

.back-button .material-icons {
  color: #555;
  font-size: 24px;
}

.breadcrumb-text {
  font-size: 16px;
  color: #666;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.content-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.info-section {
  display: flex;
  margin-bottom: 30px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 25px;
  animation: fadeIn 0.5s ease-out;
}

.site-images-container {
  flex: 0 0 300px;
  margin-right: 30px;
  display: flex;
  flex-direction: column;
}

.logo-container {
  width: 300px;
  height: 280px;
  border-radius: 8px;
  overflow: hidden;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.site-logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background-color: #f5f5f5;
}

.site-logo[src$="default-image.jpg"] {
  object-fit: contain;
  padding: 20px;
  background: linear-gradient(135deg, #f0f8f0, #e8f5e8);
}

.architecture-image {
  margin-top: 10px;
  border-radius: 8px;
  overflow: hidden;
}

.architecture-image img {
  width: 100%;
  height: auto;
  object-fit: contain;
  background-color: #f5f5f5;
}

.architecture-image img[src$="default-image.jpg"] {
  max-height: 200px;
  padding: 20px;
  background: linear-gradient(135deg, #f0f8f0, #e8f5e8);
}

.no-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #f0f8f0;
}

.no-logo .material-icons {
  font-size: 80px;
  color: #9e9e9e;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.no-logo:hover .material-icons {
  opacity: 0.8;
  transform: scale(1.1);
}

/* --- Modernized Stats Section --- */
.site-stats {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px 0 rgba(76, 175, 80, 0.07), 0 1.5px 6px 0 rgba(0,0,0,0.04);
  padding: 0;
  margin-top: 0;
  overflow: hidden;
  margin-bottom: 10px;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 18px 0 12px 0;
  background: transparent;
  transition: background 0.2s;
  border-right: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-right: none;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #388e3c;
  margin-bottom: 2px;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(76,175,80,0.08);
}

.stat-label {
  font-size: 0.85rem;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
  margin-top: 2px;
  letter-spacing: 0.7px;
}

/* Add a subtle hover effect for interactivity */
.stat-item:hover {
  background: #f5fef7;
}

/* Responsive tweaks */
@media (max-width: 768px) {
  .site-stats {
    flex-direction: column;
    box-shadow: 0 1px 6px 0 rgba(76, 175, 80, 0.07), 0 1px 3px 0 rgba(0,0,0,0.04);
  }
  .stat-item {
    border-right: none;
    border-bottom: 1px solid #f0f0f0;
    padding: 14px 0 10px 0;
  }
  .stat-item:last-child {
    border-bottom: none;
  }
}

.site-info-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.site-header {
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #e2e8f0;
}

.site-name {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: #4CAF50;
  line-height: 1.2;
}

.site-type {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  background-color: #e8f5e9;
  color: #2e7d32;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  flex: 1;
  margin-top: 24px;
}

.info-column {
  display: flex;
  flex-direction: column;
  gap: 24px; /* Add consistent gap between items */
}

.info-item {
  padding: 0; /* Remove padding */
  transition: all 0.3s ease;
  border: none; /* Remove any borders */
  background: transparent;
}

/* Remove border-bottom and margin-bottom styles completely */
.info-item:hover {
  background: transparent;
  transform: none;
  box-shadow: none;
}

.info-label {
  display: flex;
  align-items: center;
  color: #64748b;
  font-weight: 500;
  margin-bottom: 8px;
  font-size: 14px;
  gap: 8px;
}

.info-label mat-icon:first-child {
  color: #4CAF50;
  font-size: 18px;
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.info-label mat-icon,
.info-label .material-icons {
  margin-right: 8px;
  color: #4CAF50;
  font-size: 20px;
}

.info-value {
  color: #1e293b;
  font-size: 14px;
  line-height: 1.6;
  font-weight: 400;
  padding-left: 28px; /* Align with the first icon */
  margin-top: -4px; /* Better vertical alignment */
}

.info-value strong {
  color: #64748b;
  font-weight: 500;
}

.description-text {
  white-space: pre-line;
}

/* Beautiful Sensor Info Button - Modern Glass Design */
.sensor-info-btn {
  margin-left: 10px;
  width: 28px;
  height: 28px;
  min-width: 28px;
  min-height: 28px;
  backdrop-filter: blur(10px);
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  cursor: pointer;
  vertical-align: middle;
  position: relative;
  overflow: hidden;
}

.sensor-info-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12px;
}

.sensor-info-btn:hover {
  transform: translateY(-2px) scale(1.02);
}

.sensor-info-btn:hover::before {
  opacity: 1;
}

.sensor-info-btn mat-icon {
  color: #64748b;
  font-size: 16px;
  width: 16px;
  height: 16px;
  line-height: 16px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  position: relative;
}

.sensor-info-btn:hover mat-icon {
  color: #4CAF50;
  transform: rotate(15deg) scale(1.1);
}


.sensor-info-btn.active mat-icon {
  color: #4CAF50;
  transform: rotate(15deg);
}

.sensor-info-btn:focus {
  outline: none;
  box-shadow:
    0 0 0 3px rgba(76, 175, 80, 0.3),
    0 4px 15px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

.sensor-info-btn:active {
  transform: translateY(0) scale(0.98);
  transition: all 0.1s ease;
}
/* Sensor Popup Overlay */
.sensor-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeInOverlay 0.3s ease-out;
}

.sensor-popup-container {
  background: white;
  border-radius: 16px;
  border: 2px solid #000000;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  animation: slideInUp 0.3s ease-out;
}

.sensor-popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #f8fffe, #f0f9ff);
}

.popup-title {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #1e293b;
  font-size: 18px;
  font-weight: 600;
}

.popup-title mat-icon {
  color: #4CAF50;
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.close-popup-btn {
  width: 40px;
  height: 40px;
  background: #f1f5f9;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-popup-btn:hover {
  background: #e2e8f0;
  transform: scale(1.05);
}

.close-popup-btn mat-icon {
  color: #64748b;
  font-size: 20px;
}

.sensor-popup-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.sensor-count-badge {
  display: flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, #e8f5e9, #f1f8e9);
  padding: 16px 20px;
  border-radius: 12px;
  margin-bottom: 24px;
  border: 1px solid #c8e6c9;
}

.sensor-count-badge mat-icon {
  color: #4CAF50;
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.sensor-count-badge span {
  color: #2e7d32;
  font-weight: 600;
  font-size: 16px;
}

.sensor-list-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.sensor-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.sensor-card:hover {
  background: #f8fffe;
  border-color: #4CAF50;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.1);
}

.sensor-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.sensor-icon mat-icon {
  color: white;
  font-size: 20px;
  width: 20px;
  height: 20px;
}

.sensor-details {
  flex: 1;
}

.sensor-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.sensor-id {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.sensor-status {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #4CAF50;
  animation: pulse 2s infinite;
}

.status-indicator.active {
  background: #4CAF50;
}

.status-text {
  font-size: 12px;
  color: #4CAF50;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.no-sensors-message {
  text-align: center;
  padding: 40px 20px;
  color: #64748b;
}

.no-sensors-message mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: #94a3b8;
  margin-bottom: 16px;
}

.no-sensors-message h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #475569;
}

.no-sensors-message p {
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.site-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  color: #64748b;
  font-size: 0.9rem;
}

.site-info mat-icon,
.site-info .material-icons {
  color: #4CAF50;
  font-size: 20px;
  width: 20px;
  height: 20px;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInOverlay {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Custom Scrollbar for Popup */
.sensor-popup-body::-webkit-scrollbar {
  width: 6px;
}

.sensor-popup-body::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.sensor-popup-body::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.sensor-popup-body::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

@media (max-width: 1024px) {
  .info-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .info-section {
    flex-direction: column;
    padding: 25px;
  }

  .site-images-container {
    margin-right: 0;
    margin-bottom: 20px;
    align-self: center;
  }

  .logo-container {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
  }

  .site-stats {
    max-width: 400px;
    width: 100%;
  }

  .site-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .site-type {
    margin-left: 0;
  }
}

@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
    gap: 20px; /* Adjust gap for mobile */
  }

  .local-details-container {
    padding: 12px;
  }

  .info-section {
    padding: 20px;
  }

  .site-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .site-type {
    margin-top: 10px;
    margin-left: 0;
  }

  .site-name {
    font-size: 24px;
  }

  .logo-container {
    height: 200px;
  }

  .site-images-container {
    flex: none;
  }

  .stat-value {
    font-size: 24px;
  }

  .info-column {
    gap: 16px; /* Smaller gap on mobile */
  }
}

@media (max-width: 480px) {
  .site-name {
    font-size: 20px;
  }
  
  .sensor-popup-body {
    padding: 20px;
  }

  .logo-container {
    height: 180px;
  }

  .stat-value {
    font-size: 20px;
  }

  .stat-label {
    font-size: 10px;
  }

  .info-column {
    gap: 12px; /* Even smaller gap on very small screens */
  }
}

/* New Tab Styles */
.content-tabs-section {
  margin-top: 20px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* Tab styling overrides */
::ng-deep .local-tabs {
  padding: 15px 20px 0 20px;
}

::ng-deep .local-tabs .mat-mdc-tab-label {
  min-width: 120px !important;
  height: 56px !important;
  padding: 0 16px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  color: #616161 !important;
  opacity: 1 !important;
  transition: color 0.3s, background-color 0.3s;
}

::ng-deep .local-tabs .mat-mdc-tab-label.mdc-tab--active {
  color: #4CAF50 !important;
  background-color: #f0fdf4 !important;
}

::ng-deep .local-tabs .mat-mdc-tab-label:hover {
  background-color: #f5f5f5 !important;
}

::ng-deep .local-tabs .mat-mdc-tab-label.mdc-tab--active .tab-icon {
  color: #4CAF50;
}

.tab-icon {
  margin-right: 8px;
  font-size: 20px;
  transition: color 0.3s;
}

.tab-label {
  font-size: 16px;
}

::ng-deep .local-tabs .mat-mdc-tab-header {
  border-bottom: 1px solid #e0e0e0;
}

::ng-deep .local-tabs .mat-mdc-tab-group.mat-primary .mat-mdc-tab-header .mat-mdc-tab-label-container .mat-mdc-tab-list .mat-mdc-tab .mdc-tab__text-label {
  color: inherit;
}

::ng-deep .local-tabs .mdc-tab-indicator__content--underline {
  background-color: #4CAF50 !important;
  border-color: #4CAF50 !important;
}

.tab-content {
  padding: 20px;
}

/* Architecture Plan Section */
.architecture-plan-section {
  background: transparent;
  border-radius: 8px;
  margin: 0;
  padding: 0;
}

.plan-header {
  margin-bottom: 20px;
}

.plan-title-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.plan-title-section h3 {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.plan-title-section .section-icon {
  margin-right: 10px;
  font-size: 28px;
  color: #4CAF50;
}

.plan-description {
  color: #757575;
  font-size: 15px;
  margin: 0;
}

.canvas-container {
  width: 100%;
  height: 500px;
  overflow: hidden;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  background: #fafafa;
}

canvas {
  width: 100%;
  height: 100%;
}

/* Applied Rules Section */
.applied-rules-section {
  background: transparent;
  border-radius: 8px;
  padding: 0;
}

.rules-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.rules-header h3 {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
}

.rules-header .section-icon {
  margin-right: 10px;
  font-size: 28px;
  color: #4CAF50;
}

.rules-description {
  color: #757575;
  font-size: 15px;
  margin-top: 5px;
}

/* Responsive adjustments for tabs */
@media (max-width: 768px) {
  ::ng-deep .local-tabs .mat-mdc-tab-label {
    min-width: unset !important;
    padding: 0 10px !important;
    font-size: 14px !important;
  }
  
  .tab-icon {
    margin-right: 4px;
    font-size: 18px;
  }
  
  .tab-label {
    font-size: 14px;
  }

  .tab-content {
    padding: 15px;
  }
  
  .plan-title-section h3,
  .rules-header h3 {
    font-size: 18px;
  }
  
  .plan-title-section .section-icon,
  .rules-header .section-icon {
    font-size: 24px;
  }

  .canvas-container {
    height: 400px;
  }
}

@media (max-width: 480px) {
  ::ng-deep .local-tabs .mat-mdc-tab-label {
    font-size: 12px !important;
    height: 48px !important;
    padding: 0 8px !important;
  }
  
  .tab-icon {
    font-size: 16px;
    margin-right: 2px;
  }
  
  .tab-label {
    font-size: 12px;
  }

  .tab-content {
    padding: 10px;
  }
  
  .plan-title-section h3,
  .rules-header h3 {
    font-size: 16px;
  }
  
  .plan-title-section .section-icon,
  .rules-header .section-icon {
    font-size: 22px;
  }

  .canvas-container {
    height: 350px;
  }
}