import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { TagApiService } from '@app/core/services/administrative/tag.service';
import { TagAssignmentApiService } from '@app/core/services/administrative/tag-assignment.service';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { SiteApiService } from '@app/core/services/administrative/site.service';
import { LocalApiService } from '@app/core/services/administrative/local.service';

import { Tag } from '@app/core/models/tag';
import { TagAssignment, TargetType } from '@app/core/models/TagAssignment';
import { Client } from '@app/core/models/client';
import { Site } from '@app/core/models/site';
import { Local } from '@app/core/models/local';

@Component({
  selector: 'app-tag-assignment',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTabsModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatTableModule,
    MatCardModule,
    MatChipsModule,
    MatSnackBarModule
  ],
  templateUrl: './tag-assignment.component.html',
  styleUrls: ['./tag-assignment.component.css']
})
export class TagAssignmentComponent implements OnInit {
  // Forms
  assignmentForm: FormGroup;

  // Data
  tags: Tag[] = [];
  assignments: TagAssignment[] = [];
  clients: Client[] = [];
  sites: Site[] = [];
  locals: Local[] = [];
  filteredTargets: any[] = [];

  // UI State
  isLoading = false;
  selectedTab = 0;
  showValidationErrors = false;

  // Table columns
  assignmentColumns = ['tag', 'targetType', 'targetName', 'createdAt', 'actions'];

  // Target types
  targetTypes = [
    { value: TargetType.Client, label: 'Client' },
    { value: TargetType.Site, label: 'Site' },
    { value: TargetType.Local, label: 'Local' }
  ];

  constructor(
    readonly fb: FormBuilder,
    readonly snackBar: MatSnackBar,
    readonly tagService: TagApiService,
    readonly tagAssignmentService: TagAssignmentApiService,
    readonly clientService: ClientApiService,
    readonly siteService: SiteApiService,
    readonly localService: LocalApiService
  ) {
    this.assignmentForm = this.createAssignmentForm();
  }

  ngOnInit(): void {
    this.loadInitialData();
  }

  private createAssignmentForm(): FormGroup {
    return this.fb.group({
      idTag: ['', Validators.required],
      targetType: ['', Validators.required],
      targetId: ['', Validators.required]
    });
  }

  private loadInitialData(): void {
    this.isLoading = true;
    
    Promise.all([
      this.loadTags(),
      this.loadAssignments(),
      this.loadClients(),
      this.loadSites(),
      this.loadLocals()
    ]).finally(() => {
      this.isLoading = false;
    });
  }

  private async loadTags(): Promise<void> {
    try {
      this.tags = await this.tagService.getAll().toPromise() || [];
    } catch (error) {
      console.error('Error loading tags:', error);
      this.showError('Erreur lors du chargement des tags');
    }
  }

  private async loadAssignments(): Promise<void> {
    try {
      this.assignments = await this.tagAssignmentService.getAll().toPromise() || [];
    } catch (error) {
      console.error('Error loading assignments:', error);
      this.showError('Erreur lors du chargement des affectations');
    }
  }

  private async loadClients(): Promise<void> {
    try {
      this.clients = await this.clientService.getAll().toPromise() || [];
    } catch (error) {
      console.error('Error loading clients:', error);
    }
  }

  private async loadSites(): Promise<void> {
    try {
      this.sites = await this.siteService.getAll().toPromise() || [];
    } catch (error) {
      console.error('Error loading sites:', error);
    }
  }

  private async loadLocals(): Promise<void> {
    try {
      this.locals = await this.localService.getAll().toPromise() || [];
    } catch (error) {
      console.error('Error loading locals:', error);
    }
  }

  onTargetTypeChange(): void {
    const targetType = this.assignmentForm.get('targetType')?.value;
    this.assignmentForm.get('targetId')?.setValue('');
    
    switch (targetType) {
      case TargetType.Client:
        this.filteredTargets = this.clients;
        break;
      case TargetType.Site:
        this.filteredTargets = this.sites;
        break;
      case TargetType.Local:
        this.filteredTargets = this.locals;
        break;
      default:
        this.filteredTargets = [];
    }
  }

  async onSubmitAssignment(): Promise<void> {
    this.showValidationErrors = true;
    
    if (this.assignmentForm.invalid) {
      return;
    }

    this.isLoading = true;
    
    try {
      const formValue = this.assignmentForm.value;
      await this.tagAssignmentService.createAssignment(formValue).toPromise();
      
      this.showSuccess('Affectation créée avec succès');
      this.resetAssignmentForm();
      await this.loadAssignments();
    } catch (error) {
      console.error('Error creating assignment:', error);
      this.showError('Erreur lors de la création de l\'affectation');
    } finally {
      this.isLoading = false;
    }
  }

  async delete(assignment: TagAssignment): Promise<void> {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette affectation ?')) {
      return;
    }

    this.isLoading = true;
    
    try {
      await this.tagAssignmentService.deleteAssignment(assignment.Id!).toPromise();
      this.showSuccess('Affectation supprimée avec succès');
      await this.loadAssignments();
    } catch (error) {
      console.error('Error deleting assignment:', error);
      this.showError('Erreur lors de la suppression de l\'affectation');
    } finally {
      this.isLoading = false;
    }
  }

  getTargetName(assignment: TagAssignment): string {
    switch (assignment.TargetType) {
      case TargetType.Client:
        const client = this.clients.find(c => c.Id === assignment.TargetId);
        return client?.Name || 'Client inconnu';
      case TargetType.Site:
        const site = this.sites.find(s => s.Id === assignment.TargetId);
        return site?.Name || 'Site inconnu';
      case TargetType.Local:
        const local = this.locals.find(l => l.Id === assignment.TargetId);
        return local?.Name || 'Local inconnu';
      default:
        return 'Inconnu';
    }
  }

  getTagName(assignment: TagAssignment): string {
    const tag = this.tags.find(t => t.Id === assignment.IdTag);
    return tag?.Nom || 'Tag inconnu';
  }

  getTargetTypeDisplay(targetType: TargetType | string ): string {
    // Handle different possible types from API
    if (typeof targetType === 'string') {
      return targetType;
    }

    // If it's a number (enum index), convert it
    if (typeof targetType === 'number') {
      const enumKeys = Object.keys(TargetType);
      const enumValues = Object.values(TargetType);
      const index = enumValues.indexOf(targetType as TargetType);
      return index >= 0 ? enumKeys[index] : 'Inconnu';
    }

    // If it's already the enum value
    return String(targetType) || 'Inconnu';
  }

  getTargetTypeClass(targetType: TargetType | string ): string {
    const displayValue = this.getTargetTypeDisplay(targetType);
    return displayValue.toLowerCase();
  }

  resetAssignmentForm(): void {
    this.assignmentForm.reset();
    this.filteredTargets = [];
    this.showValidationErrors = false;
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }
}
