import { Component, EventEmitter, Input, OnInit, Output, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControllerServeur } from '@app/core/models/controllerServeur';
import { Licence } from '@app/core/models/licence';

@Component({
  selector: 'app-details-cs',
  imports: [CommonModule],
  templateUrl: './details-cs.component.html',
  styleUrl: './details-cs.component.css',
})
export class DetailsCsComponent implements OnInit, OnChanges {
  @Input() controllerServer: ControllerServeur | null = null;
  @Input() licences: Licence[] = [];
  @Output() detailsClosed = new EventEmitter<void>();

  selectedLicenceName: string = '';

  constructor() {}

  ngOnInit(): void {
    this.updateLicenceName();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['controllerServer'] || changes['licences']) {
      this.updateLicenceName();
    }
  }

  private updateLicenceName(): void {
    if (this.controllerServer && this.controllerServer.IdLicence && this.licences.length > 0) {
      const licence = this.licences.find(l =>
        (l.Id || l.Id) === this.controllerServer!.IdLicence
      );
      this.selectedLicenceName = licence ? (licence.Name || 'Licence inconnue') : 'Licence non trouvée';
    } else if (this.controllerServer && this.controllerServer.Licence) {
      // If licence object is directly attached
      this.selectedLicenceName = this.controllerServer.Licence.Name || 'Licence sans nom';
    } else {
      this.selectedLicenceName = 'Aucune licence assignée';
    }
  }

  onClose(): void {
    this.detailsClosed.emit();
  }

  // Helper methods for display
  getStatusDisplay(status: string): string {
    const statusMap: { [key: string]: string } = {
      'Active': 'Actif',
      'Inactive': 'Inactif',
      'Maintenance': 'Maintenance',
      'Retired': 'Retiré'
    };
    return statusMap[status] || status;
  }

  getStatusClass(status: string): string {
    const statusClasses: { [key: string]: string } = {
      'Active': 'status-active',
      'Inactive': 'status-inactive',
      'Maintenance': 'status-maintenance',
      'Retired': 'status-retired'
    };
    return statusClasses[status] || 'status-default';
  }

  formatDate(dateString: string): string {
    if (!dateString) return 'Non disponible';
    try {
      return new Date(dateString).toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return dateString;
    }
  }

  // Helper to check if a field has content
  hasContent(value: any): boolean {
    return value !== null && value !== undefined && value !== '';
  }
  getLicenceId(licence: Licence): string {
    // Based on your template, you're using licence.id
    return licence.Id || licence.Id || '';
  }

  getLicenceName(licence: Licence): string {
    // Based on your template, you're using licence.Name
    return licence.Name ||  `Licence ${this.getLicenceId(licence)}`;
  }
}
