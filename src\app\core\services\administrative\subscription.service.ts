import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiService } from '../api.service';
import { Subscription } from '@app/core/models/subscription';

@Injectable({ providedIn: 'root' })
export class SubscriptionApiService extends ApiService<Subscription> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint("subscription");
  }
}
