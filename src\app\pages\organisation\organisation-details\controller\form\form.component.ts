import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  Output,
  SimpleChanges,
  OnInit,
  OnChanges,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Controller } from '@app/core/models/controller';
import { ControllerServerController } from '@app/core/models/controllerServerController';
import { ControllerServeur } from '@app/core/models/controllerServeur';
import { Lister, Page } from '@app/core/models/util/page';
import { ControllerApiService } from '@app/core/services/administrative/controller.service';
import { ControllerServerControllerApiService } from '@app/core/services/administrative/controllerservercontroller.service';
import { ControllerServeurApiService } from '@app/core/services/administrative/controllerserveur.service';
import { NgToastService } from 'ng-angular-popup';
import { forkJoin } from 'rxjs';

@Component({
  selector: 'app-form',
  imports: [CommonModule, ReactiveFormsModule], // Removed FormsModule
  templateUrl: './form.component.html',
  styleUrl: './form.component.css',
})
export class FormComponent implements OnInit, OnChanges {
  @Input() clientId: string = '';
  @Input() isEditMode: boolean = false;
  @Input() controller: Controller | null = null;
  @Output() formClosed = new EventEmitter<void>();
  @Output() controllerCreated = new EventEmitter<Controller>();
  @Output() controllerUpdated = new EventEmitter<Controller>();

  createControllerForm!: FormGroup;
  isSubmitting: boolean = false;

  // Controller Server properties
  availableControllerServers: ControllerServeur[] = [];
  currentControllerServerRelations: ControllerServerController[] = [];
  isLoadingControllerServers: boolean = false;

  constructor(
    private fb: FormBuilder,
    private controllerService: ControllerApiService,
    private controllerServerService: ControllerServeurApiService,
    private controllerServerControllerService: ControllerServerControllerApiService,
    readonly toast: NgToastService
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.loadControllerServers();
    if (this.isEditMode && this.controller) {
      this.populateForm();
      this.loadControllerServerRelations();
    }
    console.log(this.clientId, '[clientId]');
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['controller'] || changes['isEditMode']) {
      if (this.createControllerForm) {
        if (this.isEditMode && this.controller) {
          this.populateForm();
          this.loadControllerServerRelations();
        } else {
          this.createControllerForm.reset();
          this.currentControllerServerRelations = [];
          this.initializeForm();
        }
      }
    }
  }

  private initializeForm(): void {
    this.createControllerForm = this.fb.group({
      HostName: ['', [Validators.required, Validators.minLength(2)]],
      Model: ['', [Validators.required, Validators.minLength(2)]],
      SerialNumber: ['', [Validators.required, Validators.minLength(2)]],
      MacAddress: ['', [Validators.required, Validators.minLength(2)]],
      IpAddress: ['', [Validators.required, Validators.minLength(2)]],
      State: ['true', [Validators.required]],
      BaseTopic: ['', [Validators.required]],
      InstallationDate: [''],
      LastConnection: [''],
      ControllerServer: ['', [Validators.required]],
    });
  }

  private populateForm(): void {
    if (this.controller) {
      console.log('Populating form with controller:', this.controller);

      this.createControllerForm.patchValue({
        HostName: this.controller.HostName || '',
        Model: this.controller.Model || '',
        SerialNumber: this.controller.SerialNumber || '',
        MacAddress: this.controller.MacAddress || '',
        IpAddress: this.controller.IpAddress || '',
        State: this.controller.State?.toString() || 'true',
        InstallationDate: this.controller.InstallationDate || '',
        LastConnection: this.controller.LastConnection || '',
        BaseTopic: this.controller.BaseTopic || '',
      });
    }
  }

  // Load available controller servers
  private loadControllerServers(): void {
    this.isLoadingControllerServers = true;
    this.controllerServerService.getAllLightByClient(this.clientId).subscribe({
      next: (servers: ControllerServeur[]) => {
        this.availableControllerServers = servers;
        this.isLoadingControllerServers = false;
        console.log('Loaded controller servers:', servers);
      },
      error: (error: any) => {
        console.error('Error loading controller servers:', error);
        this.isLoadingControllerServers = false;
      },
    });
  }

  lister: Lister = new Lister();
  // Load existing controller-server relationship for edit mode
  private loadControllerServerRelations(): void {
    if (!this.controller?.Id) return;

    this.lister.FilterParams = [
      {
        AndOr: 'AND',
        Column: 'IdController',
        Op: 'eq',
        Value: this.controller.Id,
      },
    ];

    this.lister.Pagination = {
      PageSize: 500,
    };

    this.controllerServerControllerService.gatePage(this.lister).subscribe({
      next: (allRelations: Page<ControllerServerController>) => {
        console.log(allRelations, 'getpage controllerservercontroller');
        if (allRelations.Content) {
          const selectedServerId =
            allRelations.Content?.length > 0
              ? allRelations.Content[0].IdControllerServeur
              : '';
          console.log(
            allRelations.Content?.length > 0
              ? allRelations.Content[0].IdControllerServeur
              : '',
            'allRelations.Content'
          );
          this.currentControllerServerRelations = allRelations.Content;
          this.createControllerForm.patchValue({
            ControllerServer: selectedServerId,
          });
          console.log('Loaded controller server relation:', selectedServerId);
        }
      },
    });
  }

  // Get selected controller server ID from form
  get selectedControllerServerId(): string {
    return this.createControllerForm.get('ControllerServer')?.value || '';
  }

  // Get selected controller server object for display
  getSelectedControllerServer(): ControllerServeur | null {
    const serverId = this.selectedControllerServerId;
    return (
      this.availableControllerServers.find(
        (server) => server.Id === serverId
      ) || null
    );
  }

  public showError(message: string, title: string) {
    this.toast.warning(message, title, 3000, false);
  }

  private createController(controllerData: Partial<Controller>): void {
    this.controllerService.post('', controllerData).subscribe({
      next: (response: Controller) => {
        console.log('Controller created successfully:', response);
        console.log('Controller ID received:', response.Id);
        console.log(
          'Will create relation with server:',
          this.selectedControllerServerId
        );
        this.createControllerServerRelation(response.Id);
        this.controllerService.setBaseTopic(response.Id).subscribe({
          next: (res) => {
            console.log('BaseTopic successfully set:', res.baseTopic);
          },
          error: (err) => {
            console.error('Failed to set BaseTopic:', err);
          },
        });
      },
      error: (error: any) => {
        console.error('Error creating controller:', error);
        this.isSubmitting = false;
        alert('Erreur lors de la création du contrôleur');
      },
    });
  }

  onSubmit(): void {
    console.log('Form submission started');
    console.log('Form valid:', this.createControllerForm.valid);
    console.log('Form value:', this.createControllerForm.value);
    console.log('Selected controller server:', this.selectedControllerServerId);

    if (this.createControllerForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;

      const formData = this.createControllerForm.value;
      console.log('Form data being processed:', formData);

      // Prepare the data according to your API structure
      const controllerData: Partial<Controller> = {
        HostName: formData.HostName,
        Model: formData.Model,
        SerialNumber: formData.SerialNumber,
        MacAddress: formData.MacAddress,
        IpAddress: formData.IpAddress,
        State: formData.State === 'true' || formData.State === true,
        InstallationDate: formData.InstallationDate || null,
        LastConnection: formData.LastConnection || new Date(),
        BaseTopic: formData.BaseTopic || null,
      };

      console.log('Controller data to be sent:', controllerData);

      if (this.isEditMode && this.controller?.Id) {
        const updateData = {
          ...controllerData,
          Id: this.controller.Id,
        };

        this.controllerService.update(updateData).subscribe({
          next: (response: any) => {
            console.log('Controller updated successfully:', response);
            this.updateControllerServerRelation(
              response.Id || this.controller!.Id
            );
          },
          error: (error: any) => {
            console.error('Error updating controller:', error);
            this.isSubmitting = false;
            alert('Erreur lors de la mise à jour du contrôleur');
          },
        });
      } else {
        console.log('form data : ', formData);
        const selectedControllerServer = this.getSelectedControllerServer();

        if (
          selectedControllerServer &&
          selectedControllerServer.SubscriptionId
        ) {
          this.controllerService
            .canAddController(selectedControllerServer.SubscriptionId)
            .subscribe({
              next: (canAdd: boolean) => {
                if (canAdd) {
                  this.createController(controllerData);
                }
              },
              error: (err: any) => {
                const message =
                  err?.error ||
                  'Vous avez atteint la limite de contrôleurs serveurs.';
                this.showError(message, 'Erreur');
                this.isSubmitting = false;
              },
            });
        } else {
          this.createController(controllerData);
        }
      }
    } else {
      console.log('Form is invalid, marking fields as touched');
      // Mark all fields as touched to show validation errors
      Object.keys(this.createControllerForm.controls).forEach((key) => {
        const control = this.createControllerForm.get(key);
        if (control && control.invalid) {
          console.log(`Field ${key} is invalid:`, control.errors);
        }
        control?.markAsTouched();
      });
    }
  }

  // Create controller-server relationship for new controller
  private createControllerServerRelation(controllerId: string): void {
    console.log(
      'Creating relation for controller:',
      controllerId,
      'with server:',
      this.selectedControllerServerId
    );

    if (!this.selectedControllerServerId) {
      // No server selection, just emit success and reset
      console.log('No server selected, skipping relation creation');
      this.controllerCreated.emit({ Id: controllerId } as Controller);
      this.resetForm();
      return;
    }

    const relationData: ControllerServerController = {
      IdController: controllerId,
      IdControllerServeur: this.selectedControllerServerId,
    } as ControllerServerController;

    console.log('Relation data to create:', relationData);

    this.controllerServerControllerService.post('', relationData).subscribe({
      next: (relation: ControllerServerController) => {
        console.log(
          'Controller server relation created successfully:',
          relation
        );
        this.controllerCreated.emit({ Id: controllerId } as Controller);
        this.resetForm();
      },
      error: (error: any) => {
        console.error('Error creating controller server relation:', error);
        console.error('Full error details:', error);
        this.isSubmitting = false;
        // Still emit success for the controller creation, but show error for relation
        this.controllerCreated.emit({ Id: controllerId } as Controller);
        alert(
          "Contrôleur créé avec succès, mais erreur lors de l'association au serveur: " +
            (error.message || 'Erreur inconnue')
        );
        this.resetForm();
      },
    });
  }

  // Update controller-server relationship for existing controller
  private updateControllerServerRelation(controllerId: string): void {
    // Get current relation
    const currentServerId =
      this.currentControllerServerRelations.length > 0
        ? this.currentControllerServerRelations[0].IdControllerServeur
        : '';

    // If no change, just complete
    if (currentServerId === this.selectedControllerServerId) {
      this.controllerUpdated.emit({ Id: controllerId } as Controller);
      this.resetForm();
      return;
    }

    console.log('Current server:', currentServerId);
    console.log('New server:', this.selectedControllerServerId);

    const operations: any[] = [];

    // Delete existing relation if any
    if (this.currentControllerServerRelations.length > 0) {
      this.currentControllerServerRelations.forEach((relation) => {
        operations.push(
          this.controllerServerControllerService.delete(relation.Id)
        );
      });
    }

    // Create new relation if selected
    if (this.selectedControllerServerId) {
      const relationData: Partial<ControllerServerController> = {
        IdController: controllerId,
        IdControllerServeur: this.selectedControllerServerId,
      };
      operations.push(
        this.controllerServerControllerService.post('', relationData)
      );
    }

    if (operations.length === 0) {
      // No changes needed
      this.controllerUpdated.emit({ Id: controllerId } as Controller);
      this.resetForm();
      return;
    }

    forkJoin(operations).subscribe({
      next: (results: any[]) => {
        console.log(
          'Controller server relation updated successfully:',
          results
        );
        this.controllerUpdated.emit({ Id: controllerId } as Controller);
        this.resetForm();
      },
      error: (error: any) => {
        console.error('Error updating controller server relation:', error);
        this.isSubmitting = false;
        alert(
          "Contrôleur mis à jour mais erreur lors de la mise à jour de l'association au serveur"
        );
      },
    });
  }

  private resetForm(): void {
    this.createControllerForm.reset();
    this.currentControllerServerRelations = [];
    this.initializeForm();
    this.isSubmitting = false;
    this.formClosed.emit();
  }

  hasError(fieldName: string): boolean {
    const field = this.createControllerForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  getErrorMessage(fieldName: string): string {
    const field = this.createControllerForm.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors['required']) {
        return `${this.getFieldLabel(fieldName)} est obligatoire`;
      }
      if (field.errors['minLength']) {
        return `${this.getFieldLabel(fieldName)} doit contenir au moins ${
          field.errors['minLength'].requiredLength
        } caractères`;
      }
      if (field.errors['min']) {
        return `${this.getFieldLabel(fieldName)} doit être supérieur à ${
          field.errors['min'].min - 1
        }`;
      }
      if (field.errors['max']) {
        return `${this.getFieldLabel(fieldName)} doit être inférieur à ${
          field.errors['max'].max + 1
        }`;
      }
      if (field.errors['pattern']) {
        return `${this.getFieldLabel(fieldName)} n'est pas valide`;
      }
    }
    return '';
  }

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      HostName: 'Le nom',
      Model: 'Le modèle',
      SerialNumber: 'Le numéro de série',
      MacAddress: "L'adresse MAC",
      IpAddress: "L'adresse IP",
      State: 'Le statut',
      InstallationDate: "La date d'installation",
      ControllerServer: 'Le serveur de contrôle',
    };
    return labels[fieldName] || fieldName;
  }

  onCancel(): void {
    this.resetForm();
  }
}
