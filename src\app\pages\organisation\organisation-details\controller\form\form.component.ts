import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  Output,
  SimpleChanges,
  OnInit,
  OnChanges,
} from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Controller } from '@app/core/models/controller';
import { ControllerApiService } from '@app/core/services/administrative/controller.service';

@Component({
  selector: 'app-form',
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './form.component.html',
  styleUrl: './form.component.css',
})
export class FormComponent implements OnInit, OnChanges {
  @Input() isEditMode: boolean = false;
  @Input() controller: Controller | null = null;
  @Output() formClosed = new EventEmitter<void>();
  @Output() controllerCreated = new EventEmitter<Controller>();
  @Output() controllerUpdated = new EventEmitter<Controller>();

  createControllerForm!: FormGroup;
  isSubmitting: boolean = false;

  constructor(
    private fb: FormBuilder,
    private controllerService: ControllerApiService
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    if (this.isEditMode && this.controller) {
      this.populateForm();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['controller'] || changes['isEditMode']) {
      if (this.createControllerForm) {
        if (this.isEditMode && this.controller) {
          this.populateForm();
        } else {
          this.createControllerForm.reset();
          this.initializeForm();
        }
      }
    }
  }

  private initializeForm(): void {
    this.createControllerForm = this.fb.group({
      HostName: ['', [Validators.required, Validators.minLength(2)]],
      Model: ['', [Validators.required, Validators.minLength(2)]],
      SerialNumber: ['', [Validators.required, Validators.minLength(2)]],
      MacAddress: ['', [Validators.required, Validators.minLength(2)]],
      IpAddress: ['', [Validators.required, Validators.minLength(2)]],
      State: ['true', [Validators.required]],
      BaseTopic:['', [Validators.required]],
      InstallationDate: [''],
      LastConnection:['']
    });
  }

  private populateForm(): void {
    if (this.controller) {
      console.log('Populating form with controller:', this.controller);

      this.createControllerForm.patchValue({
        HostName: this.controller.HostName || '',
        Model: this.controller.Model || '',
        SerialNumber: this.controller.SerialNumber || '',
        MacAddress: this.controller.MacAddress || '',
        IpAddress: this.controller.IpAddress || '',
        State: this.controller.State?.toString() || 'true',
        InstallationDate: this.controller.InstallationDate || '',
        LastConnection:this.controller.LastConnection || '',
        BaseTopic:this.controller.BaseTopic || ''
      });
    }
  }

  onSubmit(): void {
    console.log('Form submission started');
    console.log('Form valid:', this.createControllerForm.valid);
    console.log('Form value:', this.createControllerForm.value);

    if (this.createControllerForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;

      const formData = this.createControllerForm.value;
      console.log('Form data being processed:', formData);

      // Prepare the data according to your API structure
      const controllerData: Partial<Controller> = {
        HostName: formData.HostName,
        Model: formData.Model,
        SerialNumber: formData.SerialNumber,
        MacAddress: formData.MacAddress,
        IpAddress: formData.IpAddress,
        State: formData.State === 'true' || formData.State === true,
        InstallationDate: formData.InstallationDate || null,
        LastConnection: formData.LastConnection || new Date(),
        BaseTopic: formData.BaseTopic || null
      };

      console.log('Controller data to be sent:', controllerData);

      if (this.isEditMode && this.controller?.Id) {
        // Update existing controller
        const updateData = {
          ...controllerData,
          Id: this.controller.Id,
        };

        this.controllerService.update(updateData).subscribe({
          next: (response: any) => {
            console.log('Controller updated successfully:', response);
            this.controllerUpdated.emit(response);
            this.resetForm();
          },
          error: (error: any) => {
            console.error('Error updating controller:', error);
            this.isSubmitting = false;
            alert('Erreur lors de la mise à jour du contrôleur');
          },
        });
      } else {
        // Create new controller
        this.controllerService.post('', controllerData).subscribe({
          next: (response: Controller) => {
            console.log('Controller created successfully:', response);
            this.controllerCreated.emit(response);
            this.resetForm();
          },
          error: (error: any) => {
            console.error('Error creating controller:', error);
            this.isSubmitting = false;
            alert('Erreur lors de la création du contrôleur');
          },
        });
      }
    } else {
      console.log('Form is invalid, marking fields as touched');
      // Mark all fields as touched to show validation errors
      Object.keys(this.createControllerForm.controls).forEach((key) => {
        const control = this.createControllerForm.get(key);
        if (control && control.invalid) {
          console.log(`Field ${key} is invalid:`, control.errors);
        }
        control?.markAsTouched();
      });
    }
  }

  private resetForm(): void {
    this.createControllerForm.reset();
    this.initializeForm();
    this.isSubmitting = false;
    this.formClosed.emit();
  }

  hasError(fieldName: string): boolean {
    const field = this.createControllerForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  getErrorMessage(fieldName: string): string {
    const field = this.createControllerForm.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors['required']) {
        return `${this.getFieldLabel(fieldName)} est obligatoire`;
      }
      if (field.errors['minLength']) {
        return `${this.getFieldLabel(fieldName)} doit contenir au moins ${
          field.errors['minLength'].requiredLength
        } caractères`;
      }
      if (field.errors['min']) {
        return `${this.getFieldLabel(fieldName)} doit être supérieur à ${
          field.errors['min'].min - 1
        }`;
      }
      if (field.errors['max']) {
        return `${this.getFieldLabel(fieldName)} doit être inférieur à ${
          field.errors['max'].max + 1
        }`;
      }
      if (field.errors['pattern']) {
        return `${this.getFieldLabel(fieldName)} n'est pas valide`;
      }
    }
    return '';
  }

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      HostName: 'Le nom',
      Model: 'Le modèle',
      SerialNumber: 'Le numéro de série',
      MacAddress: "L'adresse MAC",
      IpAddress: "L'adresse IP",
      State: 'Le statut',
      InstallationDate: "La date d'installation",
    };
    return labels[fieldName] || fieldName;
  }

  onCancel(): void {
    this.resetForm();
  }
}