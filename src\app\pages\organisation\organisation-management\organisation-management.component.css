.organisation-management-container {
  /* max-width: 1300px; */
  margin: 30px auto;
  padding: 25px;
  background: linear-gradient(145deg, #ffffff, #f9f9f9);
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

/* Header Section */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  animation: slideIn 0.5s ease-out;
}

.page-title {
  margin: 0;
}

.title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.title-icon {
  font-size: 30px;
  color: #4CAF50;
  background: linear-gradient(45deg, #4CAF50, #81C784);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.actions {
  display: flex;
  gap: 15px;
}

.create-button {
  background: linear-gradient(45deg, #4CAF50, #81C784);
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.create-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(76, 175, 80, 0.5);
  background: linear-gradient(45deg, #81C784, #4CAF50);
}

.view-toggle {
  border: 2px solid #4CAF50;
  color: #4CAF50;
  padding: 8px 16px;
  border-radius: 8px;
  background: transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-start;
}

.action-buttons button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}
.btn-details {
  background-color: #3b82f6;
  color: white;
}
.btn-edit {
  background-color: #4CAF50;
  color: white;
}
.btn-delete {
  background-color: #ef4444;
  color: white;
}

.view-toggle:hover {
  background-color: #e8f5e9;
  transform: translateY(-2px);
}

/* Create Form */
.create-form-card {
  margin-bottom: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.4s ease-in 0.3s;
  background: white;
  padding: 25px;
}

.create-form-card form {
  width: 100%;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
  align-items: end;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
}

.form-group input,
.form-group select {
  height: 42px;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  color: #4a5568;
  background-color: white;
  transition: all 0.2s ease;
  box-sizing: border-box;
  width: 100%;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #4CAF50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-group input[type="file"] {
  padding: 8px;
  height: auto;
  border: 2px dashed #e2e8f0;
  background-color: #f8fafc;
  cursor: pointer;
}

.form-group input[type="file"]:hover {
  border-color: #4CAF50;
  background-color: #f0fff4;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}
.action-buttons button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.action-buttons button i {
  font-size: 20px;
}

.form-actions button {
  padding: 10px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.form-actions button[type="button"] {
  background-color: #fff;
  border: 1px solid #e2e8f0;
  color: #4a5568;
}

.form-actions button[type="submit"] {
  background: linear-gradient(45deg, #4CAF50, #81C784);
  border: none;
  color: white;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.2);
}

.form-actions button[type="button"]:hover {
  background-color: #f8fafc;
  border-color: #cbd5e0;
}

.form-actions button[type="submit"]:hover {
  background: linear-gradient(45deg, #81C784, #4CAF50);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(76, 175, 80, 0.3);
}

.form-actions button[type="submit"]:disabled {
  background: #e2e8f0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}
.submit-button{
  margin-left: 12px;
}

/* Search Bar */
.search-bar {
  margin-bottom: 30px;
}

.search-bar input {
  width: 100%;
  max-width: 450px;
  padding: 12px 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #f8fafc;
  color: #4a5568;
}

.search-bar input:focus {
  outline: none;
  border-color: #4CAF50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.search-bar input::placeholder {
  color: #a0aec0;
}

/* Cards Container */
.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

/* Table Container */
.table-container {
  overflow-x: auto;
  margin-bottom: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  background: #ffffff;
}

table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

thead {
  background: #f8fafc;
}

th {
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: #2d3748;
  border-bottom: 2px solid #e2e8f0;
  white-space: nowrap;
}

td {
  padding: 16px;
  border-bottom: 1px solid #edf2f7;
  color: #4a5568;
  vertical-align: middle;
}

tbody tr:hover {
  background-color: #f7fafc;
}

/* Table action buttons */
td button {
  padding: 8px 16px;
  margin-right: 8px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}



td button:last-child {
  background: #ef4444;
  color: white;
  border: none;
}

td button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Pagination Controls */
.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 30px;
}

.pagination-controls button {
  padding: 8px 16px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #4a5568;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-controls button:disabled {
  background: #f7fafc;
  color: #cbd5e0;
  cursor: not-allowed;
  border-color: #edf2f7;
}

.pagination-controls button:not(:disabled):hover {
  background: #4CAF50;
  color: white;
  border-color: #4CAF50;
}

.pagination-controls button.active {
  background: #4CAF50;
  color: white;
  border-color: #4CAF50;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .actions {
    width: 100%;
    flex-direction: column;
    gap: 10px;
  }

  .create-button,
  .view-toggle {
    width: 100%;
    padding: 12px;
    justify-content: center;
  }

  .cards-container {
    grid-template-columns: 1fr;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 24px;
  }

  .title-icon {
    font-size: 26px;
  }

  .search-field {
    max-width: 100%;
  }
}

/* Required field indicator */
.form-group label[required]:after,
.form-group label.required:after {
  content: "*";
  color: #ef4444;
  margin-left: 4px;
}

.logo-cell {
  width: 60px;
  padding: 8px;
}

.circular-logo {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
  border-radius: 50%;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f4f6;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease; /* Add transition for smooth effect */
  cursor: pointer; /* Add pointer cursor */
}

.circular-logo:hover {
  transform: scale(1.5); /* Make it 50% larger on hover */
  border-color: #4CAF50; /* Change border color on hover */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); /* Add shadow effect */
  z-index: 10; /* Ensure it appears above other elements */
}

.org-logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease; /* Add transition for image */
}

.circular-logo:hover .org-logo {
  transform: scale(1.1); /* Slight additional scale effect for the image */
}

.default-logo {
  color: #9ca3af;
  font-size: 24px;
  transition: transform 0.3s ease; /* Add transition for icon */
}

.circular-logo:hover .default-logo {
  transform: scale(1.1); /* Scale effect for the default icon */
  color: #4CAF50; /* Change color on hover */
}

.org-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.org-name {
  font-weight: 500;
  color: #2d3748;
}

/* Update table styles */
.table-container th:first-child,
.table-container td:first-child {
  width: auto;
  text-align: left;
  padding-right: 24px;
}
.search-bar {
  display: flex !important;
  align-items: center !important;
  min-width: 400px !important;
  margin: 0 0px !important;
  border: 2px solid #e1e8ed !important;
  margin-right: 10px !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  background: #ffffff !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.2s ease !important;
}

.search-bar:focus-within {
  border-color: transparent;
  /* box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1), 0 4px 12px rgba(0, 0, 0, 0.12); */
  transform: translateY(-1px);
}

.search-button{
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #4CAF50 !important;
  border-radius: 10px;
  color: white;
  padding: 7px 25px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.search-button:hover {
  background: #3e8e41 !important;
}

.search-input {
  flex: 1 !important;
  padding: 12px 16px !important;
  border: none !important;
  outline: none !important;
  font-size: 14px !important;
  color: #374151 !important;
  background: transparent !important;
  border-radius: 0 !important;
}

.search-input::placeholder {
  color: #9ca3af;
}

.search-bar-container{
  display: flex;
}

/* .search-input::focus {
  outline: none !important;
  box-shadow: none !important;
} */

.clear-btn {
  background: transparent;
  border: none;
  border-radius: 6px;
  margin: 0 8px 0 2px;
  cursor: pointer;
  color: #6c757d;
  height: 28px;
  width: 35px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  transition: all 0.2s ease;
  line-height: 1;
}

.clear-btn:hover {
  color: #495057;
  transform: scale(1.05);
}

.clear-btn:active {
  background: #dee2e6;
  transform: scale(0.95);
}

.clear-btn:focus {
  outline: 2px solid #007bff;
  outline-offset: 1px;
}
/* Checkbox styles */
.checkbox-group {
  display: flex;
  align-items: center;
  margin-top: 15px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  position: relative;
  padding-left: 30px;
  margin-bottom: 0;
  font-size: 14px;
  color: #4a5568;
}

.checkbox-label input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 20px;
  width: 20px;
  background-color: #fff;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.checkbox-label:hover input ~ .checkmark {
  border-color: #4CAF50;
}

.checkbox-label input:checked ~ .checkmark {
  background-color: #4CAF50;
  border-color: #4CAF50;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.checkbox-label input:checked ~ .checkmark:after {
  display: block;
}

.checkbox-label .checkmark:after {
  left: 7px;
  top: 3px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
.file-input-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.file-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background-color: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 6px;
  color: #495057;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  text-align: center;
  justify-content: center;
  min-height: 44px;
}

.file-button:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
  color: #212529;
}

.file-button:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.file-button .material-icons {
  font-size: 18px;
}

.file-info {
  font-size: 12px;
  color: #6c757d;
  padding: 4px 8px;
  background-color: #f1f3f4;
  border-radius: 4px;
  border-left: 3px solid #28a745;
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Optional: Preview image styles if you want to show preview */
.logo-preview {
  width: 80px;
  height: 80px;
  border-radius: 6px;
  border: 2px solid #dee2e6;
  object-fit: cover;
  margin-top: 8px;
}

.logo-preview-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.remove-logo-btn {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s ease;
}

.remove-logo-btn:hover {
  background: #c82333;
}
/* Styles pour les champs obligatoires et erreurs */
.form-group input.ng-invalid.ng-touched,
.form-group select.ng-invalid.ng-touched {
  border: 1px solid #f44336 !important;
}

.error-message {
  color: #f44336;
  font-size: 12px;
  margin-top: 5px;
}

/* Style pour les placeholders */
::placeholder {
  color: #999 !important;
  opacity: 1;
}

:-ms-input-placeholder {
  color: #999 !important;
}

::-ms-input-placeholder {
  color: #999 !important;
}

/* Style pour les labels obligatoires */
.required:after {
  content: " *";
  color: #f44336;
}
/* Animation pour les champs en erreur */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  20%, 60% { transform: translateX(-5px); }
  40%, 80% { transform: translateX(5px); }
}

.error-border {
  border: 1px solid #f44336 !important;
  animation: shake 0.5s ease-in-out;
}
/* Style de base pour le formulaire */
.form-group {
  margin-bottom: 1.5rem;
  position: relative; /* Pour positionner les messages d'erreur */
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

label.required:after {
  content: " *";
  color: #e53e3e;
}

input, select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 1rem;
  transition: border-color 0.2s;
}

input:focus, select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Style pour les erreurs - sans décalage */
.error-message {
  position: absolute;
  bottom: -1.25rem;
  left: 0;
  color: #e53e3e;
  font-size: 0.75rem;
  margin-top: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s;
}

.input-error {
  border-color: #e53e3e;
}

.input-error:focus {
  border-color: #e53e3e;
  box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.show-error .error-message {
  opacity: 1;
}

/* Placeholder stylisé */
::placeholder {
  color: #9ca3af;
  opacity: 1;
}

.show-more-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 10px 18px !important;
  border: 2px solid #4CAF50;
  border-radius: 8px;
  background: transparent;
  color: #4CAF50;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.show-more-buttons:hover {
  background: #e8f5e9;
  transform: translateY(-2px);
}

.show-more-buttons .material-icons {
  font-size: 18px;
  transition: transform 0.3s ease;
}

.show-more-buttons:hover .material-icons {
  transform: translateY(2px);
}

.form-invalid{
  background-color: rgba(255, 0, 0, 0.226) !important;
  border-radius: 12px;
  padding: 10px;
  margin-bottom: 18px;
}
.form-invalid > h4{
  color: rgba(56, 3, 3, 0.694) !important;
  padding-left: 30px;
}

.form-invalid > ul > li{
  color: rgba(56, 3, 3, 0.694) !important;
}

button{
  border: 0;
  border-color: transparent;
}