<div class="create-form-card">
  <div class="form-container">
    <form [formGroup]="createControllerForm" (ngSubmit)="onSubmit()">
      <div class="form-grid">
        <div class="form-group">
          <label for="HostName">Nom <span class="required">*</span></label>
          <input
            id="HostName"
            type="text"
            formControlName="HostName"
            [class.error]="hasError('HostName')"
          />
          <div class="error-message" *ngIf="hasError('HostName')">
            {{ getErrorMessage("HostName") }}
          </div>
        </div>

        <div class="form-group">
          <label for="Model">Modèle <span class="required">*</span></label>
          <input
            id="Model"
            type="text"
            formControlName="Model"
            [class.error]="hasError('Model')"
          />
          <div class="error-message" *ngIf="hasError('Model')">
            {{ getErrorMessage("Model") }}
          </div>
        </div>

        <div class="form-group">
          <label for="SerialNumber"
            >N° de Série <span class="required">*</span></label
          >
          <input
            id="SerialNumber"
            type="text"
            formControlName="SerialNumber"
            [class.error]="hasError('SerialNumber')"
          />
          <div class="error-message" *ngIf="hasError('SerialNumber')">
            {{ getErrorMessage("SerialNumber") }}
          </div>
        </div>

        <div class="form-group">
          <label for="MacAddress"
            >Adresse MAC <span class="required">*</span></label
          >
          <input
            id="MacAddress"
            type="text"
            formControlName="MacAddress"
            [class.error]="hasError('MacAddress')"
          />
          <div class="error-message" *ngIf="hasError('MacAddress')">
            {{ getErrorMessage("MacAddress") }}
          </div>
        </div>

        <div class="form-group">
          <label for="IpAddress"
            >Adresse IP <span class="required">*</span></label
          >
          <input
            id="IpAddress"
            type="text"
            formControlName="IpAddress"
            [class.error]="hasError('IpAddress')"
          />
          <div class="error-message" *ngIf="hasError('IpAddress')">
            {{ getErrorMessage("IpAddress") }}
          </div>
        </div>

        <div class="form-group">
          <label for="BaseTopic"
            >Base Topic <span class="required">*</span></label
          >
          <input
            id="BaseTopic"
            type="text"
            formControlName="BaseTopic"
            [class.error]="hasError('BaseTopic')"
          />
          <div class="error-message" *ngIf="hasError('BaseTopic')">
            {{ getErrorMessage("BaseTopic") }}
          </div>
        </div>

        <div class="form-group">
          <label for="State">Statut <span class="required">*</span></label>
          <select
            id="State"
            formControlName="State"
            [class.error]="hasError('State')"
          >
            <option value="">-- Sélectionnez un statut --</option>
            <option value="true">Actif</option>
            <option value="false">Inactif</option>
          </select>
          <div class="error-message" *ngIf="hasError('State')">
            {{ getErrorMessage("State") }}
          </div>
        </div>

        <div class="form-group">
          <label for="InstallationDate">Date d'installation</label>
          <input
            id="InstallationDate"
            type="date"
            formControlName="InstallationDate"
            [class.error]="hasError('InstallationDate')"
          />
          <div class="error-message" *ngIf="hasError('InstallationDate')">
            {{ getErrorMessage("InstallationDate") }}
          </div>
        </div>

        <!-- NEW: Controller Servers Multi-Select -->
        <!-- <div class="form-group full-width">
          <label for="ControllerServers">Serveurs de Contrôle</label>
          <div class="multi-select-container">
            <div class="selected-items" *ngIf="getSelectedControllerServers().length > 0">
              <div class="selected-item" *ngFor="let server of getSelectedControllerServers()">
                <span>{{ server.Name || server.Status }}</span>
                <button type="button" class="remove-btn" (click)="removeControllerServer(server.Id)">x</button>
              </div>
            </div>
            <select 
              id="ControllerServers"
              class="multi-select"
              (change)="onControllerServerSelect($event)"
              [disabled]="isLoadingControllerServers"
            >
              <option value="">-- Sélectionnez un serveur --</option>
              <option 
                *ngFor="let server of availableControllerServers" 
                [value]="server.Id"
                [disabled]="isControllerServerSelected(server.Id)"
              >
                {{ server.Name || server.Status }}
              </option>
            </select>
          </div>
          <div class="info-message" *ngIf="isLoadingControllerServers">
            Chargement des serveurs...
          </div>
          <div class="error-message" *ngIf="hasError('ControllerServers')">
            {{ getErrorMessage("ControllerServers") }}
          </div>
        </div> -->
        <div class="form-group">
          <label for="ControllerServer">Serveur de Contrôle</label>
          <select
            id="ControllerServer"
            [(ngModel)]="selectedControllerServerId"
            (change)="onControllerServerChange($event)"
            [disabled]="isLoadingControllerServers"
            name="controllerServer"
            class="form-control"
          >
            <option value="">-- Sélectionnez un serveur --</option>
            <option
              *ngFor="let server of availableControllerServers"
              [value]="server.Id"
            >
              {{ server.Name || server.Status }}
            </option>
          </select>
          <div class="info-message" *ngIf="isLoadingControllerServers">
            Chargement des serveurs...
          </div>
          <!-- Debug info -->
          <!-- <div style="color: red; font-size: 12px; margin-top: 5px">
            Debug: Selected = "{{ selectedControllerServerId }}" ({{
              selectedControllerServerId?.length || 0
            }}
            chars)
          </div> -->
        </div>
      </div>

      <div class="form-actions">
        <button
          type="button"
          class="cancel-button"
          (click)="onCancel()"
          [disabled]="isSubmitting"
        >
          Annuler
        </button>
        <button
          type="submit"
          class="submit-button"
          [disabled]="!createControllerForm.valid || isSubmitting"
        >
          <span *ngIf="isSubmitting">Enregistrement...</span>
          <span *ngIf="!isSubmitting">Enregistrer</span>
        </button>
      </div>
    </form>
  </div>
</div>
