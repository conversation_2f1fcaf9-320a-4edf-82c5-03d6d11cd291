import { Component, HostListener, Input } from '@angular/core';
import { LucideAngularModule, FileIcon, House } from 'lucide-angular';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { InstalationService } from '@app/core/services/instalation.service';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { ViewChild, ElementRef } from '@angular/core';
import { CdkDragEnd } from '@angular/cdk/drag-drop';
import * as fabric from 'fabric';
import { MatIconModule } from '@angular/material/icon';
import { CustomMqttService } from '../../core/services/custom-mqtt.service';
import { Subscription } from 'rxjs';
import { Zigbee2MqttService, ZigbeeDevice, DeviceState } from '../../core/services/mqtt.service';
import { Capteur } from '@app/core/models/capteur';
import { MqttService } from 'ngx-mqtt';
import { ControllerApiService } from '@app/core/services/administrative/controller.service';
import { TypeCapteurApiService } from '@app/core/services/administrative/typecapteur.service';
import { TypeCapteur } from '@app/shared/models/typeCapteur';
@Component({
  standalone: true,
  selector: 'app-install-caplteur',
  imports: [CommonModule, FormsModule, DragDropModule, LucideAngularModule, MatIconModule],

  templateUrl: './install-caplteur.component.html',
  styleUrl: './install-caplteur.component.css',

})


export class InstallCaplteurComponent {
  canvas!: fabric.Canvas;

  canvasInitialized = false;
  selectedTool = '';
  planType = 'local'; // 'local' ou 'site'
  isGridVisible = true;

  // Palette de couleurs
  selectedColor = '#3B82F6';
  selectedStrokeColor = '#374151';
  selectedStrokeWidth = 2;
  capteur: Capteur = new Capteur();
  peredCapteurs: Capteur[] = [];
  typeCapteurs: TypeCapteur[] = [];
  constructor(private installService: InstalationService, readonly mqttService: CustomMqttService, private zigbeeService: Zigbee2MqttService, private controlerService: ControllerApiService, private typeCapteurService: TypeCapteurApiService) { }

  generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  ngOnInit() {

    this.installService.getClients().subscribe(data => {
      this.clients = data;
      console.log(this.clients)
    });
    this.typeCapteurService.getAll().subscribe(data => {
      this.typeCapteurs = data;

    }

    )
    this.zigbeeService.subscribeToBridgeLogs();

    // Souscris aux logs et stocke-les localement
    this.logsSubscription = this.zigbeeService.getBridgeLogs()
      .subscribe((log: string) => {
        console.log('Nouveau log reçu:', log);
        this.logs.unshift(log); // garde le log brut

        try {
          // 1️⃣ Parse le log de premier niveau
          const parsedLog = JSON.parse(log);

          // 2️⃣ Extraire le payload interne
          const regex = /payload '(.*)'/;
          const match = parsedLog.message.match(regex);

          if (match && match[1]) {
            const payloadStr = match[1];
            const payload = JSON.parse(payloadStr);

            if (payload.type === "device_interview" && payload.data && payload.data.definition) {
              const definition = payload.data.definition;

              // 3️⃣ Construire ton objet Capteur
              this.capteur = {
                Id: this.generateUUID(),
                Protocol: "Zigbee2MQTT",
                Manufacturer: definition.vendor || "",
                Model: definition.model || "",
                ModelIdentifier: definition.model || "",
                FriendlyName: payload.data.friendly_name || "",
                LastSeen: new Date(),
                IeeeAddress: payload.data.ieee_address || "",
                NetworkAddress: 0,   // tu peux remplacer si tu l'as
                Endpoint: 1,         // pareil
                NodeId: 0,
                HomeId: 0,
                SecurityClasses: "",
                SensorReadings: [],
                Transactions: [],
                RowData: "",
                Topic: "",
                State: "",
                IdTypeCapteur:this.getCapteurTypeIdByDisplayName(definition.description)||"",
                TypeCapteur: undefined ,
                Brand: definition.vendor || ""
              };

              console.log("✅ Capteur créé :", this.capteur);
              this.peredCapteurs.push(this.capteur);
              this.capteur.ControllerId=this.selectedControler.Id;


              this.controlerService.create(this.capteur).subscribe(res => {

                console.log("Creation Capteur", res);
              }
              );


            } else {
              console.log("❌ Ce log ne contient pas de definition de capteur !");
            }
          } else {
            console.log("❌ Aucun payload trouvé !");
          }
        } catch (err) {
          console.error('Erreur lors du parsing JSON MQTT :', err);
        }
      });

  }





 getCapteurTypeIdByDisplayName(displayName: string): string | null {
    const capteur = this.typeCapteurs.find(
      c => c.DisplayName.toLowerCase().trim() === displayName.toLowerCase().trim()
    );
    return capteur ? capteur.Id : null;
  }

getIconForCapteur(nom: string): string {
  console.log("Type Capteur ",nom)
  const map: { [key: string]: string } = {
    'Water Leak Sensor': 'water_drop',
    'Door/Window Sensor': 'sensor_door', // ou 'door_front'
    'Motion Sensor': 'motion_photos_on',
    'Thermostat': 'device_thermostat',
    'Smart Plug': 'power',
    'Luminance Sensor': 'wb_sunny',
    
    'Air/Quality Sensor': 'air',
    'Blinds/Curtain Motor': 'window',
    'Presence Sensor': 'sensors',
    'Vibration Sensor': 'vibration',
    'Smart Button': 'radio_button_checked',
    'Smoke Detector': 'smoke_free',
    'Light Switch': 'toggle_on',
    'bridge': 'hub',
    'Temperature/Humidity Sensor':'thermostat'
  };
 console.log("Type Capteur selectioner  ",map[nom])
  return map[nom] || 'device_unknown';
}


getIconColorForCapteur(displayName: string): string {
  const map: { [key: string]: string } = {
    'Water Leak Sensor': '#2196f3',         // Bleu
    'Door/Window Sensor': '#4caf50',        // Vert
    'Motion Sensor': '#ff9800',             // Orange
    'Thermostat': '#f44336',                // Rouge
    'Smart Plug': '#9c27b0',                // Violet
    'Luminance Sensor': '#ffc107',          // Jaune
    'Temperature/Humidity Sensor': '#00bcd4', // Cyan
    'Air Quality Sensor': '#8bc34a',        // Vert clair
    'Blinds/Curtain Motor': '#795548',      // Marron
    'Presence Sensor': '#607d8b',           // Gris bleu
    'Vibration Sensor': '#e91e63',          // Rose
    'Smart Button': '#3f51b5',              // Indigo
    'Smoke Detector': '#f44336',            // Rouge
    'Light Switch': '#ffeb3b',              // Jaune vif
    'bridge': '#9e9e9e'                     // Gris
  };
  return map[displayName] || '#000000';     // Noir par défaut
}


  // Éléments de structure
  elements = [
    {
      type: 'capteur',
      name: 'Capteur',
      icon: '📡',
      description: 'Capteur IoT',
      color: '#3B82F6',
      category: 'technique'
    },
    {
      type: 'mur',
      name: 'Mur',
      icon: '🧱',
      description: 'Mur / Cloison',
      color: '#6B7280',
      category: 'structure'
    },
    {
      type: 'porte',
      name: 'Porte',
      icon: '🚪',
      description: 'Porte / Ouverture',
      color: '#92400E',
      category: 'structure'
    },
    {
      type: 'fenetre',
      name: 'Fenêtre',
      icon: '🪟',
      description: 'Fenêtre',
      color: '#0891B2',
      category: 'structure'
    },
    {
      type: 'escalier',
      name: 'Escalier',
      icon: '🪜',
      description: 'Escalier',
      color: '#7C2D12',
      category: 'structure'
    },
    {
      type: 'zone',
      name: 'Zone',
      icon: '📦',
      description: 'Zone / Pièce',
      color: '#059669',
      category: 'structure'
    }
  ];

  // Éléments de mobilier avec SVG
  mobilierElements = [
    {
      type: 'bureau',
      name: 'Bureau',
      icon: '🖥️',
      description: 'Bureau de travail',
      color: '#8B4513',
      useSVG: true
    },
    {
      type: 'table',
      name: 'Table',
      icon: '🪑',
      description: 'Table de réunion',
      color: '#CD853F',
      useSVG: true
    },
    {
      type: 'chaise',
      name: 'Chaise',
      icon: '🪑',
      description: 'Chaise de bureau',
      color: '#2F4F4F',
      useSVG: true
    },
    {
      type: 'armoire',
      name: 'Armoire',
      icon: '🗄️',
      description: 'Armoire / Rangement',
      color: '#556B2F',
      useSVG: true
    },
    {
      type: 'canape',
      name: 'Canapé',
      icon: '🛋️',
      description: 'Canapé / Sofa',
      color: '#4682B4',
      useSVG: true
    },
    {
      type: 'lit',
      name: 'Lit',
      icon: '🛏️',
      description: 'Lit',
      color: '#BC8F8F',
      useSVG: true
    },
    {
      type: 'evier',
      name: 'Évier',
      icon: '🚿',
      description: 'Évier / Lavabo',
      color: '#20B2AA',
      useSVG: true
    },
    {
      type: 'frigo',
      name: 'Réfrigérateur',
      icon: '🧊',
      description: 'Réfrigérateur',
      color: '#F0F8FF',
      useSVG: true
    },
    {
      type: 'cuisiniere',
      name: 'Cuisinière',
      icon: '🔥',
      description: 'Cuisinière / Plaque',
      color: '#FF6347',
      useSVG: true
    },
    {
      type: 'toilette',
      name: 'Toilette',
      icon: '🚽',
      description: 'WC / Toilette',
      color: '#E6E6FA',
      useSVG: true
    },
    {
      type: 'douche',
      name: 'Douche',
      icon: '🚿',
      description: 'Cabine de douche',
      color: '#B0E0E6',
      useSVG: true
    },
    {
      type: 'plante',
      name: 'Plante',
      icon: '🪴',
      description: 'Plante décorative',
      color: '#228B22',
      useSVG: true
    }
  ];

  ngAfterViewInit(): void {
    this.initializeCanvas();
  }

  initializeCanvas(): void {
    this.canvas = new fabric.Canvas('canvas', {
      backgroundColor: '#FAFAFA',
      width: 1000,
      height: 700,
      selection: true
    });


    this.canvasInitialized = true;

    // Événements du canvas
    this.canvas.on('selection:created', () => this.updateToolbar());
    this.canvas.on('selection:cleared', () => this.updateToolbar());
  }





  selectTool(elementType: string): void {
    this.selectedTool = elementType;
    this.canvas.defaultCursor = 'crosshair';

    // Désélectionner tous les objets
    this.canvas.discardActiveObject();
    this.canvas.renderAll();
  }

  getSelectedToolName(): string {
    const allElements = [...this.elements, ...this.mobilierElements];
    const selectedElement = allElements.find(e => e.type === this.selectedTool);
    return selectedElement ? selectedElement.name : '';
  }

  // Ajoutez ces propriétés à votre composant
  pairingMode: boolean = false;
  pairingTimeLeft: number = 0; // Temps restant en secondes
  private pairingInterval: any;
  private subscriptions: Subscription[] = [];
  bridgeState: string = 'offline';
  devices: ZigbeeDevice[] = [];

  private logSubscription: Subscription | undefined;

  logs: string[] = [];
  private logsSubscription!: Subscription;

  getpermitedDevices() {

    this.zigbeeService.getBridgeLogs()
      .subscribe((log: string) => {
        console.log('Nouveau log reçu:', log);
        this.logs.unshift(log); // Ajoute le log en haut de la liste
      });
  }

  togglePermitJoin(): void {
    this.pairingMode = !this.pairingMode;

    const message = this.pairingMode
      ? '{"value": true, "time": 254}'
      : '{"value": false}';

    console.log(
      'Toggling Permit Join - New Pairing Mode:',
      this.pairingMode,
      'Message:',
      message
    );

    if (!this.pairingMode) {
      // Désactiver l'appairage
      this.stopPairingCountdown();

      const deactivationMessage = '{"value": false, "time": 0}';
      setTimeout(() => {
        this.mqttService.publish(
          'zigbee2mqtt/bridge/request/permit_join',
          deactivationMessage
        );
        console.log(
          'Deactivation message sent with delay:',
          deactivationMessage
        );
      }, 1000);
    } else {
      // Activer l'appairage
      this.mqttService.publish(
        'zigbee2mqtt/bridge/request/permit_join',
        message
      );
      console.log('Activation message sent');

      // Démarrer le compteur (4 minutes = 240 secondes)
      this.startPairingCountdown(240);
    }
  }

  private startPairingCountdown(seconds: number): void {
    this.pairingTimeLeft = seconds;

    this.pairingInterval = setInterval(() => {
      this.pairingTimeLeft--;

      if (this.pairingTimeLeft <= 0) {
        // Le temps est écoulé, désactiver automatiquement l'appairage
        this.pairingMode = false;
        this.stopPairingCountdown();

        // Envoyer le message de désactivation
        const deactivationMessage = '{"value": false, "time": 0}';
        this.mqttService.publish(
          'zigbee2mqtt/bridge/request/permit_join',
          deactivationMessage
        );
        console.log('Pairing automatically disabled after timeout');
      }
    }, 1000);
  }

  private stopPairingCountdown(): void {
    if (this.pairingInterval) {
      clearInterval(this.pairingInterval);
      this.pairingInterval = null;
    }
    this.pairingTimeLeft = 0;
  }

  // Méthode utilitaire pour formater le temps d'affichage
  getFormattedTimeLeft(): string {
    const minutes = Math.floor(this.pairingTimeLeft / 60);
    const seconds = this.pairingTimeLeft % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  // N'oubliez pas de nettoyer l'interval dans ngOnDestroy
  ngOnDestroy(): void {
    this.stopPairingCountdown();
  }








  addShape(type: string): void {
    if (!this.canvasInitialized) return;

    const left = Math.random() * (this.canvas.getWidth() - 150) + 50;
    const top = Math.random() * (this.canvas.getHeight() - 100) + 50;

    // Vérifier si c'est un élément de mobilier avec SVG
    const mobilierElement = this.mobilierElements.find(el => el.type === type);
    if (mobilierElement && mobilierElement.useSVG) {
      this.createSVGShape(type, left, top);
      return;
    }

    // Créer les formes basiques pour les éléments de structure
    this.createBasicShape(type, left, top);
  }




  private createSVGShape(type: string, left: number, top: number): void {
    const svgString = this.getSVGDefinition(type);
    if (!svgString) {
      this.createBasicShape(type, left, top);
      return;
    }

    fabric.loadSVGFromString(svgString).then(({ objects, options }) => {
      const validObjects = objects.filter((obj): obj is fabric.Object => obj !== null);
      if (validObjects.length > 0) {
        const svgGroup = fabric.util.groupSVGElements(validObjects, options);
        svgGroup.set({
          left,
          top,
          scaleX: 0.8,
          scaleY: 0.8
        });

        this.canvas.add(svgGroup);
        this.canvas.setActiveObject(svgGroup);
        this.canvas.renderAll();
        this.selectedTool = '';
        this.canvas.defaultCursor = 'default';
      }
    }).catch(error => {
      console.error('Erreur lors de la création du SVG:', error);
      this.createBasicShape(type, left, top);
    });
  }

  private createBasicShape(type: string, left: number, top: number): void {
    let shape;
    const fillColor = this.selectedColor;
    const strokeColor = this.selectedStrokeColor;
    const strokeWidth = this.selectedStrokeWidth;

    switch (type) {
      case 'capteur':
        shape = new fabric.Circle({
          radius: 15,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top,
          shadow: new fabric.Shadow({
            color: 'rgba(0,0,0,0.2)',
            blur: 5,
            offsetX: 2,
            offsetY: 2
          })
        });
        break;

      case 'mur':
        shape = new fabric.Rect({
          width: 120,
          height: 15,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top
        });
        break;

      case 'porte':
        shape = new fabric.Rect({
          width: 60,
          height: 8,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top,
          rx: 4,
          ry: 4
        });
        break;

      case 'fenetre':
        shape = new fabric.Rect({
          width: 80,
          height: 6,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top
        });
        break;

      case 'escalier':
        const stairs = [];
        for (let i = 0; i < 5; i++) {
          stairs.push(new fabric.Rect({
            width: 60 - (i * 8),
            height: 8,
            fill: fillColor,
            stroke: strokeColor,
            strokeWidth: 1,
            left: left + (i * 8),
            top: top + (i * 8)
          }));
        }
        shape = new fabric.Group(stairs, { left, top });
        break;

      case 'zone':
        shape = new fabric.Rect({
          width: 150,
          height: 100,
          fill: this.hexToRgba(fillColor, 0.1),
          stroke: fillColor,
          strokeWidth: strokeWidth,
          strokeDashArray: [5, 5],
          left,
          top
        });
        break;

      // Formes basiques pour le mobilier (fallback)
      case 'bureau':
        shape = new fabric.Rect({
          width: 120,
          height: 60,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top
        });
        break;

      case 'table':
        shape = new fabric.Ellipse({
          rx: 40,
          ry: 30,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top
        });
        break;

      case 'chaise':
        shape = new fabric.Rect({
          width: 25,
          height: 25,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top
        });
        break;

      default:
        shape = new fabric.Rect({
          width: 50,
          height: 50,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top
        });
    }

    if (shape) {
      this.canvas.add(shape);
      this.canvas.setActiveObject(shape);
      this.canvas.renderAll();
      this.selectedTool = '';
      this.canvas.defaultCursor = 'default';
    }
  }

  private getSVGDefinition(type: string): string {
    const svgTemplates: { [key: string]: string } = {
      bureau: `
        
 <svg version="1.1" id="_x5F_" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
   viewBox="0 0 450 450" style="enable-background:new 0 0 450 450;" xml:space="preserve">
 
    <path style="fill:#984626;" d="M351.852,255.085c-27.091,0-49.131,22.04-49.131,49.131v39.683
      c0,27.091,22.04,49.131,49.131,49.131c27.091,0,49.131-22.04,49.131-49.131v-39.683
      C400.984,277.126,378.943,255.085,351.852,255.085z M358.723,339.492c0,3.789-3.082,6.871-6.871,6.871
      c-3.789,0-6.871-3.082-6.871-6.871v-30.869c0-3.789,3.082-6.871,6.871-6.871c3.789,0,6.871,3.082,6.871,6.871V339.492z"/>
    <path style="fill:#BF5F3D;" d="M351.852,256.719c-26.19,0-47.497,21.307-47.497,47.497v39.683
      c0,26.19,21.307,47.497,47.497,47.497c26.19,0,47.497-21.307,47.497-47.497v-39.683
      C399.35,278.027,378.042,256.719,351.852,256.719z M359.994,339.492c0,4.49-3.652,8.142-8.142,8.142
      c-4.489,0-8.142-3.652-8.142-8.142v-30.869c0-4.49,3.652-8.142,8.142-8.142c4.49,0,8.142,3.652,8.142,8.142V339.492z"/>
    <path style="fill:#984626;" d="M351.852,348.588c-3.895,0-7.36-2.468-8.65-5.918c0.996,3.941,4.404,6.871,8.65,6.871
      c4.246,0,7.655-2.93,8.65-6.871C359.213,346.12,355.748,348.588,351.852,348.588z"/>
    <path style="fill:#FFC2AD;" d="M351.852,390.031c-24.321,0-44.245-18.867-46.061-42.732c2.395,23.282,22.158,41.506,46.061,41.506
      c23.904,0,43.667-18.225,46.061-41.506C396.098,371.164,376.174,390.031,351.852,390.031z"/>
    <path style="fill:#FFC2AD;" d="M351.852,299.528c-3.895,0-7.36,2.468-8.65,5.918c0.996-3.941,4.404-6.871,8.65-6.871
      c4.246,0,7.655,2.93,8.65,6.871C359.213,301.996,355.748,299.528,351.852,299.528z"/>
    <path style="fill:#984626;" d="M351.852,258.085c-24.321,0-44.245,18.867-46.061,42.732c2.395-23.281,22.158-41.506,46.061-41.506
  </svg>
 
       `,
      chaise: `
         <svg viewBox="0 0 50 60" xmlns="http://www.w3.org/2000/svg">
           <rect x="5" y="0" width="40" height="35" rx="5" fill="${this.selectedColor}" stroke="${this.selectedStrokeColor}" stroke-width="${this.selectedStrokeWidth}"/>
           <rect x="0" y="30" width="50" height="30" rx="3" fill="${this.lightenColor(this.selectedColor)}" stroke="${this.selectedStrokeColor}" stroke-width="${this.selectedStrokeWidth}"/>
           <rect x="5" y="55" width="4" height="15" fill="${this.darkenColor(this.selectedColor)}"/>
           <rect x="41" y="55" width="4" height="15" fill="${this.darkenColor(this.selectedColor)}"/>
         </svg>
       `,
      lit: `
         <svg viewBox="0 0 100 140" xmlns="http://www.w3.org/2000/svg">
           <rect x="10" y="20" width="80" height="120" rx="5" fill="${this.selectedColor}" stroke="${this.selectedStrokeColor}" stroke-width="${this.selectedStrokeWidth}"/>
           <rect x="5" y="15" width="90" height="15" rx="7" fill="${this.darkenColor(this.selectedColor)}" stroke="${this.selectedStrokeColor}" stroke-width="1"/>
           <ellipse cx="30" cy="40" rx="15" ry="8" fill="${this.lightenColor(this.selectedColor)}" stroke="${this.selectedStrokeColor}" stroke-width="1"/>
           <ellipse cx="70" cy="40" rx="15" ry="8" fill="${this.lightenColor(this.selectedColor)}" stroke="${this.selectedStrokeColor}" stroke-width="1"/>
         </svg>
       `,
      table: `
         <svg viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
           <ellipse cx="60" cy="40" rx="55" ry="35" fill="${this.selectedColor}" stroke="${this.selectedStrokeColor}" stroke-width="${this.selectedStrokeWidth}"/>
           <rect x="55" y="65" width="10" height="15" fill="${this.darkenColor(this.selectedColor)}"/>
           <ellipse cx="60" cy="75" rx="25" ry="8" fill="${this.darkenColor(this.selectedColor)}"/>
         </svg>
       `
    };

    return svgTemplates[type] || '';
  }

  deleteSelected(): void {
    const activeObjects = this.canvas.getActiveObjects();
    if (activeObjects.length) {
      activeObjects.forEach(obj => this.canvas.remove(obj));
      this.canvas.discardActiveObject();
      this.canvas.renderAll();
    }
  }





  bringAllToFront(): void {
    if (this.canvas && this.canvas.getObjects().length > 0) {
      this.canvas.getObjects().forEach(obj => {
        this.canvas.bringObjectToFront(obj);
      });
      this.canvas.renderAll();
    }
  }

  sendAllToBack(): void {
    if (this.canvas && this.canvas.getObjects().length > 0) {
      this.canvas.getObjects().forEach(obj => {
        this.canvas.sendObjectToBack(obj);
      });
      this.canvas.renderAll();
    }
  }

  updateToolbar(): void {
    // Mise à jour de l'interface en fonction de la sélection
  }

  setPlanType(type: 'local' | 'site'): void {
    this.planType = type;
  }

  // Méthodes pour la palette de couleurs
  selectColor(color: string): void {
    this.selectedColor = color;
    this.applyColorToSelected();
  }

  selectStrokeColor(color: string): void {
    this.selectedStrokeColor = color;
    this.applyColorToSelected();
  }

  setStrokeWidth(width: number): void {
    this.selectedStrokeWidth = width;
    this.applyColorToSelected();
  }

  onColorChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.selectColor(target.value);
  }

  onStrokeWidthChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.setStrokeWidth(parseInt(target.value, 10));
  }

  applyColorToSelected(): void {
    const activeObject = this.canvas.getActiveObject();
    if (activeObject) {
      if (activeObject.type === 'group') {
        // Pour les groupes, appliquer à tous les objets
        (activeObject as fabric.Group).getObjects().forEach(obj => {
          obj.set({
            fill: this.selectedColor,
            stroke: this.selectedStrokeColor,
            strokeWidth: this.selectedStrokeWidth
          });
        });
      } else {
        // Pour les objets simples
        activeObject.set({
          fill: activeObject.get('type') === 'zone' ?
            this.hexToRgba(this.selectedColor, 0.1) : this.selectedColor,
          stroke: this.selectedStrokeColor,
          strokeWidth: this.selectedStrokeWidth
        });
      }
      this.canvas.renderAll();
    }
  }

  pickColorFromSelected(): void {
    const activeObject = this.canvas.getActiveObject();
    if (activeObject) {
      const fill = activeObject.get('fill') as string;
      const stroke = activeObject.get('stroke') as string;
      const strokeWidth = activeObject.get('strokeWidth') as number;

      if (fill && typeof fill === 'string' && fill.startsWith('#')) {
        this.selectedColor = fill;
      }
      if (stroke && typeof stroke === 'string' && stroke.startsWith('#')) {
        this.selectedStrokeColor = stroke;
      }
      if (strokeWidth) {
        this.selectedStrokeWidth = strokeWidth;
      }
    }
  }

  async importFile(event: Event): Promise<void> {
    const input = event.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) return;

    const file = input.files[0];
    const fileName = file.name.toLowerCase();

    try {
      if (fileName.endsWith('.json')) {
        const text = await file.text();
        const jsonData = JSON.parse(text);
        this.canvas.loadFromJSON(jsonData, () => {
          this.canvas.renderAll();
          console.log('✅ Plan JSON importé');
        });
      } else if (fileName.endsWith('.svg')) {
        const text = await file.text();
        const { objects, options } = await fabric.loadSVGFromString(text);
        const validObjects = objects.filter((obj): obj is fabric.Object => obj !== null);
        if (validObjects.length > 0) {
          const svgGroup = fabric.util.groupSVGElements(validObjects, options);
          svgGroup.set({ left: 100, top: 100 });
          this.canvas.add(svgGroup);
          this.canvas.renderAll();
          console.log('✅ SVG importé');
        } else {
          alert('❌ Aucun objet valide trouvé dans le SVG');
        }
      } else {
        alert('❌ Type de fichier non pris en charge');
      }
    } catch (error) {
      console.error('Erreur lors de l\'importation:', error);
      alert('❌ Erreur lors de l\'importation du fichier');
    }

    input.value = '';
  }

  exportToJson(): void {
    const json = this.canvas.toJSON();
    const exportData = {
      ...json,
      planType: this.planType,
      exportDate: new Date().toISOString(),
      version: '1.0'
    };


    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(exportData, null, 2));
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute("href", dataStr);
    downloadAnchorNode.setAttribute("download", `plan-${this.planType}-${new Date().toISOString().split('T')[0]}.json`);
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
  }

  addPlanToLocal(): void {
    const json = JSON.stringify(this.canvas.toJSON());
    this.selectedLocal.Architecture2DImage = json;

    this.selectedLocal.Site = null;
    this.selectedLocal.Transactions = null;
    //this.selectedLocal.Site.ClientId="null";

    this.selectedLocal.TypeLocal = null;
    this.installService.updateLocal(this.selectedLocal)
      .subscribe(
        (res) => {
          console.log('Mise à jour réussie', res);
        },
        (err) => {
          console.error('Erreur mise à jour', err);
        }
      );
  }
  exportToPNG(): void {
    const dataURL = this.canvas.toDataURL({
      format: 'png',
      quality: 1,
      multiplier: 2
    });

    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute("href", dataURL);
    downloadAnchorNode.setAttribute("download", `plan-${this.planType}-${new Date().toISOString().split('T')[0]}.png`);
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
  }

  // Fonctions utilitaires pour les couleurs
  hexToRgba(hex: string, alpha: number): string {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  }

  darkenColor(color: string): string {
    const hex = color.replace('#', '');
    const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - 50);
    const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - 50);
    const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - 50);
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }

  lightenColor(color: string): string {
    const hex = color.replace('#', '');
    const r = Math.min(255, parseInt(hex.substr(0, 2), 16) + 50);
    const g = Math.min(255, parseInt(hex.substr(2, 2), 16) + 50);
    const b = Math.min(255, parseInt(hex.substr(4, 2), 16) + 50);
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }




  //Données pour les listes déroulantes avec recherche
  clients: any[] = [];

  sites: any[] = []

  locals: any[] = [];
  Controlers: any[] = [];
  Capteurs: any[] = [];


  // Sélections actuelles et filtrage
  selectedClient: any = null;
  selectedSite: any = null;
  selectedLocal: any = null;
  selectedControler: any = null;
  // Recherche et filtrage
  clientSearchTerm = '';
  siteSearchTerm = '';
  localSearchTerm = '';
  ControlerSearchTerm = '';

  // États des dropdowns
  isClientDropdownOpen = false;
  isSiteDropdownOpen = false;
  isLocalDropdownOpen = false;
  isControlerDropdownOpen = false;

  // Getters pour les listes filtrées
  get filteredClients() {
    if (!this.clientSearchTerm) return this.clients;
    return this.clients.filter(client =>
      client.name.toLowerCase().includes(this.clientSearchTerm.toLowerCase()) ||
      client.code.toLowerCase().includes(this.clientSearchTerm.toLowerCase()) ||
      client.type.toLowerCase().includes(this.clientSearchTerm.toLowerCase())
    );
  }

  get filteredSites() {
    let sites = this.selectedClient ?
      this.sites.filter(site => site.clientId === this.selectedClient.id) :
      [];

    if (!this.siteSearchTerm) return sites;
    return sites.filter(site =>
      site.name.toLowerCase().includes(this.siteSearchTerm.toLowerCase()) ||
      site.address.toLowerCase().includes(this.siteSearchTerm.toLowerCase()) ||
      site.type.toLowerCase().includes(this.siteSearchTerm.toLowerCase())
    );
  }

  get filteredLocals() {
    let locals = this.selectedSite ?
      this.locals.filter(local => local.siteId === this.selectedSite.id) :
      [];

    if (!this.localSearchTerm) return locals;
    return locals.filter(local =>
      local.name.toLowerCase().includes(this.localSearchTerm.toLowerCase()) ||
      local.floor.toLowerCase().includes(this.localSearchTerm.toLowerCase()) ||
      local.type.toLowerCase().includes(this.localSearchTerm.toLowerCase())
    );
  }

  // Méthodes pour la gestion des sélections
  selectClient(client: any): void {
    this.selectedClient = client;
    this.selectedSite = null;
    this.selectedLocal = null;
    this.isClientDropdownOpen = false;
    this.clientSearchTerm = '';
    this.siteSearchTerm = '';
    this.localSearchTerm = '';
    console.log('Client sélectionné:', client);
    this.installService.getSitesByClientId(client.Id).subscribe(
      (res: any) => {
        this.sites = res['Content']; // adapte selon la structure de la réponse
        console.log(this.sites);
      },
      err => {
        console.error(err);
      }
    );

  }

  selectSite(site: any): void {
    this.selectedSite = site;
    this.selectedLocal = null;
    this.isSiteDropdownOpen = false;
    this.siteSearchTerm = '';
    this.localSearchTerm = '';
    console.log('Site sélectionné:', site);
    this.installService.getLocalBySitetId(site.Id).subscribe(
      (res: any) => {
        this.locals = res['Content']; // adapte selon la structure de la réponse
        console.log("LOCAL", this.locals);
      },
      err => {
        console.error(err);
      }
    );
  }

  selectLocal(local: any): void {
    this.selectedLocal = local;
    this.isLocalDropdownOpen = false;
    this.localSearchTerm = '';
    this.installService.getCotroler().subscribe(
      (res: any) => {
        this.Controlers = res; // adapte selon la structure de la réponse
        console.log("Controler", this.Controlers);
      },
      err => {
        console.error(err);
      }
    );
    console.log('Local sélectionné:', local);
    const jsonData = JSON.parse(local.Architecture2DImage);
    this.canvas.loadFromJSON(jsonData, () => {
      this.canvas.renderAll();
      console.log('✅ Plan JSON importé');
    });

    // Mettre à jour le titre du plan avec les informations sélectionnées

  }

  selectControler(Controler: any): void {
    this.selectedControler = Controler;
    this.isControlerDropdownOpen = false;
    this.ControlerSearchTerm = '';
    console.log('Local sélectionné:', Controler);

    this.installService.getCapteur().subscribe(
      (res: any) => {
        this.Capteurs = res; // adapte selon la structure de la réponse
        console.log("Capteurs", this.Capteurs);
      },
      err => {
        console.error(err);
      }
    );
    // Mettre à jour le titre du plan avec les informations sélectionnées

  }

  // Méthodes pour gérer l'ouverture/fermeture des dropdowns
  toggleClientDropdown(): void {
    this.isClientDropdownOpen = !this.isClientDropdownOpen;
    this.isSiteDropdownOpen = false;
    this.isLocalDropdownOpen = false;
  }

  toggleSiteDropdown(): void {
    if (!this.selectedClient) return;
    this.isSiteDropdownOpen = !this.isSiteDropdownOpen;
    this.isClientDropdownOpen = false;
    this.isLocalDropdownOpen = false;
  }

  toggleLocalDropdown(): void {
    if (!this.selectedSite) return;
    this.isLocalDropdownOpen = !this.isLocalDropdownOpen;
    this.isClientDropdownOpen = false;
    this.isSiteDropdownOpen = false;
  }

  toggleControlerDropdown(): void {
    if (!this.selectedLocal) return;
    this.isControlerDropdownOpen = !this.isControlerDropdownOpen;
    this.isClientDropdownOpen = false;
    this.isSiteDropdownOpen = false;
    this.isLocalDropdownOpen = false;
  }

  // Fermer tous les dropdowns
  closeAllDropdowns(): void {
    this.isClientDropdownOpen = false;
    this.isSiteDropdownOpen = false;
    this.isLocalDropdownOpen = false;
    this.isControlerDropdownOpen = false;
  }

  // Mettre à jour le titre du plan


  // Réinitialiser les sélections
  resetSelections(): void {
    this.selectedClient = null;
    this.selectedSite = null;
    this.selectedLocal = null;
    this.selectedControler = null;
    this.clientSearchTerm = '';
    this.siteSearchTerm = '';
    this.localSearchTerm = '';
    this.ControlerSearchTerm = '';
    this.closeAllDropdowns();
  }
  



 
  selectedCapteur: Capteur | null = null;
  isAddingCapteur = false;
 
 // Sélectionner un capteur pour l'ajouter
  selectCapteurToAdd(capteur: Capteur): void {
    this.selectedCapteur = capteur;
    this.isAddingCapteur = true;
    this.selectedTool = 'capteur';
    this.canvas.defaultCursor = 'crosshair';
    this.addCapteurToCanvas(50, 50, this.selectedCapteur);
    this.canvas.discardActiveObject();
    this.canvas.renderAll();
    
    console.log('Capteur sélectionné pour ajout:', capteur);
  }

  @HostListener('document:click', ['$event'])
  onCanvasClick(event: MouseEvent): void {
    if (!this.isAddingCapteur || !this.selectedCapteur) return;

    const canvasElement = document.getElementById('canvas') as HTMLCanvasElement;
    if (!canvasElement) return;

    const rect = canvasElement.getBoundingClientRect();
    const isClickOnCanvas = event.target === canvasElement;

    if (isClickOnCanvas) {
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      
      // Convertir les coordonnées canvas
      const pointer = this.canvas.getPointer(event as any);
      this.addCapteurToCanvas(pointer.x, pointer.y, this.selectedCapteur);
      
      // Réinitialiser la sélection
      this.resetCapteurSelection();
    }
  }

 // Ajouter le capteur sur le canvas avec son icône
  addCapteurToCanvas(x: number, y: number, capteur: Capteur): void {
    const iconName = this.getIconForCapteur(capteur.TypeCapteur?.DisplayName || '');
    const iconColor = this.getIconColorForCapteur(capteur.TypeCapteur?.DisplayName || '');

    // Créer un groupe contenant l'icône et le texte
    const capteurGroup = this.createCapteurIcon(x, y, capteur, iconName, iconColor);
    
    if (capteurGroup) {
      // Ajouter des données personnalisées au capteur
      capteurGroup.set({
        capteurData: capteur,
        type: 'capteur',
        selectable: true,
        hasControls: true,
        hasBorders: true
      });

      this.canvas.add(capteurGroup);
      this.canvas.setActiveObject(capteurGroup);
      this.canvas.renderAll();

      console.log('Capteur ajouté au canvas:', capteur.FriendlyName);
    }
  }

 // Créer l'icône du capteur avec texte
  createCapteurIcon(x: number, y: number, capteur: Capteur, iconName: string, iconColor: string): fabric.Group | null {
    try {
      // Créer un cercle de fond
      const circle = new fabric.Circle({
        radius: 25,
        fill: iconColor,
        stroke: '#ffffff',
        strokeWidth: 3,
        shadow: new fabric.Shadow({
          color: 'rgba(0,0,0,0.3)',
          blur: 8,
          offsetX: 2,
          offsetY: 2
        })
      });

      // Créer l'icône en tant que texte (Material Icons)
      const iconText = new fabric.Text(this.getIconCharacter(iconName), {
        fontSize: 24,
        fill: '#ffffff',
        fontFamily: 'Material Icons',
        textAlign: 'center',
        originX: 'center',
        originY: 'center'
      });

      // Créer le texte du nom du capteur
      const nameText = new fabric.Text(capteur.FriendlyName, {
        fontSize: 12,
        fill: '#333333',
        fontFamily: 'Arial, sans-serif',
        textAlign: 'center',
        originX: 'center',
        originY: 'center',
        top: 40,
        backgroundColor: 'rgba(255,255,255,0.9)',
        padding: 4
      });

      // Créer un groupe avec tous les éléments
      const group = new fabric.Group([circle, iconText, nameText], {
        left: x - 25,
        top: y - 25,
        selectable: true,
        hasControls: true,
        hasBorders: true
      });

      return group;

    } catch (error) {
      console.error('Erreur lors de la création de l\'icône capteur:', error);
      // Fallback: créer un cercle simple
        return null;
    }
  }

  // Mapper les noms d'icônes Material vers les caractères
  getIconCharacter(iconName: string): string {
    const iconMap: { [key: string]: string } = {
      'water_drop': '💧',
      'sensor_door': '🚪',
      'motion_photos_on': '🏃',
      'device_thermostat': '🌡️',
      'power': '🔌',
      'wb_sunny': '☀️',
      'air': '💨',
      'window': '🪟',
      'sensors': '👁️',
      'vibration': '📳',
      'radio_button_checked': '🔘',
      'smoke_free': '🚭',
      'toggle_on': '🔛',
      'hub': '🌐',
      'thermostat': '🌡️',
      'device_unknown': '❓'
    };
    return iconMap[iconName] || '📡';
  }

  // Réinitialiser la sélection de capteur
  resetCapteurSelection(): void {
    this.selectedCapteur = null;
    this.isAddingCapteur = false;
    this.selectedTool = '';
    this.canvas.defaultCursor = 'default';
  }


  
// Méthode pour afficher les informations d'un capteur
  showCapteurDetails(capteur: Capteur): void {
    console.log('Détails du capteur:', {
      nom: capteur.FriendlyName,
      modele: capteur.Model,
      fabricant: capteur.Manufacturer,
      type: capteur.TypeCapteur?.DisplayName,
      derniereActivite: capteur.LastSeen
    });
    
    // Ici vous pouvez ouvrir un modal ou un panneau avec les détails
    // this.openCapteurModal(capteur);
  }
// Nouvelles méthodes pour la gestion des capteurs sur le canvas
  getCapteurInfo(obj: fabric.Object): Capteur | null {
    return obj.get('capteurData') || null;
  }
   // Méthode pour mettre en évidence un capteur sur le plan
  highlightCapteur(capteur: Capteur): void {
    this.canvas.getObjects().forEach(obj => {
      const capteurData = this.getCapteurInfo(obj);
      if (capteurData && capteurData.Id === capteur.Id) {
        // Sauvegarder les valeurs originales
        const originalScaleX = obj.scaleX || 1;
        const originalScaleY = obj.scaleY || 1;
        const originalStroke = obj.stroke;
        const originalStrokeWidth = obj.strokeWidth || 0;
        
        // Animer l'agrandissement avec effet de pulsation
        obj.animate({
          scaleX: originalScaleX * 1.3,
          scaleY: originalScaleY * 1.3,
          strokeWidth: 4
        }, {
          duration: 400,
          easing: fabric.util.ease.easeOutBounce,
          onChange: () => this.canvas.renderAll(),
          onComplete: () => {
            // Retour à la taille normale
            obj.animate({
              scaleX: originalScaleX,
              scaleY: originalScaleY,
              strokeWidth: originalStrokeWidth
            }, {
              duration: 400,
              easing: fabric.util.ease.easeOutBounce,
              onChange: () => this.canvas.renderAll(),
              onComplete: () => {
                // Restaurer le stroke original
                obj.set('stroke', originalStroke);
                this.canvas.renderAll();
              }
            });
          }
        });
        
        // Ajouter un effet de stroke coloré temporaire
        obj.set({
          stroke: '#FFD700', // Couleur dorée pour la mise en évidence
          strokeWidth: 4
        });
        
        // Sélectionner l'objet et centrer la vue
        this.canvas.setActiveObject(obj);
        this.centerViewOnObject(obj);
        this.canvas.renderAll();
      }
    });
  }



  // Méthode pour centrer la vue sur un objet
  private centerViewOnObject(obj: fabric.Object): void {
    const canvasCenter = this.canvas.getCenter();
    const objCenter = obj.getCenterPoint();
    
    // Calculer le décalage nécessaire
    const deltaX = canvasCenter.left - objCenter.x;
    const deltaY = canvasCenter.top - objCenter.y;
    
    // Déplacer la vue (si vous avez une vue pannable)
    // Cette partie dépend de votre implémentation du zoom/pan
    // Vous pouvez l'adapter selon vos besoins
    
    console.log(`Capteur ${obj.get('capteurData')?.FriendlyName} mis en évidence à la position:`, objCenter);
  }

}