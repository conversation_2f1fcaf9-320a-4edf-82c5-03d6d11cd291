// src/app/core/models/rules.ts
import { ControllerServerRule } from "./controllerServerRule";
import { RuleTag } from "./ruleTag";
import { RuleTransaction } from "./ruleTransaction";

export interface Rules {
    Id: string;
    RawData: string;
    enabled: boolean;
    priority: number;
    controllerServerRules: ControllerServerRule[];
    ruleTransactions: RuleTransaction[];
    ruleTags: RuleTag[];
}

// DTOs matching the C# backend DTOs

// New Interfaces for Pagination and Lister

export class Pagination {
  CurrentPage!: number;
  PageCount!: number;
  PageSize!: number;
  IsLast!: boolean;
  IsFirst!: boolean;
  StartIndex!: number;
  TotalElement!: number;
}

export class Sorting {
  Column!: string;
  Sort!: string; // "ASC" or "DESC"
}

export class WhereParams {
  Column!: string;
  Value!: string;
  Operand!: string;
}

export class Lister<T> {
  Pagination!: Pagination;
  SortParams!: Sorting[];
  FilterParams!: WhereParams[];
}

export class PagedResponse<T> {
  Content!: T;
  Lister!: Lister<T>; // Using any for Lister as its type will be inferred by the generic T in PagedResponse
}

export interface RuleWithStats {
  RuleId: string;
  CreatedAt: string;
  CreatedBy: string;
  LastUpdatedAt: string | null;
  LastUpdatedBy: string | null;
  DeletedAt: string | null;
  DeletedBy: string | null;
  Enabled: boolean;
  Priority: number;
  RawData: string;
  TotalTransactions: number;
  TotalClients: number;
  TotalSites: number;
  TotalLocals: number;
  LastTriggered: string | null;
  TotalApplications: number;
}

export interface RuleWithTag {
  RuleId: string;
  Enabled: boolean;
  Priority: number;
  RawData: string;
  CreatedAt: string;
  LastUpdatedAt: string | null;
  TagId: string | null;
  TagCreatedAt: string | null;
  TagName: string | null;
  TagIsActive: boolean | null;
}

export interface RuleClientHierarchy {
  RuleId: string;
  RuleEnabled: boolean;
  RulePriority: number;
  ClientId: string;
  ClientOrganisationId: string;
  ClientRC: string;
  ClientRegion: string;
  ClientSIREN: string;
  ClientSIRET: string;
  ClientActiveEquipment: number;
  SiteId: string;
  SiteName: string;
  SiteEmail: string;
  SiteDescription: string;
  SiteImage: string;
  SiteContact: string;
  SiteManager: string;
  SitePhoneNumber: string;
  SiteStatus: string;
  SiteAddressComplement: string;
  SiteAddress: string;
  SiteEmployeeCount: number;
  SiteGrade: string;
  SiteLatitude: number | null;
  SiteLocalCount: number;
  SiteLongitude: number | null;
  LocalId: string;
  LocalCreatedAt: string;
  LocalCreatedBy: string;
  LocalLastUpdatedAt: string | null;
  LocalLastUpdatedBy: string | null;
  LocalDeletedAt: string | null;
  LocalDeletedBy: string | null;
  LocalArchitecture2DImage: string;
  LocalCapacity: number;
  LocalBaseTopicMQTT: string;
  LocalFloor: number;
  LocalImageLocal: string;
  LocalName: string;
  LocalSensorCount: number;
  LocalLatitude: number | null;
  LocalLongitude: number | null;
}

export interface RuleTransactionDetail {
  RuleId: string;
  RuleEnabled: boolean;
  TransactionId: string;
  ControllerInControl: boolean;
  ControllerId: string | null;
  ControllerIdController: string | null;
  ControllerLocalId: string;
  TransactionCreatedAt: string;
  TransactionCreatedBy: string;
  TransactionLastUpdatedAt: string | null;
  TransactionLastUpdatedBy: string | null;
  RuleTransactionId: string;
  ApplicationTimestamp: string;
  ApplicationCreatedBy: string;
  ApplicationLastUpdatedBy: string;
  LocalName: string;
  LocalBaseTopicMQTT: string;
  SiteName: string;
  SiteAddress: string;
}

export interface RulePerformanceAnalytic {
  RuleId: string;
  ApplicationDate: string;
  ApplicationHour: number;
  ApplicationDayOfWeek: string;
  TotalApplications: number;
  SuccessfulApplications: number;
  FailedApplications: number;
  AvgExecutionTimeMs: number | null;
  LocalId: string;
  LocalName: string;
}

export interface RuleComprehensive {
  RuleId: string;
  Enabled: boolean;
  Priority: number;
  RawData: string; // This will hold the JSON for conditions/actions/name
  RuleCreatedAt: string;
  RuleLastUpdatedAt: string | null;
  TotalApplications: number;
  TotalClients: number;
  TotalSites: number;
  TotalLocals: number;
  LastTriggered: string | null;
  SuccessfulApplications: number;
  FailedApplications: number;
  Status: 'active' | 'inactive';
  TagsString: string | null;
}

export interface RuleRecentApplication {
  RuleId: string;
  TransactionId: string;
  ApplicationTimestamp: string;
  ApplicationSuccess: boolean;
  LocalName: string;
  SiteName: string;
  RowNum: number; // Keep this as it's part of the view output, even if not explicitly used in the template.
}

export interface RuleWithTagsString {
  RuleId: string;
  TagsString: string | null;
}


// Interfaces for the component's internal rule representation (parsed from RawData)
// These define the structure of the JSON expected in RuleComprehensive.rawData
export interface Condition {
  type: string;
  operator: string;
  value: string;
  inputType?: string; // from getRulePreviewJson
  id?: number; // from getRulePreviewJson
  deviceId?: string | null; // from getRulePreviewJson
  propertyName?: string; // from getRulePreviewJson
}

export interface Action {
  type: string;
  action: string;
  value: string;
  target: string;
  outputType?: string; // from getRulePreviewJson
  id?: number; // from getRulePreviewJson
  deviceId?: string | null; // from getRulePreviewJson
  actionType?: string; // from getRulePreviewJson
  actionValue?: string; // from getRulePreviewJson
}

export interface RuleApplication {
  timestamp: string;
  success: boolean;
  executionTime: number; // in milliseconds
}

export interface Controller {
  id: string;
  name: string;
  model: string;
  lastSeen: string;
  status: 'online' | 'offline' | 'warning' | 'unknown';
  applications: RuleApplication[];
  performanceAnalytics: RulePerformanceAnalytic[]; // Added this line
}

export interface Location {
  id: string; // Guid
  name: string;
  type: string; // 'room', 'floor', 'building'
  controllers: Controller[];
}

export interface Site {
  id: string; // Guid
  name: string;
  address: string;
  clientId: string; // Guid
  locations: Location[];
}

export interface Client {
  id: string; // Guid
  name: string;
  email: string;
  company: string;
  status: 'active' | 'inactive';
  sites: Site[];
}

export interface RuleForm {
  id: string;
  name: string;
  priority: number;
  Status: 'active' | 'inactive';
  tags: string; // Comma-separated string for input
  conditions: Condition[];
  actions: Action[];
  tagStatus: { [key: string]: string }; // To track status of each tag
  ruleName?: string; // From getRulePreviewJson
  topicPattern?: string; // Comma-separated string for input
  subjectPattern?: string; // From getRulePreviewJson
  isActive?: boolean; // For form checkbox
  hasSchedule?: boolean;
  scheduleTime?: string;
  status?: string; // For edit form
}

// Client-side comprehensive rule content for display
export interface RuleContent {
  id: string;
  name: string;
  priority: number;
  status: 'active' | 'inactive';
  tags: string[];
  conditions: Condition[];
  actions: Action[];
  tagStatus: { [key: string]: string };
  totalApplications: number;
  lastTriggered: string;
  clients: Client[]; // Aggregated client hierarchy
}

// Internal interface for the raw data structure as it comes from the backend's `rawData` field
export interface RawDataBackendStructure {
  rule_name: string;
  topic_pattern: string[];
  conditions: {
    groups: Array<{
      operator: string;
      conditions: Array<{
        type: string; // e.g., "sensor_data"
        device: string; // e.g., "sensor_cube"
        key: string; // e.g., "side"
        operator: string; // e.g., "=="
        value: string; // e.g., "2"
      }>;
    }>;
  };
  actions: Array<{
    type: string; // e.g., "publish"
    topic: string; // e.g., "zigbee2mqtt/roller_shade/set"
    payload: { [key: string]: any }; // e.g., { "position": 25 }
  }>;
  schedule_config?: {
    enabled: boolean;
    time?: string;
    cron_expression?: string;
  };
  enabled: boolean;
  priority: number;
}
