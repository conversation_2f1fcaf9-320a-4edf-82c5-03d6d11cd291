import { Injectable } from "@angular/core";
import { ApiService } from "../api.service";
import { HttpClient } from "@angular/common/http";
import { ControllerServerController } from "@app/core/models/controllerServerController";
import { Observable } from "rxjs";

@Injectable({ providedIn: 'root' })
export class ControllerServerControllerApiService extends ApiService<ControllerServerController> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint("controller-serveur-controller");
  }

  getByControllerId(controllerId: string): Observable<ControllerServerController[]> {
    // If your base service supports query parameters
    return this.http.get<ControllerServerController[]>(`${this.baseUrl}?idController=${controllerId}`);
    
    // OR if you have a method in base service for query params
    // return this.getAllWithParams({ idController: controllerId });
  }
}