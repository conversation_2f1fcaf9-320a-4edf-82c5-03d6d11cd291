import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { TagApiService } from '@app/core/services/administrative/tag.service';
import { Tag } from '@app/core/models/tag';

@Component({
  selector: 'app-tag-list',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTabsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatTableModule,
    MatCardModule,
    MatSnackBarModule
  ],
  templateUrl: './tag-list.component.html',
  styleUrls: ['./tag-list.component.css']
})
export class TagListComponent implements OnInit {
  // Forms
  tagForm: FormGroup;

  // Data
  tags: Tag[] = [];

  // UI State
  isLoading = false;
  selectedTab = 0;
  showValidationErrors = false;

  // Table columns
  tagColumns = ['nom', 'createdAt', 'actions'];

  constructor(
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private tagService: TagApiService
  ) {
    this.tagForm = this.createTagForm();
  }

  ngOnInit(): void {
    this.loadTags();
  }

  private createTagForm(): FormGroup {
    return this.fb.group({
      id: [''],
      nom: ['', [Validators.required, Validators.minLength(2)]]
    });
  }

  private async loadTags(): Promise<void> {
    this.isLoading = true;
    try {
      this.tags = await this.tagService.getAll().toPromise() || [];
    } catch (error) {
      console.error('Error loading tags:', error);
      this.showError('Erreur lors du chargement des tags');
    } finally {
      this.isLoading = false;
    }
  }

  async onSubmitTag(): Promise<void> {
    this.showValidationErrors = true;
    
    if (this.tagForm.invalid) {
      return;
    }

    this.isLoading = true;
    
    try {
      const formValue = this.tagForm.value;
      
      if (formValue.id) {
        // Update existing tag
        await this.tagService.update(formValue).toPromise();
        this.showSuccess('Tag mis à jour avec succès');
      } else {
        // Create new tag
        await this.tagService.create(formValue).toPromise();
        this.showSuccess('Tag créé avec succès');
      }
      
      this.resetTagForm();
      await this.loadTags();
    } catch (error) {
      console.error('Error saving tag:', error);
      this.showError('Erreur lors de la sauvegarde du tag');
    } finally {
      this.isLoading = false;
    }
  }

  editTag(tag: Tag): void {
    this.tagForm.patchValue(tag);
    this.selectedTab = 0; // Switch to tag creation tab
  }

  async deleteTag(tag: Tag): Promise<void> {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce tag ?')) {
      return;
    }

    this.isLoading = true;
    
    try {
      await this.tagService.delete(tag.Id!).toPromise();
      this.showSuccess('Tag supprimé avec succès');
      await this.loadTags();
    } catch (error) {
      console.error('Error deleting tag:', error);
      this.showError('Erreur lors de la suppression du tag');
    } finally {
      this.isLoading = false;
    }
  }

  resetTagForm(): void {
    this.tagForm.reset();
    this.showValidationErrors = false;
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }
}
