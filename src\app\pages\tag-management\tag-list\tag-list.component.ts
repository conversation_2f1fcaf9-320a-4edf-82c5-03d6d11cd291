import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { TagApiService } from '@app/core/services/administrative/tag.service';
import { Tag } from '@app/core/models/tag';
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';

@Component({
  selector: 'app-tag-list',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatPaginatorModule,
    MatSnackBarModule,
    GenericTableComponent
  ],
  templateUrl: './tag-list.component.html',
  styleUrls: ['./tag-list.component.css']
})
export class TagListComponent implements OnInit {
  // Data
  tags: Tag[] = [];
  filteredTags: Tag[] = [];
  paginatedTags: Tag[] = [];

  // UI State
  isLoading = false;
  showCreateForm = false;
  showEditForm = false;
  selectedTag: Tag | null = null;

  // Pagination
  currentPage = 0;
  pageSize = 10;
  totalCount = 0;

  // Search
  searchTerm = '';

  // Table configuration
  headers: string[] = ['Nom', 'Date de Création'];
  keys: string[] = ['Nom', 'CreatedAt'];

  // Forms
  createTagForm: FormGroup;
  editTagForm: FormGroup;

  constructor(
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private tagService: TagApiService
  ) {
    this.createTagForm = this.createForm();
    this.editTagForm = this.createForm();
  }

  ngOnInit(): void {
    this.loadTags();
  }

  private createForm(): FormGroup {
    return this.fb.group({
      id: [''],
      nom: ['', [Validators.required, Validators.minLength(2)]]
    });
  }

  private async loadTags(): Promise<void> {
    this.isLoading = true;
    try {
      this.tags = await this.tagService.getAll().toPromise() || [];
      this.applyFilters();
    } catch (error) {
      console.error('Error loading tags:', error);
      this.showError('Erreur lors du chargement des tags');
    } finally {
      this.isLoading = false;
    }
  }

  private applyFilters(): void {
    this.filteredTags = this.tags.filter(tag =>
      tag.Nom.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
    this.totalCount = this.filteredTags.length;
    this.updatePaginatedTags();
  }

  private updatePaginatedTags(): void {
    const startIndex = this.currentPage * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.paginatedTags = this.filteredTags.slice(startIndex, endIndex);
  }

  onSearch(): void {
    this.currentPage = 0;
    this.applyFilters();
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.updatePaginatedTags();
  }

  // Show/Hide forms
  showAddTagForm(): void {
    this.showCreateForm = true;
    this.createTagForm.reset();
  }

  hideCreateTagForm(): void {
    this.showCreateForm = false;
    this.createTagForm.reset();
  }

  showEditTagForm(tag: Tag): void {
    this.selectedTag = tag;
    this.editTagForm.patchValue({
      id: tag.Id,
      nom: tag.Nom
    });
    this.showEditForm = true;
  }

  hideEditTagForm(): void {
    this.showEditForm = false;
    this.selectedTag = null;
    this.editTagForm.reset();
  }

  // Form submissions
  async submitCreateForm(): Promise<void> {
    if (this.createTagForm.invalid) {
      return;
    }

    this.isLoading = true;

    try {
      const formValue = this.createTagForm.value;
      const tag: Tag = {
        Id: this.generateUUID(),
        Nom: formValue.nom,
        RuleTags: []
      };

      await this.tagService.create(tag).toPromise();
      this.showSuccess('Tag créé avec succès');
      this.hideCreateTagForm();
      await this.loadTags();
    } catch (error) {
      console.error('Error creating tag:', error);
      this.showError('Erreur lors de la création du tag');
    } finally {
      this.isLoading = false;
    }
  }

  async submitEditForm(): Promise<void> {
    if (this.editTagForm.invalid || !this.selectedTag) {
      return;
    }

    this.isLoading = true;

    try {
      const formValue = this.editTagForm.value;
      const updatedTag: Tag = {
        ...this.selectedTag,
        Nom: formValue.nom
      };

      await this.tagService.update(updatedTag).toPromise();
      this.showSuccess('Tag mis à jour avec succès');
      this.hideEditTagForm();
      await this.loadTags();
    } catch (error) {
      console.error('Error updating tag:', error);
      this.showError('Erreur lors de la mise à jour du tag');
    } finally {
      this.isLoading = false;
    }
  }

  async deleteTag(tag: Tag): Promise<void> {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce tag ?')) {
      return;
    }

    this.isLoading = true;

    try {
      await this.tagService.delete(tag.Id!).toPromise();
      this.showSuccess('Tag supprimé avec succès');
      await this.loadTags();
    } catch (error) {
      console.error('Error deleting tag:', error);
      this.showError('Erreur lors de la suppression du tag');
    } finally {
      this.isLoading = false;
    }
  }

  // Handle table actions
  handleAction(event: { action: string; row: any }): void {
    const { action, row } = event;
    if (action === 'edit') {
      this.showEditTagForm(row);
    } else if (action === 'delete') {
      this.deleteTag(row);
    }
  }

  // Utility methods
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }
}
