import { ControllerServerController } from "./controllerServerController";
import { ControllerServerRule } from "./controllerServerRule";
import { Licence } from "./licence";
import { AuditModel } from "./models-audit/audit-model";

export interface ControllerServeur extends AuditModel {
    Name: string;
    MaxControllers: number;
    MaxSensors: number;
    GeographicZone: string;
    CommercialCondition: string;
    TriggerType: string;
    ActionType: string;
    EventType: string;
    Status: string;
    IdLicence: string;
    Licence: Licence;
    ControllerServerControllers: ControllerServerController[];
    ControllerServerRules: ControllerServerRule[];
}


