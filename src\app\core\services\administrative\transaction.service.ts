import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';import { ApiService } from '../api.service';
import { Transaction } from '@app/core/models/transaction';


@Injectable({ providedIn: 'root' })
export class TransactionApiService extends ApiService<Transaction> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint("transaction");
  }
}
