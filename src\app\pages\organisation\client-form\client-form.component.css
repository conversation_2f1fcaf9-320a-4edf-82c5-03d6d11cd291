::ng-deep .custom-dialog {
    min-width: 900px !important;
    min-height: max-content !important;
    border-radius: 5px !important;
}

::ng-deep .confirmation-message>p {
    font-size: 1rem !important;
    align-items: center !important;
    justify-content: center;
}

::ng-deep .confirmation-message {
    align-items: center !important;
}

::ng-deep .mdc-dialog__surface {
    border-radius: 15px !important;
}

::ng-deep .cdk-global-overlay-wrapper {
    align-items: center;
    justify-content: center;
}

/* this component styles */

.pop-up-container {
    padding: 25px;
    background-color: white;
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e2e8f0;
}

.form-title {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 20px;
    color: #2d3748;
}

.title-icon {
    font-size: 26px;
    color: #49b38d;
}

.submit-button{
  background-color: #49b38d !important;
}

.submit-button:hover{
  background-color: #49b38cab !important;
}

.close-button {
    background: none;
    border-radius: 50% !important;
    border: none !important;
    border-color: transparent !important;
    color: #6b7280;
    cursor: pointer;
    padding: 5px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
}

.close-button:hover {
    background-color: #f3f4f6;
    color: #ef4444;
}

.close-button mat-icon {
    font-size: 20px;
}

.validation-errors-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.validation-errors-list li {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 14px;
  color: #b91c1c;
}
.validation-errors {
  background-color: #fee2e2;
  border-left: 4px solid #ef4444;
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 4px;
}


.validation-errors-list li:last-child {
  margin-bottom: 0;
}

.validation-errors-list li i {
  font-size: 16px;
}

.validation-errors-title {
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-invalid {
    background-color: rgba(255, 0, 0, 0.226) !important;
    border-radius: 12px;
    padding: 3px;
    margin-bottom: 5px;
}

.form-invalid>ul>li {
    color: rgba(56, 3, 3, 0.694) !important;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 16px 24px;
    width: 100%;
    overflow-y: scroll !important;
    max-height: 640px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-size: 14px;
    font-weight: 500;
    color: #2d3748;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 10px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    color: #4a5568;
    background-color: white;
    transition: all 0.2s ease;
    box-sizing: border-box;
    width: 100%;
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-group input[type="file"] {
    padding: 8px;
    border: 2px dashed #e2e8f0;
    background-color: #f8fafc;
    cursor: pointer;
}

.required {
    color: #ef4444;
    margin-left: 4px;
}

.form1{
margin: 10px;
}

.show-more-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 2px solid #49b38d;
  border-radius: 8px;
  background: transparent;
  color: #49b38d;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.show-more-button:hover {
  background: #e8f5e9;
  transform: translateY(-2px);
}

.show-more-button .material-icons {
  font-size: 20px;
  transition: transform 0.3s ease;
}

.show-more-button:hover .material-icons {
  transform: translateY(2px);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
    margin-top: 5px;
}

.form-actions button {
    padding: 10px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    margin-left: 10px;
    transition: all 0.2s ease;
}

.form-actions button[type="button"] {
    background-color: #fff;
    border: 1px solid #e2e8f0;
    color: #4a5568;
}

.form-actions button[type="button"]:hover {
    background-color: #f8fafc;
}

.form-actions button[type="submit"] {
    background: #4CAF50;
    border: none;
    color: white;
}

.form-actions button[type="submit"]:hover {
    background: #3e8e41;
}

.form-actions button[type="submit"]:disabled {
    background: #e2e8f0;
    cursor: not-allowed;
}

.file-input-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.file-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 6px;
  color: #495057;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  text-align: center;
  justify-content: center;
  max-height: 44px;
  width: 100%;
}

.file-button:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
  color: #212529;
}

.file-button:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.file-button .material-icons {
  font-size: 18px;
}

.file-info {
  font-size: 12px;
  color: #6c757d;
  padding: 4px 8px;
  background-color: #f1f3f4;
  border-radius: 4px;
  border-left: 3px solid #28a745;
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.logo{
    width: 100%;
}

.display-none{
    display: none !important;
}

.confirm-btn[_ngcontent-ng-c1412875956]{
  background-color: #49b38d;
}