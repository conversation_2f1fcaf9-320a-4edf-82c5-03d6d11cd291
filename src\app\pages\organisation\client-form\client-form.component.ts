import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Client } from '@app/core/models/client'; // Adjust path as needed
import { formElements } from '../form-element';
import { FormaValidationService } from '@app/shared/services/forma-validation.service';
import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { NgToastComponent, NgToastService, TOAST_POSITIONS } from 'ng-angular-popup';
import { NumberSpacingPipe } from '@app/pips/number-spacing.pipe';

@Component({
  selector: 'app-client-form',
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatIconModule,
    NgToastComponent,
    NumberSpacingPipe,
  ],
  templateUrl: './client-form.component.html',
  styleUrl: './client-form.component.css',
  exportAs: 'clientForm'
})
export class ClientFormComponent implements OnInit {
  clientData: Client;
  isEditMode: boolean = false;
  showMoreFields: boolean = false;
  showErrorMessages: string[] = [];
  uploadedLogo: File | null = null;
  base64Image: string = "";
  TOAST_POSITIONS = TOAST_POSITIONS;
  datePipe = new DatePipe('en-US');
  date: string = "";
  rawValue: string = "";

  countries: string[] = [
  "Afghanistan", "Albanie", "Algérie", "Andorre", "Angola", "Antigua-et-Barbuda", "Argentine",
  "Arménie", "Australie", "Autriche", "Azerbaïdjan", "Bahamas", "Bahreïn", "Bangladesh", "Barbade",
  "Biélorussie", "Belgique", "Belize", "Bénin", "Bhoutan", "Bolivie", "Bosnie-Herzégovine", "Botswana",
  "Brésil", "Brunei", "Bulgarie", "Burkina Faso", "Burundi", "Cap-Vert", "Cambodge", "Cameroun",
  "Canada", "République centrafricaine", "Tchad", "Chili", "Chine", "Colombie", "Comores", "Congo",
  "Costa Rica", "Croatie", "Cuba", "Chypre", "République tchèque", "Danemark", "Djibouti", "Dominique",
  "République dominicaine", "Équateur", "Égypte", "El Salvador", "Guinée équatoriale", "Érythrée", "Estonie",
  "Eswatini", "Éthiopie", "Fidji", "Finlande", "France", "Gabon", "Gambie", "Géorgie", "Allemagne", "Ghana",
  "Grèce", "Grenade", "Guatemala", "Guinée", "Guinée-Bissau", "Guyana", "Haïti", "Honduras", "Hongrie",
  "Islande", "Inde", "Indonésie", "Iran", "Irak", "Irlande", "Israël", "Italie", "Côte d'Ivoire", "Jamaïque",
  "Japon", "Jordanie", "Kazakhstan", "Kenya", "Kiribati", "Koweït", "Kirghizistan", "Laos", "Lettonie",
  "Liban", "Lesotho", "Libéria", "Libye", "Liechtenstein", "Lituanie", "Luxembourg", "Madagascar",
  "Malawi", "Malaisie", "Maldives", "Mali", "Malte", "Îles Marshall", "Mauritanie", "Maurice",
  "Mexique", "Micronésie", "Moldavie", "Monaco", "Mongolie", "Monténégro", "Maroc", "Mozambique",
  "Myanmar", "Namibie", "Nauru", "Népal", "Pays-Bas", "Nouvelle-Zélande", "Nicaragua", "Niger", "Nigéria",
  "Corée du Nord", "Macédoine du Nord", "Norvège", "Oman", "Pakistan", "Palaos", "Panama", "Papouasie-Nouvelle-Guinée",
  "Paraguay", "Pérou", "Philippines", "Pologne", "Portugal", "Qatar", "Roumanie", "Russie", "Rwanda",
  "Saint-Christophe-et-Niévès", "Sainte-Lucie", "Saint-Vincent-et-les-Grenadines", "Samoa", "Saint-Marin",
  "Sao Tomé-et-Principe", "Arabie saoudite", "Sénégal", "Serbie", "Seychelles", "Sierra Leone", "Singapour",
  "Slovaquie", "Slovénie", "Îles Salomon", "Somalie", "Afrique du Sud", "Corée du Sud", "Soudan du Sud",
  "Espagne", "Sri Lanka", "Soudan", "Suriname", "Suède", "Suisse", "Syrie", "Taïwan", "Tadjikistan",
  "Tanzanie", "Thaïlande", "Timor oriental", "Togo", "Tonga", "Trinité-et-Tobago", "Tunisie", "Turquie",
  "Turkménistan", "Tuvalu", "Ouganda", "Ukraine", "Émirats arabes unis", "Royaume-Uni",
  "États-Unis", "Uruguay", "Ouzbékistan", "Vanuatu", "Vatican", "Venezuela", "Viêt Nam",
  "Yémen", "Zambie", "Zimbabwe"
];

  formesJuridiques: string[] = [
    "Auto-entrepreneur",         // (ou Micro-entreprise)
    "Entreprise individuelle (EI)",
    "Entreprise individuelle à responsabilité limitée (EIRL)",
    "Société à responsabilité limitée (SARL)",
    "Société anonyme (SA)",
    "Société par actions simplifiée (SAS)",
    "Société par actions simplifiée unipersonnelle (SASU)",
    "Société en nom collectif (SNC)",
    "Société en commandite simple (SCS)",
    "Société en commandite par actions (SCA)",
    "Société civile",
    "Société civile professionnelle (SCP)",
    "Société civile immobilière (SCI)",
    "Groupement d'intérêt économique (GIE)",
    "Groupement d'intérêt public (GIP)",
    "Coopérative",
    "Association",
    "Société à responsabilité limitée à associé unique (EURL)",
    "Société unipersonnelle"
  ];

  secteursActivite: {
    code: string;
    label: string;
    sousSecteurs: { code: string; label: string }[];
  }[] = [
      {
        code: 'INDU',
        label: 'Industrie',
        sousSecteurs: [
          { code: 'AGRO', label: 'Agroalimentaire' },
          { code: 'TEXT', label: 'Textile et habillement' },
          { code: 'CHIM', label: 'Chimie et pharmacie' },
          { code: 'META', label: 'Métallurgie' },
          { code: 'ELECT', label: 'Électronique' },
          { code: 'AUTO', label: 'Automobile & aéronautique' }
        ]
      },
      {
        code: 'SERV',
        label: 'Services',
        sousSecteurs: [
          { code: 'INFO', label: 'Informatique et TIC' },
          { code: 'CONS', label: 'Conseil et expertise' },
          { code: 'RH', label: 'Ressources humaines' },
          { code: 'JURI', label: 'Juridique et comptabilité' },
          { code: 'MARK', label: 'Marketing et communication' },
          { code: 'IMMO', label: 'Immobilier' }
        ]
      },
      {
        code: 'COMM',
        label: 'Commerce',
        sousSecteurs: [
          { code: 'DET', label: 'Vente au détail' },
          { code: 'GRO', label: 'Commerce de gros' },
          { code: 'E_COM', label: 'E-commerce' }
        ]
      },
      {
        code: 'BTP',
        label: 'Bâtiment et construction',
        sousSecteurs: [
          { code: 'CONSTR', label: 'Construction générale' },
          { code: 'ARCHI', label: 'Architecture' },
          { code: 'TP', label: 'Travaux publics' }
        ]
      },
      {
        code: 'TRANS',
        label: 'Transport et logistique',
        sousSecteurs: [
          { code: 'ROUTIER', label: 'Transport routier' },
          { code: 'AERIEN', label: 'Transport aérien' },
          { code: 'MARITIME', label: 'Transport maritime' },
          { code: 'LOG', label: 'Logistique' }
        ]
      },
      {
        code: 'EDU',
        label: 'Éducation et formation',
        sousSecteurs: [
          { code: 'ECOLE', label: 'Établissements scolaires' },
          { code: 'SUP', label: 'Enseignement supérieur' },
          { code: 'FORM', label: 'Organisme de formation' }
        ]
      },
      {
        code: 'SANTE',
        label: 'Santé et action sociale',
        sousSecteurs: [
          { code: 'HOP', label: 'Hôpitaux et cliniques' },
          { code: 'MED', label: 'Médecins et cabinets' },
          { code: 'SOCIAL', label: 'Services sociaux' }
        ]
      },
      {
        code: 'CULT',
        label: 'Culture, sport et loisirs',
        sousSecteurs: [
          { code: 'ART', label: 'Arts et spectacles' },
          { code: 'SPORT', label: 'Activités sportives' },
          { code: 'LOISIRS', label: 'Loisirs et détente' }
        ]
      }
    ];

  constructor(
    public dialogRef: MatDialogRef<ClientFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { client: Client, organisations: any[], isEdit: boolean },
    private formValidationService: FormaValidationService,
    private dialog: MatDialog,
    private clientApiService: ClientApiService,
    private toast: NgToastService
  ) {
    this.clientData = data.client ? data.client : new Client();
    this.isEditMode = data.isEdit ? data.isEdit : false;
  }

  ngOnInit(): void {
    if (this.data.isEdit) {
      this.formatDate(this.clientData?.CompanyCreationDate.toString());
    }
  }

  formatNumber(val: string): string {
    if (!val) return '';
    return val.replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
  }

  // onInput(event: Event, name: string) {
  //   const input = (event.target as HTMLInputElement).value;
  //   this.rawValue = input.replace(/\s/g, ''); // Remove all spaces
  //   this.clientData[`${name}`] = this.formatNumber(this.rawValue);
  //   console.log("Attributs", clientAttributs);
  // }

  formatDate(dateString: string) {
    const date = new Date(dateString);
    const x = date.toISOString().split('T')[0];
    const [year, month, day] = x.split('-');
    this.date = `${year}-${month}-${day}`; // Extracts 'YYYY-MM-DD'
  }

  cancel(): void {
    this.dialogRef.close();
  }

  showError(message: string, title: string) {
    this.toast.warning(message, title, 1000, false);
  }

  showSuccess(message: string, title: string) {
    this.toast.success(message, title, 1000, false);
  }

  save(): void {
    // this.dialogRef.close(this.clientData); // return client data
    // this.showError("Erreur lors de la création de client", "Erreur");
    // return;
    this.clientData.CompanyCreationDate = new Date(this.date);
    this.showErrorMessages = this.formValidationService.validate(this.clientData, formElements);
    if (this.clientData && this.showErrorMessages.length == 0) {
      if (!this.isEditMode) {
        this.clientData.Status = "Inactif";
        this.clientData.ClientStatus = "Inactif";
        this.clientData.CompanyCreationDate = new Date(this.date);
        const dialogRefs = this.dialog.open(ConfirmationDialogComponent, {
          panelClass: "custom-confirmation",
          width: '400px',
          data: {
            title: 'Confirmation de création',
            message: 'Êtes-vous sûr de vouloir créer ce client ?',
            icon: 'add'
          }
        });
        dialogRefs.afterClosed().subscribe(result => {
          if (result) {
            this.clientApiService.create(this.clientData).subscribe({
              next: (response) => {
                this.showSuccess("Le Client a été créé avec succes", "Information");
                setTimeout(() => {
                  this.dialogRef.close(response);
                }, 1000);
              },
              error: (error: any) => {
                this.showError("Erreur lors de la création de client", "Erreur");
              },
            });
          }
        });
      }
      else {
        this.clientData.CompanyCreationDate = new Date(this.date);
        const dialogRefs = this.dialog.open(ConfirmationDialogComponent, {
          width: '400px',
          data: {
            title: 'Confirmation de modification',
            message: 'Êtes-vous sûr de vouloir modifier ce client ?',
            icon: 'edit'
          }
        });
        dialogRefs.afterClosed().subscribe(result => {
          if (result) {
            this.clientApiService.update(this.clientData).subscribe({
              next: (response) => {
                this.showSuccess("Le Client a été modifié avec succes", "Information");
                setTimeout(() => {
                  this.dialogRef.close(response);
                }, 1000);
              },
              error: (error: any) => {
                this.showError("Erreur lors de la modification de client", "Erreur");
              },
            });
          }
        });
      }
    }
  }

  onFileSelected(event: any): void {
    const file: File = event.target.files[0];
    if (file) {
      this.uploadedLogo = file;
      console.log('File selected:', file.name);

      const reader = new FileReader();
      reader.onload = () => {
        var base64String = reader.result as string;

        this.clientData.ClientLogo = base64String.split(",")[1];
        this.base64Image = base64String.split(",")[1];
        // console.log('Base64 string:', this.base64Image);
      };
      reader.onerror = (error) => {
        console.error('Error reading file:', error);
        this.uploadedLogo = null;
      };

      reader.readAsDataURL(file);
    }
    else {
      this.uploadedLogo = null;
    }
  }

  // cancel(): void {
  //   this.dialogRef.close();
  // }
}
