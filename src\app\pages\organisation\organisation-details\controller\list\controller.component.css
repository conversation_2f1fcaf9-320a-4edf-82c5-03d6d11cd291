.actions {
  display: flex;
  gap: 15px;
}

.create-button-container {
  display: flex;
  justify-content: center;
  margin: 20px 0; /* optional spacing */
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}


.create-button {
  background: linear-gradient(45deg, #4caf50, #81c784) !important;
  color: white !important;
  padding: 10px 20px !important;
  border-radius: 8px !important;
  border: none !important;
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3) !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
}

.create-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(76, 175, 80, 0.5) !important;
  background: linear-gradient(45deg, #81c784, #4caf50) !important;
}

/* Modal Overlay - Perfect centering */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  
  /* Flexbox centering - this is the key! */
  display: flex;
  align-items: center;
  justify-content: center;
  
  /* Allow scrolling if content is too tall */
  overflow-y: auto;
  padding: 20px;
  box-sizing: border-box;
}

/* Modal Container */
.modal-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  max-width: 90vw;
  max-height: 90vh;
  width: 800px; /* Adjust based on your needs */
  overflow-y: auto;
  position: relative;
  
  /* Animation */
  animation: modalSlideIn 0.3s ease-out;
  
  /* Prevent clicks from bubbling to overlay */
  pointer-events: auto;
}

/* Modal Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
  border-radius: 12px 12px 0 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #718096;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.close-button:hover {
  background-color: #e2e8f0;
  color: #2d3748;
}

.close-button i {
  font-size: 20px;
}

/* Modal Content */
.modal-overlay{
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px; /* Add padding to prevent content from touching edges */
}
.modal-content {
  padding: 0;
  max-height: calc(90vh - 100px);
  max-width: calc(90vw - 100px);
  overflow-y: auto;
  overflow-x: hidden;
}

mat-icon {
  vertical-align: middle;
  margin-right: 8px;
}

/* Animation */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modal-container {
    width: 95vw;
    max-width: 95vw;
    margin: 0;
  }
  
  .modal-overlay {
    padding: 10px;
  }
  
  .modal-header {
    padding: 15px 20px;
  }
  
  .modal-header h3 {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .modal-container {
    width: 100vw;
    max-width: 100vw;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
  }
  
  .modal-overlay {
    padding: 0;
  }
  
  .modal-header {
    border-radius: 0;
  }
}

/* Prevent body scrolling when modal is open */
body.modal-open {
  overflow: hidden;
}

.no-sites-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  text-align: center;
  color: #718096;
}

.no-sites-message .material-icons {
  font-size: 48px;
  color: #cbd5e0;
  margin-bottom: 15px;
}

.no-sites-message p {
  margin: 0 0 20px;
  font-size: 16px;
}

.table-container,.controller-section{
  animation: fadeIn 0.5s ease-out;
}

.search-section {
  margin: 20px 0;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-container {
  display: flex;
  align-items: center;
  gap: 12px;
  max-width: 500px;
}

.search-input {
  flex: 1;
  height: 025px;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: white;
}

.search-input:focus {
  border-color: #4CAF50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  outline: none;
}

.search-input::placeholder {
  color: #a0aec0;
}

.search-button,
.clear-button {
  height: 40px;
  padding: 0 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s ease;
  min-width: 80px;
}

.search-button {
  background: linear-gradient(45deg, #4CAF50, #81C784);
  color: white;
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

.search-button:hover {
  background: linear-gradient(45deg, #81C784, #4CAF50);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.clear-button {
  background: #f44336;
  color: white;
  box-shadow: 0 2px 6px rgba(244, 67, 54, 0.3);
}

.clear-button:hover {
  background: #d32f2f;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
}