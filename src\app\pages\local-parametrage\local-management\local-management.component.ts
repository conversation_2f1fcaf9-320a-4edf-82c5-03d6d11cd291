import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FormsModule, ReactiveFormsModule, FormGroup, FormControl, Validators } from '@angular/forms';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { Local } from '@app/core/models/local';
import { TypeLocal } from "@app/core/models/TypeLocal.1";
import { PageEvent } from '@angular/material/paginator';
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';
import { LocalApiService, TypeLocalApiService } from '@app/core/services/administrative/local.service';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatIconModule } from '@angular/material/icon';
import { SiteApiService } from '@app/core/services/administrative/site.service';
import { Site } from '@app/core/models/site';
import { <PERSON>, Lister, SortPage, FilterParam } from '@app/core/models/util/page';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { NgToastComponent, NgToastService, TOAST_POSITIONS } from 'ng-angular-popup';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';


interface IndexableLocal extends Local {
  [key: string]: any;
}

@Component({
  selector: 'app-local-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    GenericTableComponent,
    MatPaginatorModule,
    MatIconModule,
    MatDialogModule,
    NgxUiLoaderModule,
    ConfirmationDialogComponent,
    NgToastComponent
  ],
  templateUrl: './local-management.component.html',
  styleUrls: ['./local-management.component.css'],
  animations: [
    trigger('tableRowAnimation', [
      state('void', style({ opacity: 0, transform: 'translateY(20px)' })),
      transition('void => *', animate('300ms ease-in'))
    ]),
    trigger('fadeIn', [
      state('void', style({ opacity: 0 })),
      transition('void => *', animate('400ms 300ms ease-in'))
    ])
  ]
})
export class LocalManagementComponent implements OnInit {
  TOAST_POSITIONS = TOAST_POSITIONS;

  constructor(
    private readonly localService: LocalApiService,
    private readonly siteService: SiteApiService,
    private readonly typeLocalService: TypeLocalApiService,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private dialog: MatDialog,
    private toast: NgToastService,
    private ngxUiLoaderService: NgxUiLoaderService
  ) {}

  @ViewChild('editFormSection') editFormSection!: ElementRef;
  sites: any[] = [];
  typeLocals: TypeLocal[] = [];
  isLoading: boolean = true;
  showCreateForm: boolean = false;
  uploadedImages: File[] = [];
  viewMode: string = 'table';
  currentPage: number = 0;
  pageSize: number = 5;
  paginatedLocals: Local[] = [];
  totalCount: number = 0;
  showEditForm: boolean = false;
  selectedLocal: Local | null = null;
  editUploadedImages: File[] = [];
  architecture2DPreview: string | null = null;
  imageLocalPreview: string | null = null;
  isSubmitting: boolean = false;
  searchTerm: string = '';
  locals: any[] = []; // all data
  filteredLocals: any[] = []; // data to display

  filterLocals(): void {
    const rawTerm = this.searchTerm.trim();
    
    if (!rawTerm) {
      this.loadLocals();
      return;
    }
  
    const searchTerm = rawTerm.toLowerCase();
    const filters: FilterParam[] = [];

    const isNumeric = !isNaN(Number(rawTerm)) && rawTerm !== '';
    const numericValue = Number(rawTerm);

    if (searchTerm) {
      if (isNumeric) {
        // Numeric search with specific numeric operators
        ['Floor', 'SensorsCount', 'Capacity'].forEach((field, index) => {
          filters.push({
            column: field,
            value: numericValue,
            op: 'equals',  // Use 'equals' for numeric comparison
            andOr: index === 0 ? undefined : 'OR'
          });
        });
      } else {
        // Text search
        ['Name'].forEach((field, index) => {
          filters.push({
            column: field,
            value: searchTerm,
            op: 'contains',
            andOr: filters.length > 0 ? 'OR' : 'undefined'
          });
        });
      }

      const request: Lister = {
        pagination: {
          currentPage: this.currentPage + 1,
          pageSize: this.pageSize
        },
        sortPage: this.currentSort,
        filterParams: filters
      };

      this.isLoading = true;

      this.localService.gatePage(request).subscribe({
        next: (response: Page<Local>) => {
          this.filteredLocals = response.Content ?? [];
          this.totalCount = response.Lister?.pagination?.totalElement ?? 0;
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Search error:', error);
          this.filteredLocals = [];
          this.totalCount = 0;
          this.isLoading = false;
        }
      });
    } else {
      this.loadLocals();
    }
  }

  updatePaginatedLocals(): void {
    const startIndex = this.currentPage * this.pageSize;
    this.paginatedLocals = this.filteredLocals.slice(startIndex, startIndex + this.pageSize);
  }

  // Table configuration
  tableConfig = {
    keys: ['Name', 'Floor', 'SensorsCount', 'Capacity', 'Site.name', 'TypeLocal.Nom'],
    headers: ['Nom', 'Étage', 'Capteurs', 'Capacité', 'Site', 'Type'],
  };

  currentSort: SortPage = {
    Column: 'CreatedAt',
    Sort: 'desc'
  };

  createLocalForm = new FormGroup({
    nom: new FormControl('', [Validators.required]),
    description: new FormControl(''),
    etage: new FormControl<number>(0),
    floor: new FormControl<number>(0),
    surface: new FormControl<number>(0),
    nombreCapteurs: new FormControl<number>(0),
    latitude: new FormControl(''),
    longtitude: new FormControl(''),
    capacitePersonnes: new FormControl<number>(0),
    architecture2DImage: new FormControl(''),
    imageLocal: new FormControl(''),
    idSite: new FormControl('', [Validators.required]),
    typeLocalId: new FormControl('', [Validators.required])
  });

  editLocalForm = new FormGroup({
    id: new FormControl(''),
    nom: new FormControl('', [Validators.required]),
    description: new FormControl(''),
    etage: new FormControl<number>(0),
    floor: new FormControl<number>(0), // Add floor control
    surface: new FormControl<number>(0),
    nombreCapteurs: new FormControl<number>(0),
    capacitePersonnes: new FormControl<number>(0),
    architecture2DImage: new FormControl(''),
    imageLocal: new FormControl(''),
    idSite: new FormControl('', [Validators.required]),
    typeLocalId: new FormControl('', [Validators.required]),
    latittude: new FormControl(''),
    longtitude: new FormControl('')
  });

  ngOnInit(): void {
    this.currentPage = 0;
    this.pageSize = 5;
    this.totalCount = 0;
    this.loadLocals();
    this.loadSites();
    this.loadTypeLocals();

    this.route.queryParams.subscribe(params => {
      if (params['action'] === 'create') {
        this.showAddLocalForm();
      }
    });
  }

  loadSites(): void {
    this.isLoading = true;
    this.siteService.getAll().subscribe({
      next: (sites) => {
        this.sites = sites;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading sites:', error);
        this.isLoading = false;
      }
    });
  }

  loadTypeLocals(): void {
    this.isLoading = true;
    this.typeLocalService.getAll().subscribe({
      next: (typeLocals) => {
        this.typeLocals = typeLocals;
        console.log('TypeLocals loaded:', this.typeLocals);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading typeLocals:', error);
        this.isLoading = false;
      }
    });
  }

  loadLocals(): void {
    this.ngxUiLoaderService.start();
    this.isLoading = true;

    const request: Lister = {
      pagination: {
        currentPage: this.currentPage + 1,
        pageSize: this.pageSize
      },
      sortPage: this.currentSort,
      filterParams: this.buildFilterParams()
    };

    this.localService.gatePage(request).subscribe({
      next: (response: Page<Local>) => {
        this.filteredLocals = response.Content ?? [];
        this.totalCount = response.Lister?.pagination?.totalElement ?? 0;
        this.isLoading = false;
        this.ngxUiLoaderService.stop();
      },
      error: (error: Error) => {
        console.error('Error loading locals:', error);
        this.showError('Erreur lors du chargement des locaux', 'Erreur');
        this.isLoading = false;
        this.ngxUiLoaderService.stop();
      }
    });
  }

  private buildFilterParams(): FilterParam[] {
    const filters: FilterParam[] = [];
    const searchTerm = this.searchTerm.trim();

    if (searchTerm) {
      const isNumeric = !isNaN(Number(searchTerm)) && searchTerm !== '';

      if (isNumeric) {
        filters.push({
          column: 'Floor',
          value: Number(searchTerm),
          op: 'equals',
          andOr: 'AND'
        });
      } else {
        filters.push({
          column: 'Name',
          value: searchTerm,
          op: 'contains',
          andOr: 'AND'
        });
      }
    }

    return filters;
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadLocals();
  }

  onSearchKeyup(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      this.filterLocals();
    } else if (event.key === 'Backspace' && this.searchTerm === '') {
      this.loadLocals();
    }
  }
  
  onSort(column: string): void {
    this.currentSort = {
      Column: column,
      Sort: this.currentSort.Sort === 'asc' ? 'desc' : 'asc'
    };
    this.loadLocals();
  }

  showAddLocalForm(): void {
    this.showEditForm = false; // Hide edit form when creating
    this.selectedLocal = null; // Reset selected local
    this.showCreateForm = true;
  }

  hideAddLocalForm(): void {
    this.showCreateForm = false;
    this.createLocalForm.reset();
    this.architecture2DPreview = null;
    this.imageLocalPreview = null;
    this.isSubmitting = false;
  }

  onImagesSelected(event: any, type: string): void {
    if (event.target.files && event.target.files.length > 0) {
      const file = event.target.files[0];
      const reader = new FileReader();
  
      reader.onload = (e: any) => {
        const base64String = e.target.result.split(',')[1]; // Remove data:image/... prefix
        
        if (type === 'Architecture2DImage') {
          this.architecture2DPreview = e.target.result;
          this.editLocalForm.patchValue({ architecture2DImage: base64String });
        } else if (type === 'ImageLocal') {
          this.imageLocalPreview = e.target.result;
          this.editLocalForm.patchValue({ imageLocal: base64String });
        }
      };
  
      reader.readAsDataURL(file);
    }
  }

  generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  submitCreateForm(): void {
    if (this.createLocalForm.valid) {
      this.isSubmitting = true;
      const formValues = this.createLocalForm.value;
      const local: Local = {
        Id: this.generateUUID(),
        Name: formValues.nom ?? '',
        Floor: formValues.floor ?? 0,
        SensorsCount: formValues.nombreCapteurs ?? 0, 
        Capacity: formValues.capacitePersonnes ?? 0, 
        Architecture2DImage: formValues.architecture2DImage ?? 'null',
        ImageLocal: formValues.imageLocal ?? 'null',
        Latitude: 0,
        Longtitude: 0,
        IdSite: formValues.idSite ?? '',
        TypeLocalId: formValues.typeLocalId ?? '',
        Description: ''
      };

      this.localService.create(local).subscribe({
        next: () => {
          this.loadLocals();
          this.hideAddLocalForm();
          this.isSubmitting = false;
          this.showSuccess('Le local a été créé avec succès', 'Information');
        },
        error: (error: Error) => {
          this.isSubmitting = false;
          this.showError('Erreur lors de la création du local', 'Erreur');
        }
      });
    }
  }

  editLocal(id: string): void {
    this.showCreateForm = false;
    const local = this.filteredLocals.find(l => l.Id == id);
    console.log('Selected Local for Edit:', id);

    if (local) {
      this.selectedLocal = local;
      this.editLocalForm.patchValue({
        id: local.Id,
        nom: local.Name,
        description: local['description'] ?? '', // Fix property access
        etage: local.Floor,
        floor: local.Floor,
        nombreCapteurs: local.SensorsCount,
        capacitePersonnes: local.Capacity,
        architecture2DImage: local.Architecture2DImage,
        imageLocal: local.ImageLocal,
        idSite: local.IdSite,
        typeLocalId: local.TypeLocalId,
      });

      console.log('Selected Local for Edit:', this.editLocalForm);

      this.showEditForm = true;

      setTimeout(() => {
        this.editFormSection?.nativeElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }, 100);
    }
  }

  deleteLocal(id: string): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirmer la suppression',
        message: 'Êtes-vous sûr de vouloir supprimer ce local ?',
        icon: 'delete'
      }
    });
  
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.localService.delete(id).subscribe({
          next: () => {
            this.loadLocals();
            this.showSuccess('Le local a été supprimé avec succès', 'Information');
          },
          error: (error) => {
            this.showError('Erreur lors de la suppression du local', 'Erreur');
          }
        });
      }
    });
  }

  viewDetails(id: string): void {
    this.router.navigate(['/site-details/', id]);

  }

  hideEditForm(): void {
    this.showEditForm = false;
    this.selectedLocal = null;
    this.editLocalForm.reset();
    this.architecture2DPreview = null;
    this.imageLocalPreview = null;
    this.isSubmitting = false;
  }

  hideEditLocalForm(): void {
    this.showEditForm = false;
    this.editLocalForm.reset();
    this.architecture2DPreview = null;
    this.imageLocalPreview = null;
    this.isSubmitting = false;
    this.selectedLocal = null;
  }

  onEditImagesSelected(event: any, type: string): void {
    if (event.target.files && event.target.files.length > 0) {
      const file = event.target.files[0];
      const reader = new FileReader();

      reader.onload = (e: any) => {
        if (type === 'Architecture2DImage') {
          this.architecture2DPreview = e.target.result;
          this.editLocalForm.patchValue({ architecture2DImage: e.target.result });
        } else if (type === 'ImageLocal') {
          this.imageLocalPreview = e.target.result;
          this.editLocalForm.patchValue({ imageLocal: e.target.result });
        }
      };

      reader.readAsDataURL(file);
    }
  }

  submitEditForm(): void {
    if (this.editLocalForm.valid && this.selectedLocal) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        width: '400px',
        data: {
          title: 'Confirmer la modification',
          message: 'Êtes-vous sûr de vouloir modifier ce local ?',
          icon: 'edit'
        }
      });
  
      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.isSubmitting = true;
          const formValues = this.editLocalForm.value;
          const updatedLocal: Local = {
            ...this.selectedLocal!,
          Name: formValues.nom ?? '',
          Floor: formValues.floor ?? 0,
          SensorsCount: formValues.nombreCapteurs ?? 0,
          Capacity: formValues.capacitePersonnes ?? 0,
          Architecture2DImage: formValues.architecture2DImage ?? this.selectedLocal?.Architecture2DImage ?? '',
          ImageLocal: formValues.imageLocal ?? this.selectedLocal?.ImageLocal ?? '',
          IdSite: formValues.idSite ?? this.selectedLocal?.IdSite ?? '',
          TypeLocalId: formValues.typeLocalId ?? this.selectedLocal?.TypeLocalId ?? '',
          Description: formValues.description ?? this.selectedLocal?.Description ?? '', // Ensure string
          Latitude: this.selectedLocal?.Latitude ?? 0,
          Longtitude: this.selectedLocal?.Longtitude ?? 0
          };

          this.localService.update(updatedLocal).subscribe({
            next: () => {
              this.loadLocals();
              this.hideEditLocalForm();
              this.isSubmitting = false;
              this.showSuccess('Le local a été modifié avec succès', 'Information');
            },
            error: (error: Error) => {
              this.handleError(error);
              this.showError('Erreur lors de la modification du local', 'Erreur');
            }
          });
        }
      });
    }
  }
  handleAction(event: { action: string; row: any }): void {
    const { action, row } = event;
    if (action === 'edit') {
      this.editLocal(row.Id);
    } else if (action === 'delete') {
      this.deleteLocal(row.Id);
    } else if (action === 'view') {
      this.viewDetails(row.Id);
    }
  }


  private handleError(error: any): void {
    console.error('An error occurred:', error);
    
    // Show error message to user
    if (error.error?.Message) {
      // Handle specific API error messages
      console.error('API Error:', error.error.Message);
    } else {
      // Handle generic errors
      console.error('Unknown error occurred');
    }
    
    this.isLoading = false;
    this.isSubmitting = false;
  }

  private showSuccess(message: string, title: string) {
    this.toast.success(message, title, 3000, false);
  }

  private showError(message: string, title: string) {
    this.toast.warning(message, title, 3000, false);
  }
}