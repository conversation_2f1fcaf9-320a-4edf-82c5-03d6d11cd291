import { Injectable } from '@angular/core';
import { HttpClient,HttpParams  } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '@app/environments/environment';
import { Transaction } from '../models/transaction';
import { Local } from '../models/local';
@Injectable({
  providedIn: 'root'
})
export class InstalationService {


   private baseUrl = environment.host +"/api"; // à adapter à ton backend

  constructor(private http: HttpClient) {}

  getClients(): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/client`);
  }

  addTransactions(transactions: Transaction[]) {
    return this.http.post(`${this.baseUrl}/transaction/add-range`, transactions);
  }
  getControllersByClient(clientId: number): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/controllers`);
  }

  getSites(): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/site`);
  }

  getCotroler(): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/Controller`);
  }

   getCapteur(): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/Capteur`);
  }

  getCapteurByIdControler(ControlerId:number) {
    const body = {
      pagination: {
        currentPage: 1,
        pageCount: 0,
        pageSize: 10,
        isLast: true,
        isFirst: true,
        startIndex: 0,
        totalElement: 0
      },
      sortParams: [
        {
          column: "nom",
          sort: "asc"
        }
      ],
      filterParams: [
        {
          column: "ControllerId",
          value: ControlerId,
          op: "eq",
          andOr: "and"
        }
      ]
    };
      
    return this.http.post(`${this.baseUrl}/Capteur/search`, body);
  }

getSitesByClientId(ClientId: number) {
    const body = {
      pagination: {
        currentPage: 1,
        pageCount: 0,
        pageSize: 10,
        isLast: true,
        isFirst: true,
        startIndex: 0,
        totalElement: 0
      },
      sortParams: [
        {
          column: "nom",
          sort: "asc"
        }
      ],
      filterParams: [
        {
          column: "ClientId",
          value: ClientId,
          op: "eq",
          andOr: "and"
        }
      ]
    };
      
    return this.http.post(`${this.baseUrl}/site/search`, body);
  }


  getLocalBySitetId(IdSite: number) {
    
  return this.http.get<Local[]>(`${this.baseUrl}/local/by-site/${IdSite}`);
  }

    
 searchClients(searchTerm: string) {
    const body = {
      pagination: {
        currentPage: 1,
        pageCount: 1,
        pageSize: 10,
        isLast: false,
        isFirst: true,
        startIndex: 0,
        totalElement: 0
      },
      filterParams: [
        {
          column: "Name",
          value: searchTerm,
          op: "contains", // ou "eq" ou ton opérateur selon ton backend
          andOr: "and"
        }
      ],
      sortParams: []
    };

    return this.http.post<any>(`${this.baseUrl}/client/search`, body);
  }


  searchSites(searchTerm: string) {
    const body = {
      pagination: {
        currentPage: 1,
        pageCount: 1,
        pageSize: 10,
        isLast: false,
        isFirst: true,
        startIndex: 0,
        totalElement: 0
      },
      filterParams: [
        {
          column: "Name",
          value: searchTerm,
          op: "contains", // ou "eq" ou ton opérateur selon ton backend
          andOr: "and"
        }
      ],
      sortParams: []
    };

    return this.http.post<any>(`${this.baseUrl}/site/search`, body);
  }

  searchLocals(searchTerm: string) {
    const body = {
      pagination: {
        currentPage: 1,
        pageCount: 1,
        pageSize: 10,
        isLast: false,
        isFirst: true,
        startIndex: 0,
        totalElement: 0
      },
      filterParams: [
        {
          column: "Name",
          value: searchTerm,
          op: "contains", // ou "eq" ou ton opérateur selon ton backend
          andOr: "and"
        }
      ],
      sortParams: []
    };

    return this.http.post<any>(`${this.baseUrl}/local/search`, body);
  }

   searchControlers(searchTerm: string) {
    const body = {
      pagination: {
        currentPage: 1,
        pageCount: 1,
        pageSize: 10,
        isLast: false,
        isFirst: true,
        startIndex: 0,
        totalElement: 0
      },
      filterParams: [
        {
          column: "Model",
          value: searchTerm,
          op: "contains", // ou "eq" ou ton opérateur selon ton backend
          andOr: "and"
        }
      ],
      sortParams: []
    };

    return this.http.post<any>(`${this.baseUrl}/Controller/search`, body);
  }

updateLocal(local: any): Observable<any> {
  console.log(local)
  const body = {
    ...local,
    architecture2DImage: JSON.stringify(local.Architecture2DImage)  // 🔑
  };

  return this.http.put(`${this.baseUrl}/local`, body);
}

}



