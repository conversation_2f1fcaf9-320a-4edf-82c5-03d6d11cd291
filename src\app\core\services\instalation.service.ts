import { Injectable } from '@angular/core';
import { HttpClient,HttpParams  } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '@app/environments/environment';
@Injectable({
  providedIn: 'root'
})
export class InstalationService {


   private baseUrl = environment.host +"/api"; // à adapter à ton backend

  constructor(private http: HttpClient) {}

  getClients(): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/client`);
  }

  getControllersByClient(clientId: number): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/controllers`);
  }

  getSites(): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/site`);
  }

  getCotroler(): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/Controller`);
  }

   getCapteur(): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/Capteur`);
  }

getSitesByClientId(ClientId: number) {
    const body = {
      pagination: {
        currentPage: 1,
        pageCount: 0,
        pageSize: 10,
        isLast: true,
        isFirst: true,
        startIndex: 0,
        totalElement: 0
      },
      sortParams: [
        {
          column: "nom",
          sort: "asc"
        }
      ],
      filterParams: [
        {
          column: "ClientId",
          value: ClientId,
          op: "eq",
          andOr: "and"
        }
      ]
    };
      
    return this.http.post(`${this.baseUrl}/site/search`, body);
  }


  getLocalBySitetId(IdSite: number) {
    const body = {
      pagination: {
        currentPage: 1,
        pageCount: 0,
        pageSize: 10,
        isLast: true,
        isFirst: true,
        startIndex: 0,
        totalElement: 0
      },
      sortParams: [
        {
          column: "nom",
          sort: "asc"
        }
      ],
      filterParams: [
        {
          column: "IdSite",
          value: IdSite,
          op: "eq",
          andOr: "and"
        }
      ]
    };
      
    return this.http.post(`${this.baseUrl}/local/search`, body);
  }

    


updateLocal(local: any): Observable<any> {
  console.log(local)
  const body = {
    ...local,
    architecture2DImage: JSON.stringify(local.Architecture2DImage)  // 🔑
  };

  return this.http.put(`${this.baseUrl}/local`, body);
}

}



