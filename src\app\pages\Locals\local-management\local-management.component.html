<div class="local-management-container">
 <div class="site-info-section" *ngIf="currentSite">
    <div class="breadcrumb-nav">
      <button class="back-button" (click)="goBackToOrganisation()">
        <i class="material-icons">arrow_back</i>
      </button>
      <span class="breadcrumb-text">Gestion des locaux pour le site: {{ currentSite.Name }}</span>
    </div>

    <div class="info-section">
      <div class="site-images-container">
        <div class="logo-container">
          <img
            *ngIf="currentSite.Image"
            [src]="'data:image/jpeg;base64,' + currentSite.Image"
            [alt]="currentSite.Name"
            class="site-logo"
          />
          <div *ngIf="!currentSite.Image" class="no-logo">
            <i class="material-icons">location_city</i>
          </div>
        </div>
        <div class="site-stats">
          <div class="stat-item">
            <div class="stat-value">{{ currentSite.LocalsCount || 0  }}</div>
            <div class="stat-label">Locaux</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ currentSite.employeesCount || 0 }}</div>
            <div class="stat-label">Employés</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ currentSite.Surface || 0 }} m²</div>
            <div class="stat-label">Surface</div>
          </div>
        </div>

      </div>

      <div class="site-info-container">
        <div class="site-header">
          <h2 class="site-name">{{ currentSite.Name }}</h2>
          <div class="site-status" [class.active]="currentSite.Status === 'Actif'"
                                  [class.inactive]="currentSite.Status === 'Inactif'"
                                  [class.maintenance]="currentSite.Status === 'En maintenance'"
                                  [class.maintenance]="currentSite.Status === 'En Installation'">
            {{ currentSite.Status || 'Statut inconnu' }}
          </div>
        </div>

        <div class="info-grid">
          <!-- Column 1 -->
          <div class="info-column">
            <div class="info-item">
              <div class="info-label">
                <i class="material-icons">location_on</i> Adresse complète:
              </div>
              <div class="info-value">
                {{ currentSite.Adress || 'Non spécifié' }}<br>
                <span *ngIf="currentSite.AddressComplement">{{ currentSite.AddressComplement }}</span>
              </div>
            </div>

            <div class="info-item">
              <div class="info-label">
                <i class="material-icons">map</i> Coordonnées:
              </div>
              <div class="info-value" *ngIf="currentSite.Latitude && currentSite.Longtitude">
                Lat: {{ currentSite.Latitude | number:'1.4-4' }},
                Long: {{ currentSite.Longtitude | number:'1.4-4' }}
              </div>
              <div class="info-value" *ngIf="!currentSite.Latitude || !currentSite.Longtitude">
                Non spécifiées
              </div>
            </div>

            <div class="info-item">
              <div class="info-label">
                <i class="material-icons">grade</i> Grade:
              </div>
              <div class="info-value">
                {{ currentSite.Grade || 'Non spécifié' }}
              </div>
            </div>
          </div>

          <!-- Column 2 -->
          <div class="info-column">
            <div class="info-item">
              <div class="info-label">
                <i class="material-icons">contacts</i> Contacts:
              </div>
              <div class="info-value">
                <div *ngIf="currentSite.Contact">
                  <strong>Contact:</strong> {{ currentSite.Contact }}
                </div>
                <div *ngIf="currentSite.Manager">
                  <strong>Responsable:</strong> {{ currentSite.Manager }}
                </div>
                <div *ngIf="currentSite.Email">
                  <strong>Email:</strong> {{ currentSite.Email }}
                </div>
                <div *ngIf="currentSite.PhoneNumber">
                  <strong>Téléphone:</strong> {{ currentSite.PhoneNumber }}
                </div>
              </div>
            </div>
          </div>

          <!-- Column 3 -->
          <div class="info-column">
            <div class="info-item">
              <div class="info-label">
                <i class="material-icons">description</i> Description:
              </div>
              <div class="info-value description-text">
                {{ currentSite.Description || 'Aucune description disponible' }}
              </div>
            </div>

            <!-- <div class="info-item">
              <div class="info-label">
                <i class="material-icons">date_range</i> Dates:
              </div>
              <div class="info-value">
                <div *ngIf="currentSite.reatedAt">
                  <strong>Créé le:</strong> {{ currentSite.CreatedAt | date:'mediumDate' }}
                </div>
                <div *ngIf="currentSite.LastUpdatedAt">
                  <strong>Modifié le:</strong> {{ currentSite.LastUpdatedAt | date:'mediumDate' }}
                </div>
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="header-section">
    <div class="page-title">
      <h1 class="title">
        <mat-icon class="title-icon">meeting_room</mat-icon> Liste des Locaux
      </h1>
    </div>
    <div class="actions">
      <button
        class="create-button"
        (click)="showAddLocalForm()"
      >
        <mat-icon class="action-icon">add</mat-icon> Créer Local
      </button>
      <button class="view-toggle" (click)="toggleViewMode()">
        <mat-icon class="action-icon">{{ viewMode === 'cards' ? 'view_list' : 'grid_view' }}</mat-icon>
        {{ viewMode === 'cards' ? 'Vue Tableau' : 'Vue Cartes' }}
      </button>
    </div>
  </div>

  <div class="search-section">
    <div class="search-container">
      <input
        type="text"
        [(ngModel)]="searchTerm"
        placeholder="Rechercher un local"
        class="search-input"
        (keyup.enter)="filterLocals()"
      />
      <button class="search-button" (click)="filterLocals()">
        <mat-icon>search</mat-icon>
      </button>
    </div>
  </div>

  <ng-container *ngIf="isLoading">
    <div class="loading-container">
      <div class="spinner"></div>
      <p>Chargement des locaux...</p>
    </div>
  </ng-container>

  <ng-container *ngIf="!isLoading">
    <!-- Card View -->
    <div *ngIf="viewMode === 'cards'">
      <div class="cards-container">
        <app-card-local
          *ngFor="let local of paginatedLocals"
          [local]="local"
          (viewDetails)="viewDetails(local.Id)"
          (editLocal)="editLocal(local)"
          (deleteLocal)="deleteLocal(local.Id)"
        ></app-card-local>
      </div>

      <div class="card-pagination-container" *ngIf="filteredLocals.length > 0">
        <mat-paginator
          [length]="totalCount"
          [pageSize]="pageSize"
          [pageIndex]="currentPage"
          [pageSizeOptions]="[5, 10, 25, 50]"
          (page)="onPageChange($event)"
          aria-label="Select page"
        ></mat-paginator>
      </div>

      <div class="no-sites-message" *ngIf="filteredLocals.length === 0">
        <i class="material-icons">location_off</i>
        <p>Aucun local trouvé</p>
        <!-- <button class="btn btn-primary" (click)="showAddLocalForm()">
          <i class="material-icons">add</i> Ajouter un local
        </button> -->
      </div>
    </div>

    <!-- Table View -->
    <div *ngIf="viewMode === 'table'">
      <div class="table-view" *ngIf="filteredLocals.length > 0">
        <app-generic-table
          [data]="paginatedLocals"
          [headers]="headers"
          [keys]="keys"
          [actions]="['edit', 'view', 'delete']"
          (actionTriggered)="handleAction($event)"
        ></app-generic-table>
      </div>

      <div class="pagination-container" *ngIf="filteredLocals.length > 0">
        <mat-paginator
          [length]="totalCount"
          [pageSize]="pageSize"
          [pageIndex]="currentPage"
          [pageSizeOptions]="[5, 10, 25, 50]"
          (page)="onPageChange($event)"
          aria-label="Select page"
        ></mat-paginator>
      </div>

      <div class="no-sites-message" *ngIf="filteredLocals.length === 0">
        <i class="material-icons">location_off</i>
        <p>Aucun local trouvé</p>
      </div>
    </div>
  </ng-container>

  <!-- Create Local Popup Form -->
  <div class="popup-overlay" *ngIf="showCreateForm" (click)="hideAddLocalForm()">
    <div class="popup-form" (click)="$event.stopPropagation()">
      <div class="popup-header">
        <h3>
          <mat-icon>add</mat-icon>
          Créer Local
        </h3>
        <button class="close-btn" (click)="hideAddLocalForm()">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <form [formGroup]="createLocalForm" (ngSubmit)="submitCreateForm()" class="site-form">
        <div class="validation-errors" *ngIf="createLocalForm.invalid && (createLocalForm.touched || createLocalForm.dirty)">
          <div class="validation-errors-title">
            <mat-icon>error_outline</mat-icon>
            Erreurs de validation
          </div>
          <ul class="validation-errors-list">
            <li *ngIf="createLocalForm.get('Name')?.invalid">
              <mat-icon>error</mat-icon>
              Le nom est requis
            </li>
            <li *ngIf="createLocalForm.get('TypeLocalId')?.invalid">
              <mat-icon>error</mat-icon>
              Le type de local est requis
            </li>
            <li *ngIf="createLocalForm.get('IdSite')?.invalid">
              <mat-icon>error</mat-icon>
              Le site est requis
            </li>
          </ul>
        </div>

        <div class="form-grid">
          <div class="form-group">
            <label for="Name">Nom <span class="required">*</span></label>
            <input id="Name" type="text" formControlName="Name" required>
            <div class="error-message" *ngIf="createLocalForm.get('Name')?.invalid && createLocalForm.get('Name')?.touched">
              Le nom est requis
            </div>
          </div>

          <div class="form-group">
            <label for="TypeLocalId">Type de Local <span class="required">*</span></label>
            <select id="TypeLocalId" formControlName="TypeLocalId" required>
              <option value="">Sélectionnez un type</option>
              <option *ngFor="let type of typeLocals" [value]="type.Id">
                {{ type.Nom }}
              </option>
            </select>
            <div class="error-message" *ngIf="createLocalForm.get('TypeLocalId')?.invalid && createLocalForm.get('TypeLocalId')?.touched">
              Le type de local est requis
            </div>
          </div>

          <div class="form-group">
            <label for="IdSite">Site <span class="required">*</span></label>
            <input id="IdSite" type="text" [value]="getSiteNameById(createLocalForm.get('IdSite')?.value)" readonly class="readonly-site-input">
            <div class="error-message" *ngIf="createLocalForm.get('IdSite')?.invalid && createLocalForm.get('IdSite')?.touched">
              Le site est requis
            </div>
          </div>

          <div class="form-group">
            <label for="Floor">Étage</label>
            <input id="Floor" type="number" formControlName="Floor">
          </div>

          <div class="form-group full-width">
            <label for="Capacity">Capacité (personnes)</label>
            <input id="Capacity" type="number" formControlName="Capacity">
          </div>

          <!-- <div class="form-group">
            <label for="SensorsCount">Nombre de capteurs</label>
            <input id="SensorsCount" [disabled]=true type="number" formControlName="SensorsCount">
          </div> -->

          <div class="form-group">
            <label for="Latitude">Latitude</label>
            <input id="Latitude" type="number" formControlName="Latitude">
          </div>

          <div class="form-group">
            <label for="Longtitude">Longtitude</label>
            <input id="Longtitude" type="number" formControlName="Longtitude">
          </div>

          <div class="form-group full-width">
            <label for="Description">Description</label>
            <textarea id="Description" formControlName="Description" rows="3"></textarea>
          </div>

          <div class="form-group full-width">
            <label for="BaseTopicMQTT">Base Topic MQTT</label>
            <input id="BaseTopicMQTT" type="text" formControlName="BaseTopicMQTT">
          </div>

          <div class="form-group full-width">
            <label for="Architecture2DImage">Plan 2D</label>
            <input type="file" id="Architecture2DImage" (change)="onImagesSelected($event, 'Architecture2DImage')" accept="image/*">
          </div>

          <div class="form-group full-width">
            <label for="ImageLocal">Image du local</label>
            <input type="file" id="ImageLocal" (change)="onImagesSelected($event, 'ImageLocal')" accept="image/*">
          </div>
        </div>

        <div class="form-actions">
          <button type="button" class="btn-cancel" (click)="hideAddLocalForm()">
            Annuler
          </button>
          <button type="submit" class="btn-submit" [disabled]="!createLocalForm.valid">
            Créer
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Edit Local Popup Form -->
  <div class="popup-overlay" *ngIf="showEditForm" (click)="hideEditLocalForm()">
    <div class="popup-form" (click)="$event.stopPropagation()">
      <div class="popup-header">
        <h3>
          <mat-icon>edit</mat-icon>
          Modifier le Local
        </h3>
        <button class="close-btn" (click)="hideEditLocalForm()">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <form [formGroup]="editLocalForm" (ngSubmit)="submitEditForm()" class="site-form">
        <div class="validation-errors" *ngIf="editLocalForm.invalid && (editLocalForm.touched || editLocalForm.dirty)">
          <div class="validation-errors-title">
            <mat-icon>error_outline</mat-icon>
            Erreurs de validation
          </div>
          <ul class="validation-errors-list">
            <li *ngIf="editLocalForm.get('Name')?.invalid">
              <mat-icon>error</mat-icon>
              Le nom est requis
            </li>
            <li *ngIf="editLocalForm.get('TypeLocalId')?.invalid">
              <mat-icon>error</mat-icon>
              Le type de local est requis
            </li>
            <li *ngIf="editLocalForm.get('IdSite')?.invalid">
              <mat-icon>error</mat-icon>
              Le site est requis
            </li>
          </ul>
        </div>

        <div class="form-grid">
          <div class="form-group">
            <label for="editName">Nom <span class="required">*</span></label>
            <input id="editName" type="text" formControlName="Name" required>
          </div>

          <div class="form-group">
            <label for="editTypeLocalId">Type de Local <span class="required">*</span></label>
            <select id="editTypeLocalId" formControlName="TypeLocalId" required>
              <option value="">Sélectionnez un type</option>
              <option *ngFor="let type of typeLocals" [value]="type.Id">
                {{ type.Nom }}
              </option>
            </select>
          </div>

          <div class="form-group">
            <label for="editIdSite">Site <span class="required">*</span></label>
            <input id="editIdSite" type="text" [value]="getSiteNameById(editLocalForm.get('IdSite')?.value)" readonly class="readonly-site-input">
          </div>

          <div class="form-group">
            <label for="editFloor">Étage</label>
            <input id="editFloor" type="number" formControlName="Floor">
          </div>

          <div class="form-group">
            <label for="editCapacity">Capacité (personnes)</label>
            <input id="editCapacity" type="number" formControlName="Capacity">
          </div>

            <div class="form-group">
            <label for="editSensorsCount">Nombre de capteurs</label>
            <input id="editSensorsCount" type="number" formControlName="SensorsCount" readonly>
          </div>

          <div class="form-group">
            <label for="editLatitude">Latitude</label>
            <input id="editLatitude" type="number" formControlName="Latitude">
          </div>

          <div class="form-group">
            <label for="editLongtitude">Longitude</label>
            <input id="editLongtitude" type="number" formControlName="Longtitude">
          </div>

          <div class="form-group full-width">
            <label for="editDescription">Description</label>
            <textarea id="editDescription" formControlName="Description" rows="3"></textarea>
          </div>

          <div class="form-group">
            <label for="editBaseTopicMQTT">Base Topic</label>
            <input id="editBaseTopicMQTT" type="text" formControlName="BaseTopicMQTT">
          </div>

          <div class="form-group full-width">
            <label>Plan 2D</label>
            <div *ngIf="selectedLocal?.Architecture2DImage" class="current-images-container">
              <div class="current-image-item">
                <img [src]="'data:image/jpeg;base64,' + selectedLocal?.Architecture2DImage" class="current-image-preview">
              </div>
            </div>
            <input type="file" id="editArchitecture2DImage" (change)="onImagesSelected($event, 'Architecture2DImage')" accept="image/*">
          </div>

          <div class="form-group full-width">
            <label>Image du local</label>
            <div *ngIf="selectedLocal?.ImageLocal" class="current-images-container">
              <div class="current-image-item">
                <img [src]="'data:image/jpeg;base64,' + selectedLocal?.ImageLocal" class="current-image-preview">
              </div>
            </div>
            <input type="file" id="editImageLocal" (change)="onImagesSelected($event, 'ImageLocal')" accept="image/*">
          </div>
        </div>

        <div class="form-actions">
          <button type="button" class="btn-cancel" (click)="hideEditLocalForm()">
            Annuler
          </button>
          <button type="submit" class="btn-submit" [disabled]="!editLocalForm.valid">
            Enregistrer
          </button>
        </div>
      </form>
    </div>
  </div>

  <ngx-ui-loader></ngx-ui-loader>
</div>
