import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { Zigbee2MqttService, ZigbeeDevice, DeviceState } from '../../core/services/mqtt.service';
import { MatSliderChange } from '@angular/material/slider';
import { CommonModule, DatePipe } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatSliderModule } from '@angular/material/slider';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatDividerModule } from '@angular/material/divider';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatTableModule } from '@angular/material/table'; // Import MatTableModule
import { MatButtonToggleModule } from '@angular/material/button-toggle'; // Import MatButtonToggleModule
import { BackEndDevice, SensorsBackEndDataService, SimpleLocal, SimpleSite } from '@app/core/sensors-back-end-data.service';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { Client } from '@app/core/models/client';
import { Site } from '@app/core/models/site';


// Define device type categories
export enum DeviceCategory {
  LIGHT = 'light',
  SWITCH = 'switch',
  SENSOR = 'sensor',
  MOTION = 'motion',
  DOOR = 'door',
  PLUG = 'plug',
  CLIMATE = 'climate',
  UNKNOWN = 'unknown'
}

// Define data types for sensor values
interface SensorData {
  key: string;
  value: any;
  unit?: string;
  icon: string;
  label: string;
  type: 'number' | 'boolean' | 'string' | 'object';
  isControl?: boolean;
  min?: number;
  max?: number;
  vendor?: string; // Optional vendor field for future use
  model?: string; // Optional model field for future use

}

@Component({
  selector: 'app-devices',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatIconModule,
    MatChipsModule,
    MatSlideToggleModule,
    MatSliderModule,
    MatButtonModule,
    MatMenuModule,
    MatProgressBarModule,
    MatDividerModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatTooltipModule,
    MatTableModule, // Add MatTableModule
    MatButtonToggleModule
  ],
  templateUrl: './devices.component.html',
  styleUrls: ['./devices.component.css']
})
export class DeviceControlComponent implements OnInit, OnDestroy {
   // Search and filter properties
  searchTerm: string = '';
  selectedCategory: string = 'all';
  selectedStatus: string = 'all';
  filteredDevices: ZigbeeDevice[] = [];
  filtredDevicesBackEnd: any[] = [];
  devices: ZigbeeDevice[] = [];
  devicesBackEnd: { RowData: any; State: any }[] = [];
  countDevicesSynchronised: number = 0 ;
  bridgeState: string = 'offline';
  private subscriptions: Subscription[] = [];
  readonly DeviceCategory = DeviceCategory;
  currentView: 'grid' | 'table' = 'grid'; // Default view
  displayedColumns: string[] = ['name', 'category', 'vendorModel', 'lastSeen', 'controls'];
  buttonon: boolean = false;
  buttonon2: boolean = true;
  DateSynchronisation: string = '';
  AllClients: Client[] = [];
  selectedClientId : string = '';
  Sites: SimpleSite[] = [];
  selectedSiteId : string = '';
  locaux : SimpleLocal[] = [];
  selectedLocalId : string = '' ;

   // Statistics
  get deviceStats() {
    return {
      total: this.devices.length,
      online: this.devices.filter(d => this.isDeviceOnline(d)).length,
      lights: this.devices.filter(d => this.getDeviceCategory(d) === DeviceCategory.LIGHT).length,
      sensors: this.devices.filter(d => this.getDeviceCategory(d) === DeviceCategory.SENSOR).length,
      switches: this.devices.filter(d => this.getDeviceCategory(d) === DeviceCategory.SWITCH).length // Added switches for completeness
    };
  }

  get deviceStatsBackEnd() {
    return {
      total: this.devicesBackEnd.length,
      online: this.devicesBackEnd.filter(d => this.isDeviceOnline(d.RowData)).length,
      lights: this.devicesBackEnd.filter(d => this.getDeviceCategory(d.RowData) === DeviceCategory.LIGHT).length,
      sensors: this.devicesBackEnd.filter(d => this.getDeviceCategory(d.RowData) === DeviceCategory.SENSOR).length,
      switches: this.devicesBackEnd.filter(d => this.getDeviceCategory(d.RowData) === DeviceCategory.SWITCH).length // Added switches for completeness
    };

  }

  // Mapping of data keys to display information (translated to French)
   private readonly dataTypeMap: { [key: string]: Partial<SensorData> } = {
    'temperature': { icon: 'device_thermostat', label: 'Température', unit: '°C', type: 'number' },
    'humidity': { icon: 'water_drop', label: 'Humidité', unit: '%', type: 'number' },
    'pressure': { icon: 'speed', label: 'Pression', unit: 'hPa', type: 'number' },
    'illuminance': { icon: 'light_mode', label: 'Illumination', unit: 'lux', type: 'number' },
    'occupancy': { icon: 'motion_sensor_active', label: 'Mouvement', type: 'boolean' },
    'contact': { icon: 'sensor_door', label: 'Contact', type: 'boolean' },
    'state': { icon: 'power_settings_new', label: 'Alimentation', type: 'boolean', isControl: true },
    'brightness': { icon: 'brightness_medium', label: 'Luminosité', unit: '%', type: 'number', isControl: true },
    'battery': { icon: 'battery_full', label: 'Batterie', unit: '%', type: 'number' },
    'linkquality': { icon: 'network_wifi', label: 'Signal', type: 'number' }
    };



constructor(
  private zigbeeService: Zigbee2MqttService,
  private sensorsService: SensorsBackEndDataService ,
  private clientApiService: ClientApiService,
) {}
rowDataFromBackend: any[] = [];

ngOnInit(): void {
  this.loadClients();
  this.getAllCapteurs();

}
getAllCapteurs(): void {
    this.sensorsService.getRowDataOnly().subscribe({
    next: (devices: { RowData: any; State: any }[]) => {
      this.devicesBackEnd = devices;
      // console.log(this.devicesBackEnd);
      this.applyFiltersFromBackEnd();
    },
    error: console.error
  });
}
clientOnChange(): void {
  this.locaux = [];
  this.selectedSiteId = '';
  if(this.selectedClientId){
      this.sensorsService.getSitesByClientId(this.selectedClientId).subscribe({
      next: (sites) => {
        this.Sites = sites
      }
      })
      console.log('sites loaded :' , this.Sites)
        this.sensorsService.getCapteursByClientId(this.selectedClientId).subscribe({
        next: (devices: { RowData: any; State: any }[]) => {
          this.devicesBackEnd = devices;
          this.applyFiltersFromBackEnd();
        }
      })
      console.log(this.devicesBackEnd , "devicesby clients");
  }
  else{
    this.Sites = [];
    this.getAllCapteurs();
  }


}
siteOnChange(): void {
  if(this.selectedSiteId){
      this.sensorsService.getlocauxBySiteId(this.selectedSiteId).subscribe({
      next: (locaux) => {
        this.locaux = locaux
      }
      })
      console.log('locaux loaded :' , this.locaux);
      this.sensorsService.getCapteursBySiteId(this.selectedSiteId).subscribe({
        next: (devices: { RowData: any; State: any }[]) => {
          this.devicesBackEnd = devices;
          this.applyFiltersFromBackEnd();
        }
      })
      console.log(this.devicesBackEnd , "devicesby site");
  }
  else{
    this.locaux = [];
    this.clientOnChange()
  }


}
localOnChange(): void {
  if(this.selectedLocalId){
      this.sensorsService.getCapteursByLocalId(this.selectedLocalId).subscribe({
        next: (devices: { RowData: any; State: any }[]) => {
          this.devicesBackEnd = devices;
          this.applyFiltersFromBackEnd();
        }
      })
      console.log(this.devicesBackEnd , "devicesby locaux");
  }
  else{
    this.siteOnChange()
  }
}



  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

    loadClients(): void {
    this.clientApiService.getAll().subscribe({
      next: (clients) => {
        // Transform the API response to match your interface
        this.AllClients = clients;
        console.log('Clients loaded in devices:', this.AllClients);
      },
      error: (error) => {
        console.error('Error loading clients:', error);
      },
    });
  }

  // Filter methods
  applyFilters(): void {
    this.filteredDevices = this.devices.filter(device => {
      const matchesSearch = !this.searchTerm || 
        device.friendly_name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        (device.definition?.vendor || '').toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        (device.definition?.model || '').toLowerCase().includes(this.searchTerm.toLowerCase());

      const matchesCategory = this.selectedCategory === 'all' || 
        this.getDeviceCategory(device) === this.selectedCategory;

      // const matchesStatus = this.selectedStatus === 'all' ||
      //   (this.selectedStatus === 'online' && this.isDeviceOnline(device)) ||
      //   (this.selectedStatus === 'offline' && !this.isDeviceOnline(device));

      return matchesSearch && matchesCategory;
    });
  }
  applyFiltersFromBackEnd(): void {
  const ONE_HOUR = 60 * 60 * 1000;
  const now = Date.now();
  this.countDevicesSynchronised = 0; 

  this.filtredDevicesBackEnd = this.devicesBackEnd
    .map(device => {
      try {
        const rowData = device.RowData ? device.RowData : {};

        rowData.state = device.State; // Inject State

        const lastSeenStr = rowData.LastSeenOriginal ;
        const lastSeenDate = lastSeenStr ? new Date(lastSeenStr) : null;
        if (lastSeenDate && now - lastSeenDate.getTime() < ONE_HOUR) {
          this.countDevicesSynchronised += 1;
          rowData.Actif = true
        }
        else{
          rowData.Actif = false
        }
        console.log("rowData" , rowData)
        return rowData;
      } catch (e) {
        console.error('Invalid RowData:', device.RowData);
        return null;
      }
    })
    .filter(rowData => {
      if (!rowData) return false;

      const matchesSearch =
        !this.searchTerm ||
        rowData.friendly_name?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        (rowData.definition?.vendor || '').toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        (rowData.definition?.model || '').toLowerCase().includes(this.searchTerm.toLowerCase());

      const matchesCategory =
        this.selectedCategory === 'all' ||
        this.getDeviceCategory(rowData) === this.selectedCategory;

      return matchesSearch && matchesCategory;
    });

  // Set formatted sync date
  this.DateSynchronisation = new Date().toLocaleString('fr-FR', {
    dateStyle: 'medium',
    timeStyle: 'medium'
  });
}

  ChangeText(): void {
      this.buttonon = !this.buttonon;
    if(this.buttonon){
      this.subscriptions.push(
        this.zigbeeService.devices$.subscribe(devices => {
          this.devices = devices;
          this.applyFilters(); 
        })
      );

      this.subscriptions.push(
        this.zigbeeService.bridgeState$.subscribe(state => {
          this.bridgeState = state;
        })
      );
      console.log('Devices :', this.devices);
    }
    else{
      this.localOnChange();
    }

  }

  refreshDevices():void{
    this.localOnChange();

  }

  onSearchChange(): void {
    if(this.buttonon){
      this.applyFilters();
    }
    else{
      this.applyFiltersFromBackEnd();
    }
  }

  onCategoryChange(): void {
    this.applyFilters();
  }

  onStatusChange(): void {
    this.applyFilters();
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.applyFilters();
  }

  clearFilters(): void {
    // this.searchTerm = '';
    // this.selectedCategory = 'all';
    // this.selectedStatus = 'all';
    // this.applyFiltersFromBackEnd();
    // this.applyFilters();
    this.selectedClientId = '';
    this.selectedLocalId = '';
    this.selectedSiteId = '';
    this.getAllCapteurs();
  }

  // getDeviceStateFormBackEnd(device: SensorsBackEndDataService): DeviceState | undefined {
  //   return this.zigbeeService.getDeviceExtended(device);
  // }

  getDeviceState(device: ZigbeeDevice): DeviceState | undefined {
    return this.zigbeeService.getDeviceExtended(device.networkAddress);
  }
  //   getDeviceStateFormBackend(device: BackEndDevice): DeviceState | undefined {
  //   return this.sensorsService.getDeviceExtended(device.networkAddress);
  // }

  isDeviceOnline(device: ZigbeeDevice): boolean {
    const state = this.getDeviceState(device);
    // Consider device online if last_seen is within the last 5 minutes (300,000 ms)
    return state?.last_seen ? 
      (Date.now() - new Date(state.last_seen).getTime()) < 300000 : 
      false;
  }

  trackByKey(index: number, item: SensorData): string {
    return item.key;
  }

  isDeviceStateOn(device: ZigbeeDevice): boolean {
    const state = this.getDeviceState(device);
    if (!state) return false;
    // Check for both 'ON' string and true boolean
    return state.state === 'ON' || state.state === 'true';
  }

  getDeviceCategory(device: ZigbeeDevice): DeviceCategory {
    const definition = device.definition;
    if (!definition) return DeviceCategory.UNKNOWN;

    const model = definition.model?.toLowerCase() || '';
    const description = definition.description?.toLowerCase() || '';
    const supports = definition.supports?.toLowerCase() || '';
    const features = device.supported_features?.join(' ').toLowerCase() || '';
    const type = device.type?.toLowerCase() || ''; // Consider device type directly

    if (type.includes('router') && (model.includes('plug') || description.includes('plug'))) {
      return DeviceCategory.PLUG; // Specifically handle router plugs
    }
    if (type.includes('light') || model.includes('bulb') || description.includes('light')) {
      return DeviceCategory.LIGHT;
    } 
    if (type.includes('switch') || model.includes('switch')) {
      return DeviceCategory.SWITCH;
    } 
    if (type.includes('plug') || model.includes('plug')) {
      return DeviceCategory.PLUG;
    } 
    if (type.includes('motion') || model.includes('motion') || description.includes('motion')) {
      return DeviceCategory.MOTION;
    } 
    if (type.includes('door') || model.includes('door') || model.includes('contact')) {
      return DeviceCategory.DOOR;
    } 
    if (type.includes('climate') || model.includes('temperature') || description.includes('temperature') || 
               model.includes('humidity') || description.includes('climate')) {
      return DeviceCategory.CLIMATE;
    } 
    if (type.includes('sensor') || model.includes('sensor') || description.includes('sensor')) {
      return DeviceCategory.SENSOR;
    }

    // Fallback based on exposed features if type/model is generic
    if (features.includes('state') || features.includes('brightness')) {
      return DeviceCategory.LIGHT; // If it has state/brightness control, it's likely a light or controlled plug
    }
    if (features.includes('occupancy') || features.includes('contact') || features.includes('temperature') || features.includes('humidity')) {
      return DeviceCategory.SENSOR;
    }


    return DeviceCategory.UNKNOWN; // Explicitly unknown if no match
  }

  getSensorData(device: ZigbeeDevice): SensorData[] {
    const state = this.getDeviceState(device);
    if (!state) return [];

    const sensorData: SensorData[] = [];
    
    // Skip meta fields that shouldn't be displayed as sensor data
    const skipFields = ['ieee_address', 'friendly_name', 'device', 'update', 'update_available', 'linkquality', 'last_seen', 'elapsed'];
    
    Object.entries(state).forEach(([key, value]) => {
      if (skipFields.includes(key) || value === null || value === undefined) return;
      
      const typeInfo = this.dataTypeMap[key] || {
        icon: this.getDefaultIcon(key, value),
        label: this.formatLabel(key),
        type: this.inferType(value)
      };

      sensorData.push({
        key,
        value,
        unit: typeInfo.unit,
        icon: typeInfo.icon!,
        label: typeInfo.label!,
        type: typeInfo.type!,
        isControl: typeInfo.isControl || false,
        min: typeInfo.min,
        max: typeInfo.max
      });
    });

    // Sort data: controls first, then by importance
    return sensorData.sort((a, b) => {
      if (a.isControl && !b.isControl) return -1;
      if (!a.isControl && b.isControl) return 1;
      
      const importanceOrder = ['state', 'brightness', 'temperature', 'humidity', 'battery', 'linkquality'];
      const aIndex = importanceOrder.indexOf(a.key);
      const bIndex = importanceOrder.indexOf(b.key);
      
      if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
      if (aIndex !== -1) return -1;
      if (bIndex !== -1) return 1;
      
      return a.label.localeCompare(b.label);
    });
  }

  getSensorDataFromBack(device: any): SensorData[] {
    const state = device.state ;

    if (!state) return [];

    const sensorData: SensorData[] = [];
    
    // Skip meta fields that shouldn't be displayed as sensor data
    const skipFields = ['ieee_address', 'friendly_name', 'device', 'update', 'update_available', 'linkquality', 'last_seen', 'elapsed'];
    
    Object.entries(state).forEach(([key, value]) => {
      if (skipFields.includes(key) || value === null || value === undefined) return;
      
      const typeInfo = this.dataTypeMap[key] || {
        icon: this.getDefaultIcon(key, value),
        label: this.formatLabel(key),
        type: this.inferType(value)
      };

      sensorData.push({
        key,
        value,
        unit: typeInfo.unit,
        icon: typeInfo.icon!,
        label: typeInfo.label!,
        type: typeInfo.type!,
        isControl: typeInfo.isControl || false,
        min: typeInfo.min,
        max: typeInfo.max
      });
    });

    // Sort data: controls first, then by importance
    return sensorData.sort((a, b) => {
      if (a.isControl && !b.isControl) return -1;
      if (!a.isControl && b.isControl) return 1;
      
      const importanceOrder = ['state', 'brightness', 'temperature', 'humidity', 'battery', 'linkquality'];
      const aIndex = importanceOrder.indexOf(a.key);
      const bIndex = importanceOrder.indexOf(b.key);
      
      if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
      if (aIndex !== -1) return -1;
      if (bIndex !== -1) return 1;
      
      return a.label.localeCompare(b.label);
    });
  }
  //   getSensorDataFromBack(device: BackEndDevice): SensorData[] {
  //   const state = this.getDeviceState(device.NetworkAddress);
  //   if (!state) return [];

  //   const sensorData: SensorData[] = [];
    
  //   // Skip meta fields that shouldn't be displayed as sensor data
  //   const skipFields = ['ieee_address', 'friendly_name', 'device', 'update', 'update_available', 'linkquality', 'last_seen', 'elapsed'];
    
  //   Object.entries(state).forEach(([key, value]) => {
  //     if (skipFields.includes(key) || value === null || value === undefined) return;
      
  //     const typeInfo = this.dataTypeMap[key] || {
  //       icon: this.getDefaultIcon(key, value),
  //       label: this.formatLabel(key),
  //       type: this.inferType(value)
  //     };

  //     sensorData.push({
  //       key,
  //       value,
  //       unit: typeInfo.unit,
  //       icon: typeInfo.icon!,
  //       label: typeInfo.label!,
  //       type: typeInfo.type!,
  //       isControl: typeInfo.isControl || false,
  //       min: typeInfo.min,
  //       max: typeInfo.max
  //     });
  //   });

  //   // Sort data: controls first, then by importance
  //   return sensorData.sort((a, b) => {
  //     if (a.isControl && !b.isControl) return -1;
  //     if (!a.isControl && b.isControl) return 1;
      
  //     const importanceOrder = ['state', 'brightness', 'temperature', 'humidity', 'battery', 'linkquality'];
  //     const aIndex = importanceOrder.indexOf(a.key);
  //     const bIndex = importanceOrder.indexOf(b.key);
      
  //     if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
  //     if (aIndex !== -1) return -1;
  //     if (bIndex !== -1) return 1;
      
  //     return a.label.localeCompare(b.label);
  //   });
  // }

  private getDefaultIcon(key: string, value: any): string {
    if (typeof value === 'boolean') return 'toggle_on';
    if (typeof value === 'number') return 'straighten';
    if (key.includes('time') || key.includes('date') || key.includes('seen')) return 'schedule';
    return 'info';
  }

  private formatLabel(key: string): string {
    // Basic formatting for keys not in dataTypeMap
    return key.split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  private inferType(value: any): 'number' | 'boolean' | 'string' | 'object' {
    if (typeof value === 'boolean') return 'boolean';
    if (typeof value === 'number') return 'number';
    if (typeof value === 'object' && value !== null) return 'object';
    return 'string';
  }

  formatSensorValue(data: SensorData): string {
    if (data.type === 'boolean') {
      if (data.key === 'contact') return data.value ? 'Fermé' : 'Ouvert';
      if (data.key === 'occupancy' || data.key === 'motion') return data.value ? 'Détecté' : 'Clair';
      if (data.key === 'state') return data.value ? 'ALLUMÉ' : 'ÉTEINT';
      return data.value ? 'Oui' : 'Non';
    }
    
    if (data.type === 'number') {
      if (data.key === 'brightness') {
        // Brightness is usually 0-254, convert to percentage
        return `${Math.round((data.value / 254) * 100)}`;
      }
      // For battery and humidity, display as whole numbers
      if (data.key === 'battery' || data.key === 'humidity') {
        return `${Math.round(data.value)}`;
      }
      // For temperature, display with one decimal place
      if (data.key === 'temperature') {
        return `${data.value.toFixed(1)}`;
      }
      return data.value.toString();
    }
    
    if (data.key === 'last_seen') {
      // Use DatePipe for consistent date formatting if needed here, but done in template
      return new Date(data.value).toLocaleString('fr-FR', { dateStyle: 'short', timeStyle: 'short' });
    }
    
    if (data.type === 'object') {
      // For object values, stringify for display (can be improved)
      return JSON.stringify(data.value);
    }
    
    return data.value.toString();
  }


  getDeviceIcon(device: any): string {
    switch (this.getDeviceCategory(device)) {
      case DeviceCategory.LIGHT: return 'lightbulb';
      case DeviceCategory.SWITCH: return 'toggle_on';
      case DeviceCategory.PLUG: return 'outlet';
      case DeviceCategory.DOOR: return 'door_front';
      case DeviceCategory.CLIMATE: return 'thermostat';
      case DeviceCategory.SENSOR: return 'sensors';
      case DeviceCategory.UNKNOWN: return 'device_unknown';
      default: return 'devices';
    }
  }

  // Control methods
  toggleDevice(device: ZigbeeDevice): void {
    // This method is generic, better to use onToggleChange directly
    // based on the state key
    const currentState = this.getDeviceState(device)?.state;
    this.zigbeeService.setDeviceState(device.friendly_name, { state: currentState === 'ON' ? 'OFF' : 'ON' });
  }

  onBrightnessChange(device: ZigbeeDevice, event: MatSliderChange | any): void {
    // Handle both MatSliderChange and regular input event (for table)
    const value = event.value ?? event.target.value;
    if (value !== null && value !== undefined) {
      this.zigbeeService.setBrightness(device.friendly_name, value);
    }
  }

  onToggleChange(device: ZigbeeDevice, data: SensorData, event: any): void {
    if (data.key === 'state') {
      this.zigbeeService.setDeviceState(device.friendly_name, { state: event.checked ? 'ON' : 'OFF' });
    }
    // Add other toggleable controls if needed (e.g., lock, open/close for blinds)
  }

  getBatteryIcon(batteryLevel: number): string {
    if (batteryLevel > 90) return 'battery_full';
    if (batteryLevel > 70) return 'battery_6_bar';
    if (batteryLevel > 50) return 'battery_4_bar';
    if (batteryLevel > 30) return 'battery_2_bar';
    if (batteryLevel > 10) return 'battery_alert';
    return 'battery_0_bar'; // Very low battery
  }

  getBatteryColor(batteryLevel: number): string {
    if (batteryLevel > 60) return '#4caf50'; // Green
    if (batteryLevel > 20) return '#ff9800'; // Orange
    return '#f44336'; // Red
  }

  getSignalColor(linkQuality: number): string {
    if (linkQuality > 200) return '#4caf50'; // Excellent
    if (linkQuality > 120) return '#8bc34a'; // Good
    if (linkQuality > 60) return '#ff9800'; // Fair
    return '#f44336'; // Poor
  }

  isControlEnabled(device: ZigbeeDevice): boolean {
    // Controls are enabled only if bridge is online and device is considered online
    return this.bridgeState === 'online' && this.isDeviceOnline(device);
  }

  get availableCategories(): string[] {
    const categories = new Set(this.devices.map(d => this.getDeviceCategory(d)));
    return Array.from(categories)
      .filter(cat => cat !== DeviceCategory.UNKNOWN) // Filter out 'unknown' from filter options
      .sort();
  }

  toggleView(view: 'grid' | 'table'): void {
    this.currentView = view;
  }
  hasNoControls(device: ZigbeeDevice): boolean {
    return this.getSensorData(device).filter(s => s.isControl).length === 0;
  }
  
}