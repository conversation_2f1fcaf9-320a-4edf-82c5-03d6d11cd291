import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { TagApiService } from '@app/core/services/administrative/tag.service';
import { TagAssignmentApiService } from '@app/core/services/administrative/tag-assignment.service';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { SiteApiService } from '@app/core/services/administrative/site.service';
import { LocalApiService } from '@app/core/services/administrative/local.service';

import { Tag } from '@app/core/models/tag';
import { TagAssignment, TargetType } from '@app/core/models/TagAssignment';
import { Client } from '@app/core/models/client';
import { Site } from '@app/core/models/site';
import { Local } from '@app/core/models/local';

@Component({
  selector: 'app-tag-assignment-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    MatIconModule,
    MatButtonModule,
    MatTableModule,
    MatCardModule,
    MatSnackBarModule
  ],
  templateUrl: './tag-assignment-list.component.html',
  styleUrls: ['./tag-assignment-list.component.css']
})
export class TagAssignmentListComponent implements OnInit {
  // Data
  tags: Tag[] = [];
  assignments: TagAssignment[] = [];
  clients: Client[] = [];
  sites: Site[] = [];
  locals: Local[] = [];

  // UI State
  isLoading = false;

  constructor(
    readonly snackBar: MatSnackBar,
    readonly tagService: TagApiService,
    readonly tagAssignmentService: TagAssignmentApiService,
    readonly clientService: ClientApiService,
    readonly siteService: SiteApiService,
    readonly localService: LocalApiService
  ) {}

  ngOnInit(): void {
    this.loadInitialData();
  }

  private loadInitialData(): void {
    this.isLoading = true;
    
    Promise.all([
      this.loadTags(),
      this.loadAssignments(),
      this.loadClients(),
      this.loadSites(),
      this.loadLocals()
    ]).finally(() => {
      this.isLoading = false;
    });
  }

  private async loadTags(): Promise<void> {
    try {
      this.tags = await this.tagService.getAll().toPromise() || [];
    } catch (error) {
      console.error('Error loading tags:', error);
      this.showError('Erreur lors du chargement des tags');
    }
  }

  private async loadAssignments(): Promise<void> {
    try {
      this.assignments = await this.tagAssignmentService.getAll().toPromise() || [];
    } catch (error) {
      console.error('Error loading assignments:', error);
      this.showError('Erreur lors du chargement des affectations');
    }
  }

  private async loadClients(): Promise<void> {
    try {
      this.clients = await this.clientService.getAll().toPromise() || [];
    } catch (error) {
      console.error('Error loading clients:', error);
    }
  }

  private async loadSites(): Promise<void> {
    try {
      this.sites = await this.siteService.getAll().toPromise() || [];
    } catch (error) {
      console.error('Error loading sites:', error);
    }
  }

  private async loadLocals(): Promise<void> {
    try {
      this.locals = await this.localService.getAll().toPromise() || [];
    } catch (error) {
      console.error('Error loading locals:', error);
    }
  }

  async deleteAssignment(assignment: TagAssignment): Promise<void> {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette affectation ?')) {
      return;
    }

    this.isLoading = true;
    
    try {
      await this.tagAssignmentService.delete(assignment.Id!).toPromise();
      this.showSuccess('Affectation supprimée avec succès');
      await this.loadAssignments();
    } catch (error) {
      console.error('Error deleting assignment:', error);
      this.showError('Erreur lors de la suppression de l\'affectation');
    } finally {
      this.isLoading = false;
    }
  }

  getTargetName(assignment: TagAssignment): string {
    switch (assignment.TargetType) {
      case TargetType.Client:
        const client = this.clients.find(c => c.Id === assignment.TargetId);
        return client?.Name || 'Client inconnu';
      case TargetType.Site:
        const site = this.sites.find(s => s.Id === assignment.TargetId);
        return site?.Name || 'Site inconnu';
      case TargetType.Local:
        const local = this.locals.find(l => l.Id === assignment.TargetId);
        return local?.Name || 'Local inconnu';
      default:
        return 'Inconnu';
    }
  }

  getTagName(assignment: TagAssignment): string {
    const tag = this.tags.find(t => t.Id === assignment.IdTag);
    return tag?.Nom || 'Tag inconnu';
  }

  getTargetTypeDisplay(targetType: TargetType | string): string {
    // Handle different possible types from API
    if (typeof targetType === 'string') {
      return targetType;
    }

    // If it's a number (enum index), convert it
    if (typeof targetType === 'number') {
      const enumKeys = Object.keys(TargetType);
      const enumValues = Object.values(TargetType);
      const index = enumValues.indexOf(targetType as TargetType);
      return index >= 0 ? enumKeys[index] : 'Inconnu';
    }

    // If it's already the enum value
    return String(targetType) || 'Inconnu';
  }

  getTargetTypeClass(targetType: TargetType | string): string {
    const displayValue = this.getTargetTypeDisplay(targetType);
    return displayValue.toLowerCase();
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }
}
