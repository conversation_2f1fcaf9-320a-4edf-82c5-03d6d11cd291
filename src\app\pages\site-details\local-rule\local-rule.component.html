<div class="dialog-container">
    <div class="dialog-header">
        <h1 class="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-2">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z">
                </path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z">
                </path>
            </svg>
            <span>Créateur de RuleTransaction</span>
        </h1>

        <button mat-icon-button (click)="onCancel()" class="close-button" [disabled]="isBusy">
            <mat-icon>close</mat-icon>
        </button>
    </div>

    <div class="dialog-content" [class.pointer-events-none]="isBusy">
        <ngx-ui-loader *ngIf="isLoading"></ngx-ui-loader>

        <div *ngIf="loadingError && !isLoading" class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                        clip-rule="evenodd"></path>
                </svg>
                <div>
                    <h3 class="text-sm font-medium text-red-800">Erreur de chargement</h3>
                    <p class="text-sm text-red-700 mt-1">{{ loadingError }}</p>
                </div>
            </div>
            <div class="mt-3">
                <button (click)="retryLoadData()"
                    class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700">
                    Réessayer
                </button>
            </div>
        </div>

        <div *ngIf="!isLoading && !loadingError">

            <div class="mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <span
                        class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">1</span>
                    Sélection de la règle de base
                </h2>

                <div class="dropdown-container">
                    <label for="ruleDropdown" class="block text-sm font-medium text-gray-700 mb-2">
                        Choisir une règle existante:
                    </label>
                    <select id="ruleDropdown" [(ngModel)]="selectedRuleId" (ngModelChange)="onRuleSelectionChange()"
                        [disabled]="isLoading || isBusy"
                        class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">-- Sélectionner une règle --</option>
                        <option *ngFor="let rule of ruleList" [value]="rule.Id">
                            {{ getRuleNameFromRawData(rule.RawData) }}
                        </option>
                    </select>
                </div>

                <div *ngIf="baseRule" class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                    <h4 class="text-sm font-medium text-blue-800 mb-2">Aperçu de la règle sélectionnée:</h4>
                    <div class="text-sm text-blue-700">
                        <div><strong>Nom:</strong> {{ baseRule.rule_name }}</div>
                        <div><strong>Conditions:</strong> {{ baseRule.conditions.groups.length }} groupe(s)</div>
                        <div><strong>Actions:</strong> {{ baseRule.actions.length }} action(s)</div>
                    </div>
                </div>
            </div>

            <div *ngIf="isRuleSelected" class="mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <span
                        class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">2</span>
                    Sélection du contrôleur
                </h2>

                <div class="grid grid-cols-1 gap-4">
                    <div class="dropdown-container">
                        <label for="controllerDropdown" class="block text-sm font-medium text-gray-700 mb-2">
                            Contrôleur:
                        </label>
                        <select id="controllerDropdown" [(ngModel)]="selectedControllerId"
                            (ngModelChange)="onControllerSelectionChange()" [disabled]="isLoading || isBusy"
                            class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">-- Sélectionner un contrôleur --</option>
                            <option *ngFor="let controller of controllers" [value]="controller.Id">
                                {{ controller.Model }}
                            </option>
                        </select>
                    </div>

                    <div *ngIf="selectedControllerId && availableCapteursForMapping.length === 0"
                        class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                        <p class="text-sm text-yellow-800">
                            Aucun capteur associé trouvé pour ce contrôleur via les transactions disponibles.
                        </p>
                    </div>

                </div>
            </div>

            <div *ngIf="isRuleSelected && deviceMappings.length > 0" class="mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <span
                        class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">3</span>
                    Mappage des capteurs
                </h2>

                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
                    <p class="text-sm text-yellow-800">
                        <strong>Instructions:</strong> Associez chaque dispositif de la règle de base avec un capteur
                        réel de vos transactions.
                    </p>
                </div>

                <div class="space-y-4">
                    <div *ngFor="let mapping of deviceMappings" class="bg-white p-4 border border-gray-200 rounded-md">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-600 mb-1">
                                    Dispositif dans la règle:
                                </label>
                                <div class="p-2 bg-gray-100 border border-gray-300 rounded-md text-sm text-gray-700">
                                    {{ mapping.originalDevice }}
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600 mb-1">
                                    Capteur réel à associer:
                                </label>
                                <select [(ngModel)]="mapping.selectedCapteurId"
                                    (ngModelChange)="onDeviceMappingChange(mapping)" [disabled]="isFieldDisabled()"
                                    class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">-- Sélectionner un capteur --</option>
                                    <!-- <option *ngFor="let capteur of getFilteredCapteursForDeviceMapping(mapping)"
                                        [value]="capteur.Id">
                                        {{ capteur.TypeCapteur?.DisplayName || capteur.TypeCapteur?.Nom }}
                                        ({{ capteur.TypeCapteur?.Topic }})
                                    </option> -->
                                    <option *ngFor="let capteur of getFilteredCapteursForDeviceMapping(mapping)"
                                        [value]="capteur.Id">
                                        {{ capteur.FriendlyName }} ({{ capteur.Topic }})
                                    </option>
                                </select>
                            </div>
                        </div>

                        <div *ngIf="mapping.selectedTypeCapteur"
                            class="mt-2 p-2 bg-green-50 border border-green-200 rounded-md">
                            <div class="text-sm text-green-800">
                                <strong>Associé à:</strong> {{ mapping.selectedTypeCapteur.DisplayName ||
                                mapping.selectedTypeCapteur.Nom }}
                                <br>
                                <strong>Topic:</strong> {{ mapping.selectedTypeCapteur.Topic }}
                                <div *ngIf="mapping.availableProperties && mapping.availableProperties.length > 0"
                                    class="mt-1">
                                    <strong>Propriétés disponibles:</strong>
                                    <span *ngFor="let prop of mapping.availableProperties; let last = last">
                                        {{ prop.key }}<span *ngIf="!last">, </span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div *ngIf="isRuleSelected && actionMappings.length > 0" class="mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <span
                        class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">4</span>
                    Mappage des actionneurs
                </h2>

                <div class="space-y-4">
                    <div *ngFor="let mapping of actionMappings" class="bg-white p-4 border border-gray-200 rounded-md">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-600 mb-1">
                                    Topic dans la règle:
                                </label>
                                <div class="p-2 bg-gray-100 border border-gray-300 rounded-md text-sm text-gray-700">
                                    {{ mapping.originalTopic }}
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600 mb-1">
                                    Actionneur réel à associer:
                                </label>
                                <select [(ngModel)]="mapping.selectedCapteurId"
                                    (ngModelChange)="onActionMappingChange(mapping)" [disabled]="isFieldDisabled()"
                                    class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">-- Sélectionner un actionneur --</option>
                                    <!-- <option *ngFor="let capteur of getFilteredCapteursForActionMapping(mapping)"
                                        [value]="capteur.Id">
                                        {{ capteur.TypeCapteur?.DisplayName || capteur.TypeCapteur?.Nom }}
                                        ({{ capteur.TypeCapteur?.Topic }})
                                    </option> -->
                                    <option *ngFor="let capteur of getFilteredCapteursForActionMapping(mapping)"
                                        [value]="capteur.Id">
                                        {{ capteur.FriendlyName }} ({{ capteur.Topic }})
                                    </option>
                                </select>
                            </div>
                        </div>

                        <div *ngIf="mapping.selectedTypeCapteur"
                            class="mt-2 p-2 bg-green-50 border border-green-200 rounded-md">
                            <div class="text-sm text-green-800">
                                <strong>Associé à:</strong> {{ mapping.selectedTypeCapteur.DisplayName ||
                                mapping.selectedTypeCapteur.Nom }}
                                <br>
                                <strong>Topic:</strong> {{ mapping.selectedTypeCapteur.Topic }}
                                <div *ngIf="mapping.availableActions && mapping.availableActions.length > 0"
                                    class="mt-1">
                                    <strong>Actions disponibles:</strong>
                                    <span *ngFor="let action of mapping.availableActions; let last = last">
                                        {{ action.type }}<span *ngIf="!last">, </span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div *ngIf="isRuleSelected" class="mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <span
                        class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">5</span>
                    Aperçu de la règle transformée
                </h2>

                <div class="bg-gray-50 p-4 rounded-md mb-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Nom de la règle
                                transformée:</label>
                            <input type="text" [(ngModel)]="transformedRule.rule_name" [disabled]="isFieldDisabled()"
                                class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Nom de la règle" />
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Priorité:</label>
                            <select [(ngModel)]="transformedRule.priority" [disabled]="isFieldDisabled()"
                                class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option *ngFor="let p of priorities" [value]="p">Priorité {{ p }}</option>
                            </select>
                        </div>
                    </div>

                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Topics utilisés:</label>
                        <div class="p-2 border border-gray-300 rounded-md bg-white min-h-[40px]">
                            <div *ngIf="transformedRule.topic_pattern.length === 0" class="text-gray-500 text-sm">
                                Aucun topic défini
                            </div>
                            <div *ngFor="let topic of transformedRule.topic_pattern" class="text-sm text-blue-600 mb-1">
                                {{ topic }}
                            </div>
                        </div>
                    </div>

                    <div class="mt-4 flex items-center">
                        <label class="flex items-center gap-2">
                            <input type="checkbox" [(ngModel)]="transformedRule.enabled"
                                [disabled]="isFieldDisabled()" />
                            <span class="text-sm font-medium text-gray-700">Règle activée</span>
                        </label>
                    </div>
                </div>

                <div class="mt-4">
                    <h3 class="text-md font-semibold text-gray-800 mb-2">Aperçu JSON de la règle transformée:</h3>
                    <pre
                        class="bg-gray-900 text-green-400 p-4 rounded-md text-sm overflow-x-auto max-h-64">{{ generateJSON() }}</pre>
                </div>
            </div>

            <div *ngIf="isRuleSelected" class="mb-6">
                <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                    <h3 class="text-sm font-medium text-blue-800 mb-2">Résumé de l'opération:</h3>
                    <div class="text-sm text-blue-700">
                        <div><strong>Règle de base:</strong> {{ baseRule?.rule_name }}</div>
                        <div><strong>Contrôleur sélectionné:</strong> {{ getSelectedControllerModel() }}</div>
                        <div><strong>Capteurs mappés:</strong> {{ mappedDeviceCount }}/{{ deviceMappings.length }}</div>
                        <div><strong>Actionneurs mappés:</strong> {{ mappedActionCount }}/{{ actionMappings.length }}
                        </div>
                    </div>
                    <div *ngIf="selectedControllerId" class="mt-2">
                        <p class="text-xs text-blue-600">
                            Des RuleTransactions seront créé(s) pour les transactions associées au contrôleur
                            sélectionné.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="dialog-footer">
        <button mat-button (click)="onCancel()" class="cancel-button" [disabled]="isBusy">
            <mat-icon>cancel</mat-icon>
            <span>Annuler</span>
        </button>

        <button mat-button (click)="downloadJSON()" class="download-button" [disabled]="isBusy || !isRuleSelected">
            <mat-icon>download</mat-icon>
            Télécharger JSON
        </button>

        <button mat-raised-button (click)="onSave()" class="save-button"
            [disabled]="!isRuleSelected || !transformedRule.rule_name.trim() || !selectedControllerId || isBusy">
            <mat-icon>save</mat-icon>
            <span>Créer RuleTransactions</span>
        </button>
    </div>

    <div *ngIf="isBusy"
        class="absolute inset-0 bg-white bg-opacity-75 z-50 flex items-center justify-center pointer-events-auto">
        <div class="flex flex-col items-center">
            <div class="animate-spin rounded-full h-12 w-12 border-t-4 border-primary border-solid mb-4"></div>
            <span class="text-primary font-semibold text-lg">{{ statusMessage }}</span>
            <div class="progress-bar mt-2">
                <div class="progress-bar-fill" [style.width.%]="operationProgress"></div>
            </div>
        </div>
    </div>
</div>