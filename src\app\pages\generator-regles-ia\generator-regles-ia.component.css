/* === CORE LAYOUT === */

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: var(--container-bg);
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  position: relative;
}

.card {
  border: none;
  box-shadow: var(--card-shadow);
  background: var(--card-bg);
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

/* === HEADER === */

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid var(--card-border);
  background: var(--white);
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
}

/* === SEARCH AND CREATE SECTION === */

.search-create {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  margin-top: 24px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.create-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.ai-create-button {
  background: linear-gradient(45deg, var(--primary), var(--primary-light)) !important;
  color: white !important;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(46, 125, 50, 0.3);
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 0 20px;
  height: 40px;
}

.ai-create-button:hover:not(:disabled) {
  background: linear-gradient(45deg, var(--primary-dark), var(--primary)) !important;
  box-shadow: 0 4px 12px rgba(46, 125, 50, 0.4);
  transform: translateY(-1px);
}

.ai-create-button:disabled {
  background: var(--grey-light) !important;
  color: var(--text-secondary) !important;
  box-shadow: none;
}

.ai-create-button mat-icon {
  margin-right: 8px;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { 
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  50% { 
    transform: scale(1.1) rotate(180deg);
    opacity: 0.8;
  }
}

.create-button {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: white !important;
  font-weight: 500;
  border-radius: 8px;
  padding: 0 20px;
  height: 40px;
  transition: all 0.3s ease;
}

.create-button:hover:not(:disabled) {
  background: linear-gradient(45deg,#81c784, var(--primary)) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(46, 125, 50, 0.3);
}

.create-button mat-icon {
  margin-right: 8px;
}

/* Dialog panel styles */
.ai-rule-generator-dialog-panel .mat-dialog-container {
  border-radius: 12px;
  overflow: hidden;
}

/* Responsive design for mobile */
@media (max-width: 768px) {
  .search-create {
    flex-direction: column;
    align-items: stretch;
  }
  
  .create-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .ai-create-button,
  .create-button {
    flex: 1;
    min-width: 160px;
  }
}
.search-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}

.input {
  flex: 1;
  min-width: 200px;
  padding: 12px 16px;
  padding-right: 40px;
  border: 1px solid var(--card-border);
  border-radius: 6px;
  font-size: 14px;
  color: var(--text-primary);
  background: var(--white);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.input:focus {
  border-color: var(--green-main);
  outline: none;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.input:disabled {
  background: var(--background);
  color: var(--text-secondary);
  cursor: not-allowed;
  opacity: 0.7;
}

.search-loading {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.clear-search-button {
  margin-left: 8px;
  padding: 4px 8px;
  min-height: 32px;
  color: var(--green-main) !important;
  border-color: var(--green-main) !important;
  transition: all 0.2s ease;
}

.clear-search-button:hover {
  background: var(--green-light) !important;
  transform: translateY(-1px);
}

.clear-search-button mat-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
}

/* === LOADING STATES === */

.loading-icon {
  font-size: 18px !important;
  width: 18px !important;
  height: 18px !important;
  color: var(--green-main);
  animation: spin 1s linear infinite;
}

.loading-icon.large {
  font-size: 48px !important;
  width: 48px !important;
  height: 48px !important;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.search-loading-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 40px 20px;
  color: var(--text-secondary);
  font-size: 16px;
  background: var(--background);
  border-radius: 8px;
  margin-top: 20px;
}

.search-loading-message .loading-icon {
  font-size: 24px !important;
  width: 24px !important;
  height: 24px !important;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border-radius: 8px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 40px;
  background: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--card-border);
}

/* === EMPTY STATES === */

.no-data-message {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-secondary);
  font-size: 16px;
  background: var(--background);
  border-radius: 12px;
  margin-top: 20px;
  border: 2px dashed var(--card-border);
  position: relative;
}

.no-data-message::before {
  content: '🔍';
  font-size: 64px;
  display: block;
  margin-bottom: 16px;
  opacity: 0.2;
}

.hierarchy-empty-state {
  text-align: center;
  padding: 48px 20px;
  color: var(--text-secondary);
  font-size: 16px;
  background: var(--background);
  border-radius: 12px;
  margin: 24px 0;
  border: 2px dashed var(--card-border);
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.hierarchy-empty-state::before {
  content: '🗂️';
  font-size: 48px;
  display: block;
  margin-bottom: 12px;
  opacity: 0.2;
}

/* === RULES LIST === */

.rules-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px 0;
}

.rule-item {
  background: var(--white);
  border-radius: 12px;
  border: 1px solid var(--card-border);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
  animation: fadeInUp 0.3s ease-out;
}

.rule-item:hover:not(.action-pending) {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border-color: var(--green-main);
  transform: translateY(-2px);
}

.rule-item.rule-expanded {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: var(--green-main);
}

.rule-item.action-pending {
  opacity: 0.6;
  pointer-events: none;
  position: relative;
}

.rule-item.action-pending::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  z-index: 10;
  border-radius: inherit;
}

/* Simple vs Complex Rules */
.rule-item.simple-rule {
  border-left: 4px solid var(--green-main);
  min-height: 80px;
}

.rule-item.simple-rule .rule-content {
  padding: 24px 28px;
  padding-right: 140px;
  display: flex;
  align-items: flex-start;
  min-height: 80px;
}

.rule-item.simple-rule .rule-details {
  flex: 1;
  gap: 8px;
}

.rule-item.simple-rule .rule-header {
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 0;
  flex-wrap: nowrap;
  gap: 16px;
}

.rule-item.simple-rule .rule-name {
  font-size: 16px;
  font-weight: 500;
  flex: 1;
  margin-right: 12px;
}

.rule-item.simple-rule .rule-actions {
  position: absolute;
  top: 24px;
  right: 24px;
  gap: 6px;
}

.rule-item.complex-rule {
  background: var(--white);
  position: relative;
}

.rule-item.complex-rule .rule-content {
  padding: 24px 28px;
  padding-right: 140px;
}

.rule-item.complex-rule .rule-actions {
  position: absolute;
  top: 24px;
  right: 24px;
  gap: 6px;
}

/* === RULE CONTENT === */

.rule-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 20px;
}

.rule-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 0;
}

.rule-header {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 8px;
}

.rule-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--card-title);
  flex: 1;
  min-width: 0;
  word-break: break-word;
}

.priority {
  font-size: 12px;
  color: var(--text-secondary);
  background: var(--beige-darl);
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.status {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  white-space: nowrap;
}

.status-active {
  background: var(--green-light);
  color: var(--green-dark);
}

.status-inactive {
  background: var(--beige-darl);
  color: var(--text-secondary);
}

/* === SUMMARY PREVIEW === */

.summary-preview {
  margin-top: 8px;
  padding: 12px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8px;
  border: 1px solid #bae6fd;
  transition: all 0.2s ease;
}

.summary-preview:hover {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
  border-color: #38bdf8;
}

.summary-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.summary-icon {
  color: #0ea5e9 !important;
  font-size: 20px !important;
  width: 20px !important;
  height: 20px !important;
  flex-shrink: 0;
  margin-top: 2px;
}

.summary-text {
  font-size: 13px;
  line-height: 1.5;
  color: #0c4a6e;
  margin: 0;
  font-style: italic;
  flex: 1;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* === USAGE STATISTICS === */

.usage-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 12px;
  margin: 12px 0;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--background);
  padding: 10px 12px;
  border-radius: 8px;
  border: 1px solid var(--card-border);
  transition: all 0.2s ease;
}

.stat-item:hover {
  background: var(--green-light);
  border-color: var(--green-main);
}

.stat-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
  color: var(--green-main);
  flex-shrink: 0;
}

.stat-value {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
}

.stat-label {
  font-size: 11px;
  color: var(--text-secondary);
  font-weight: 500;
}

/* === TAGS === */

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 8px 0;
}

.no-tags {
  color: var(--text-secondary);
  font-style: italic;
  font-size: 12px;
  padding: 8px 12px;
  background: var(--background);
  border-radius: 6px;
  border: 1px dashed var(--card-border);
}

.tag {
  font-size: 12px;
  font-weight: 500;
  background: var(--green-light);
  color: var(--green-dark);
  padding: 6px 12px;
  border-radius: 16px;
  white-space: nowrap;
  border: 1px solid var(--green-main);
  transition: all 0.2s ease;
}

.tag:hover {
  background: var(--green-main);
  color: var(--white);
}

/* === ACTIONS TRIGGERED === */

.actions-triggered {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 12px;
  padding: 12px;
  background: var(--background);
  border-radius: 8px;
  border: 1px solid var(--card-border);
}

.actions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.action-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
  white-space: nowrap;
  margin-right: 8px;
}

.action-bubble {
  background: var(--white);
  color: var(--text-primary);
  padding: 6px 10px;
  border-radius: 12px;
  font-size: 12px;
  white-space: nowrap;
  border: 1px solid var(--card-border);
  transition: all 0.2s ease;
}

.action-bubble:hover {
  background: var(--green-light);
  border-color: var(--green-main);
}

.no-actions {
  color: var(--text-secondary);
  font-style: italic;
  font-size: 12px;
  padding: 8px 0;
}

.last-triggered {
  font-size: 12px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px dashed var(--card-border);
}

.timestamp {
  font-weight: 500;
  color: var(--text-primary);
}

/* === RULE ACTIONS (BUTTONS) === */

.rule-actions {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
  align-items: center;
  position: absolute;
  top: 24px;
  right: 24px;
  min-width: 96px;
  justify-content: flex-end;
  z-index: 2;
}

.rule-actions button[mat-icon-button] {
  width: 32px;
  height: 32px;
  margin: 0;
  transition: all 0.2s ease;
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  color: #374151 !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}

.rule-actions button[mat-icon-button]:hover:not(:disabled) {
  background: #f3f4f6 !important;
  border-color: #10b981 !important;
  color: #10b981 !important;
}

.rule-actions button[mat-icon-button]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.rule-actions button[mat-icon-button][color="primary"] {
  color: #10b981 !important;
}

.rule-actions button[mat-icon-button][color="primary"]:hover:not(:disabled) {
  background: #d1fae5 !important;
  border-color: #10b981 !important;
  color: #047857 !important;
}

.rule-actions button[mat-icon-button][color="warn"] {
  color: #dc2626 !important;
}

.rule-actions button[mat-icon-button][color="warn"]:hover:not(:disabled) {
  background: #fee2e2 !important;
  border-color: #dc2626 !important;
  color: #b91c1c !important;
}

.destructive-action {
  position: relative;
  overflow: visible;
}

.destructive-action:hover::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--danger), var(--warning));
  border-radius: calc(50% + 2px);
  opacity: 0.3;
  z-index: -1;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

/* === EXPANDED DETAILS === */

.expanded-details {
  margin-top: 0;
  background: var(--background);
  border-top: 2px solid var(--green-main);
  overflow: hidden;
}

/* === TAB NAVIGATION === */

.tab-navigation {
  display: flex;
  background: var(--white);
  border-bottom: 1px solid var(--card-border);
}

.tab-button {
  flex: 1;
  padding: 16px 20px;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
  position: relative;
}

.tab-button:hover {
  background: var(--background);
  color: var(--text-primary);
}

.tab-button.active {
  color: var(--green-main);
  background: var(--green-light);
  border-bottom-color: var(--green-main);
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--green-main);
}

.tab-button mat-icon {
  font-size: 18px !important;
  width: 18px !important;
  height: 18px !important;
}

/* === TAB CONTENT === */

.tab-content {
  padding: 20px;
  background: var(--white);
}

/* === SUMMARY TAB === */

.summary-tab {
  min-height: 300px;
}

.summary-viewer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--green-main);
}

.summary-header h4 {
  color: var(--green-main);
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.summary-actions {
  display: flex;
  gap: 8px;
}

.summary-content-display {
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  padding: 24px;
  position: relative;
  overflow: hidden;
}

.summary-content-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #06b6d4, #10b981);
}

.summary-text-content {
  font-size: 15px;
  line-height: 1.7;
  color: #334155;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.summary-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60px 20px;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 2px dashed #cbd5e1;
  color: var(--text-secondary);
}

.summary-empty-state .empty-icon {
  font-size: 64px !important;
  width: 64px !important;
  height: 64px !important;
  color: #94a3b8;
  margin-bottom: 16px;
  opacity: 0.6;
}

.summary-empty-state h5 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
}

.summary-empty-state p {
  font-size: 14px;
  line-height: 1.6;
  max-width: 400px;
  margin: 0;
  color: var(--text-secondary);
}

.summary-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  background: var(--background);
  border-radius: 6px;
  border: 1px solid var(--card-border);
}

.summary-info .info-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 13px;
  color: var(--text-secondary);
}

.summary-info .info-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
  color: var(--text-secondary);
  flex-shrink: 0;
  margin-top: 1px;
}

/* === HIERARCHY TAB === */

.hierarchy h4 {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid var(--green-main);
  color: var(--green-main);
  font-size: 16px;
  font-weight: 600;
}

.client-node, .site-node, .location-node, .controller-node {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--card-border);
  background: var(--white);
  margin-bottom: 8px;
  transition: all 0.2s ease;
}

.client-node:hover, .site-node:hover, .location-node:hover, .controller-node:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.site-node {
  margin-left: 20px;
}

.location-node {
  margin-left: 40px;
}

.controller-node {
  margin-left: 60px;
}

.node-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: var(--background);
}

.controller-header {
  cursor: pointer;
  transition: all 0.2s ease;
}

.controller-header:hover {
  background: var(--green-light);
}

.controller-header.selected {
  background: var(--green-main);
  color: var(--white);
}

.controller-header.selected .controller-icon,
.controller-header.selected span,
.controller-header.selected .arrow-icon {
   color: var(--white) !important;
}

.node-header mat-icon {
  font-size: 20px !important;
  width: 20px !important;
  height: 20px !important;
  color: var(--green-main);
  flex-shrink: 0;
}

.arrow-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
  color: var(--text-secondary);
  transition: transform 0.2s ease;
  margin-left: auto;
}

.controller-header.selected .arrow-icon {
  transform: rotate(180deg);
}

.controller-icon.status-online {
  color: var(--success) !important;
}

.controller-icon.status-offline {
  color: var(--danger) !important;
}

.controller-icon.status-warning {
  color: var(--warning) !important;
}

.controller-icon.status-unknown {
  color: var(--text-secondary) !important;
}

.controller-details-panel {
  padding: 16px;
  background: var(--white);
  border-top: 1px solid var(--card-border);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-row {
  font-size: 14px;
  color: var(--text-primary);
  padding: 8px 0;
  border-bottom: 1px dashed var(--card-border);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row strong {
  color: var(--card-title);
  margin-right: 8px;
}

.controller-details-panel h5 {
  font-size: 14px;
  font-weight: 600;
  color: var(--green-main);
  margin-top: 16px;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: var(--green-light);
  border-radius: 6px;
  border-left: 4px solid var(--green-main);
}

/* === PERFORMANCE CHART === */

.performance-chart {
  background: var(--background);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid var(--card-border);
  margin-top: 16px;
}

.chart-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 8px;
  padding: 8px 16px;
  background: var(--white);
  border-radius: 6px;
  font-weight: 600;
  font-size: 12px;
  color: var(--text-secondary);
  border: 1px solid var(--card-border);
  margin-bottom: 12px;
}

.chart-grid {
  min-height: 200px;
  background: var(--white);
  border-radius: 6px;
  border: 1px solid var(--card-border);
  padding: 16px;
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  gap: 8px;
  position: relative;
}

.chart-grid.empty-state {
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  color: var(--text-secondary);
}

.chart-grid.empty-state::before {
  content: '📊';
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.3;
}

.chart-grid.empty-state .empty-message {
  font-size: 14px;
  color: var(--text-secondary);
  font-style: italic;
}

.chart-bar-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  gap: 4px;
  height: 160px;
  min-width: 40px;
  position: relative;
  padding-bottom: 20px;
}

.bar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 120px;
  width: 20px;
  background: var(--background);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  border: 1px solid var(--card-border);
}

.bar-segment {
  width: 100%;
  transition: height 0.3s ease;
  position: relative;
}

.success-bar {
  background: linear-gradient(180deg, var(--success) 0%, #10b981 100%);
}

.failure-bar {
  background: linear-gradient(180deg, var(--danger) 0%, #dc2626 100%);
}

.bar-label {
  font-size: 10px;
  color: var(--text-secondary);
  font-weight: 500;
  position: absolute;
  bottom: 0;
  white-space: nowrap;
  text-align: center;
}

/* === APPLICATIONS TABLE === */

.applications-table {
  background: var(--white);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--card-border);
  margin-top: 16px;
}

.applications-table.empty-state {
  padding: 40px 20px;
  text-align: center;
  background: var(--background);
}

.applications-table.empty-state::before {
  content: '📱';
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
  opacity: 0.3;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 12px;
  padding: 12px 16px;
  background: var(--background);
  font-weight: 600;
  font-size: 12px;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--card-border);
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 12px;
  padding: 12px 16px;
  border-bottom: 1px solid var(--card-border);
  align-items: center;
  font-size: 13px;
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background: var(--background);
}

.table-row:last-child {
  border-bottom: none;
}

.table-row mat-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
}

.empty-message {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-secondary);
  font-style: italic;
  background: var(--background);
  border-radius: 8px;
  margin-top: 20px;
  border: 2px dashed var(--card-border);
}

/* === RAW DATA TAB === */

.rawdata-tab {
  min-height: 400px;
}

.rawdata-viewer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.rawdata-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--green-main);
}

.rawdata-header h4 {
  color: var(--green-main);
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.rawdata-actions {
  display: flex;
  gap: 8px;
}

.copy-button, .download-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  font-size: 13px;
  color: var(--text-primary);
  border: 1px solid var(--card-border);
  border-radius: 6px;
  background: var(--white);
  transition: all 0.2s ease;
}

.copy-button:hover, .download-button:hover {
  border-color: var(--green-main);
  color: var(--green-main);
  background: var(--green-light);
  transform: translateY(-1px);
}

.copy-button mat-icon, .download-button mat-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
}

.json-viewer {
  background: #1e1e1e;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--card-border);
  max-height: 500px;
  overflow-y: auto;
}

.json-content {
  margin: 0;
  padding: 20px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #d4d4d4;
  background: transparent;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.rawdata-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  background: var(--background);
  border-radius: 6px;
  border: 1px solid var(--card-border);
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 13px;
  color: var(--text-secondary);
}

.info-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
  color: var(--text-secondary);
  flex-shrink: 0;
  margin-top: 1px;
}

/* === PAGINATION === */

.pagination-info {
  border-top: 1px solid var(--card-border);
  background: var(--background);
  border-radius: 0 0 8px 8px;
}

.pagination-info.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 20px;
  color: var(--text-secondary);
  font-size: 14px;
}

.pagination-info.simple-info {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px 20px;
  color: var(--text-secondary);
  font-size: 14px;
}

.simple-info .page-info {
  margin: 0;
  font-weight: 500;
  color: var(--text-primary);
}

.page-number {
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid var(--card-border);
  background: var(--white);
  color: var(--text-primary);
  font-weight: 500;
}

.page-number:hover:not(.disabled) {
  background: var(--green-light);
  border-color: var(--green-main);
  color: var(--green-dark);
}

.page-number.active {
  background: var(--green-main);
  color: var(--white);
  border-color: var(--green-main);
}

.page-number.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.operation-indicator {
  display: inline-flex;
  align-items: center;
  color: var(--text-secondary);
  font-style: italic;
}

/* === BUTTONS === */

.create-button {
  background-color: var(--green-main) !important;
  color: var(--white) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.create-button:hover:not(:disabled) {
  background-color: var(--primary-dark) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.create-button:disabled {
  background-color: var(--grey-light) !important;
  cursor: not-allowed;
  transform: none !important;
}

button.mat-raised-button {
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

button.mat-raised-button:not(.header button.mat-raised-button) {
  background: var(--green-main);
  color: var(--white);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

button.mat-raised-button:not(.header button.mat-raised-button):hover:not(:disabled) {
  background: var(--primary-dark);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

button.mat-raised-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  background: var(--grey-light) !important;
}

button[mat-icon-button] {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

button[mat-icon-button]:hover:not(:disabled) {
  background: var(--hover-bg-color);
  transform: scale(1.1);
}

button[mat-icon-button]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* === CONFIRMATION DIALOG OVERRIDE === */

::ng-deep .confirmation-dialog-panel {
  max-width: 500px !important;
  max-height: 90vh !important;
}

::ng-deep .confirmation-dialog-panel .mat-dialog-container {
  padding: 0 !important;
  background: transparent !important;
  box-shadow: none !important;
  border-radius: 16px !important;
  overflow: visible !important;
}

/* === ANIMATIONS === */

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.rule-item:nth-child(odd) {
  animation-delay: 0.1s;
}

.rule-item:nth-child(even) {
  animation-delay: 0.2s;
}

/* === MATERIAL ICONS FIX === */

.mat-icon, mat-icon {
  font-family: 'Material Icons' !important;
  font-weight: normal !important;
  font-style: normal !important;
  font-size: 18px !important;
  line-height: 1 !important;
  letter-spacing: normal !important;
  text-transform: none !important;
  display: inline-block !important;
  white-space: nowrap !important;
  word-wrap: normal !important;
  direction: ltr !important;
  -webkit-font-feature-settings: 'liga' !important;
  -webkit-font-smoothing: antialiased !important;
  text-rendering: optimizeLegibility !important;
  -moz-osx-font-smoothing: grayscale !important;
  font-feature-settings: 'liga' !important;
}

button .mat-icon,
button mat-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 18px !important;
  height: 18px !important;
  font-size: 18px !important;
  line-height: 18px !important;
}

/* === RESPONSIVE DESIGN === */

@media (max-width: 768px) {
  .container {
    padding: 16px;
    margin: 0;
    border-radius: 0;
  }

  .header {
    padding: 16px;
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .title {
    font-size: 20px;
  }

  .search-create {
    flex-direction: column;
    align-items: stretch;
    padding: 16px;
  }

  .input {
    min-width: unset;
  }

  .rules-list {
    gap: 12px;
    padding: 12px 0;
  }

  .rule-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding-right: 20px !important;
  }

  .rule-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .rule-actions {
    position: static !important;
    align-self: flex-end;
    margin-left: 0;
    margin-top: 10px;
    transform: none !important;
  }

  .rule-item.simple-rule .rule-content,
  .rule-item.complex-rule .rule-content {
    padding: 16px;
  }

  .summary-preview {
    margin-top: 8px;
  }

  .summary-content {
    flex-direction: column;
    gap: 8px;
  }

  .summary-icon {
    align-self: flex-start;
  }

  .usage-stats {
    grid-template-columns: 1fr 1fr;
    gap: 8px;
  }

  .site-node, .location-node, .controller-node {
    margin-left: 10px;
  }

  .tab-navigation {
    flex-direction: column;
  }

  .tab-button {
    flex: none;
    padding: 12px 16px;
  }

  .tab-content {
    padding: 16px;
  }

  .summary-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .summary-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .summary-empty-state {
    padding: 40px 16px;
  }

  .summary-text-content {
    font-size: 14px;
  }

  .rawdata-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .rawdata-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .json-viewer {
    max-height: 300px;
  }

  .chart-grid {
    gap: 4px;
    min-height: 150px;
  }

  .chart-bar-column {
    height: 120px;
    min-width: 30px;
  }

  .table-header, .table-row {
    grid-template-columns: 1fr;
    gap: 4px;
  }

  .table-header span, .table-row span {
    padding: 4px 0;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 18px;
  }

  .rule-name {
    font-size: 16px;
  }

  .usage-stats {
    grid-template-columns: 1fr;
  }

  .chart-bar-column {
    min-width: 25px;
    height: 100px;
  }

  .bar-container {
    height: 80px;
    width: 16px;
  }

  .summary-preview {
    padding: 8px;
  }

  .summary-text {
    font-size: 12px;
  }

  .summary-content-display {
    padding: 16px;
  }

  .summary-text-content {
    font-size: 13px;
    line-height: 1.6;
  }

  .summary-empty-state {
    padding: 30px 12px;
  }

  .summary-empty-state .empty-icon {
    font-size: 48px !important;
    width: 48px !important;
    height: 48px !important;
  }

  .summary-empty-state h5 {
    font-size: 16px;
  }

  .summary-empty-state p {
    font-size: 13px;
  }

  button.mat-raised-button {
    padding: 6px 12px;
    font-size: 12px;
  }

  .tab-content {
    padding: 12px;
  }

  .json-content {
    font-size: 11px;
    padding: 12px;
  }
}

/* === ACCESSIBILITY === */

@media (prefers-contrast: high) {
  .rule-item {
    border: 2px solid currentColor;
  }
  
  .destructive-action:hover::before {
    border: 2px solid var(--danger);
    background: transparent;
  }

  .summary-content-display {
    border: 2px solid #64748b;
  }
}
/* === SORTING AND FILTERING CONTROLS === */

.sorting-filtering-controls {
  background: var(--white);
  border-radius: 8px;
  border: 1px solid var(--card-border);
  margin: 16px 0 24px 0;
  box-shadow: var(--card-shadow);
  overflow: hidden;
}

/* === QUICK SORT SECTION === */

.quick-sort-section {
  padding: 20px 24px;
  background: var(--background);
  border-bottom: 1px solid var(--card-border);
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.sort-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
  white-space: nowrap;
}

.sort-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  flex: 1;
}

.sort-button {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
  background: var(--white) !important;
  color: var(--text-secondary) !important;
  border: 1px solid var(--card-border) !important;
  min-height: 36px !important;
}

.sort-button:hover:not(:disabled) {
  background: var(--green-light) !important;
  border-color: var(--green-main) !important;
  color: var(--green-dark) !important;
}

.sort-button.active {
  background: var(--green-main) !important;
  color: var(--white) !important;
  border-color: var(--green-main) !important;
}

.sort-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
}

/* Filter count badge */
.filter-count-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: var(--danger);
  color: var(--white);
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 10px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Advanced filters toggle button */
button[matTooltip="Filtres avancés"] {
  position: relative !important;
  background: var(--white) !important;
  border: 1px solid var(--card-border) !important;
  color: var(--text-secondary) !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
}

button[matTooltip="Filtres avancés"]:hover:not(:disabled) {
  background: var(--green-light) !important;
  border-color: var(--green-main) !important;
  color: var(--green-main) !important;
}

button[matTooltip="Filtres avancés"].active {
  background: var(--green-main) !important;
  color: var(--white) !important;
  border-color: var(--green-main) !important;
}

/* Reset button */
.reset-button {
  background: var(--white) !important;
  border: 1px solid var(--card-border) !important;
  color: var(--text-secondary) !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
}

.reset-button:hover:not(:disabled) {
  background: var(--beige-darl) !important;
  border-color: var(--text-secondary) !important;
  color: var(--text-primary) !important;
}

/* === APPLIED FILTERS DISPLAY === */

.applied-filters {
  padding: 16px 24px;
  background: var(--green-light);
  border-bottom: 1px solid var(--green-main);
}

.filters-label {
  font-weight: 600;
  color: var(--green-dark);
  font-size: 13px;
  margin-bottom: 12px;
}

.filter-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.filter-chip {
  display: flex;
  align-items: center;
  gap: 6px;
  background: var(--white);
  color: var(--green-dark);
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid var(--green-main);
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-chip:hover:not(.disabled) {
  background: var(--background);
}

.remove-filter {
  font-size: 14px !important;
  width: 14px !important;
  height: 14px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  border-radius: 50% !important;
}

.remove-filter:hover:not(.disabled) {
  background: var(--danger) !important;
  color: var(--white) !important;
}

.remove-filter.disabled {
  opacity: 0.5;
  cursor: not-allowed !important;
}

.clear-filters-button {
  background: var(--white) !important;
  color: var(--danger) !important;
  border: 1px solid var(--danger) !important;
  padding: 6px 12px !important;
  border-radius: 6px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 4px !important;
}

.clear-filters-button:hover:not(:disabled) {
  background: var(--danger) !important;
  color: var(--white) !important;
}

/* === ADVANCED FILTERS PANEL === */

.advanced-filters-panel {
  background: var(--white);
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: var(--green-main);
  color: var(--white);
  border-bottom: 1px solid var(--card-border);
}

.filters-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.filters-header button {
  background: transparent !important;
  color: var(--white) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  width: 32px !important;
  height: 32px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
}

.filters-header button:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

/* === QUICK FILTERS SECTION === */

.quick-filters-section {
  padding: 20px 24px;
  background: var(--background);
  border-bottom: 1px solid var(--card-border);
}

.quick-filters-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
  margin-bottom: 12px;
}

.quick-filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.quick-filter-btn {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  background: var(--white) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--card-border) !important;
  transition: all 0.2s ease !important;
}

.quick-filter-btn:hover:not(:disabled) {
  background: var(--green-light) !important;
  border-color: var(--green-main) !important;
  color: var(--green-dark) !important;
}

.quick-filter-btn mat-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
}

/* === FILTERS GRID === */

.filters-grid {
  padding: 24px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  background: var(--background);
}

.filter-group {
  background: var(--white);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid var(--card-border);
  box-shadow: var(--card-shadow);
  transition: all 0.2s ease;
  position: relative;
}

.filter-group:hover {
  border-color: var(--green-main);
}

.filter-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
  margin-bottom: 12px;
  display: block;
}

/* === FILTER INPUT GROUPS === */

.filter-input-group {
  display: flex;
  gap: 8px;
  align-items: stretch;
  margin-bottom: 8px;
}

.operator-select,
.filter-select {
  padding: 8px 12px;
  border: 1px solid var(--card-border);
  border-radius: 6px;
  font-size: 13px;
  background: var(--white);
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.operator-select {
  min-width: 120px;
  flex-shrink: 0;
}

.filter-select.full-width {
  width: 100%;
}

.operator-select:focus,
.filter-select:focus {
  border-color: var(--green-main);
  outline: none;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.filter-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--card-border);
  border-radius: 6px;
  font-size: 13px;
  background: var(--white);
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.filter-input:focus {
  border-color: var(--green-main);
  outline: none;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.filter-input:disabled,
.operator-select:disabled,
.filter-select:disabled {
  background: var(--background);
  color: var(--text-secondary);
  cursor: not-allowed;
  opacity: 0.7;
}

/* === CLEAR FILTER BUTTON === */

.clear-filter-button {
  background: var(--white) !important;
  color: var(--danger) !important;
  border: 1px solid var(--card-border) !important;
  width: 32px !important;
  height: 32px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  align-self: flex-start !important;
  margin-top: 4px !important;
}

.clear-filter-button:hover:not(:disabled) {
  background: var(--danger) !important;
  color: var(--white) !important;
  border-color: var(--danger) !important;
}

/* === FILTERS ACTIONS === */

.filters-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: var(--background);
  border-top: 1px solid var(--card-border);
  gap: 16px;
}

.clear-all-button {
  background: var(--white) !important;
  color: var(--text-secondary) !important;
  border: 1px solid var(--card-border) !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
}

.clear-all-button:hover:not(:disabled) {
  background: var(--beige-darl) !important;
  color: var(--text-primary) !important;
}

.filters-actions button[color="primary"] {
  background: var(--green-main) !important;
  color: var(--white) !important;
  border: 1px solid var(--green-main) !important;
  padding: 8px 20px !important;
  border-radius: 6px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
}

.filters-actions button[color="primary"]:hover:not(:disabled) {
  background: var(--primary-dark) !important;
}

/* === RESPONSIVE DESIGN FOR FILTERS === */

@media (max-width: 768px) {
  .sorting-filtering-controls {
    margin: 12px 0 20px 0;
  }

  .quick-sort-section {
    padding: 16px 20px;
  }

  .sort-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .sort-options {
    flex-direction: column;
    gap: 6px;
  }

  .sort-button {
    justify-content: center !important;
  }

  .applied-filters {
    padding: 12px 20px;
  }

  .filter-chips {
    gap: 6px;
  }

  .quick-filters-section {
    padding: 16px 20px;
  }

  .quick-filter-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .quick-filter-btn {
    justify-content: center !important;
  }

  .filters-grid {
    padding: 20px;
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .filter-group {
    padding: 16px;
  }

  .filter-input-group {
    flex-direction: column;
    gap: 8px;
  }

  .operator-select {
    min-width: unset;
  }

  .filters-actions {
    padding: 16px 20px;
    flex-direction: column-reverse;
    gap: 12px;
  }

  .filters-actions button {
    width: 100% !important;
    justify-content: center !important;
  }
}

@media (max-width: 480px) {
  .sort-controls {
    text-align: center;
  }

  .sort-label {
    align-self: center;
    margin-bottom: 8px;
  }

  .filter-chips {
    justify-content: center;
  }

  .quick-filter-buttons {
    align-items: stretch;
  }

  .filters-header {
    padding: 16px 20px;
  }

  .filters-header h4 {
    font-size: 14px;
  }
}

/* === ACCESSIBILITY === */

@media (prefers-reduced-motion: reduce) {
  .filter-chip,
  .sort-button,
  .quick-filter-btn,
  .filter-group {
    transition: none !important;
  }
}

.sort-button:focus-visible,
.quick-filter-btn:focus-visible,
.filter-chip:focus-visible {
  outline: 2px solid var(--green-main);
  outline-offset: 2px;
}

.filter-input:focus-visible,
.operator-select:focus-visible,
.filter-select:focus-visible {
  outline: 2px solid var(--green-main);
  outline-offset: 1px;
}

@media (prefers-reduced-motion: reduce) {
  .rule-item,
  .loading-icon,
  button,
  .destructive-action::before,
  .summary-preview {
    animation: none !important;
    transition: none !important;
  }
  
  .rule-item:hover {
    transform: none !important;
  }
  
  button:hover {
    transform: none !important;
  }
}

/* === UTILITY CLASSES === */

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}