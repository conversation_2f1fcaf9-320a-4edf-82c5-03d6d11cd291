.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

.confirmation-popup {
  background: linear-gradient(145deg, #ffffff, #f8fafc);
  border-radius: 16px;
  padding: 20px;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  position: relative;
  animation: slideIn 0.4s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.popup-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.close-popup-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  background: #f1f5f9;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s;
}

.close-popup-btn:hover {
  background: #e2e8f0;
  color: #475569;
  transform: rotate(90deg);
}


.confirmation-message {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  border-radius: 12px;
}

.confirmation-icon {
  flex-shrink: 0;
  font-size: 32px !important;
  height: 32px !important;
  width: 32px !important;
  color: #49b38c;
  background: #4a556712;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirmation-message p {
  font-size: 1.1rem;
  color: #475569;
  line-height: 1.6;
  margin: 0;
  flex: 1;
}

.popup-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.cancel-btn, 
.confirm-btn {
  padding: 0.75rem 1.75rem;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 1rem;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 120px;
  justify-content: center;
}

.confirm-btn{
  background-color: #49b38d;
}

.cancel-btn {
  background: #f1f5f9;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.cancel-btn:hover {
  background: #e2e8f0;
  color: #475569;
  transform: translateY(-2px);
}

.confirm-btn {
  background-color: #49b38d;
  color: white;
  box-shadow: 0 4px 6px -1px rgba(76, 175, 80, 0.2);
}

.confirm-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 8px -1px rgba(76, 175, 80, 0.3);
  background-color: #49b38cd3;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 640px) {
  .confirmation-popup {
    width: 95%;
    padding: 1.5rem;
  }

  .confirmation-message {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1rem;
    padding: 1rem;
  }

  .confirmation-icon {
    margin: 0.5rem 0;
  }

  .popup-actions {
    flex-direction: column-reverse;
  }

  .cancel-btn,
  .confirm-btn {
    width: 100%;
  }
}