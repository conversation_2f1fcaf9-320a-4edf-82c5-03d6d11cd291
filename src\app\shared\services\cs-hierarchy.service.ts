// hierarchy.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, map } from 'rxjs';
import { ControllerServerHierarchyDto, GroupedControllerServer } from '../models/ControllerServerHierarchyDto';

@Injectable({
  providedIn: 'root'
})
export class HierarchyService {
  constructor(private http: HttpClient) {}
  baseUrl = "https://localhost:7199/api/controller-serveur"

  getHierarchyByClient(clientId: string): Observable<GroupedControllerServer[]> {
    return this.http.get<ControllerServerHierarchyDto[]>(`${this.baseUrl}/by-client/${clientId}`)
      .pipe(
        map(data => this.groupHierarchyData(data))
      );
  }

  private groupHierarchyData(flatData: ControllerServerHierarchyDto[]): GroupedControllerServer[] {
    console.log('Grouping hierarchy data:', flatData);
    
    // Group by ControllerServerId
    const groupedMap = new Map<string, GroupedControllerServer>();
    
    flatData.forEach(item => {
      const serverId = item.ControllerServerId;
      
      // If controller server doesn't exist in map, create it
      if (!groupedMap.has(serverId)) {
        groupedMap.set(serverId, {
          Id: item.ControllerServerId,
          Name: item.ControllerServerName,
          MaxControllers: item.MaxControllers,
          MaxSensors: item.MaxSensors,
          GeographicZone: item.GeographicZone,
          CommercialCondition: item.CommercialCondition,
          TriggerType: item.TriggerType,
          ActionType: item.ActionType,
          EventType: item.EventType,
          Status: item.ControllerServerStatus,
          IdLicence: item.IdLicence,
          CreatedAt: item.ControllerServerCreatedAt,
          LastUpdatedAt: item.ControllerServerLastUpdatedAt,
          Licence: {
            Id: item.LicenceId,
            Name: item.LicenceName,
            Description: item.LicenceDescription
          },
          ControllerServerControllers: []
        });
      }
      
      // Add controller if it exists
      const controllerServer = groupedMap.get(serverId)!;
      
      if (item.ControllerId && item.RelationId) {
        // Check if this controller is already added (avoid duplicates)
        const existingController = controllerServer.ControllerServerControllers
          .find(csc => csc.IdController === item.ControllerId);
          
        if (!existingController) {
          controllerServer.ControllerServerControllers.push({
            Id: item.RelationId,
            IdController: item.IdController!,
            Controller: {
              Id: item.ControllerId,
              HostName: item.ControllerHostName || '',
              Model: item.ControllerModel || '',
              SerialNumber: item.ControllerSerialNumber || '',
              MacAddress: item.ControllerMacAddress || '',
              IpAddress: item.ControllerIpAddress || '',
              BaseTopic: item.ControllerBaseTopic || '',
              State: item.ControllerState || false,
              LastConnection: item.ControllerLastConnection
            }
          });
        }
      }
    });
    
    const result = Array.from(groupedMap.values());
    console.log('Grouped controller servers:', result);
    
    return result;
  }
}