import { Local } from "./local";
import { Capteur } from "./capteur";
import { Controller } from "./controller";
import { Log } from "./log";
import { RuleTransaction } from "./ruleTransaction";
import { AuditModel } from "./models-audit/audit-model";

export class Transaction extends AuditModel {
  InControl!: boolean;
  IdCapteur!: string;
  Capteur!: Capteur;
  IdController!: string;
  Controller!: Controller;
  IdLocal!: string;
  Local!: Local;
  Logs!: Log[];
  RuleTransactions!: RuleTransaction[];
}
