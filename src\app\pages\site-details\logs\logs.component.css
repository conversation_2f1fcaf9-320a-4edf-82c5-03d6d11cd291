/**
 * Redesigned Logs CSS for interactive, modern, and beautiful UI
 * Inspired by site-management styles for consistency
 */

/* Filter Form */
.log-filter-form {
  background: linear-gradient(135deg, #f8fafc 60%, #e8f5e9 100%);
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(76, 175, 80, 0.07);
  padding: 1.5rem 1rem 1rem 1rem;
  margin-bottom: 2rem;
  animation: fadeIn 0.5s;
}

/* Updated Form Grid */
.form-grid {
  display: grid;
  grid-template-columns: minmax(180px, 1fr) minmax(300px, 2fr) minmax(150px, 1fr) auto;
  gap: 24px;
  padding: 16px;
  align-items: start;
}

/* Enhanced Form Groups */
.form-group {
  position: relative;
  margin-bottom: 8px;
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
  transition: color 0.2s;
}

/* Custom Select Styling */
.custom-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236B7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

/* Search Input Styling */
.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  right: 12px;
  color: #9ca3af;
  transition: color 0.2s ease;
  cursor: pointer;
}

.search-input-wrapper input {
  width: 100%;
  padding-right: 40px;
  padding-left: 16px;
}

.search-input-wrapper:hover .search-icon {
  color: #4caf50;
}

.search-input-wrapper input:focus + .search-icon {
  color: #4caf50;
}

/* Form Group Specific Styles */
.log-type-group select {
  font-weight: 500;
  color: #1f2937;
  
}

.search-group {
  flex: 1;
  min-width: 250px;
}

/* Input and Select Common Styles */
.form-group input,
.form-group select {
  width: 100%;
  height: 42px;
  padding: 8px 16px;
  font-size: 0.95rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background-color: #ffffff;
  color: #1f2937;
  transition: all 0.2s ease-in-out;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 10px;
}

.create-button {
  background: linear-gradient(45deg, #4caf50, #81c784);
  color: #fff;
  padding: 8px 20px;
  border-radius: 6px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.13);
  transition: background 0.2s, box-shadow 0.2s, transform 0.2s;
}
.create-button:hover {
  background: linear-gradient(45deg, #81c784, #4caf50);
  box-shadow: 0 4px 16px rgba(76, 175, 80, 0.18);
  transform: translateY(-2px);
}

.required {
  color: #ef4444;
  margin-left: 2px;
}

/* Log Results */





/* Log type colors */


.json-container {
  background: #23272e;
  color: #f8f8f2;
  padding: 0.7rem 1rem;
  border-radius: 6px;
  margin-top: 0.5rem;
  overflow-x: auto;
  font-size: 0.97rem;
  box-shadow: 0 1px 6px rgba(44, 62, 80, 0.09);
}

.json-line {
  display: block;
  font-family: 'Fira Mono', 'Consolas', monospace;
  line-height: 1.5;
}




.log-empty {
  text-align: center;
  padding: 2.5rem 1rem;
  color: #b0b0b0;
  font-size: 1.1rem;
  background: #f8fafc;
  border-radius: 8px;
  margin-top: 1.5rem;
  box-shadow: 0 1px 6px rgba(44, 62, 80, 0.04);
}

/* Loading Styles */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 1rem;
  background: #f9fafb;
  border-radius: 8px;
  margin: 1rem 0;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid #e5e7eb;
  border-radius: 50%;
  border-top-color: #4caf50;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Reset Button Styling */
.reset-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f3f4f6;
  color: #4b5563;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reset-button:hover {
  background: #e5e7eb;
  color: #374151;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: none; }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .form-grid {
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }
}

@media (max-width: 700px) {
  .log-item {
    padding: 0.8rem 0.5rem 0.7rem 0.7rem;
  }
  .form-grid {
    grid-template-columns: 1fr;
  }

}

@media (max-width: 640px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .search-group {
    grid-column: 1 / -1;
  }
}


/* Updated Log Results Section */
.log-results {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  animation: fadeIn 0.7s;
}

.log-item {
  background: rgb(242, 157, 157);
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.2rem;
  transition: all 0.2s ease;
  border-top: 4px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  min-height: 180px;
}

.log-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8rem;
  
}

.log-type {
  background: #4caf50;
  font-weight: 600;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.log-message {
  font-size: 0.9rem;
  color: #374151;
  line-height: 1.5;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-grow: 1;
}

.log-details {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #6B7280;
  margin-top: auto;
}

.log-detail {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  background: #F3F4F6;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
}

/* Log type colors - updated for cards */
.log-info {
  border-top-color: #3B82F6;
}
.log-info .log-type {
  background: #4caf50;
  color: #1D4ED8;
}

.log-warning {
  border-top-color: #F59E0B;
}
.log-warning .log-type {
  background: #FFFBEB;
  color: #B45309;
}

.log-error {
  border-top-color: #EF4444;
}
.log-error .log-type {
  background: #FEE2E2;
  color: #B91C1C;
}

.log-other {
  border-top-color: #9CA3AF;
}
.log-other .log-type {
  background: #F3F4F6;
  color: #4B5563;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .log-results {
    grid-template-columns: 1fr;
  }
}


.log-card {
  border-radius: 10px;
  padding: 16px;
  margin: 12px 0;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  background-color: #fff;
  border-left: 5px solid;
  transition: transform 0.2s ease;
}

.log-card:hover {
  transform: scale(1.01);
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.log-level {
  font-weight: bold;
  font-size: 14px;
  padding: 2px 8px;
  border-radius: 4px;
  color: #fff;
}

.log-time {
  font-size: 12px;
  color: #666;
}

.log-message {
  font-size: 15px;
  margin: 8px 0;
}

.log-meta {
  margin-top: 8px;
  font-size: 13px;
  color: #333;
}

.meta-key {
  font-weight: 600;
  margin-right: 4px;
  color: #555;
}

.meta-value {
  color: #222;
}

/* Color themes by level */
.log-card.info .log-level {
  background-color: #4caf50;
}
.log-card.warn .log-level {
  background-color: #ff9800;
}
.log-card.error .log-level {
  background-color: #f44336;
}
.log-card.debug .log-level {
  background-color: #2196f3;
}


/* Color themes by level */
.log-card.info {
  border-left-color: #4caf50;
}
.log-card.info .log-level {
  background-color: #4caf50;
}

.log-card.warning {
  border-left-color: #ff9800;
}
.log-card.warning .log-level {
  background-color: #ff9800;
}

.log-card.error {
  border-left-color: #f44336;
}
.log-card.error .log-level {
  background-color: #f44336;
}

.log-card.debug {
  border-left-color: #2196f3;
}
.log-card.debug .log-level {
  background-color: #2196f3;
}