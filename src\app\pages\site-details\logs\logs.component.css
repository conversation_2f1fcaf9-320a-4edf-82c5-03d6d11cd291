/* Container */
.logs-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* Page Header */
.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #16a34a, #22c55e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  color: var(--gray-600, #4b5563);
  font-size: 1.1rem;
  font-weight: 400;
}

/* Filter Card */
.filter-card {
  background: #fff;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  border: 1px solid var(--gray-200, #e5e7eb);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
}

/* Filter Grid */
.filter-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

/* Form Group */
.form-group {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 5rem; /* Fixed height for input alignment */
}

/* Form Label */
.form-label {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-700, #374151);
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.form-label mat-icon {
  font-size: 1rem;
  line-height: 1;
  display: inline-flex;
  align-self: baseline;
  margin: 0;
  position: static;
}

/* Required Indicator */
.required {
  color: #ef4444;
}

/* Form Control */
.form-control {
  flex-grow: 1;
  height: 100%;
  padding: 0 1rem;
  border: 2px solid var(--gray-200, #e5e7eb);
  border-radius: 8px;
  font-size: 0.95rem;
  background: #fff;
  color: var(--gray-900, #111827);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  box-sizing: border-box;
}

.form-control:focus {
  outline: none;
  border-color: #16a34a;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.15);
}

.form-control:hover {
  border-color: var(--gray-300, #d1d5db);
}

/* Search Wrapper */
.search-wrapper {
  position: relative;
  width: 100%;
  height: 5rem;
}

.search-wrapper input {
  padding-right: 3rem;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border: 2px solid var(--gray-200, #e5e7eb);
  border-radius: 8px;
  font-size: 0.95rem;
  color: var(--gray-900, #111827);
  background: #fff;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.search-wrapper input:focus {
  outline: none;
  border-color: #16a34a;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.15);
}

.search-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400, #9ca3af);
  font-size: 1.25rem;
  pointer-events: none;
}

/* Filter Actions */
.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--gray-200, #e5e7eb);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: transform 0.15s ease, box-shadow 0.15s ease;
  user-select: none;
}

.btn mat-icon {
  font-size: 1rem;
  vertical-align: middle;
  display: inline-flex;
  align-items: center;
  line-height: 1;
  margin: 0;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #16a34a, #22c55e);
  color: #fff;
  box-shadow: 0 2px 6px rgba(22, 163, 74, 0.4);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 14px rgba(22, 163, 74, 0.6);
}

.btn-secondary {
  background: var(--gray-100, #f3f4f6);
  color: var(--gray-700, #374151);
  border: 1px solid var(--gray-200, #e5e7eb);
  box-shadow: inset 0 0 0 0 transparent;
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
}

.btn-secondary:hover:not(:disabled) {
  background: var(--gray-200, #e5e7eb);
  box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.05);
}

/* Loading State */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background: #fff;
  border-radius: 12px;
  gap: 1rem;
}

.spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #e5e7eb;
  border-top-color: #16a34a;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Logs Grid */
.log-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 1.25rem;
  animation: fadeIn 0.5s ease-out;
}

/* Log Card - Completely Redesigned */
.log-card {
  position: relative;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #f1f5f9;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
  min-height: 160px;
  display: flex;
  flex-direction: column;
  user-select: none;
}

.log-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
  border-color: #e2e8f0;
}

.log-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 4px;
  background-color: #e5e7eb;
  transition: background-color 0.2s ease;
}

.log-card.info::before {
  background: linear-gradient(180deg, #22c55e, #16a34a);
}
.log-card.warning::before {
  background: linear-gradient(180deg, #f59e0b, #d97706);
}
.log-card.error::before {
  background: linear-gradient(180deg, #ef4444, #dc2626);
}
.log-card.debug::before {
  background: linear-gradient(180deg, #10b981, #059669);
}

/* Card Content Structure */
.log-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.25rem 1.25rem 0.75rem 1.5rem;
  gap: 0.75rem;
}

.log-level {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  line-height: 1.2;
  min-width: fit-content;
  white-space: nowrap;
}

.log-level.info {
  background-color: #ecfdf5;
  color: #059669;
  border: 1px solid #bbf7d0;
}
.log-level.warning {
  background-color: #fffbeb;
  color: #d97706;
  border: 1px solid #fed7aa;
}
.log-level.error {
  background-color: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}
.log-level.debug {
  background-color: #f0fdfa;
  color: #0d9488;
  border: 1px solid #99f6e4;
}

.log-timestamp {
  font-size: 0.75rem;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
}

.log-timestamp mat-icon {
  font-size: 0.875rem !important;
  width: 0.875rem;
  height: 0.875rem;
}

/* Message Content */
.log-message {
  padding: 0 1.5rem;
  margin-bottom: 1rem;
  flex-grow: 1;
  display: flex;
  align-items: flex-start;
}

.log-message {
  font-size: 0.875rem;
  color: #334155;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 400;
}

.log-message.empty {
  color: #94a3b8;
  font-style: italic;
}

/* Payload Preview */
.log-payload-preview {
  padding: 0 1.5rem;
  margin-bottom: 0.75rem;
}

.meta-line {
  font-size: 0.75rem;
  color: #64748b;
  margin-bottom: 0.25rem;
  line-height: 1.4;
}

.meta-line strong {
  color: #475569;
  font-weight: 600;
}

/* Footer Area */
.log-topic {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #64748b;
  background: #f8fafc;
  padding: 0.5rem 1.5rem;
  font-weight: 500;
  border-top: 1px solid #f1f5f9;
  margin-top: auto;
}

.log-topic mat-icon {
  font-size: 0.875rem !important;
  width: 0.875rem;
  height: 0.875rem;
}

/* Meta Indicator */
.log-meta-indicator {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 1.5rem;
  height: 1.5rem;
  background: #f1f5f9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  font-size: 0.75rem;
}

.log-meta-indicator mat-icon {
  font-size: 0.875rem !important;
  width: 0.875rem;
  height: 0.875rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  user-select: none;
}

.empty-icon mat-icon {
  font-size: 3rem;
  color: #9ca3af;
  margin-bottom: 1rem;
}

.empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.empty-description {
  color: #4b5563;
  font-weight: 400;
}

/* Modal */
/* Popup backdrop for better readability */
.log-modal-backdrop {
  position: fixed;
  z-index: 1000;
  inset: 0;
  background: rgba(30, 41, 59, 0.55);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

/* Popup modal styling */
.log-modal {
  background: #fff;
  border-radius: 18px;
  max-width: 650px;
  width: 100%;
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.18);
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.log-modal-header {
  padding: 1.5rem 2rem 1rem 2rem;
  background: linear-gradient(90deg, #16a34a 0%, #22c55e 100%);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.log-modal-header h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.close-btn {
  background: transparent;
  border: none;
  color: #fff;
  font-size: 1.5rem;
  cursor: pointer;
  transition: color 0.2s;
}
.close-btn:hover {
  color: #ef4444;
}

.log-modal-body {
  padding: 2rem;
  max-height: 70vh;
  overflow-y: auto;
  color: #222;
  font-size: 1.05rem;
  line-height: 1.6;
}

/* Résumé section */
.summary-container {
  margin-bottom: 1.5rem;
  min-height: 120px;
  background: #f3f4f6;
  border-radius: 10px;
  padding: 1rem 1.2rem;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.summary-loading {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  color: #16a34a;
  font-weight: 500;
  font-size: 1.1rem;
}

.summary-loading .spin {
  animation: spin 1s linear infinite;
}

.summary-text {
  font-size: 1.08rem;
  font-family: "Fira Mono", "Consolas", monospace;
  white-space: pre-wrap;
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  min-height: 100px;
  max-height: 300px;
  overflow-y: auto;
  color: #222;
}

/* Responsive for modal */
@media (max-width: 700px) {
  .log-modal {
    max-width: 98vw;
    padding: 0;
  }
  .log-modal-body {
    padding: 1rem;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(2rem);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .logs-container {
    padding: 1rem;
  }

  .page-title {
    font-size: 2rem;
  }

  .filter-card {
    padding: 1.5rem;
  }

  .filter-grid {
    grid-template-columns: 1fr;
  }

  .filter-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .log-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .log-modal {
    width: 95%;
    max-width: none;
    padding: 1rem 1.25rem;
  }
}
