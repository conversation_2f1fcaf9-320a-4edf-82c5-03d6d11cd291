/* Color Variables - Inspired by card-site component */
:root {
  /* Primary Green Palette - Exact colors from card-site */
  --primary-green: #4caf50;
  --primary-green-light: #2ecc71;
  --primary-green-dark: #43a047;
  --primary-green-darker: #2e7d32;
  --success-green: #2ecc71;
  
  /* Secondary Colors - Exact colors from card-site */
  --blue-primary: #2196f3;
  --blue-dark: #1976d2;
  --edit-blue: #2196f3;
  --red-primary: #e74c3c;
  --red-dark: #e53935;
  --delete-red: #e74c3c;
  --orange-primary: #f44336;
  --orange-dark: #d32f2f;
  --warning-orange: #f39c12;
  
  /* Green Variations */
  --green-bg-light: #e8f5e9;
  --green-bg-lighter: #f0f8f0;
  --green-text: #155724;
  --green-border: #2e7d32;
  
  /* Neutral colors */
  --white: #ffffff;
  --gray-50: #fafafa;
  --gray-100: #f5f5f5;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9e9e9e;
  --gray-500: #757575;
  --gray-600: #555555;
  --gray-700: #2d3748;
  --gray-800: #2c3e50;
  --gray-900: #111827;
  
  /* Status Colors - Exact from card-site */
  --status-actif: #d4edda;
  --status-inactif: #f8d7da;
  --status-maintenance: #fff3cd;
  --text-actif: #155724;
  --text-inactif: #721c24;
  --text-maintenance: #856404;
  
  /* Log Level Colors */
  --status-info-bg: #e8f5e9;
  --status-info-text: #2e7d32;
  --status-info-border: #4caf50;
  --status-warning-bg: #fff3cd;
  --status-warning-text: #856404;
  --status-warning-border: #ffc107;
  --status-error-bg: #f8d7da;
  --status-error-text: #721c24;
  --status-error-border: #dc3545;
  --status-debug-bg: #e8f5e9;
  --status-debug-text: #155724;
  --status-debug-border: #28a745;
}

/* Container */
.logs-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  background: var(--gray-50);
  min-height: 100vh;
}

/* Page Header */
.page-header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  background: var(--white);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--gray-200);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, var(--primary-green), var(--primary-green-light));
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
}

.header-icon:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
}

.header-icon mat-icon {
  font-size: 2rem;
  color: var(--white);
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, var(--primary-green), var(--primary-green-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  color: var(--gray-600);
  font-size: 1.1rem;
  margin: 0;
  font-weight: 500;
}

/* Filter Card */
.filter-card {
  background: var(--white);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  border: 1px solid var(--gray-200);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.filter-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--gray-200);
}

.filter-header mat-icon {
  color: var(--primary-green);
  font-size: 1.5rem;
}

.filter-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-800);
}

/* Filter Grid */
.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* Form Group */
.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 0.75rem;
}

.form-label mat-icon {
  font-size: 1rem;
  color: var(--primary-green);
}

/* Form Controls */
.form-control {
  padding: 0.875rem 1rem;
  border: 2px solid var(--gray-200);
  border-radius: 8px;
  font-size: 0.95rem;
  background: var(--white);
  color: var(--gray-900);
  transition: all 0.2s ease;
  min-height: 48px;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-green);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.15);
}

.form-control:hover {
  border-color: var(--gray-300);
}

/* Select Wrapper */
.select-wrapper {
  position: relative;
}

.select-wrapper select {
  appearance: none;
  padding-right: 3rem;
}

.select-arrow {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
  pointer-events: none;
}

/* Search Wrapper */
.search-wrapper {
  position: relative;
}

.search-wrapper input {
  padding-right: 3rem;
}

.search-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
  pointer-events: none;
}

/* Filter Actions */
.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--gray-200);
}

/* Buttons - Styled like card-site buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  min-height: 48px;
}

.btn mat-icon {
  font-size: 1rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary-green);
  color: var(--white);
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-green-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.btn-secondary {
  background: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--gray-200);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--gray-200);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-accent {
  background: var(--edit-blue);
  color: var(--white);
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.btn-accent:hover:not(:disabled) {
  background: var(--blue-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
}

.btn-danger {
  background: var(--delete-red);
  color: var(--white);
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

.btn-danger:hover:not(:disabled) {
  background: var(--red-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
}

/* Button Spinner */
.button-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Loading State */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  background: var(--white);
  border-radius: 12px;
  border: 1px solid var(--gray-200);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.loading-content {
  text-align: center;
}

.main-spinner {
  width: 3rem;
  height: 3rem;
  border: 4px solid var(--gray-200);
  border-top-color: var(--primary-green);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.loading-content h3 {
  margin: 0 0 0.5rem 0;
  color: var(--gray-800);
  font-size: 1.25rem;
  font-weight: 600;
}

.loading-content p {
  margin: 0;
  color: var(--gray-600);
}

/* Logs Section */
.logs-section {
  background: var(--white);
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid var(--gray-200);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--gray-200);
}

.section-header mat-icon {
  color: var(--primary-green);
  font-size: 1.5rem;
}

.section-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-800);
}

/* Log Grid */
.log-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

/* Log Card - Styled like site-card */
.log-card {
  background: var(--white);
  border-radius: 12px;
  border: 1px solid var(--gray-200);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  height: 100%;
  display: flex;
  flex-direction: column;
  will-change: transform;
  backface-visibility: hidden;
  animation: fadeInUp 0.3s ease-out;
}

.log-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: var(--primary-green);
}

.log-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 4px;
  background: var(--gray-300);
  transition: background 0.2s ease;
}

.log-card.info::before { 
  background: linear-gradient(45deg, var(--primary-green), var(--primary-green-light), var(--primary-green));
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

.log-card.warning::before { 
  background: linear-gradient(180deg, #ffc107, #ff9800); 
}

.log-card.error::before { 
  background: linear-gradient(180deg, var(--red-primary), var(--red-dark)); 
}

.log-card.debug::before { 
  background: linear-gradient(180deg, var(--primary-green-light), var(--primary-green-dark)); 
}

/* Log Header */
.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.25rem 0.75rem 1.5rem;
  gap: 1rem;
}

.log-level-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.level-indicator {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background: var(--gray-300);
  transition: all 0.2s ease;
}

.level-indicator.info { 
  background: var(--primary-green);
  animation: pulse 2s infinite;
}

.level-indicator.warning { background: #ffc107; }
.level-indicator.error { background: var(--red-primary); }
.level-indicator.debug { background: var(--primary-green-light); }

.log-card:hover .level-indicator {
  box-shadow: 0 0 10px currentColor;
}

.log-level {
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.log-level.info {
  background: var(--status-info-bg);
  color: var(--status-info-text);
  border: 1px solid var(--status-info-border);
}

.log-level.warning {
  background: var(--status-warning-bg);
  color: var(--status-warning-text);
  border: 1px solid var(--status-warning-border);
}

.log-level.error {
  background: var(--status-error-bg);
  color: var(--status-error-text);
  border: 1px solid var(--status-error-border);
}

.log-level.debug {
  background: var(--status-debug-bg);
  color: var(--status-debug-text);
  border: 1px solid var(--status-debug-border);
}

.log-timestamp {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: var(--gray-500);
  font-weight: 500;
}

.log-timestamp mat-icon {
  font-size: 0.875rem !important;
  width: 0.875rem;
  height: 0.875rem;
}

/* Log Content */
.log-content {
  padding: 0 1.5rem 1rem;
  flex: 1;
}

.log-message {
  font-size: 0.875rem;
  color: var(--gray-700);
  line-height: 1.5;
  margin-bottom: 1rem;
  min-height: 2.5rem;
  font-weight: 500;
}

.log-message.empty {
  color: var(--gray-400);
  font-style: italic;
}

/* Payload Preview */
.payload-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.meta-chip {
  display: inline-flex;
  align-items: center;
  background: var(--green-bg-light);
  border: 1px solid var(--primary-green);
  border-radius: 6px;
  padding: 0.25rem 0.5rem;
  font-size: 0.7rem;
  max-width: 150px;
  transition: transform 0.2s ease;
}

.log-card:hover .meta-chip {
  transform: scale(1.05);
}

.meta-chip.battery {
  background: #e8f5e9;
  border-color: #4caf50;
}

.meta-chip.temperature {
  background: #fff3e0;
  border-color: #ff9800;
}

.meta-chip.motion {
  background: #e3f2fd;
  border-color: #2196f3;
}

.chip-key {
  color: var(--primary-green-darker);
  font-weight: 600;
  margin-right: 0.25rem;
}

.chip-value {
  color: var(--green-text);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

/* Log Footer */
.log-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background: var(--gray-50);
  border-top: 1px solid var(--gray-200);
  margin-top: auto;
}

.log-topic {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: var(--gray-500);
  font-weight: 500;
}

.log-topic mat-icon {
  font-size: 0.875rem !important;
  width: 0.875rem;
  height: 0.875rem;
}

.log-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.meta-indicator,
.view-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background: var(--green-bg-light);
  color: var(--primary-green);
  transition: all 0.2s ease;
}

.view-indicator {
  background: var(--gray-100);
  color: var(--gray-500);
}

.meta-indicator:hover,
.view-indicator:hover {
  transform: scale(1.1);
}

.meta-indicator mat-icon,
.view-indicator mat-icon {
  font-size: 0.875rem !important;
  width: 0.875rem;
  height: 0.875rem;
}

/* Empty State */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  background: var(--white);
  border-radius: 12px;
  border: 1px solid var(--gray-200);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.empty-content {
  text-align: center;
  max-width: 400px;
}

.empty-icon {
  margin-bottom: 1.5rem;
}

.empty-icon mat-icon {
  font-size: 4rem;
  color: var(--gray-300);
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--gray-800);
  margin: 0 0 0.75rem 0;
}

.empty-description {
  color: var(--gray-600);
  margin: 0 0 2rem 0;
  line-height: 1.6;
}

/* Modal Styles */
.log-modal-backdrop {
  position: fixed;
  z-index: 1000;
  inset: 0;
  background: rgba(44, 62, 80, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  backdrop-filter: blur(4px);
}

.log-modal {
  background: var(--white);
  border-radius: 12px;
  max-width: 700px;
  width: 100%;
  max-height: 90vh;
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: modalSlideIn 0.3s ease-out;
}

.log-modal-header {
  padding: 2rem;
  background: linear-gradient(135deg, var(--primary-green), var(--primary-green-light));
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.modal-icon {
  font-size: 1.5rem !important;
}

.log-modal-header h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: var(--white);
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.log-modal-body {
  padding: 2rem;
  overflow-y: auto;
  flex: 1;
}

/* Detail Grid */
.detail-grid {
  display: grid;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--gray-50);
  border-radius: 8px;
  border: 1px solid var(--gray-200);
  transition: all 0.2s ease;
}

.detail-item:hover {
  background: var(--green-bg-lighter);
  border-color: var(--primary-green);
}

.detail-icon {
  color: var(--primary-green);
  font-size: 1.25rem !important;
  margin-top: 0.125rem;
}

.detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.detail-value {
  font-size: 1rem;
  color: var(--gray-800);
  font-weight: 500;
}

.level-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  width: fit-content;
}

.topic-value {
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
  background: var(--green-bg-light);
  padding: 0.5rem;
  border-radius: 6px;
  border: 1px solid var(--primary-green);
  font-size: 0.875rem;
  word-break: break-all;
}

/* Summary Section */
.summary-section {
  margin-bottom: 2rem;
}

.summary-container {
  background: var(--gray-50);
  border-radius: 8px;
  border: 1px solid var(--gray-200);
  padding: 1.5rem;
  min-height: 120px;
  position: relative;
  overflow: hidden;
}

.summary-loading {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: var(--primary-green);
  font-weight: 500;
  justify-content: center;
  height: 100px;
  background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.1), transparent);
  animation: shimmer 1.5s infinite;
}

.loading-spinner {
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid var(--green-bg-light);
  border-top-color: var(--primary-green);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.summary-content {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--gray-700);
  white-space: pre-wrap;
}

/* Metadata Section */
.metadata-section {
  margin-bottom: 1rem;
}

.metadata-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.metadata-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 1rem;
  background: var(--green-bg-light);
  border-radius: 8px;
  border: 1px solid var(--primary-green);
  transition: all 0.2s ease;
}

.metadata-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(76, 175, 80, 0.2);
}

.metadata-key {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--primary-green-darker);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metadata-value {
  font-size: 0.875rem;
  color: var(--green-text);
  font-weight: 500;
  word-break: break-word;
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
}

/* Status Classes - Exact from card-site */
.status-actif {
  background-color: var(--status-actif);
  color: var(--text-actif);
}

.status-inactif {
  background-color: var(--status-inactif);
  color: var(--text-inactif);
}

.status-maintenance {
  background-color: var(--status-maintenance);
  color: var(--text-maintenance);
}

/* Animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1); 
  }
  50% { 
    opacity: 0.7; 
    transform: scale(1.1); 
  }
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .logs-container {
    padding: 1rem;
  }

  .page-title {
    font-size: 2rem;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .filter-card,
  .logs-section {
    padding: 1.5rem;
  }

  .filter-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .filter-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .filter-actions .btn {
    width: 100%;
    justify-content: center;
  }

  .log-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .log-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem 1rem 0.5rem 1.25rem;
  }

  .log-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem 1.25rem;
  }

  .log-modal {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 2rem);
    border-radius: 12px;
  }

  .log-modal-header {
    padding: 1.5rem;
  }

  .log-modal-body {
    padding: 1.5rem;
  }

  .detail-grid {
    grid-template-columns: 1fr;
  }

  .metadata-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 600px) {
  .log-grid {
    grid-template-columns: 1fr;
  }
  
  .log-footer {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }

  .payload-preview {
    flex-direction: column;
  }

  .meta-chip {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .logs-container {
    padding: 0.5rem;
  }

  .header-content,
  .filter-card,
  .logs-section {
    padding: 1rem;
    border-radius: 8px;
  }

  .page-title {
    font-size: 1.75rem;
  }

  .header-icon {
    width: 3rem;
    height: 3rem;
  }

  .header-icon mat-icon {
    font-size: 1.5rem;
  }

  .log-modal {
    margin: 0.5rem;
    max-width: calc(100vw - 1rem);
    max-height: calc(100vh - 1rem);
    border-radius: 8px;
  }

  .log-modal-header,
  .log-modal-body {
    padding: 1rem;
  }

  .log-card {
    border-radius: 8px;
  }

  .log-content {
    padding: 0 1rem 1rem;
  }

  .log-header {
    padding: 1rem 1rem 0.5rem;
  }

  .log-footer {
    padding: 0.75rem 1rem;
  }

  .filter-grid {
    gap: 0.75rem;
  }

  .form-control {
    min-height: 44px;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .btn {
    min-height: 44px;
    padding: 0.75rem 1rem;
  }
}

/* Focus and Accessibility */
.log-card:focus {
  outline: 3px solid var(--primary-green);
  outline-offset: 2px;
}

.btn:focus {
  outline: 3px solid var(--primary-green);
  outline-offset: 2px;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-green);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.15);
}

.close-btn:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --primary-green: #0f5132;
    --primary-green-light: #146c43;
    --gray-200: #dee2e6;
    --gray-300: #ced4da;
  }

  .log-card {
    border-width: 2px;
  }

  .log-level {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .log-card:hover {
    transform: none;
  }

  .btn:hover:not(:disabled) {
    transform: none;
  }

  .meta-chip,
  .level-indicator,
  .meta-indicator,
  .view-indicator {
    transition: none;
  }
}

/* Print Styles */
@media print {
  .logs-container {
    background: white;
    padding: 0;
  }

  .filter-card,
  .log-modal-backdrop {
    display: none;
  }

  .log-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid var(--gray-300);
    margin-bottom: 1rem;
  }

  .log-card:hover {
    transform: none;
    box-shadow: none;
  }

  .log-card::before {
    print-color-adjust: exact;
  }

  .page-title {
    color: var(--gray-800) !important;
    -webkit-text-fill-color: var(--gray-800) !important;
  }

  .btn,
  .filter-actions {
    display: none;
  }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  :root {
    --white: #1a1a1a;
    --gray-50: #0f0f0f;
    --gray-100: #1a1a1a;
    --gray-200: #2d2d2d;
    --gray-300: #404040;
    --gray-700: #e0e0e0;
    --gray-800: #ffffff;
    --gray-900: #ffffff;
  }
}

/* Performance optimizations */
.log-card,
.btn,
.form-control {
  contain: layout style paint;
}

.log-grid {
  contain: layout;
}

/* Custom scrollbar for modal */
.log-modal-body::-webkit-scrollbar {
  width: 8px;
}

.log-modal-body::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: 4px;
}

.log-modal-body::-webkit-scrollbar-thumb {
  background: var(--primary-green);
  border-radius: 4px;
}

.log-modal-body::-webkit-scrollbar-thumb:hover {
  background: var(--primary-green-dark);
}

/* Utility classes */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Loading skeleton for better UX */
.skeleton {
  background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-text {
  height: 1em;
  border-radius: 4px;
  margin-bottom: 0.5em;
}

.skeleton-text:last-child {
  margin-bottom: 0;
}

/* Tooltips */
[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--gray-800);
  color: var(--white);
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
}

/* Enhanced focus styles for better accessibility */
.log-card:focus-visible {
  outline: 3px solid var(--primary-green);
  outline-offset: 2px;
}

.btn:focus-visible {
  outline: 3px solid var(--primary-green);
  outline-offset: 2px;
}

.form-control:focus-visible {
  outline: none;
  border-color: var(--primary-green);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.15);
}

/* Enhanced meta chips with specific colors */
.meta-chip[data-type="battery"] {
  background: linear-gradient(135deg, #e8f5e9, #c8e6c9);
  border-color: var(--primary-green);
}

.meta-chip[data-type="temperature"] {
  background: linear-gradient(135deg, #fff3e0, #ffe0b2);
  border-color: #ff9800;
}

.meta-chip[data-type="motion"] {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  border-color: var(--blue-primary);
}

.meta-chip[data-type="voltage"] {
  background: linear-gradient(135deg, #f3e5f5, #e1bee7);
  border-color: #9c27b0;
}

/* Enhanced button states */
.btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.btn-primary:active {
  background: var(--primary-green-darker);
}

.btn-secondary:active {
  background: var(--gray-300);
}

.btn-accent:active {
  background: #1565c0;
}

.btn-danger:active {
  background: #c62828;
}