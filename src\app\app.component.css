/* src/app/app.component.css */
:host {
    --primary-color: #378c18;
    --accent-color: #4be44b;
    --background-color: #d5d9df;
    --text-color: #333333;
    --card-background: #ffffff;
    --sidebar-background: #f5f5f5;
    --header-background: #ffffff;
    --shadow-color: rgba(0, 0, 0, 0.1);
  }
  
  :host.dark-mode {
    --primary-color: #378c18;
    --accent-color: #4be44b;
    --background-color: #1a1a1a;
    --text-color: #d5d9df;
    --card-background: #2a2a2a;
    --sidebar-background: #252525;
    --header-background: #2a2a2a;
    --shadow-color: rgba(255, 255, 255, 0.1);
  }
  
  .app-container {
    display: flex;
    min-height: 100vh;
    background-color: var(--background-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
  }
  
  .main-content {
    flex: 1;
    transition: margin-left 0.3s ease;
    margin-left: 250px;
    width: calc(100% - 250px);
    overflow: auto;
  }
  
  .main-content.sidebar-collapsed {
    margin-left: 70px;
    width: calc(100% - 70px);
  }
  