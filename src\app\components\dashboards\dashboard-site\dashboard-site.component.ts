// dashboard-site.component.ts
import {
  Component,
  OnInit,
  AfterViewInit,
  ViewChild,
  ElementRef,
  OnDestroy,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { Chart, ChartConfiguration, registerables } from 'chart.js';
import L from 'leaflet';
import { Site } from '../../../core/models/site';

Chart.register(...registerables);

interface SiteMetric {
  title: string;
  amount: string;
  icon: string;
  iconColor: string;
  progress: number;
  color: string;
}

@Component({
  selector: 'app-dashboard-site',
  standalone: true,
  imports: [CommonModule, MatIconModule],
  templateUrl: './dashboard-site.component.html',
  styleUrls: ['./dashboard-site.component.css'],
})
export class DashboardSiteComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('chartCanvas', { static: false })
  chartCanvas!: ElementRef<HTMLCanvasElement>;

  private consommationChart?: Chart;
  private energyChart?: Chart;
  private expenseChart?: Chart;
  private map?: L.Map;

  currentSite!: Site;

  siteMetrics: SiteMetric[] = [
    {
      title: 'Surface Totale',
      amount: `${this.currentSite.Surface} m²`,
      icon: 'fa-solid fa-map',
      iconColor: '#28a745',
      progress: 100,
      color: '#28a745',
    },
    {
      title: "Nombre d'Employés",
      amount: this.currentSite.EmployeesCount?.toString() || 'N/A',
      icon: 'fa-solid fa-users',
      iconColor: '#00c9a7',
      progress: 100,
      color: '#00c9a7',
    },
    {
      title: 'Nombre de Locaux',
      amount: this.currentSite.LocalsCount?.toString() || 'N/A',
      icon: 'fa-solid fa-building',
      iconColor: '#ffc107',
      progress: 100,
      color: '#ffc107',
    },
    {
      title: 'Consommation Moyenne',
      amount: '1,250 kWh',
      icon: 'fa-solid fa-bolt',
      iconColor: '#17a2b8',
      progress: 65,
      color: '#17a2b8',
    },
    {
      title: 'Économies Énergétiques',
      amount: '33%',
      icon: 'fa-solid fa-leaf',
      iconColor: '#28a745',
      progress: 33,
      color: '#28a745',
    },
    {
      title: 'Coût Énergétique Mensuel',
      amount: '€ 2,859.95',
      icon: 'fa-solid fa-money-bill',
      iconColor: '#dc3545',
      progress: 85,
      color: '#dc3545',
    }
  ];

  ngOnInit() {}

  ngAfterViewInit() {
    this.createConsommationChart();
    this.createEnergyChart();
    this.createExpenseChart();
    this.initMap();
  }

  private initMap(): void {
    if (!this.currentSite.Latitude || !this.currentSite.Longtitude) return;

    const defaultIcon = L.icon({
      iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
      shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
      iconSize: [25, 41],
      iconAnchor: [12, 41],
      popupAnchor: [1, -34],
    });

    L.Marker.prototype.options.icon = defaultIcon;

    this.map = L.map('map').setView(
      [this.currentSite.Latitude, this.currentSite.Longtitude],
      15
    );

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>',
    }).addTo(this.map);

    L.marker([this.currentSite.Latitude, this.currentSite.Longtitude])
      .addTo(this.map)
      .bindPopup(`<b>${this.currentSite.Name}</b><br>${this.currentSite.Address}`);
  }

  private createConsommationChart() {
    const ctx = this.chartCanvas.nativeElement.getContext('2d');
    if (!ctx) return;

    const labels = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'];
    const data = [400, 500, 600, 700, 800, 900, 850, 800, 750, 700, 650, 600];

    new Chart(ctx, {
      type: 'line',
      data: {
        labels,
        datasets: [{
          label: 'Consommation (kWh)',
          data,
          borderColor: '#28a745',
          backgroundColor: 'rgba(40, 167, 69, 0.1)',
          fill: true,
          tension: 0.4,
          borderWidth: 3,
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: 'Consommation Énergétique du Site',
          },
          legend: { position: 'top' },
        },
        scales: {
          y: {
            beginAtZero: true,
            title: { display: true, text: 'kWh' },
          },
          x: {
            title: { display: true, text: 'Mois' },
          },
        },
      },
    });
  }

  private createEnergyChart() {
    const energyCanvas = document.getElementById('energyLineChart') as HTMLCanvasElement;
    if (!energyCanvas) return;
    const ctx = energyCanvas.getContext('2d');
    if (!ctx) return;

    const data = {
      labels: ['00h', '04h', '08h', '12h', '16h', '20h'],
      datasets: [{
        label: 'Consommation Journalière (kWh)',
        data: [150, 120, 300, 450, 350, 200],
        borderColor: '#007bff',
        backgroundColor: 'rgba(0, 123, 255, 0.1)',
        fill: true,
        tension: 0.3,
        borderWidth: 3,
      }],
    };

    this.energyChart = new Chart(ctx, {
      type: 'line',
      data,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: 'Profil de Consommation Journalière',
          },
          legend: { position: 'top' },
        },
        scales: {
          y: { beginAtZero: true, title: { display: true, text: 'kWh' } },
          x: { title: { display: true, text: 'Heure' } },
        },
      },
    });
  }

  private createExpenseChart() {
    const expenseCanvas = document.getElementById('expenseChart') as HTMLCanvasElement;
    if (!expenseCanvas) return;
    const ctx = expenseCanvas.getContext('2d');
    if (!ctx) return;

    const data = {
      labels: ['Éclairage', 'Climatisation', 'Machines', 'Autres'],
      datasets: [{
        data: [35, 30, 25, 10],
        backgroundColor: ['#ffc107', '#17a2b8', '#28a745', '#6c757d'],
        borderWidth: 3,
        borderColor: '#fff',
      }],
    };

    this.expenseChart = new Chart(ctx, {
      type: 'doughnut',
      data,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: 'Répartition de la Consommation',
          },
          legend: { position: 'bottom' },
        },
      },
    });
  }

  ngOnDestroy() {
    this.consommationChart?.destroy();
    this.energyChart?.destroy();
    this.expenseChart?.destroy();
    this.map?.remove();
  }
}
