import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class TagAssignmentApiService extends ApiService<any> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint('tag-assignment');
  }

  /**
   * Get tag assignments by target type and target ID
   * @param targetType - Type of the target entity
   * @param targetId - ID of the target entity
   * @returns Observable with tag assignments
   */
  getByTarget(targetType: string, targetId: string): Observable<any[]> {
    return this.http.get<any[]>(
      `${this.getFullUrl(`/target/${targetType}/${targetId}`)}`,
      this.httpOptions
    );
  }

  /**
   * Get tag assignments by tag ID
   * @param tagId - The ID of the tag
   * @returns Observable with tag assignments
   */
  getByTagId(tagId: string): Observable<any[]> {
    return this.http.get<any[]>(
      `${this.getFullUrl(`/tag/${tagId}`)}`,
      this.httpOptions
    );
  }

  /**
   * Create a new tag assignment
   * @param data - Tag assignment data to create
   * @returns Observable with created tag assignment
   */
  createAssignment(data: any): Observable<any> {
    return this.http.post(
      `${this.getFullUrl()}`,
      data,
      this.httpOptions
    );
  }

  /**
   * Update a tag assignment
   * @param data - Tag assignment data to update
   * @returns Observable with updated tag assignment
   */
  updateAssignment(data: any): Observable<any> {
    return this.http.put(
      `${this.getFullUrl()}`,
      data,
      this.httpOptions
    );
  }

  /**
   * Delete a tag assignment
   * @param id - ID of the tag assignment to delete
   * @returns Observable with deletion result
   */
  deleteAssignment(id: string): Observable<any> {
    return this.http.delete(
      `${this.getFullUrl(`/${id}`)}`,
      this.httpOptions
    );
  }

  /**
   * Search tag assignments with filters
   * @param filter - Filter criteria for searching tag assignments
   * @returns Observable with search results
   */
  searchTagAssignments(filter: any): Observable<any> {
    return this.http.post(
      `${this.getFullUrl('/search')}`,
      filter,
      this.httpOptions
    );
  }

  /**
   * Get count of tag assignments
   * @returns Observable with count
   */
  getCount(): Observable<number> {
    return this.http.get<number>(
      `${this.getFullUrl('/count')}`,
      this.httpOptions
    );
  }


  /**
   * Check if tag assignment exists by criteria
   * @returns Observable with existence result
   */
  existsBy(): Observable<boolean> {
    return this.http.get<boolean>(
      `${this.getFullUrl('/existBy')}`,
      this.httpOptions
    );
  }


  /**
   * Add multiple tag assignments in bulk
   * @param assignments - Array of tag assignments to add
   * @returns Observable with result
   */
  addRange(assignments: any[]): Observable<any> {
    return this.http.post(
      `${this.getFullUrl('/add-range')}`,
      assignments,
      this.httpOptions
    );
  }

}