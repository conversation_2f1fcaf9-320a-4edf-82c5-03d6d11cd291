/** Container Styles **/
.container {
  padding: 1.5rem;
  max-width: 100%;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
    "Helvetica Neue", <PERSON><PERSON>, sans-serif;
  background-color: #fafafa;
}

/** Header Styles **/
.header {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 400;
  color: #333;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.title-icon {
  color: #666;
  font-size: 1.5rem;
}

/** Search Field Styling **/
.mat-mdc-form-field {
  width: 100% !important;
  margin-bottom: 0 !important;
}

.mat-mdc-form-field .mat-mdc-text-field-wrapper {
  background-color: white !important;
  border-radius: 4px !important;
  border: 1px solid #ddd !important;
}

.mat-mdc-form-field .mat-mdc-floating-label {
  color: #666 !important;
  font-size: 0.875rem !important;
}

/** Material Table Styling **/
.mat-mdc-table {
  background-color: white !important;
  border-radius: 0 !important;
  overflow: visible !important;
  box-shadow: none !important;
  border: none !important;
  width: 100% !important;
  border-collapse: separate !important;
  border-spacing: 0 !important;
}

/** Green header bar **/
.table-header-bar {
  height: 4px;
  background-color: #4caf50;
  width: 100%;
  margin-bottom: 0;
}

/** Header Row Styling **/
.mat-mdc-header-row {
  background-color: #f8f9fa !important;
  border: none !important;
  height: 48px !important;
}

.mat-mdc-header-cell {
  padding: 0 16px !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  color: #333 !important;
  border-bottom: 1px solid #e0e0e0 !important;
  border-right: none !important;
  background-color: #f8f9fa !important;
  height: 48px !important;
  line-height: 48px !important;
  text-align: left !important;
}

.mat-mdc-header-cell:first-child {
  border-left: none !important;
}

/** Data Row Styling **/
.mat-mdc-row {
  background-color: white !important;
  border: none !important;
  height: 60px !important;
  transition: background-color 0.2s ease !important;
}

.mat-mdc-row:hover {
  background-color: #fafafa !important;
}

.mat-mdc-row:not(:last-child) {
  border-bottom: 1px solid #f0f0f0 !important;
}

.mat-mdc-cell {
  padding: 0 16px !important;
  font-size: 0.875rem !important;
  color: #333 !important;
  border-bottom: none !important;
  border-right: none !important;
  height: 60px !important;
  line-height: 60px !important;
  vertical-align: middle !important;
}

/** Battery Level Styling **/
.battery-cell {
  font-weight: 500 !important;
}

/* Battery level color coding */
.battery-high {
  color: #4caf50 !important;
}

.battery-medium {
  color: #ff9800 !important;
}

.battery-low {
  color: #f44336 !important;
}

/** Value Cell Styling **/
.value-cell {
  font-weight: 500 !important;
}

/** Unit Cell Styling **/
.unit-cell {
  color: #666 !important;
  font-style: italic;
}

/** Timestamp Cell Styling **/
.timestamp-cell {
  color: #666 !important;
  font-size: 0.8rem !important;
}

/** Sort Header Styling **/
.mat-sort-header-arrow {
  color: #666 !important;
}

.mat-sort-header-sorted {
  color: #333 !important;
}

/** Paginator Styling **/
.mat-mdc-paginator {
  background-color: white !important;
  border: none !important;
  border-radius: 0 !important;
  font-size: 0.875rem !important;
  color: #666 !important;
  padding: 8px 16px !important;
}

.mat-mdc-paginator .mat-mdc-paginator-range-label {
  color: #666 !important;
  margin: 0 32px 0 24px !important;
}

.mat-mdc-paginator .mat-mdc-paginator-page-size-label {
  color: #666 !important;
}

.mat-mdc-select {
  font-size: 0.875rem !important;
}

.mat-mdc-icon-button {
  color: #666 !important;
  width: 32px !important;
  height: 32px !important;
}

.mat-mdc-icon-button:hover {
  background-color: #f0f0f0 !important;
}

.mat-mdc-icon-button[disabled] {
  color: #ccc !important;
}

/** Table Container **/
.table-container {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-top: 1rem;
}

/** Remove Material Design Elevation **/
.mat-elevation-z8 {
  box-shadow: none !important;
}

/** Responsive Design **/
@media (max-width: 1024px) {
  .container {
    padding: 1rem;
  }

  .mat-mdc-header-cell,
  .mat-mdc-cell {
    padding: 0 12px !important;
    font-size: 0.8rem !important;
  }

  .page-title {
    font-size: 1.25rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0.75rem;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .mat-mdc-header-cell,
  .mat-mdc-cell {
    padding: 0 8px !important;
    font-size: 0.75rem !important;
  }

  .mat-mdc-row {
    height: 48px !important;
  }

  .mat-mdc-header-row {
    height: 40px !important;
  }

  .mat-mdc-cell,
  .mat-mdc-header-cell {
    height: 48px !important;
    line-height: 48px !important;
  }
}

/** Focus States **/
.mat-mdc-form-field.mat-focused .mat-mdc-text-field-wrapper {
  border-color: #4caf50 !important;
}

.mat-mdc-form-field.mat-focused .mat-mdc-floating-label {
  color: #4caf50 !important;
}

/** Loading State **/
.loading-message {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  font-size: 0.875rem;
  color: #666;
}

/** No Data Message **/
.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  font-size: 0.875rem;
  color: #666;
  font-style: italic;
}
