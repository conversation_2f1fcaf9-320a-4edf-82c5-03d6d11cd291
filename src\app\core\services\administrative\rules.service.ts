import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ApiService } from '../api.service';
import { Rules } from '@app/core/models/rules';
import { RuleRecentApplication } from '@app/shared/models/rule/RuleRecentApplication';
import { RuleComprehensive } from '@app/shared/models/rule/RuleComprehensive';
import { RulePerformanceAnalytic } from '@app/shared/models/rule/RulePerformanceAnalytic';
import { RuleClientHierarchy } from '@app/shared/models/rule/RuleClientHierarchy';
import { RuleWithTag } from '@app/shared/models/rule/RuleWithTag';
import { PagedResponse } from '@app/shared/models/rule/PagedResponse';
import { Lister } from '@app/shared/models/rule/Lister';
import { WhereParams } from '@app/shared/models/rule/WhereParams';
import { Sorting } from '@app/shared/models/rule/Sorting';
import { Observable } from 'rxjs';
import { RuleTransactionDetail } from '@app/shared/models/rule/RuleTransactionDetail';
import { RuleDto } from '@app/shared/models/RuleDto';
import { RuleWithAiRes } from '@app/shared/models/rule/RuleWithAiRes';

export interface AdvancedRulesRequest {
  page?: number;
  pageSize?: number;
  sortParams?: Sorting[];
  filterParams?: WhereParams[];
}

export interface AdvancedSearchRequest {
  searchTerm: string;
  includeInactive?: boolean;
  page?: number;
  pageSize?: number;
  sortParams?: Sorting[];
  filterParams?: WhereParams[];
}

@Injectable({ providedIn: 'root' })
export class RulesApiService extends ApiService<Rules> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint('rule');
  }

  apiUrl = this.getFullUrl();

  /**
   * Retrieves all rules with comprehensive statistics and pagination.
   * Uses GET method with query parameters - supports simple filtering and sorting
   */
  getRulesComprehensive(
    lister: Lister<RuleComprehensive>
  ): Observable<PagedResponse<RuleComprehensive[]>> {
    let params = new HttpParams();

    // Basic pagination
    params = params.set('page', lister.Pagination.CurrentPage.toString());
    params = params.set('pageSize', lister.Pagination.PageSize.toString());

    // Multi-column sorting support
    if (lister.SortParams && lister.SortParams.length > 0) {
      const sortColumns = lister.SortParams.map(s => s.Column).join(',');
      const sortDirections = lister.SortParams.map(s => s.Sort).join(',');
      params = params.set('sortBy', sortColumns);
      params = params.set('sortDirection', sortDirections);
    }

    // Simple filters as JSON string
    if (lister.FilterParams && lister.FilterParams.length > 0) {
      params = params.set('filters', JSON.stringify(lister.FilterParams));
    }

    console.log('🚀 Service: Sending GET params to backend:', params.toString());

    return this.http.get<PagedResponse<RuleComprehensive[]>>(
      `${this.apiUrl}rule/comprehensive`,
      { params: params }
    );
  }

  /**
   * Advanced rules retrieval with complex filtering and sorting using POST
   */
  getRulesComprehensiveAdvanced(
    request: AdvancedRulesRequest
  ): Observable<PagedResponse<RuleComprehensive[]>> {
    console.log('🚀 Service: Sending advanced rules request:', request);

    return this.http.post<PagedResponse<RuleComprehensive[]>>(
      `${this.apiUrl}rule/comprehensive/advanced`,
      request
    );
  }

  /**
   * Searches for rules based on a search term - supports simple filtering
   */
  searchRules(
    searchTerm: string,
    lister: Lister<RuleComprehensive>
  ): Observable<PagedResponse<RuleComprehensive[]>> {
    let params = new HttpParams();

    params = params.set('searchTerm', searchTerm);
    params = params.set('page', lister.Pagination.CurrentPage.toString());
    params = params.set('pageSize', lister.Pagination.PageSize.toString());
    params = params.set('includeInactive', 'false');

    // Multi-column sorting support
    if (lister.SortParams && lister.SortParams.length > 0) {
      const sortColumns = lister.SortParams.map(s => s.Column).join(',');
      const sortDirections = lister.SortParams.map(s => s.Sort).join(',');
      params = params.set('sortBy', sortColumns);
      params = params.set('sortDirection', sortDirections);
    }

    // Simple filters as JSON string
    if (lister.FilterParams && lister.FilterParams.length > 0) {
      params = params.set('filters', JSON.stringify(lister.FilterParams));
    }

    console.log('🔍 Service: Sending search GET params:', params.toString());

    return this.http.get<PagedResponse<RuleComprehensive[]>>(
      `${this.apiUrl}rule/search`,
      { params: params }
    );
  }

  /**
   * Advanced search with complex filtering using POST
   */
  searchRulesAdvanced(
    request: AdvancedSearchRequest
  ): Observable<PagedResponse<RuleComprehensive[]>> {
    console.log('🔍 Service: Sending advanced search request:', request);

    return this.http.post<PagedResponse<RuleComprehensive[]>>(
      `${this.apiUrl}rule/search/advanced`,
      request
    );
  }

  /**
   * Retrieves comprehensive details for a specific rule by its ID, including statistics and tags.
   */
  getRuleComprehensiveById(
    ruleId: string
  ): Observable<RuleComprehensive | null> {
    return this.http.get<RuleComprehensive | null>(
      `${this.apiUrl}rule/${ruleId}/comprehensive`
    );
  }

  /**
   * Retrieves the client, site, and local hierarchy associated with a specific rule.
   */
  getRuleClientHierarchy(ruleId: string): Observable<RuleClientHierarchy[]> {
    return this.http.get<RuleClientHierarchy[]>(
      `${this.apiUrl}rule/${ruleId}/hierarchy`
    );
  }

  /**
   * Retrieves all tags associated with a specific rule.
   */
  getRuleTags(ruleId: string): Observable<RuleWithTag[]> {
    return this.http.get<RuleWithTag[]>(`${this.apiUrl}rule/${ruleId}/tags`);
  }

  /**
   * Retrieves the most recent applications (transactions) for a specific rule.
   */
  getRuleRecentApplications(
    ruleId: string,
    limit: number = 5
  ): Observable<RuleRecentApplication[]> {
    return this.http.get<RuleRecentApplication[]>(
      `${this.apiUrl}rule/${ruleId}/recent-applications?limit=${limit}`
    );
  }

  /**
   * Retrieves performance analytics data for a specific rule over a given number of days.
   */
  getRulePerformance(
    ruleId: string,
    days: number = 7
  ): Observable<RulePerformanceAnalytic[]> {
    return this.http.get<RulePerformanceAnalytic[]>(
      `${this.apiUrl}rule/${ruleId}/performance?days=${days}`
    );
  }

  /**
   * Retrieves transaction details for a specific rule (includes controller information).
   */
  getRuleTransactionDetails(ruleId: string): Observable<RuleTransactionDetail[]> {
    console.log('🔄 Service: Fetching transaction details for rule:', ruleId);
    return this.http.get<RuleTransactionDetail[]>(
      `${this.apiUrl}rule/${ruleId}/transaction-details`
    );
  }

  /**
   * Toggles the enabled/disabled status of a specific rule.
   */
  toggleRuleStatus(ruleId: string, userId: string): Observable<boolean> {
    return this.http.put<boolean>(
      `${this.apiUrl}rule/${ruleId}/toggle-status`,
      { userId }
    );
  }

  /**
   * Creates a rule using AI generation
   */
  createRuleWithAi(prompt: string): Observable<RuleWithAiRes> {
    const createRuleUrl = `${this.apiUrl}text-summarization/rule-with-ai`;
    
    const requestBody = {
      prompt: prompt.trim()
    };

    console.log('🤖 Service: Sending AI rule creation request:', requestBody);
    return this.http.post<RuleWithAiRes>(createRuleUrl, requestBody);
  }

  /**
   * Summarizes a rule using AI
   */
  summarizeRule(rule: any): Observable<String> {
    const summarizationUrl = `${this.apiUrl}text-summarization/summarize`;
    return this.http.post<String>(summarizationUrl, rule);
  }

  /**
   * Toggles the active/inactive status of a specific tag associated with a rule.
   */
  toggleTagStatus(
    ruleId: string,
    tagId: string,
    userId: string
  ): Observable<boolean> {
    return this.http.put<boolean>(
      `${this.apiUrl}rule/${ruleId}/tags/${tagId}/toggle-status`,
      { userId }
    );
  }

  // === UTILITY METHODS ===

  /**
   * Helper method to create simple filter params
   */
  createSimpleFilter(column: string, value: any, operator: string = 'eq'): WhereParams {
    return {
      Column: column,
      Value: value.toString(),
      Operand: operator
    };
  }

  /**
   * Helper method to create simple sort params
   */
  createSimpleSort(column: string, direction: string = 'ASC'): Sorting {
    return {
      Column: column,
      Sort: direction.toUpperCase()
    };
  }

  /**
   * Helper method to build a basic lister with common defaults
   */
  createBasicLister(
    page: number = 1, 
    pageSize: number = 10, 
    sortColumn: string = 'Priority',
    sortDirection: string = 'ASC'
  ): Lister<RuleComprehensive> {
    return {
      Pagination: {
        CurrentPage: page,
        PageSize: pageSize,
        PageCount: 0,
        IsLast: false,
        IsFirst: page === 1,
        StartIndex: (page - 1) * pageSize,
        TotalElement: 0
      },
      SortParams: [this.createSimpleSort(sortColumn, sortDirection)],
      FilterParams: []
    };
  }
}