import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ApiService } from '../api.service';
import { 
  Rules} from '@app/core/models/rules';
import { RuleRecentApplication } from "@app/shared/models/rule/RuleRecentApplication";
import { RuleComprehensive } from "@app/shared/models/rule/RuleComprehensive";
import { RulePerformanceAnalytic } from "@app/shared/models/rule/RulePerformanceAnalytic";
import { RuleClientHierarchy } from "@app/shared/models/rule/RuleClientHierarchy";
import { RuleWithTag } from "@app/shared/models/rule/RuleWithTag";
import { PagedResponse } from "@app/shared/models/rule/PagedResponse";
import { Lister } from "@app/shared/models/rule/Lister";
import { WhereParams } from "@app/shared/models/rule/WhereParams";
import { Sorting } from "@app/shared/models/rule/Sorting";
import { Observable } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class RulesApiService extends ApiService<Rules> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint("rule");
  }
 
  apiUrl = this.getFullUrl();

  /**
   * Retrieves all rules with comprehensive statistics and pagination.
   * Uses GET method with query parameters as the backend expects
   */
  getRulesComprehensive(lister: Lister<RuleComprehensive>): Observable<PagedResponse<RuleComprehensive[]>> {
    // Construct HttpParams using the EXACT parameter names from the backend controller
    let params = new HttpParams();
    
    // Backend expects: page, pageSize, sortBy, sortDirection
    params = params.set('page', lister.Pagination.CurrentPage.toString());
    params = params.set('pageSize', lister.Pagination.PageSize.toString());

    // Add sorting parameters
    if (lister.SortParams && lister.SortParams.length > 0) {
      params = params.set('sortBy', lister.SortParams[0].Column);
      params = params.set('sortDirection', lister.SortParams[0].Sort);
    }

    console.log('🚀 Service: Sending GET params to backend:', params.toString());
    console.log('🚀 Service: Full URL will be:', `${this.apiUrl}rule/comprehensive?${params.toString()}`);

    return this.http.get<PagedResponse<RuleComprehensive[]>>(`${this.apiUrl}rule/comprehensive`, { params: params });
  }

  /**
   * Retrieves comprehensive details for a specific rule by its ID, including statistics and tags.
   */
  getRuleComprehensiveById(ruleId: string): Observable<RuleComprehensive | null> {
    return this.http.get<RuleComprehensive | null>(`${this.apiUrl}rule/comprehensive/${ruleId}`);
  }

  /**
   * Retrieves the client, site, and local hierarchy associated with a specific rule.
   */
  getRuleClientHierarchy(ruleId: string): Observable<RuleClientHierarchy[]> {
    return this.http.get<RuleClientHierarchy[]>(`${this.apiUrl}rule/${ruleId}/hierarchy`);
  }

  /**
   * Retrieves all tags associated with a specific rule.
   */
  getRuleTags(ruleId: string): Observable<RuleWithTag[]> {
    return this.http.get<RuleWithTag[]>(`${this.apiUrl}rule/${ruleId}/tags`);
  }

  /**
   * Retrieves the most recent applications (transactions) for a specific rule.
   */
  getRuleRecentApplications(ruleId: string, limit: number = 5): Observable<RuleRecentApplication[]> {
    return this.http.get<RuleRecentApplication[]>(`${this.apiUrl}rule/${ruleId}/recent-applications?limit=${limit}`);
  }

  /**
   * Retrieves performance analytics data for a specific rule over a given number of days.
   */
  getRulePerformance(ruleId: string, days: number = 7): Observable<RulePerformanceAnalytic[]> {
    return this.http.get<RulePerformanceAnalytic[]>(`${this.apiUrl}rule/${ruleId}/performance?days=${days}`);
  }

  /**
   * Toggles the enabled/disabled status of a specific rule.
   */
  toggleRuleStatus(ruleId: string, userId: string): Observable<boolean> {
    return this.http.put<boolean>(`${this.apiUrl}rule/${ruleId}/toggle-status`, { userId });
  }

  summarizeRule(rule: any): Observable<String> {
    const summarizationUrl = `${this.apiUrl}text-summarization/summarize`;
    return this.http.post<String>(summarizationUrl, rule);
  }

  /**
   * Toggles the active/inactive status of a specific tag associated with a rule.
   */
  toggleTagStatus(ruleId: string, tagId: string, userId: string): Observable<boolean> {
    return this.http.put<boolean>(`${this.apiUrl}rule/${ruleId}/tags/${tagId}/toggle-status`, { userId });
  }

  /**
   * Searches for rules based on a search term in their raw data or associated tags.
   * Uses the GET /rule/search endpoint with query parameters
   */
  searchRules(searchTerm: string, lister: Lister<RuleComprehensive>): Observable<PagedResponse<RuleComprehensive[]>> {
    // Construct HttpParams using the EXACT parameter names from the backend controller
    let params = new HttpParams();
    
    // Backend expects: searchTerm, page, pageSize, sortBy, sortDirection, includeInactive
    params = params.set('searchTerm', searchTerm);
    params = params.set('page', lister.Pagination.CurrentPage.toString());
    params = params.set('pageSize', lister.Pagination.PageSize.toString());
    params = params.set('includeInactive', 'false'); // Default to false unless you need to make this configurable
    
    // Add sorting parameters
    if (lister.SortParams && lister.SortParams.length > 0) {
      params = params.set('sortBy', lister.SortParams[0].Column);
      params = params.set('sortDirection', lister.SortParams[0].Sort);
    }

    console.log('🔍 Service: Sending search GET params to backend:', params.toString());
    console.log('🔍 Service: Search URL will be:', `${this.apiUrl}rule/search?${params.toString()}`);

    // Use the search endpoint (not comprehensive/search)
    return this.http.get<PagedResponse<RuleComprehensive[]>>(`${this.apiUrl}rule/search`, { params: params });
  }

  /**
   * DEBUGGING: Test method to check what the backend actually receives
   */
  testBackendPagination(lister: Lister<RuleComprehensive>): Observable<any> {
    console.log('🧪 Testing backend pagination with:', lister);
    
    // Add a debug endpoint call if your backend has one
    return this.http.post<any>(`${this.apiUrl}rule/test-pagination`, lister);
  }
}