/* Tag List Container */
.tag-list-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* Popup Styles */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.popup-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(45deg, #2E7D32, #81C784);
  color: white;
  border-radius: 12px 12px 0 0;
}

.popup-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.popup-content {
  padding: 20px;
}

.popup-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

/* Header Card Container */
.header-card-container {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
  animation: slideIn 0.5s ease-out;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: #2E7D32;
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
}

.title-icon {
  font-size: 28px !important;
  width: 28px !important;
  height: 28px !important;
  color: #2E7D32;
}

.actions {
  display: flex;
  gap: 10px;
}

.create-button {
  background: linear-gradient(45deg, #2E7D32, #81C784);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(46, 125, 50, 0.3);
}

.create-button:hover {
  background: linear-gradient(45deg, #81C784, #2E7D32);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(46, 125, 50, 0.4);
}

.action-icon {
  font-size: 18px !important;
  width: 18px !important;
  height: 18px !important;
}

/* Search Section */
.search-section {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.search-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-input {
  padding: 10px 15px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  width: 300px;
}

.search-input:focus {
  border-color: #2E7D32;
  box-shadow: 0 0 0 2px rgba(46, 125, 50, 0.2);
  outline: none;
}

.search-button {
  background: linear-gradient(45deg, #2E7D32, #81C784);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(46, 125, 50, 0.3);
}

.search-button:hover {
  background: linear-gradient(45deg, #81C784, #2E7D32);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(46, 125, 50, 0.4);
}

/* Table Section */
.table-section {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

/* Loading Spinner */
.loading-spinner {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 40px;
  text-align: center;
  color: #666;
  font-size: 16px;
}

/* No Data Message */
.no-data-message {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.no-data-icon {
  font-size: 48px !important;
  width: 48px !important;
  height: 48px !important;
  color: #cbd5e0;
  margin-bottom: 16px;
}

.no-data-message p {
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #718096;
}

/* Form Styles */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 20px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.required {
  color: #e53e3e;
}

.form-group input {
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.form-group input:focus {
  border-color: #2E7D32;
  box-shadow: 0 0 0 2px rgba(46, 125, 50, 0.2);
  outline: none;
}

.error-message {
  font-size: 12px;
  color: #e53e3e;
  margin-top: 4px;
}

/* Button Styles */
.btn-cancel {
  padding: 10px 20px;
  background: transparent;
  border: 1px solid #cbd5e0;
  color: #718096;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-cancel:hover {
  background-color: #f7fafc;
}

.btn-submit {
  padding: 10px 20px;
  background: linear-gradient(45deg, #2E7D32, #81C784);
  color: white;
  border: none;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(46, 125, 50, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-submit:hover {
  background: linear-gradient(45deg, #81C784, #2E7D32);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(46, 125, 50, 0.4);
}

.btn-submit:disabled {
  background: #cbd5e0;
  transform: none;
  box-shadow: none;
  cursor: not-allowed;
}

/* Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .tag-list-container {
    padding: 15px;
  }

  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .search-container {
    flex-direction: column;
  }

  .popup-form {
    width: 95%;
    margin: 10px;
  }

  .popup-actions {
    flex-direction: column;
  }
}
