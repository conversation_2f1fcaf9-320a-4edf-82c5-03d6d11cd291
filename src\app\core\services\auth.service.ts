import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError, of } from 'rxjs';
import { tap, catchError, map } from 'rxjs/operators';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { environment } from '@app/environments/environment';

export interface LoginResponse {
  token: string;
  expiration: string;
  roles: string[];
  userId: string;
  username: string;
  email: string;
  fullName?: string;
  user: User;
}

export interface RegisterModel {
  email: string;
  password: string;
  confirmPassword: string;
  phoneNumber?: string;
  userRole: string;
}

export interface AvailableRole {
  name: string;
  description: string;
}

export interface User {
  email: string,
  id: string,
  roles: string[],
  username: string
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly apiUrl = `${environment.host}/api/auth`;
  private availableRoles: AvailableRole[] = [];
  private userDetails: LoginResponse | null = null;

  constructor(
    readonly http: HttpClient,
    readonly router: Router,
    readonly messageService: MessageService
  ) {}

  // Login with username/password
  login(username: string, password: string): Observable<LoginResponse> {
    return this.http.post<LoginResponse>(`${this.apiUrl}/login`, { username, password }).pipe(
      tap(response => {
        console.log('Login response:', response);
        this.saveToken(response);
        this.userDetails = response;
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Login successful'
        });
      }),
      catchError(this.handleError)
    );
  }

  // Register new user with dynamic role
  register(model: RegisterModel): Observable<any> {
    return this.http.post(`${this.apiUrl}/register`, model).pipe(
      tap(() => {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Registration successful. Please login.'
        });
      }),
      catchError(this.handleError)
    );
  }

  // Fetch available roles from server
  getAvailableRoles(): Observable<AvailableRole[]> {
    if (this.availableRoles.length > 0) {
      return of(this.availableRoles);
    }
    return this.http.get<AvailableRole[]>(`${this.apiUrl}/roles`).pipe(
      tap(roles => this.availableRoles = roles),
      catchError(() => {
        // Fallback if roles endpoint not available
        this.availableRoles = [
          { name: 'USER', description: 'Regular application user' },
          { name: 'ADMIN', description: 'Administrator with full access' }
        ];
        return of(this.availableRoles);
      })
    );
  }

  // Get current user details
  getCurrentUser(): LoginResponse | null {
    if (!this.userDetails && this.isLoggedIn()) {
      
      this.userDetails = {
        token: this.getToken() ?? '',
        expiration: localStorage.getItem('expiration') ?? '',
        roles: this.getRoles(), // ← pas utilisé dans header
        userId: this.getUserId() ?? '',
        username: this.getUsername() ?? '',
        email: this.getEmail() ?? '',
        fullName: localStorage.getItem('fullName') ?? '',
        user: localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user') ?? '{}') : {}
      };
    }
    console.log("Current user roles:", this.userDetails?.user?.roles);
    return this.userDetails;
  }
  

  // Check if user has any of the required roles
  hasAnyRole(requiredRoles: string[]): boolean {
    const userRoles = this.getRoles();
    return requiredRoles.some(role => userRoles.includes(role));
  }

  getToken(): string | null {
    return localStorage.getItem('token');
  }
  
  getRoles(): string[] {
    localStorage.setItem('roles', JSON.stringify(['USER', 'ADMIN', 'SuperADMIN']));
    const rolesRaw = localStorage.getItem('roles');
  
    if (!rolesRaw || rolesRaw === 'undefined') {
      return [];
    }
  
    try {
      return JSON.parse(rolesRaw);
    } catch (e) {
      console.error('Erreur lors de la lecture des rôles :', e);
      return [];
    }
  }  
  
  getUserId(): string | null {
    return localStorage.getItem('userId');
  }
  
  getUsername(): string | null {
    return localStorage.getItem('username');
  }
  
  getEmail(): string | null {
    return localStorage.getItem('email');
  }
  
  isTokenExpired(): boolean {
    const exp = localStorage.getItem('expiration');
    if (!exp) return true;
    return new Date(exp) < new Date();
  }
  
  isLoggedIn(): boolean {
    return !!this.getToken() && !this.isTokenExpired();
  }
  

  // Save token and user details to storage
  private saveToken(response: LoginResponse): void {
    localStorage.setItem('token', response.token);
    localStorage.setItem('expiration', response.expiration);
    localStorage.setItem('roles', JSON.stringify(response.roles ?? []));
    localStorage.setItem('userId', response.userId);
    localStorage.setItem('username', response.username);
    localStorage.setItem('email', response.email);
    localStorage.setItem('user', response.user ? JSON.stringify(response.user) : '{}');
  }  

  // Clear authentication data
  logout(): void {
    localStorage.clear();
    this.userDetails = null;
    this.router.navigate(['/login']);
    this.messageService.add({
      severity: 'info',
      summary: 'Logged Out',
      detail: 'You have been successfully logged out'
    });
  }

  // Error handling
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unexpected error occurred. Please try again.';
    
    if (error.status === 401) {
      errorMessage = 'Invalid credentials. Please check your username and password.';
    } else if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.statusText) {
      errorMessage = error.statusText;
    }

    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: errorMessage,
      life: 5000
    });

    return throwError(() => new Error(errorMessage));
  }
}