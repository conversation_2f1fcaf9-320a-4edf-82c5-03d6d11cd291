import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError, of } from 'rxjs';
import { tap, catchError, map } from 'rxjs/operators';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { environment } from '@app/environments/environment';
import { ClientApiService } from '@app/core/services/administrative/client.service';

export interface LoginResponse {
  token: string;
  expiration: string;
  roles: string[];
  userId: string;
  username: string;
  email: string;
  fullName?: string;
  user: User;
}

export interface RegisterModel {
  email: string;
  password: string;
  confirmPassword: string;
  username: string;
  phoneNumber?: string;
  userRole: string;
}



export interface AvailableRole {
  name: string;
  description: string;
}

export interface User {
  email: string,
  id: string,
  roles: string[],
  username: string
}

export interface UserDetails {
  id: string;
  userName: string;
  email: string;
  phoneNumber: string;
  roles: string[];
  emailConfirmed: boolean;
  lockoutEnabled: boolean;
  lockoutEnd: string | null;
  accessFailedCount: number;
}

export interface GetAllUsersResponse {
  Status: string;
  Data: UserDetails[];
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly apiUrl = `${environment.host}/api/auth`;
  private availableRoles: AvailableRole[] = [];
  private userDetails: LoginResponse | null = null;

  constructor(
    readonly http: HttpClient,
    readonly router: Router,
    readonly messageService: MessageService,
    readonly clientApiService: ClientApiService // <-- Inject ClientApiService

  ) {}

  // Login with username/password
  login(username: string, password: string): Observable<LoginResponse> {
    return this.http.post<LoginResponse>(`${this.apiUrl}/login`, { username, password }).pipe(
      tap(response => {
        this.saveToken(response);
        this.userDetails = response;
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Login successful'
        });

        // Check if user role is CLIENT and redirect
        if ( response.roles.includes('CLIENT')) {
          console.log('Redirecting to client details page...');
          this.clientApiService.getClientByUserId(response.userId).subscribe({
            next: (client) => {
              console.log('Client found:', client.Id);
              this.router.navigate([`/organisation-details/${client.Id}`]);
            },
            error: () => {
              this.router.navigate(['/user-management']);
            }
          });
        }
      }),
      catchError(this.handleError)
    );
  }

  // Register new user with dynamic role
  register(model: RegisterModel): Observable<any> {
    return this.http.post(`${this.apiUrl}/register`, model).pipe(
      tap(() => {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Registration successful. Please login.'
        });
      }),
      catchError(this.handleError)
    );
  }

  // Get all users from the system
  getAllUsers(): Observable<UserDetails[]> {
    return this.http.get<GetAllUsersResponse>(`${this.apiUrl}/users`).pipe(
      map(response => response.Data),
      catchError(this.handleError)
    );
  }

  // Fetch available roles from server
  getAvailableRoles(): Observable<AvailableRole[]> {
    if (this.availableRoles.length > 0) {
      return of(this.availableRoles);
    }
    return this.http.get<AvailableRole[]>(`${this.apiUrl}/roles`).pipe(
      tap(roles => this.availableRoles = roles),
      catchError(() => {
        // Fallback if roles endpoint not available
     this.availableRoles = [
  { name: 'CLIENT', description: 'Client user' },
  { name: 'INTEGRATEUR', description: 'Integrator user' },
  { name: 'ADMINISTRATEUR', description: 'Administrator with full access' }
];
        return of(this.availableRoles);
      })
    );
  }

  // Get current user details
  getCurrentUser(): LoginResponse | null {
    if (!this.userDetails && this.isLoggedIn()) {
      
      this.userDetails = {
        token: this.getToken() ?? '',
        expiration: localStorage.getItem('expiration') ?? '',
        roles: this.getRoles(), // ← pas utilisé dans header
        userId: this.getUserId() ?? '',
        username: this.getUsername() ?? '',
        email: this.getEmail() ?? '',
        fullName: localStorage.getItem('fullName') ?? '',
        user: localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user') ?? '{}') : {}
      };
    }
    return this.userDetails;
  }


  // Check if user has any of the required roles
  hasAnyRole(requiredRoles: string[]): boolean {
    const userRoles = this.getRoles();
    return requiredRoles.some(role => userRoles.includes(role));
  }

  getToken(): string | null {
    return localStorage.getItem('token');
  }
  
// In auth.service.ts, update the getRoles() method:
getRoles(): string[] {
  const rolesRaw = localStorage.getItem('roles');
  
  if (!rolesRaw || rolesRaw === 'undefined' || rolesRaw === 'null') {
    return [];
  }
  
  try {
    const roles = JSON.parse(rolesRaw);
    // Ensure we always return an array
    return Array.isArray(roles) ? roles : [];
  } catch (e) {
    console.error('Error parsing roles:', e);
    return [];
  }
}
  
  getUserId(): string | null {
    return localStorage.getItem('userId');
  }
  
  getUsername(): string | null {
    return localStorage.getItem('username');
  }
  
  getEmail(): string | null {
    return localStorage.getItem('email');
  }
  
  isTokenExpired(): boolean {
    const exp = localStorage.getItem('expiration');
    if (!exp) return true;
    return new Date(exp) < new Date();
  }
  
  isLoggedIn(): boolean {
    return !!this.getToken() && !this.isTokenExpired();
  }
  

  // Save token and user details to storage
  private saveToken(response: LoginResponse): void {
    localStorage.setItem('token', response.token);
    localStorage.setItem('expiration', response.expiration);
  const roles = response.roles?.map(role => 
    role === 'ADMIN' ? 'ADMINISTRATEUR' : role
  ) ?? [];
  
  localStorage.setItem('roles', JSON.stringify(roles));    localStorage.setItem('userId', response.userId);
    localStorage.setItem('username', response.username);
    localStorage.setItem('email', response.email);
    localStorage.setItem('user', response.user ? JSON.stringify(response.user) : '{}');
  }  

  // Clear authentication data
  logout(): void {
    localStorage.clear();
    this.userDetails = null;
    this.router.navigate(['/login']);
    this.messageService.add({
      severity: 'info',
      summary: 'Logged Out',
      detail: 'You have been successfully logged out'
    });
  }

  // Error handling
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unexpected error occurred. Please try again.';
    
    if (error.status === 401) {
      errorMessage = 'Invalid credentials. Please check your username and password.';
    } else if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.statusText) {
      errorMessage = error.statusText;
    }

    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: errorMessage,
      life: 5000
    });

    return throwError(() => new Error(errorMessage));
  }

  deleteUser(id: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/users/${id}`).pipe(
      tap(() => {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'User deleted successfully'
        });
      }),
      catchError(this.handleError)
    );
  }
  updateUser(id: string, userData: Partial<UserDetails>): Observable<UserDetails> {
    return this.http.put<UserDetails>(`${this.apiUrl}/users/${id}`, userData).pipe(
      tap(() => {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'User updated successfully'
        });
      }),
      catchError(this.handleError)
    );
  }
  getUserById(id: string): Observable<UserDetails> {
    return this.http.get<UserDetails>(`${this.apiUrl}/users/${id}`).pipe(
      catchError(this.handleError)
    );
  }
}
  // Debug method to verify role assignment

