
/* ===== CONTAINER ===== */
.applied-rules-container {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* ===== HEADER ===== */
.rules-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.rules-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
  color: #1e293b;
}

.title-icon {
  color: #4CAF50;
  font-size: 2rem !important;
  width: 2rem !important;
  height: 2rem !important;
}

.rules-count {
  background: #4CAF50;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  margin-left: 0.5rem;
}

.add-rule-btn {
  background: linear-gradient(135deg, #4CAF50, #45a049) !important;
  color: white !important;
  border-radius: 0.75rem !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.25) !important;
  transition: all 0.3s ease !important;
}

.add-rule-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(76, 175, 80, 0.35) !important;
}

/* ===== LOADING STATE ===== */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== EMPTY STATE ===== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  background: #f8fafc;
  border-radius: 1rem;
  border: 2px dashed #cbd5e1;
}

.empty-icon {
  font-size: 4rem !important;
  width: 4rem !important;
  height: 4rem !important;
  color: #94a3b8;
  margin-bottom: 1rem;
}

.empty-state h4 {
  color: #475569;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.empty-state p {
  color: #64748b;
  margin-bottom: 2rem;
  font-size: 1.125rem;
}

/* ===== RULES LIST ===== */
.rules-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* ===== RULE CARD ===== */
.rule-card {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  overflow: hidden;
}

.rule-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.rule-card.mat-expanded {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* ===== RULE HEADER ===== */
.rule-header {
  padding: 1.5rem 2rem !important;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-bottom: 1px solid #e2e8f0;
}

.rule-title-container {
  width: 100%;
}

.rule-main-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.rule-name {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.rule-icon {
  color: #4CAF50;
  font-size: 1.5rem !important;
  width: 1.5rem !important;
  height: 1.5rem !important;
}

.name-text {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
}

.rule-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  background: white;
  border: 1px solid #e2e8f0;
}

.status-icon.enabled {
  color: #22c55e;
}

.status-icon.disabled {
  color: #ef4444;
}

.status-text {
  font-weight: 600;
  font-size: 0.875rem;
}

.rule-description {
  margin-top: 1rem !important;
  width: 100% !important;
}

.rule-stats {
  display: flex;
  gap: 2rem;
  align-items: center;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

.stat-icon {
  font-size: 1rem !important;
  width: 1rem !important;
  height: 1rem !important;
}

.priority-badge {
  background: #fef3c7;
  color: #92400e;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  border: 1px solid #fcd34d;
}

.priority-badge[data-priority="1"] {
  background: #fee2e2;
  color: #dc2626;
  border-color: #fca5a5;
}

.priority-badge[data-priority="2"] {
  background: #fef3c7;
  color: #d97706;
  border-color: #fcd34d;
}

/* ===== RULE CONTENT ===== */
.rule-content {
  padding: 2rem;
}

/* ===== RULE SUMMARY ===== */
.rule-summary-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  border: 1px solid #e2e8f0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.rule-summary-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.summary-label {
  font-weight: 600;
  color: #6b7280;
  font-size: 0.875rem;
}

.summary-value {
  color: #111827;
  font-weight: 500;
}

.topic-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.topic-chip {
  background: #dbeafe !important;
  color: #1d4ed8 !important;
  font-size: 0.75rem !important;
  height: auto !important;
  padding: 0.25rem 0.75rem !important;
  border-radius: 1rem !important;
  border: 1px solid #93c5fd !important;
}

/* ===== CONTROLLERS SECTION ===== */
.controllers-section {
  margin-bottom: 2rem;
}

.controller-application {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.controller-application:last-child {
  margin-bottom: 0;
}

.controller-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f3f4f6;
}

.controller-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.controller-icon {
  color: #6366f1;
  font-size: 2rem !important;
  width: 2rem !important;
  height: 2rem !important;
}

.controller-details h5 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.controller-details p {
  margin: 0.25rem 0 0 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.controller-stats {
  background: #f3f4f6;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  color: #374151;
  font-weight: 600;
  font-size: 0.875rem;
}

/* ===== CAPTEURS GRID ===== */
.capteurs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.capteur-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  transition: all 0.2s ease;
}

.capteur-card:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.capteur-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.capteur-type-icon {
  font-size: 1.5rem !important;
  width: 1.5rem !important;
  height: 1.5rem !important;
}

.capteur-type-icon.sensor {
  color: #059669;
}

.capteur-type-icon.actuator {
  color: #dc2626;
}

.capteur-info h6 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
  line-height: 1.2;
}

.capteur-info p {
  margin: 0.25rem 0 0 0;
  color: #6b7280;
  font-size: 0.75rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.capteur-type-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
}

.sensor-badge {
  background: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.actuator-badge {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}

/* ===== CONNECTION FLOW ===== */
.connection-flow {
  margin-top: 1.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-radius: 0.75rem;
  border: 1px solid #bae6fd;
}

.flow-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
  color: #0c4a6e;
  font-size: 0.875rem;
}

.flow-diagram {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.flow-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border-radius: 0.75rem;
  background: white;
  border: 2px solid #e0f2fe;
  min-width: 120px;
  text-align: center;
}

.flow-sensors {
  border-color: #a7f3d0;
  background: #f0fdf4;
}

.flow-controller {
  border-color: #c7d2fe;
  background: #f0f9ff;
}

.flow-actuators {
  border-color: #fca5a5;
  background: #fef2f2;
}

.flow-item mat-icon {
  font-size: 1.5rem !important;
  width: 1.5rem !important;
  height: 1.5rem !important;
}

.flow-sensors mat-icon {
  color: #059669;
}

.flow-controller mat-icon {
  color: #3b82f6;
}

.flow-actuators mat-icon {
  color: #dc2626;
}

.flow-item span {
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
}

.flow-arrow {
  color: #9ca3af;
  font-size: 1.5rem !important;
  width: 1.5rem !important;
  height: 1.5rem !important;
}

/* ===== RULE ACTIONS ===== */
.rule-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem !important;
  border-radius: 0.5rem !important;
  font-weight: 600 !important;
  transition: all 0.2s ease !important;
  border: 1px solid transparent !important;
}

.action-btn.secondary {
  background: #f8fafc !important;
  color: #64748b !important;
  border-color: #e2e8f0 !important;
}

.action-btn.secondary:hover {
  background: #f1f5f9 !important;
  color: #475569 !important;
  border-color: #cbd5e1 !important;
}

.action-btn.success {
  background: #dcfce7 !important;
  color: #166534 !important;
  border-color: #bbf7d0 !important;
}

.action-btn.success:hover {
  background: #bbf7d0 !important;
  color: #14532d !important;
}

.action-btn.danger {
  background: #fef2f2 !important;
  color: #dc2626 !important;
  border-color: #fecaca !important;
}

.action-btn.danger:hover {
  background: #fee2e2 !important;
  color: #b91c1c !important;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .rules-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .add-rule-btn {
    width: 100%;
    justify-content: center;
  }

  .rule-main-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .rule-stats {
    gap: 1rem;
  }

  .rule-summary-content {
    grid-template-columns: 1fr;
  }

  .controller-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .capteurs-grid {
    grid-template-columns: 1fr;
  }

  .flow-diagram {
    flex-direction: column;
    gap: 0.5rem;
  }

  .flow-arrow {
    transform: rotate(90deg);
  }
}

@media (max-width: 768px) {
  .applied-rules-container {
    padding: 1rem;
  }

  .rules-title {
    font-size: 1.5rem;
  }

  .rule-header {
    padding: 1rem 1.5rem !important;
  }

  .rule-content {
    padding: 1.5rem;
  }

  .rule-summary-section,
  .controller-application {
    padding: 1rem;
  }

  .capteur-card {
    padding: 0.75rem;
  }

  .rule-actions {
    justify-content: stretch;
  }

  .action-btn {
    flex: 1;
    justify-content: center;
    min-width: 0;
  }
}

@media (max-width: 480px) {
  .rules-title {
    font-size: 1.25rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .rules-count {
    margin-left: 0;
  }

  .rule-stats {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .flow-item {
    min-width: 100px;
    padding: 0.75rem;
  }

  .flow-item span {
    font-size: 0.7rem;
  }

  .rule-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .action-btn {
    width: 100%;
  }
}

/* ===== MATERIAL DESIGN OVERRIDES ===== */
::ng-deep .mat-expansion-panel {
  box-shadow: none !important;
}

::ng-deep .mat-expansion-panel-header {
  height: auto !important;
  padding: 0 !important;
}

::ng-deep .mat-expansion-panel-content .mat-expansion-panel-body {
  padding: 0 !important;
}

::ng-deep .mat-expansion-indicator {
  color: #4CAF50 !important;
}

::ng-deep .mat-expansion-panel-header:hover {
  background: transparent !important;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.rule-card {
  animation: fadeIn 0.3s ease-out;
}

.capteur-card,
.controller-application {
  animation: fadeIn 0.3s ease-out;
}

/* ===== ACCESSIBILITY ===== */
.rule-card:focus-within {
  outline: 2px solid #4CAF50;
  outline-offset: 2px;
}

.action-btn:focus {
  outline: 2px solid #4CAF50 !important;
  outline-offset: 2px !important;
}

/* ===== PRINT STYLES ===== */
@media print {
  .rules-header,
  .rule-actions,
  .add-rule-btn {
    display: none !important;
  }

  .rule-card {
    break-inside: avoid;
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }

  .flow-diagram {
    flex-direction: row !important;
  }

  .flow-arrow {
    transform: none !important;
  }
}