<!-- Device-control view aligned with the provided CSS -->
 <ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>
<div class="device-control-container">

  <!-- ───────────── Header ───────────── -->
  <section class="header-section">
    <mat-card class="header-card">
      <div class="header-title">
        <mat-icon>hub</mat-icon>
        Contrôle des Appareils
      </div>
    </mat-card>
  </section>

  <!-- ───────────── Stats grid ───────────── -->
  <div class="stats-grid">
    <mat-card class="stat-card">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon" style="background: linear-gradient(135deg, #6366f1, #4f46e5);">
              <mat-icon class="stat-icon total">devices</mat-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ buttonon ? deviceStats.total : deviceStatsBackEnd.total }}</div>
            <div class="stat-label">Appareils Total</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- <mat-card class="stat-card">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon" style="background: linear-gradient(135deg, #22c55e, #16a34a);">
              <mat-icon class="stat-icon online">wifi</mat-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ deviceStats.online }}</div>
            <div class="stat-label">En Ligne</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card> -->

    <mat-card class="stat-card">
      <mat-card-content>
        <div class="stat-content">

          <div class="stat-icon" style="background: linear-gradient(135deg, #fbbf24, #f59e0b);">
            <mat-icon>lightbulb</mat-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ buttonon ? deviceStats.lights : deviceStatsBackEnd.lights }}</div>
            <div class="stat-label">Lumières</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
            <mat-icon>sensors</mat-icon>
          </div>
           <div class="stat-info">
            <div class="stat-number">{{ buttonon ? deviceStats.sensors : deviceStatsBackEnd.sensors }}</div>
            <div class="stat-label">Capteurs</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- <mat-card class="stat-card">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon" style="background: linear-gradient(135deg, #06b6d4, #0891b2);">
            <mat-icon>power</mat-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ buttonon ? deviceStats.plugs : deviceStatsBackEnd.plugs }}</div>
            <div class="stat-label">Prises</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card> -->

    <!-- Mouvements -->
    <!-- <mat-card class="stat-card">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
            <mat-icon>directions_run</mat-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ buttonon ? deviceStats.motion : deviceStatsBackEnd.motion }}</div>
            <div class="stat-label">Mouvements</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card> -->

    <!-- Portes -->
    <!-- <mat-card class="stat-card">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
            <mat-icon>door_front</mat-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ buttonon ? deviceStats.door : deviceStatsBackEnd.door }}</div>
            <div class="stat-label">Portes</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card> -->

    <!-- Climat -->
    <!-- <mat-card class="stat-card">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon" style="background: linear-gradient(135deg, #22c55e, #16a34a);">
            <mat-icon>device_thermostat</mat-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ buttonon ? deviceStats.climate : deviceStatsBackEnd.climate }}</div>
            <div class="stat-label">Climat</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card> -->

    <mat-card class="stat-card">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon" style="background: linear-gradient(135deg, #22c55e, #16a34a);">
          <mat-icon class="stat-icon online">more_horiz</mat-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ buttonon ? deviceStats.others : deviceStatsBackEnd.others }}</div>
            <div class="stat-label">Autres</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
    <!-- ---------------------------------------- -->
    <mat-card class="stat-card">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon" style="background: linear-gradient(135deg, #66bb6a, var(--primary));">
          <mat-icon>check_circle</mat-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ countActifDevices }}</div>
            <div class="stat-label">Actif</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

        <mat-card class="stat-card">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon" style="background: linear-gradient(135deg, #ffb74d, #ff9800);">
          <mat-icon>hourglass_top</mat-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ countEnVeilleDevices }}</div>
            <div class="stat-label">En Veille</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

        <mat-card class="stat-card">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon" style="background: linear-gradient(135deg, #ef5350, #f44336);">
          <mat-icon>error</mat-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ countInActifDevices}}</div>
            <div class="stat-label">Inactif</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon" style="background: linear-gradient(135deg, #bdbdbd, #9e9e9e);">
          <mat-icon>sync_disabled</mat-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ countJamisSynchroniseDevices }}</div>
            <div class="stat-label">Jamais Synchronisé</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- ───────────── Filters & view toggle ───────────── -->

<!-- HTML Template -->
<div class="filters-container">
  <label class="filters-title">
    <mat-icon class="filters-icon">filter_list</mat-icon>
    Filters
  </label>
  <div class="filters-grid">
    <div class="filter-field">
      <label class="filter-label">Clients</label>
      <div class="select-container">
        <mat-select class="custom-select" [(ngModel)]="selectedClientId" (selectionChange)="clientOnChange()">
          <mat-option value="" selected>Tous les Clients</mat-option>
          <mat-option *ngFor="let client of AllClients"
                      [value]="client.Id || '-'">
            {{client.Name}}
          </mat-option>
        </mat-select>
      </div>
    </div>

    <div class="filter-field">
      <label class="filter-label">Sites</label>
      <div class="select-container">
        <mat-select class="custom-select" [(ngModel)]="selectedSiteId" (selectionChange)="siteOnChange()">
          <mat-option value="">Tous les Sites</mat-option>
          <mat-option *ngFor="let site of Sites"
                      [value]="site.Id">
            {{site.Name}}
          </mat-option>
        </mat-select>
      </div>
    </div>

    <div class="filter-field">
      <label class="filter-label" >Local</label>
      <div class="select-container">
        <mat-select class="custom-select" [(ngModel)]="selectedLocalId" (selectionChange)="localOnChange()">
          <mat-option value="">Tous les Locaux</mat-option>
          <mat-option *ngFor="let local of locaux"
                      [value]="local.Id">
            {{local.Name}}
          </mat-option>
        </mat-select>
      </div>
    </div>
  </div>

  <div class="filters-actions">
    <button
      mat-stroked-button
      (click)="clearFilters()"
      class="clear-filters-button">
      <mat-icon>filter_alt_off</mat-icon>
      Effacer les Filtres
    </button>
  </div>
</div>
  <div class="filter-card">
  <div class="filter-controls">
    <div class="input-group">
      <label class="input-label">Rechercher des appareils</label>
      <div class="input-wrapper">
        <span class="icon-prefix">
          <mat-icon>search</mat-icon>
        </span>
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (ngModelChange)="onSearchChange()"
          class="custom-input"
          placeholder="Rechercher par nom, modèle ou marque"/>
        <button *ngIf="searchTerm" class="icon-button" (click)="clearSearch()">
          <mat-icon>close</mat-icon>
        </button>
      </div>
    </div> 
  </div>


</div>

  <div *ngIf="buttonon" class="toggle-buttons-wrapper">
          <!-- Toggle Card -->
      <div id="card1"
          class="toggle-card"
          (click)="ChangeText()"
          [class.active]="buttonon">
        <div class="toggle-card-content">
          <div class="toggle-card-icon">
            <mat-icon>{{ buttonon ? 'stop' : 'compare' }}</mat-icon>
          </div>
          <div class="toggle-card-text">
            {{ buttonon ? 'Arrêter la comparaison' : 'Comparer le temps réel' }}
          </div>
        </div>
      


    </div>
    </div>




  <!-- ───────────── GRID VIEW – MQTT live data ───────────── -->

<ng-container *ngIf="buttonon">

  <div class="devices-grid" *ngIf="currentView === 'grid' && (devices$ | async)?.length">

    <mat-card *ngFor="let device of devices$ | async"
              class="device-card"
              [ngClass]="'device-type-' + getDeviceCategory(device)">

      <mat-card-header class="device-header">
        <div mat-card-avatar class="device-icon">
          <mat-icon>{{ getDeviceIcon(device) }}</mat-icon>
        </div>

        <mat-card-title class="device-name">{{ device.DisplayName ? device.DisplayName : device.friendly_name }}</mat-card-title>

        <mat-card-subtitle>
          <div class="device-model">
            <div>
                <span > {{device.friendly_name}} </span>
            </div>
            <span *ngIf="device.definition?.vendor">{{ device.definition?.vendor }}</span>
            <span *ngIf="device.definition?.model"> {{ device.definition?.model }}</span>
              <div>
                <span *ngIf="device['LastSeen']"> {{ device['LastSeen'] }} </span>
              </div>
          </div>
        </mat-card-subtitle>

        <div class="right-part">
          <div class="actif" [ngClass]="{
                'active': device['Actif'] === 'Actif',
                'enveille': device['Actif'] === 'En Veille',
                'inactive': device['Actif'] === 'InActif',
                'never': device['Actif'] === 'Jamais Synchronisé'
              }">
            <span>{{ device['Actif'] }}</span>
          </div>
            <button class="options-btn" mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>

            <mat-menu #menu="matMenu">
                <button mat-menu-item (click)="openPopupForDevice(device)">
                  <mat-icon>edit</mat-icon>
                  <span>Modifier Le Nom</span>
                </button>            
            </mat-menu>
            <app-display-pop-up
              [show]="showPopup"
              [title]="'Modifier Le Nom'"
              [inputs]="selectedDevice ? [{ label: 'Nom', key: 'name', value: selectedDevice.DisplayName }] : []"
              [sensorData]="selectedDevice ? {friendly_name: selectedDevice.friendly_name, Model: selectedDevice.definition?.model} : {}"
              [confirmFn]="getConfirmFn(selectedDevice)"
              (close)="showPopup = false"
            />

        </div> 
        

      </mat-card-header>

      <mat-card-content>

        <div class="sensor-grid" *ngIf="getSensorDataFromBack(device).length > 0">
          <ng-container *ngFor="let sensorData of getSensorDataFromBack(device); trackBy: trackByKey">

            <div *ngIf="sensorData.isControl" class="control-item">
              <div class="toggle_Alimentation">
                <div class="control-label">
                  <mat-icon class="control-icon">{{ sensorData.icon }}</mat-icon>
                  <span class="label-text">{{ sensorData.label }}</span>
                </div>

                  <ng-container *ngIf="sensorData.key === 'state'">
                    <!-- Dropdown for open/close/stop -->
                    <ng-container *ngIf="['OPEN', 'CLOSE', 'STOP'].includes(sensorData.value)">
                      <div class="custom-select-container">
                        <mat-select
                          class="custom-select"
                          [value]="sensorData.value"
                          (selectionChange)="onStateSelectChange(device, sensorData, $event.value)">
                          <mat-option value="OPEN">Open</mat-option>
                          <mat-option value="CLOSE">Close</mat-option>
                          <mat-option value="STOP">Stop</mat-option>
                        </mat-select>
                      </div>
                    </ng-container>



                    <!-- Default ON/OFF toggle fallback -->
                    <mat-slide-toggle
                      *ngIf="!['OPEN', 'CLOSE', 'STOP'].includes(sensorData.value)"
                      [checked]="sensorData.value === 'ON' || sensorData.value === true"
                      [disabled]="!isControlEnabled(device)"
                      (change)="onToggleChange(device, sensorData, $event)">
                    </mat-slide-toggle>
                  </ng-container>
              </div>

              <div *ngIf="sensorData.key === 'brightness' && isDeviceStateOn(device)"
                   class="brightness-control">
                <mat-icon>{{ sensorData.icon }}</mat-icon>
                <mat-slider  [min]="sensorData.min || 0"
                             [max]="sensorData.max || 254"
                             [step]="1"
                             [disabled]="!isControlEnabled(device)"
                             (input)="onBrightnessChange(device, $event)"
                             (change)="onBrightnessChange(device, $event)">
                  <input matSliderThumb [value]="sensorData.value">
                </mat-slider>
                <span class="brightness-value">
                  {{ formatSensorValue(sensorData) }}{{ sensorData.unit || '' }}
                </span>
              </div>
            </div>

            <div *ngIf="!sensorData.isControl" class="sensor-item">

              <!-- Boolean sensor -->
              <ng-container *ngIf="sensorData.type === 'boolean'">
                <mat-icon class="sensor-icon">{{ sensorData.icon }}</mat-icon>
                <div class="sensor-info">
                  <span class="sensor-label">{{ sensorData.label }}</span>
                  <span class="sensor-value" [class]="'boolean-' + sensorData.value">
                    {{ formatSensorValue(sensorData) }}
                  </span>
                </div>
              </ng-container>

              <!-- Number sensor -->
              <ng-container *ngIf="sensorData.type === 'number'">
                <mat-icon class="sensor-icon">{{ sensorData.icon }}</mat-icon>
                <div class="sensor-info">
                  <span class="sensor-label">{{ sensorData.label }}</span>
                  <span class="sensor-value">
                    {{ formatSensorValue(sensorData) }}
                    <span class="sensor-unit" *ngIf="sensorData.unit">{{ sensorData.unit }}</span>
                  </span>
                </div>
                <mat-progress-bar
                  *ngIf="sensorData.key === 'battery'"
                  mode="determinate"
                  [value]="sensorData.value"
                  [ngClass]="{
                    'bar-green': sensorData.value > 80,
                    'bar-orange': sensorData.value <= 80 && sensorData.value >= 30,
                    'bar-red': sensorData.value < 30
                  }">
                </mat-progress-bar>
              </ng-container>

              <!-- String sensor -->
              <ng-container *ngIf="sensorData.type === 'string'">
                <mat-icon class="sensor-icon">{{ sensorData.icon }}</mat-icon>
                <div class="sensor-info">
                  <span class="sensor-label">{{ sensorData.label }}</span>
                  <span class="sensor-value">{{ formatSensorValue(sensorData) }}</span>
                </div>
              </ng-container>

              <!-- Object sensor -->
              <ng-container *ngIf="sensorData.type === 'object'">
                <mat-icon class="sensor-icon">{{ sensorData.icon }}</mat-icon>
                <div class="sensor-info">
                  <span class="sensor-label">{{ sensorData.label }}</span>
                  <code class="sensor-value">{{ sensorData.value | json }}</code>
                </div>
              </ng-container>

            </div>
          </ng-container>
        </div>

        <div *ngIf="!getSensorDataFromBack(device).length" class="no-data">
          <mat-icon>info_outline</mat-icon>
          <span>Aucune donnée disponible</span>
        </div>

        <ng-container *ngIf="getDeviceState(device)">
          <mat-divider></mat-divider>
          <div class="additional-info">

            <div class="info-item" *ngIf="getDeviceState(device)?.battery">
              <mat-icon [style.color]="getBatteryColor(getDeviceState(device)!.battery!)">
                {{ getBatteryIcon(getDeviceState(device)!.battery!) }}
              </mat-icon>
              <span>{{ getDeviceState(device)!.battery }}%</span>
            </div>

            <div class="info-item" *ngIf="getDeviceState(device)?.linkquality">
              <mat-icon [style.color]="getSignalColor(getDeviceState(device)!.linkquality!)">
                signal_wifi_4_bar
              </mat-icon>
              <span>{{ getDeviceState(device)!.linkquality }}</span>
            </div>

            <div class="info-item" *ngIf="getDeviceState(device)?.last_seen">
              <mat-icon>schedule</mat-icon>
              <span>{{ getDeviceState(device)!.last_seen | date:'short' }}</span>
            </div>

          </div>
        </ng-container>

      </mat-card-content>
    </mat-card>
  </div>

  <!-- TABLE VIEW – live data -->
  <div class="devices-table" *ngIf="currentView === 'table' && filteredDevices.length">
    <div class="mat-elevation-z2 table-container">
      <table mat-table [dataSource]="filteredDevices">

        <!-- Name -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef>Nom</th>
          <td mat-cell *matCellDef="let d">
            <div class="device-name-cell">
              <mat-icon class="table-device-icon">{{ getDeviceIcon(d) }}</mat-icon>
              {{ d.friendly_name }}
            </div>
          </td>
        </ng-container>

        <!-- Category -->
        <ng-container matColumnDef="category">
          <th mat-header-cell *matHeaderCellDef>Catégorie</th>
          <td mat-cell *matCellDef="let d">
            {{ getDeviceCategory(d) | titlecase }}
          </td>
        </ng-container>

        <!-- Vendor/Model -->
        <ng-container matColumnDef="vendorModel">
          <th mat-header-cell *matHeaderCellDef>Vendeur / Modèle</th>
          <td mat-cell *matCellDef="let d">
            <span *ngIf="d.definition?.vendor">{{ d.definition.vendor }}</span>
            <span *ngIf="d.definition?.model"> {{ d.definition.model }}</span>
            <span *ngIf="!d.definition || (!d.definition.vendor && !d.definition.model)">N/A</span>
          </td>
        </ng-container>

        <!-- Last seen -->
        <ng-container matColumnDef="lastSeen">
          <th mat-header-cell *matHeaderCellDef>Dernière Vue</th>
          <td mat-cell *matCellDef="let d">
            <span *ngIf="getDeviceState(d)?.last_seen">
              {{ getDeviceState(d)!.last_seen | date:'short' }}
            </span>
            <span *ngIf="!getDeviceState(d)?.last_seen">N/A</span>
          </td>
        </ng-container>

        <!-- Controls -->
        <ng-container matColumnDef="controls">
          <th mat-header-cell *matHeaderCellDef>Contrôles</th>
          <td mat-cell *matCellDef="let d">
            <div class="table-controls">
              <ng-container *ngFor="let s of getSensorDataFromBack(d); trackBy: trackByKey">
                <ng-container *ngIf="s.isControl">

                  <mat-slide-toggle *ngIf="s.key === 'state'"
                                    [checked]="s.value === 'ON' || s.value === true"
                                    [disabled]="!isControlEnabled(d)"
                                    (change)="onToggleChange(d, s, $event)"
                                    matTooltip="Activer / Désactiver">
                  </mat-slide-toggle>

                  <mat-slider *ngIf="s.key === 'brightness' && isDeviceStateOn(d)"
                              [min]="s.min || 0"
                              [max]="s.max || 254"
                              [step]="1"
                              [disabled]="!isControlEnabled(d)"
                              (input)="onBrightnessChange(d, $event)"
                              (change)="onBrightnessChange(d, $event)"
                              matTooltip="Luminosité">
                    <input matSliderThumb [value]="s.value">
                  </mat-slider>

                </ng-container>
              </ng-container>
              <span *ngIf="hasNoControls(d)">N/A</span>
            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </div>
  </div>

</ng-container>


  <!-- ───────────── GRID VIEW – backend data ───────────── -->
  <ng-container *ngIf="!buttonon">
    
    <div class="sync-row">
          <!-- Toggle Card -->
      <div id="card1"
          class="toggle-card"
          (click)="ChangeText()"
          [class.active]="buttonon">
        <div class="toggle-card-content">
          <div class="toggle-card-icon">
            <mat-icon>{{ buttonon ? 'stop' : 'compare' }}</mat-icon>
          </div>
          <div class="toggle-card-text">
            {{ buttonon ? 'Arrêter la comparaison' : 'Comparer le temps réel' }}
          </div>
    </div>
    </div>
    <div class="right-section">
      <div>
          <span class="sync-date">Date de dernière synchronisation : {{ DateSynchronisation }} </span>
          <span class="sync-date">Appareils Synchronisé : {{countDevicesSynchronised$ | async}} / {{devicesBackEnd.length}}</span>
      </div>
      <button mat-stroked-button (click)="refreshDevices()" class="actualiser-btn">
        <mat-icon>refresh</mat-icon>
        Actualiser
      </button>
    </div>

    </div>
    <div  class="devices-grid"
          *ngIf="currentView === 'grid' && filtredDevicesBackEnd.length">

      <mat-card *ngFor="let device of filtredDevicesBackEnd"
                class="device-card">

        <mat-card-header class="device-header">
          <div>
          
          </div>
          <div mat-card-avatar class="device-icon">
            <mat-icon>{{ getDeviceIcon(device) }}</mat-icon>
          </div>

          <mat-card-title class="device-name">{{ device.DisplayName ? device.DisplayName : device.friendly_name }}</mat-card-title>

          <mat-card-subtitle>
            <div class="device-model">
              <div>
                <span > {{device.friendly_name}} </span>
              </div>
              <span *ngIf="device.definition?.vendor">{{ device.definition.vendor }}</span>
              <span *ngIf="device.definition?.model"> {{ device.definition.model }}</span>
              <div>
                <span *ngIf="device['LastSeen']"> {{ device['LastSeen'] }} </span>
              </div>
            </div>
          </mat-card-subtitle>

        <div class="right-part">
          <div class="actif" [ngClass]="{
                'active': device['Actif'] === 'Actif',
                'enveille': device['Actif'] === 'En Veille',
                'inactive': device['Actif'] === 'InActif',
                'never': device['Actif'] === 'Jamais Synchronisé'
              }">
            <span>{{ device['Actif'] }}</span>
          </div>
            <button class="options-btn" mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>

            <mat-menu #menu="matMenu">
                <button mat-menu-item (click)="openPopupForDevice(device)">
                  <mat-icon>edit</mat-icon>
                  <span>Modifier Le Nom</span>
                </button>            
            </mat-menu>
            <app-display-pop-up
              [show]="showPopup"
              [title]="'Modifier Le Nom'"
              [inputs]="selectedDevice ? [{ label: 'Nom', key: 'name', value: selectedDevice.DisplayName }] : []"
              [sensorData]="selectedDevice ? {friendly_name: selectedDevice.friendly_name, Model: selectedDevice.definition?.model} : {}"
              [confirmFn]="getConfirmFn(selectedDevice)"
              (close)="showPopup = false"
            />

        </div>  


        </mat-card-header>

        <mat-card-content>

          <!-- Sensor / control items -->
          <div class="sensor-grid" *ngIf="getSensorDataFromBack(device).length">
            <ng-container *ngFor="let s of getSensorDataFromBack(device); trackBy: trackByKey">

              <!-- Controls -->
              <div *ngIf="s.isControl" class="control-item">
                <div class="toggle_Alimentation">
                  <div class="control-label">
                    <mat-icon class="control-icon">{{ s.icon }}</mat-icon>
                    <span class="label-text">{{ s.label }}</span>
                  </div>

                  <ng-container *ngIf="s.key === 'state'">
                    <!-- Dropdown for open/close/stop -->
                    <ng-container *ngIf="['OPEN', 'CLOSE', 'STOP'].includes(s.value)">
                      <div class="custom-select-container">
                        <mat-select
                          class="custom-select"
                          [value]="s.value"
                          (selectionChange)="onStateSelectChange(device, s, $event.value)">
                          <mat-option value="OPEN">Open</mat-option>
                          <mat-option value="CLOSE">Close</mat-option>
                          <mat-option value="STOP">Stop</mat-option>
                        </mat-select>
                      </div>
                    </ng-container>



                    <!-- Default ON/OFF toggle fallback -->
                    <mat-slide-toggle
                      *ngIf="!['OPEN', 'CLOSE', 'STOP'].includes(s.value)"
                      [checked]="s.value === 'ON' || s.value === true"
                      [disabled]="!isControlEnabled(device)"
                      (change)="onToggleChange(device, s, $event)">
                    </mat-slide-toggle>
                  </ng-container>

                </div>



                <div *ngIf="s.key === 'brightness' && isDeviceStateOn(device.RowData)"
                     class="brightness-control">
                  <mat-icon>{{ s.icon }}</mat-icon>
                  <mat-slider  [min]="s.min || 0"
                               [max]="s.max || 254"
                               [step]="1"
                               [disabled]="!isControlEnabled(device.RowData)"
                               (input)="onBrightnessChange(device.RowData, $event)"
                               (change)="onBrightnessChange(device.RowData, $event)">
                    <input matSliderThumb [value]="s.value">
                  </mat-slider>
                  <span class="brightness-value">
                    {{ formatSensorValue(s) }}{{ s.unit || '' }}
                  </span>
                </div>
              </div>

              <!-- Sensors -->
              <div *ngIf="!s.isControl" class="sensor-item">
                <mat-icon class="sensor-icon">{{ s.icon }}</mat-icon>
                <div class="sensor-info">
                  <span class="sensor-label">{{ s.label }}</span>
                  <span class="sensor-value">
                    {{ formatSensorValue(s) }}
                    <span class="sensor-unit" *ngIf="s.unit">{{ s.unit }}</span>
                  </span>
                </div>

                   <mat-progress-bar
                   *ngIf="s.key === 'battery'"
                    mode="determinate"
                    [value]="s.value"
                    [ngClass]="{
                      'bar-green' : s.value > 80,
                      'bar-orange': s.value <= 80 && s.value >= 30,
                      'bar-red'   : s.value < 30
                    }">
                  </mat-progress-bar>
              </div>
            </ng-container>
          </div>

          <!-- No data -->
          <div *ngIf="!getSensorDataFromBack(device).length" class="no-data">
            <mat-icon>info_outline</mat-icon>
            <span>Aucune donnée disponible</span>
          </div>

        </mat-card-content>
      </mat-card>
    </div>
  </ng-container>
  <div class="filter-results">
    {{ buttonon ? filteredDevices.length : filtredDevicesBackEnd.length }} / {{ buttonon ? devices.length : devicesBackEnd.length }}
  </div>

</div>
