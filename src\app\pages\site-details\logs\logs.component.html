<div class="logs-container">
  <!-- Modal -->
  <div
    class="log-modal-backdrop"
    *ngIf="selectedLog"
    (click)="closeLogDetails()"
  >
    <div class="log-modal" (click)="$event.stopPropagation()">
      <div class="log-modal-header">
        <h2>Détail du Journal</h2>
        <button
          class="close-btn"
          (click)="closeLogDetails()"
          aria-label="Fermer la fenêtre de détails"
        >
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <div class="log-modal-body">
        <p><strong>Niveau:</strong> {{ selectedLog.level || "INFO" }}</p>
        <p>
          <strong>Date:</strong>
          {{
            selectedLog.timestamp ? formatTimestamp(selectedLog.timestamp) : "—"
          }}
        </p>
        <p><strong>Résumé:</strong></p>
        <div class="summary-container">
          <div *ngIf="isSummaryLoading" class="summary-loading">
            <mat-icon class="spin">sync</mat-icon>
            <span>Génération du résumé...</span>
          </div>
          <pre *ngIf="!isSummaryLoading" class="summary-text">
    {{ selectedLog.summary || "Pas de message" }}
  </pre
          >
        </div>

        <div *ngIf="selectedLog.payload && hasMetaData(selectedLog.payload)">
          <h4>Données supplémentaires:</h4>
          <ul>
            <!-- Priority keys first -->
            <ng-container *ngFor="let key of priorityMetaKeys">
              <li
                *ngIf="
                  selectedLog.payload[key] !== undefined &&
                  selectedLog.payload[key] !== null
                "
              >
                <strong>{{ key }}:</strong>
                {{ getMetaValue(selectedLog.payload[key]) }}
              </li>
            </ng-container>

            <!-- Then other keys -->
            <li *ngFor="let key of getOtherMetaKeys(selectedLog.payload)">
              <strong>{{ key }}:</strong>
              {{ getMetaValue(selectedLog.payload[key]) }}
            </li>
          </ul>
        </div>

        <div *ngIf="selectedLog.topic">
          <p><strong>Topic:</strong> {{ selectedLog.topic }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Page Header -->
  <header class="page-header">
    <h1 class="page-title">Journaux du Local</h1>
    <p class="page-subtitle">
      Visualisez et filtrez les journaux de votre système en temps réel
    </p>
  </header>

  <!-- Filter Form -->
  <div class="filter-card">
    <form [formGroup]="logForm" class="filter-form">
      <div class="filter-grid">
        <div class="form-group">
          <label class="form-label" for="logType">
            <mat-icon>filter_list</mat-icon>
            Type de journal <span class="required">*</span>
          </label>
          <select
            id="logType"
            formControlName="logType"
            class="form-control"
            required
          >
            <option value="Tout">Tout afficher</option>
            <option value="info">Information</option>
            <option value="warning">Avertissement</option>
            <option value="error">Erreur</option>
            <option value="debug">Debug</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label" for="textSearch">
            <mat-icon>search</mat-icon>
            Recherche
          </label>
          <div class="search-wrapper">
            <input
              id="textSearch"
              type="text"
              formControlName="textSearch"
              class="form-control"
              placeholder="Rechercher dans les messages..."
            />
            <mat-icon class="search-icon">search</mat-icon>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label" for="pageSize">
            <mat-icon>view_list</mat-icon>
            Nombre d'entrées
          </label>
          <select id="pageSize" formControlName="pageSize" class="form-control">
            <option value="50">50 entrées</option>
            <option value="100">100 entrées</option>
            <option value="200">200 entrées</option>
            <option value="500">500 entrées</option>
          </select>
        </div>
      </div>

      <div class="filter-actions">
        <button type="button" class="btn btn-secondary" (click)="resetForm()">
          <mat-icon>refresh</mat-icon>
          Réinitialiser
        </button>
        <button
          type="button"
          class="btn btn-primary"
          (click)="fetchLogs()"
          [disabled]="isLoading"
        >
          <mat-icon>sync</mat-icon>
          Actualiser
        </button>
      </div>
    </form>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-state">
    <div class="spinner"></div>
    <span>Chargement des journaux en cours...</span>
  </div>

  <!-- Log Results -->
  <div
    *ngIf="!isLoading && filteredLogs && filteredLogs.length > 0"
    class="log-grid"
  >
    <div
      *ngFor="let log of filteredLogs"
      class="log-card"
      [ngClass]="getLogLevelClass(log.level || 'info')"
      (click)="openLogDetails(log)"
      tabindex="0"
      role="button"
      aria-label="Voir détails du journal"
    >
      <div class="log-header">
        <span
          class="log-level"
          [ngClass]="getLogLevelClass(log.level || 'info')"
        >
          {{ log.level || "INFO" | uppercase }}
        </span>
        <span class="log-timestamp">
          <mat-icon>schedule</mat-icon>
          {{ log.timestamp ? formatTimestamp(log.timestamp) : "—" }}
        </span>
      </div>

      <div
        class="log-message"
        [class.empty]="!log.message"
        [title]="
          log.message
            ? log.message
            : log.payload
            ? 'Détails disponibles'
            : 'Pas de message'
        "
      >
        <!-- Show message or fallback -->
        <ng-container
          *ngIf="log.message && log.message.length > 0; else noMessage"
        >
          {{
            log.message.length > 100
              ? (log.message | slice : 0 : 100) + "..."
              : log.message
          }}
        </ng-container>
        <ng-template #noMessage>
          <em>Pas de message disponible</em>
          <br />
          <!-- Optional: show part of payload or topic for context -->
          <small *ngIf="log.payload">
            Payload:
            {{
              log.payload.message || "Données supplémentaires disponibles"
                | slice : 0 : 50
            }}...
          </small>
          <small *ngIf="!log.payload && log.topic">
            Topic: {{ log.topic }}
          </small>
        </ng-template>
      </div>

      <div *ngIf="log.topic" class="log-topic">
        <mat-icon>topic</mat-icon>
        <span>{{ log.topic }}</span>
      </div>

      <div
        *ngIf="log.payload && hasMetaData(log.payload)"
        class="log-meta-indicator"
        title="Données supplémentaires disponibles"
      >
        <mat-icon>info</mat-icon>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div
    *ngIf="!isLoading && (!filteredLogs || filteredLogs.length === 0)"
    class="empty-state"
  >
    <div class="empty-icon">
      <mat-icon>inbox</mat-icon>
    </div>
    <h3 class="empty-title">Aucun journal trouvé</h3>
    <p class="empty-description">
      Aucun journal ne correspond aux critères de recherche sélectionnés pour ce
      local.
    </p>
  </div>
</div>
