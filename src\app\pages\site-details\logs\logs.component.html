<h2 class="log-title">Journaux du Local</h2>

<form class="log-filter-form" [formGroup]="logForm">
  <div class="form-grid">
    <div class="form-group log-type-group">
      <label for="logType">Type de journal <span class="required">*</span></label>
      <select id="logType" formControlName="logType" class="form-select custom-select" required>
        <option value="Tout">Tout</option>
        <option value="Info">Info</option>
        <option value="Avertissement">Avertissement</option>
        <option value="Erreur">Erreur</option>
      </select>
    </div>

    <div class="form-group search-group">
      <label for="textSearch">Texte à rechercher</label>
      <div class="search-input-wrapper">
        <input id="textSearch" type="text" formControlName="textSearch" placeholder="Rechercher dans les messages..." />
        <mat-icon class="search-icon">search</mat-icon>
      </div>
    </div>

    <div class="form-group page-size-group">
      <label for="pageSize">Nombre de logs</label>
      <select id="pageSize" formControlName="pageSize" class="form-select custom-select" required>
        <option value="100">100 logs</option>
        <option value="200">200 logs</option>
        <option value="500">500 logs</option>
        <option value="1000">1000 logs</option>
      </select>
    </div>

    <div class="form-actions">
      <button type="button" class="reset-button" (click)="logForm.reset()">
        <mat-icon>refresh</mat-icon>
        <span>Réinitialiser</span>
      </button>
    </div>
  </div>
</form>

<div class="loading-container" *ngIf="isLoading">
  <div class="loading-spinner"></div>
  <span>Chargement des journaux...</span>
</div>

<div class="log-results" *ngIf="filteredLogs != null; else noLogs">
  <div *ngFor="let log of logs" class="log-card" [ngClass]="log.level">
    <div class="log-header">
      <span class="log-level">{{ log.level | uppercase }}</span>
      <span class="log-time">{{ log.timestamp | date: 'short' }}</span>
    </div>

    <div class="log-message">
      {{ log.message }}
    </div>

    <div *ngIf="log.payload" class="log-meta">
      <div *ngFor="let key of getMetaKeys(log.payload)">
        <span class="meta-key">{{ key }}:</span>
        <span class="meta-value">{{ log.payload[key] }}</span>
      </div>
    </div>
  </div>
</div>

<ng-template #noLogs>
  <p class="log-empty">Aucun journal trouvé pour ce local.</p>
</ng-template>