
export interface RuleClientHierarchy {
  RuleId: string;
  RuleEnabled: boolean;
  RulePriority: number;
  ClientId: string;
  ClientOrganisationId: string;
  ClientRC: string;
  ClientRegion: string;
  ClientSIREN: string;
  ClientSIRET: string;
  ClientActiveEquipment: number;
  SiteId: string;
  SiteName: string;
  SiteEmail: string;
  SiteDescription: string;
  SiteImage: string;
  SiteContact: string;
  SiteManager: string;
  SitePhoneNumber: string;
  SiteStatus: string;
  SiteAddressComplement: string;
  SiteAddress: string;
  SiteEmployeeCount: number;
  SiteGrade: string;
  SiteLatitude: number | null;
  SiteLocalCount: number;
  SiteLongitude: number | null;
  LocalId: string;
  LocalCreatedAt: string;
  LocalCreatedBy: string;
  LocalLastUpdatedAt: string | null;
  LocalLastUpdatedBy: string | null;
  LocalDeletedAt: string | null;
  LocalDeletedBy: string | null;
  LocalArchitecture2DImage: string;
  LocalCapacity: number;
  LocalBaseTopicMQTT: string;
  LocalFloor: number;
  LocalImageLocal: string;
  LocalName: string;
  LocalSensorCount: number;
  LocalLatitude: number | null;
  LocalLongitude: number | null;
}
