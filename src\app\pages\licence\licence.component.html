<div class="container">

  <div class="client-billing-section">
    <div class="client-search-section">
      <div class="search-container">
        <div class="search-input-container" *ngIf="!selectedClient">
          <div class="search-input-glass-wrapper" *ngIf="!selectedClient">
            <span class="search-input-glass-icon material-icons">search</span>
            <input
              type="text"
              class="search-input glass"
              [ngClass]="{'input-error': showChooseClientError}"
              [(ngModel)]="searchQuery"
              (input)="filterClients()"
              (focus)="showDropdown = true"
              placeholder="Rechercher un client..."
              [readonly]="selectedClient !== null"
              autocomplete="off"
            >
            <button
              *ngIf="searchQuery && !selectedClient"
              class="search-input-glass-clear-btn"
              type="button"
              (click)="searchQuery=''; filterClients();"
              tabindex="-1"
              aria-label="Effacer"
            >
              <span class="material-icons">close</span>
            </button>
            <div class="choose-client-error" *ngIf="showChooseClientError">
              Veuillez choisir un client d'abord.
            </div>
            <div class="dropdown-glass" *ngIf="showDropdown && filteredClients.length" @slideInOut>
              <div 
                *ngFor="let client of filteredClients | slice:0:3" 
                class="dropdown-glass-item"
                (click)="selectClient(client)"
              >
                <div class="dropdown-glass-avatar">
                  <img *ngIf="client.ClientLogo" [src]="'data:image/png;base64,' + client.ClientLogo"
                       [alt]="client.Name" />
                  <span *ngIf="!client.ClientLogo" class="dropdown-glass-avatar-fallback">
                    {{ client.Name.charAt(0) | uppercase }}
                  </span>
                </div>
                <div class="dropdown-glass-info">
                  <div class="dropdown-glass-name">{{ client.Name }}</div>
                </div>
                <!-- Removed selected check icon to fix NG9 error -->
              </div>
            </div>
          </div>
        </div>
        
        <div class="search-input-container" *ngIf="selectedClient" style="display: flex; align-items: center; justify-content: center;">
          <div class="selected-client-container improved-client-card" @growIn>
            <div class="client-avatar-section">
              <img *ngIf="selectedClient.ClientLogo" [src]="'data:image/png;base64,' + selectedClient.ClientLogo"
                   [alt]="selectedClient.Name" class="client-image-large">
            </div>
            <div class="client-details-section">
              <div class="client-name-row">
                <span class="client-name-large">{{ selectedClient.Name }}</span>
              </div>
              <div class="client-subtitle">Gérez les licences et options pour ce client</div>
            </div>
            <button class="clear-button improved-clear-btn" 
                    (click)="onClearSelection()" 
                    title="Désélectionner le client">
              <span class="material-icons">close</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="license-grid">
    <div 
      *ngFor="let licence of licences; let i = index" 
      class="license-card"
      [ngClass]="{'popular': i === 1, 'featured': i === 2}"
    >
      <div class="card-header">
        <h3>{{ licence.Name }}</h3>
        <p class="card-description">{{ licence.Description }}</p>
      </div>
      <ul class="features-list">
        <li *ngFor="let option of options">
          <ng-container *ngIf="isOptionLinkedToLicence(licence.Id, option.Id)">
            <div class="feature-item">
              <label class="custom-checkbox">
                <input 
                  type="checkbox"
                  [checked]="isOptionChecked(licence, option.Id)"
                  (change)="toggleOption(licence, option.Id, $event)"
                  [disabled]="!selectedClient"
                >
                <span class="checkmark" [ngClass]="{
                  'checked-green': isOptionChecked(licence, option.Id),
                  'unchecked-red': !isOptionChecked(licence, option.Id)
                }">
                  <span 
                    class="material-icons verified-icon"
                    *ngIf="isOptionChecked(licence, option.Id)"
                    style="color: #10b981; opacity: 1; transform: scale(1);"
                  >check_circle</span>
                  <span 
                    class="material-icons cancel-icon"
                    *ngIf="!isOptionChecked(licence, option.Id)"
                    style="color: #ef4444; opacity: 1; transform: scale(1);"
                  >cancel</span>
                </span>
              </label>
              <div class="feature-details">
                <span class="feature-name">{{ option.Name }}</span>
              </div>
            </div>
          </ng-container>
        </li>
      </ul>

      <!-- If licence is assigned to client, show "Annuler la licence" only for the assigned one,
           and show "Upgrade" for other cards (not assigned) -->
      <div *ngIf="showAssignedActions(licence)" style="display: flex; gap: 10px;">
        <!-- Show "Annuler la licence" if checked options match DB, else show "Sauvegarder" -->
        <button *ngIf="!hasOptionChanges(licence)" 
                class="select-btn" 
                style="background:#ef4444; flex: 1;" 
                (click)="cancelLicenceForClient(licence)">
          Annuler la licence
        </button>
        <button *ngIf="hasOptionChanges(licence)" 
                class="select-btn" 
                style="background:#10b981; flex: 1;" 
                (click)="openModifyPopup(licence)">
          Sauvegarder
        </button>
      </div>
      <div *ngIf="!showAssignedActions(licence) && selectedClient && isAffecterDisabled(licence)">
        <button class="select-btn" style="background:#2561a9;" (click)="upgradeLicenceForClient(licence)">
          Mise à niveau
        </button>
      </div>
      <button 
        *ngIf="!showAssignedActions(licence) && (!selectedClient || !isAffecterDisabled(licence))"
        class="select-btn" 
        (click)="selectLicense(licence)"
        [disabled]="!selectedClient"
      >
        Affecter
      </button>

      <!-- Remove inline Save/Cancel buttons for modification -->
    </div>
  </div>

  <!-- Confirmation Popup for Affectation -->
  <div class="overlay" *ngIf="showConfirmationPopup && !showUpgradePopup" @fadeInOut>
    <div class="confirmation-popup" @slideDown>
      <div class="popup-header">
        <h3>Confirmer l'affectation de la licence</h3>
        <button class="close-popup-btn" (click)="cancelLicenseApplication()">
          <span class="material-icons">close</span>
        </button>
      </div>
      
      <div class="popup-content">
        <div class="confirmation-message">
          <span class="material-icons confirmation-icon">help_outline</span>
          <p>Êtes-vous sûr de vouloir affecter la licence <strong>{{ selectedLicenseForConfirmation?.Name }}</strong> au client <strong>{{ selectedClient?.Name }}</strong> ?</p>
        </div>
        
        <div class="client-license-summary">
          <div class="summary-item">
            <span class="label">Client :</span>
            <div class="client-info-summary">
              <img *ngIf="selectedClient?.ClientLogo" [src]="'data:image/png;base64,' + selectedClient?.ClientLogo"
                   [alt]="selectedClient?.Name" class="client-image">
              <span>{{ selectedClient?.Name }}</span>
            </div>
          </div>
          
          <div class="summary-item">
            <span class="label">Licence :</span>
            <span class="license-name">{{ selectedLicenseForConfirmation?.Name }}</span>
          </div>

          <div class="summary-item">
            <span class="label">Total des options :</span>
            <span class="license-total">
              {{ selectedLicenseForConfirmation ? (getLicenceOptionsTotal(selectedLicenseForConfirmation) | number:'1.0-2') : '0.00' }} €
            </span>
          </div>

          <!-- Added: Show only checked options -->
          <div class="summary-item" *ngIf="selectedLicenseForConfirmation">
            <span class="label">Options choisies :</span>
            <ul class="chosen-options-list" style="list-style: none; margin: 0; padding: 0; display: flex; flex-wrap: wrap; gap: 8px;">
              <li *ngFor="let option of getOptionsForLicence(selectedLicenseForConfirmation)" style="margin: 0; padding: 0; border: none; background: none;">
                <ng-container *ngIf="isOptionChecked(selectedLicenseForConfirmation, option.Id)">
                  <span style="
                    display: inline-block;
                    background: linear-gradient(90deg, #10b981 60%, #34d399 100%);
                    color: #fff;
                    border-radius: 18px;
                    padding: 5px 16px;
                    font-size: 1rem;
                    font-weight: 600;
                    margin-bottom: 2px;
                    letter-spacing: 0.01em;
                    box-shadow: 0 2px 8px rgba(16,185,129,0.10);
                    border: none;
                  ">
                    {{ option.Name }}
                  </span>
                </ng-container>
              </li>
            </ul>
          </div>

          <div class="summary-item" *ngIf="selectedLicenseForConfirmation && selectedClient && clientSubscription">
            <span class="label">Début :</span>
            <span>{{ formatDate(clientSubscription.DateDebut) }}</span>
          </div>
          <div class="summary-item" *ngIf="selectedLicenseForConfirmation && selectedClient && clientSubscription">
            <span class="label">Fin :</span>
            <span>{{ formatDate(clientSubscription.DateFin) }}</span>
          </div>
        </div>
      </div>
      
      <div class="popup-actions">
        <button class="cancel-btn" (click)="cancelLicenseApplication()">
          Annuler
        </button>
        <button class="confirm-btn" (click)="confirmLicenseApplication()">
          Oui, affecter la licence
        </button>
      </div>
    </div>
  </div>

  <!-- Popup d'annulation de licence -->
  <div class="overlay" *ngIf="showCancelPopup" @fadeInOut>
    <div class="confirmation-popup" @slideDown>
      <div class="popup-header">
        <h3>Confirmer l'annulation de la licence</h3>
        <button class="close-popup-btn" (click)="closeCancelPopup()">
          <span class="material-icons">close</span>
        </button>
      </div>
      <div class="popup-content">
        <div class="confirmation-message">
          <span class="material-icons confirmation-icon">help_outline</span>
          <p>
            Êtes-vous sûr de vouloir annuler la licence
            <strong>{{ licenceToCancel?.Name }}</strong>
            pour le client
            <strong>{{ selectedClient?.Name }}</strong> ?
          </p>
        </div>
        <div class="client-license-summary">
          <div class="summary-item">
            <span class="label">Client :</span>
            <div class="client-info-summary">
              <img *ngIf="selectedClient?.ClientLogo" [src]="'data:image/png;base64,' + selectedClient?.ClientLogo"
                   [alt]="selectedClient?.Name" class="client-image">
              <span>{{ selectedClient?.Name }}</span>
            </div>
          </div>
          <div class="summary-item">
            <span class="label">Licence :</span>
            <span class="license-name">{{ licenceToCancel?.Name }}</span>
          </div>
        </div>
      </div>
      <div class="popup-actions">
        <button class="cancel-btn" (click)="closeCancelPopup()">
          Annuler
        </button>
        <button class="confirm-btn" (click)="confirmCancelLicenceForClient()" style="background: linear-gradient(135deg, #ef4444, red);">
          Oui, annuler la licence
        </button>
      </div>
    </div>
  </div>

  <!-- Notification de succès d'annulation -->
  <div class="success-notification" *ngIf="showCancelNotification" @slideDown>
    <div class="notification-content">
      <span class="material-icons success-icon">check_circle</span>
      <div class="notification-text">
        <h4>Licence annulée avec succès !</h4>
        <p>La licence a été annulée pour le client.</p>
      </div>
      <button class="close-notification-btn" (click)="showCancelNotification = false">
        <span class="material-icons">close</span>
      </button>
    </div>
  </div>

  <!-- Notification de succès d'affectation -->
  <div class="success-notification" *ngIf="showSuccessNotification" @slideDown>
    <div class="notification-content">
      <span class="material-icons success-icon">check_circle</span>
      <div class="notification-text">
        <h4>Licence affectée avec succès !</h4>
        <p>La licence a été affectée avec succès.</p>
      </div>
      <button class="close-notification-btn" (click)="closeSuccessNotification()">
        <span class="material-icons">close</span>
      </button>
    </div>
  </div>

  <!-- Upgrade Confirmation Popup -->
  <div class="overlay" *ngIf="showUpgradePopup" @fadeInOut>
    <div class="confirmation-popup" @slideDown>
      <div class="popup-header">
        <h3>Confirmer la mise à niveau de la licence</h3>
        <button class="close-popup-btn" (click)="cancelUpgradePopup()">
          <span class="material-icons">close</span>
        </button>
      </div>
      <div class="popup-content">
        <div class="confirmation-message">
          <span class="material-icons confirmation-icon">upgrade</span>
          <p>
            Êtes-vous sûr de vouloir mettre à niveaula licence <strong>{{ selectedLicenseForConfirmation?.Name }}</strong> pour le client
            <strong>{{ selectedClient?.Name }}</strong> ?
          </p>
        </div>
        <div class="client-license-summary">
          <div class="summary-item">
            <span class="label">Client :</span>
            <div class="client-info-summary">
              <img *ngIf="selectedClient?.ClientLogo" [src]="'data:image/png;base64,' + selectedClient?.ClientLogo"
                   [alt]="selectedClient?.Name" class="client-image">
              <span>{{ selectedClient?.Name }}</span>
            </div>
          </div>
          <div class="summary-item">
            <span class="label">Ancienne licence :</span>
            <span class="license-name">{{ oldLicence?.Name }}</span>
          </div>
          <div class="summary-item">
            <span class="label">Anciennes options :</span>
            <ul class="chosen-options-list" style="list-style: none; margin: 0; padding: 0; display: flex; flex-wrap: wrap; gap: 8px;">
              <li *ngFor="let option of oldOptions" style="margin: 0; padding: 0;">
                <span style="
                  display: inline-block;
                  background: #e5e7eb;
                  color: #374151;
                  border-radius: 18px;
                  padding: 5px 16px;
                  font-size: 1rem;
                  font-weight: 600;
                  margin-bottom: 2px;
                  letter-spacing: 0.01em;
                  border: none;
                ">
                  {{ option.Name }}
                </span>
              </li>
            </ul>
          </div>
          <div class="summary-item">
            <span class="label">Nouvelle licence :</span>
            <span class="license-name">{{ selectedLicenseForConfirmation?.Name }}</span>
          </div>
          <div class="summary-item">
            <span class="label">Nouvelles options :</span>
            <ul class="chosen-options-list" style="list-style: none; margin: 0; padding: 0; display: flex; flex-wrap: wrap; gap: 8px;">
              <li *ngFor="let option of getNewOptionsForUpgrade()" style="margin: 0; padding: 0;">
                <span style="
                  display: inline-block;
                  background: linear-gradient(90deg, #10b981 60%, #34d399 100%);
                  color: #fff;
                  border-radius: 18px;
                  padding: 5px 16px;
                  font-size: 1rem;
                  font-weight: 600;
                  margin-bottom: 2px;
                  letter-spacing: 0.01em;
                  border: none;
                ">
                  {{ option.Name }}
                </span>
              </li>
            </ul>
          </div>
          <div class="summary-item">
            <span class="label">Ancien total proratisé :</span>
            <span class="license-total">{{ proratedOldForUpgrade | number:'1.0-2' }} €</span>
          </div>
          
          <div class="summary-item">
            <span class="label">Nouveau total proratisé :</span>
            <span class="license-total">{{ proratedNewForUpgrade | number:'1.0-2' }} €</span>
          </div>

          <div class="summary-item">
            <span class="label">Total du mois :</span>
            <span class="license-total">{{ proratedTotalForUpgrade | number:'1.0-2' }} €</span>
          </div>

          <!-- In the upgrade popup, show old/new subscription dates if needed -->
          <div class="summary-item" *ngIf="oldLicence && clientSubscription">
            <span class="label">Ancienne date début :</span>
            <span>{{ formatDate(clientSubscription.DateDebut) }}</span>
          </div>
        </div>
      </div>
      <div class="popup-actions">
        <button class="cancel-btn" (click)="cancelUpgradePopup()">
          Annuler
        </button>
        <button class="confirm-btn" (click)="confirmUpgradeLicence()">
          Oui, mise a niveau la licence
        </button>
      </div>
    </div>
  </div>

  <!-- Modification Confirmation Popup -->
  <div class="overlay" *ngIf="showModifyPopup" @fadeInOut>
    <div class="confirmation-popup" @slideDown>
      <div class="popup-header">
        <h3>Confirmer la modification de la licence</h3>
        <button class="close-popup-btn" (click)="cancelModifyLicence()">
          <span class="material-icons">close</span>
        </button>
      </div>
      <div class="popup-content">
        <div class="confirmation-message">
          <span class="material-icons confirmation-icon">edit</span>
          <p>
            Êtes-vous sûr de vouloir modifier les options de la licence
            <strong>{{ modifyingLicence?.Name }}</strong>
            pour le client
            <strong>{{ selectedClient?.Name }}</strong> ?
          </p>
        </div>
        <div class="client-license-summary">
          <div class="summary-item">
            <span class="label">Client :</span>
            <div class="client-info-summary">
              <img *ngIf="selectedClient?.ClientLogo" [src]="'data:image/png;base64,' + selectedClient?.ClientLogo"
                   [alt]="selectedClient?.Name" class="client-image">
              <span>{{ selectedClient?.Name }}</span>
            </div>
          </div>
          <div class="summary-item">
            <span class="label">Licence :</span>
            <span class="license-name">{{ modifyingLicence?.Name }}</span>
          </div>
          <div class="summary-item">
            <span class="label">Anciennes options :</span>
            <ul class="chosen-options-list" style="list-style: none; margin: 0; padding: 0; display: flex; flex-wrap: wrap; gap: 8px;">
              <li *ngFor="let option of getOldOptionsForModifying()" style="margin: 0; padding: 0;">
                <span style="
                  display: inline-block;
                  background: #e5e7eb;
                  color: #374151;
                  border-radius: 18px;
                  padding: 5px 16px;
                  font-size: 1rem;
                  font-weight: 600;
                  margin-bottom: 2px;
                  letter-spacing: 0.01em;
                  border: none;
                ">
                  {{ option.Name }}
                </span>
              </li>
            </ul>
          </div>
          <div class="summary-item">
            <span class="label">Nouvelles options :</span>
            <ul class="chosen-options-list" style="list-style: none; margin: 0; padding: 0; display: flex; flex-wrap: wrap; gap: 8px;">
              <li *ngFor="let option of getNewOptionsForModifying()" style="margin: 0; padding: 0;">
                <span style="
                  display: inline-block;
                  background: linear-gradient(90deg, #10b981 60%, #34d399 100%);
                  color: #fff;
                  border-radius: 18px;
                  padding: 5px 16px;
                  font-size: 1rem;
                  font-weight: 600;
                  margin-bottom: 2px;
                  letter-spacing: 0.01em;
                  border: none;
                ">
                  {{ option.Name }}
                </span>
              </li>
            </ul>
          </div>
          <div class="summary-item">
            <span class="label">Ancien total :</span>
            <span class="license-total">{{ getOldTotalForModifying() | number:'1.0-2' }} €</span>
          </div>
          <!-- Show "Prix à payer" if adding, "Prix à rembourser" if removing, or both if both -->
          <div class="summary-item" *ngIf="getModificationType() === 'add'">
            <span class="label">Prix à payer :</span>
            <span class="license-total">{{ getPriceToPayForModification() | number:'1.0-2' }} €</span>
          </div>
          <div class="summary-item" *ngIf="getModificationType() === 'remove'">
            <span class="label">Prix à rembourser :</span>
            <span class="license-total">{{ getPriceToRefundForModification() | number:'1.0-2' }} €</span>
          </div>
          <div class="summary-item" *ngIf="getModificationType() === 'both'">
            <span class="label">Prix à payer :</span>
            <span class="license-total">{{ getPriceToPayForModification() | number:'1.0-2' }} €</span>
          </div>
          <div class="summary-item" *ngIf="getModificationType() === 'both'">
            <span class="label">Prix à rembourser :</span>
            <span class="license-total">{{ getPriceToRefundForModification() | number:'1.0-2' }} €</span>
          </div>
          <div class="summary-item">
            <span class="label">Nouveau total :</span>
            <span class="license-total">{{ getNewTotalWithOldForModifying() | number:'1.0-2' }} €</span>
          </div>
        </div>
      </div>
      <div class="popup-actions">
        <button class="cancel-btn" (click)="cancelModifyLicence()">
          Annuler
        </button>
        <button class="confirm-btn" (click)="confirmSaveModifiedLicenceOptions()">
          Oui, sauvegarder les modifications
        </button>
      </div>
    </div>
  </div>

  <!-- Save or Discard Popup for Unsaved Option Changes -->
  <div class="overlay" *ngIf="showSaveOrDiscardPopup" @fadeInOut>
    <div class="confirmation-popup" @slideDown>
      <div class="popup-header">
        <h3>Modifications non sauvegardées</h3>
        <button class="close-popup-btn" (click)="showSaveOrDiscardPopup = false">
          <span class="material-icons">close</span>
        </button>
      </div>
      <div class="popup-content">
        <div class="confirmation-message">
          <span class="material-icons confirmation-icon">warning</span>
          <p>
            Vous avez des modifications non sauvegardées pour la licence
            <strong>{{ licenceWithUnsavedChanges?.Name }}</strong>.
            Voulez-vous sauvegarder les modifications avant de désélectionner le client ?
          </p>
        </div>
      </div>
      <div class="popup-actions">
        <button class="cancel-btn" (click)="discardChangesAndClearSelection()">
          Ne pas sauvegarder
        </button>
        <button class="confirm-btn" (click)="confirmSaveAndClearSelection()">
          Sauvegarder et continuer
        </button>
      </div>
    </div>
  </div>

  <!-- Save notification -->
  <div class="success-notification" *ngIf="showSaveNotification" @slideDown>
    <div class="notification-content">
      <span class="material-icons success-icon">check_circle</span>
      <div class="notification-text">
        <h4>Options sauvegardées avec succès !</h4>
        <p>Les modifications ont été enregistrées.</p>
      </div>
      <button class="close-notification-btn" (click)="showSaveNotification = false">
        <span class="material-icons">close</span>
      </button>
    </div>
  </div>

  <!-- No Option Checked Popup -->
  <div class="overlay" *ngIf="showNoOptionCheckedPopup" @fadeInOut>
    <div class="confirmation-popup" @slideDown>
      <div class="popup-header">
        <h3>Attention</h3>
      </div>
      <div class="popup-content">
        <div class="confirmation-message">
          <span class="material-icons confirmation-icon" style="color:#ef4444;">error_outline</span>
          <p>Vous devez cocher au moins une option.</p>
        </div>
      </div>
      <div class="popup-actions">
        <button class="confirm-btn" (click)="closeNoOptionCheckedPopup()">OK</button>
      </div>
    </div>
  </div>
</div>