<div class="container">

  <div class="client-billing-section">
    <div class="client-search-section">
      <div class="search-container">
        <div class="search-input-container">
          <input
            type="text"
            class="search-input"
            [ngClass]="{'input-error': showChooseClientError}"
            [(ngModel)]="searchQuery"
            (input)="filterClients()"
            (focus)="showDropdown = true"
            placeholder="Rechercher un client..."
          >
          <span class="search-icon material-icons">search</span>
          
          <div class="choose-client-error" *ngIf="showChooseClientError">
            Veuillez choisir un client d'abord.
          </div>
          
          <div class="dropdown" *ngIf="showDropdown && filteredClients.length" @slideInOut>
            <div 
              *ngFor="let client of filteredClients" 
              class="dropdown-item"
              (click)="selectClient(client)"
            >
              <img *ngIf="client.ClientLogo" [src]="'data:image/png;base64,' + client.ClientLogo"
                   [alt]="client.Name" class="client-logo">
              <div class="client-text">
                <div class="client-name">{{ client.Name }}</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="selected-client-container" *ngIf="selectedClient" @growIn>
          <div class="client-info">
              <img *ngIf="selectedClient.ClientLogo" [src]="'data:image/png;base64,' + selectedClient.ClientLogo"
                   [alt]="selectedClient.Name" class="client-image">
            <div class="client-name">{{ selectedClient.Name }}</div>
          </div>
          <button class="clear-button" (click)="clearSelection()">
            <span class="material-icons">close</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="license-grid">
    <div 
      *ngFor="let licence of licences; let i = index" 
      class="license-card"
      [ngClass]="{'popular': i === 1, 'featured': i === 2}"
    >
      <div class="card-header">
        <h3>{{ licence.Name }}</h3>
        <p class="card-description">{{ licence.Description }}</p>
      </div>
      <ul class="features-list">
        <li *ngFor="let lo of getLicenceOptionsForCard(licence)">
          <div class="feature-item">
            <label class="custom-checkbox">
              <input 
                type="checkbox"
                [checked]="isOptionChecked(licence, lo.optionId)"
                (change)="toggleOption(licence, lo.optionId, $event)"
              >
              <span class="checkmark" [ngClass]="{
    'checked-green': isOptionChecked(licence, lo.optionId),
    'unchecked-red': !isOptionChecked(licence, lo.optionId)
}">
  <span 
    class="material-icons verified-icon"
    *ngIf="isOptionChecked(licence, lo.optionId)"
    style="color: #10b981; opacity: 1; transform: scale(1);"
  >check_circle</span>
  <span 
    class="material-icons cancel-icon"
    *ngIf="!isOptionChecked(licence, lo.optionId)"
    style="color: #ef4444; opacity: 1; transform: scale(1);"
  >cancel</span>
</span>
            </label>
            <div class="feature-details">
              <span class="feature-name">{{ lo.optionName }}</span>
            </div>
          </div>
        </li>
      </ul>

      <!-- If licence is assigned to client, show "Annuler la licence" only for the assigned one,
           and show "Upgrade" for other cards (not assigned) -->
      <div *ngIf="showAssignedActions(licence)">
        <button class="select-btn" style="background:#ef4444;" (click)="cancelLicenceForClient(licence)">
          Annuler la licence
        </button>
      </div>
      <div *ngIf="!showAssignedActions(licence) && selectedClient && isAffecterDisabled(licence)">
        <button class="select-btn" style="background:#6366f1;" (click)="upgradeLicenceForClient(licence)">
          Upgrade
        </button>
      </div>
      <button 
        *ngIf="!showAssignedActions(licence) && (!selectedClient || !isAffecterDisabled(licence))"
        class="select-btn" 
        (click)="selectLicense(licence)">
        Affecter
      </button>
    </div>
  </div>

  <!-- Confirmation Popup for Affectation -->
  <div class="overlay" *ngIf="showConfirmationPopup && !showUpgradePopup" @fadeInOut>
    <div class="confirmation-popup" @slideDown>
      <div class="popup-header">
        <h3>Confirmer l'affectation de la licence</h3>
        <button class="close-popup-btn" (click)="cancelLicenseApplication()">
          <span class="material-icons">close</span>
        </button>
      </div>
      
      <div class="popup-content">
        <div class="confirmation-message">
          <span class="material-icons confirmation-icon">help_outline</span>
          <p>Êtes-vous sûr de vouloir affecter la licence <strong>{{ selectedLicenseForConfirmation?.Name }}</strong> au client <strong>{{ selectedClient?.Name }}</strong> ?</p>
        </div>
        
        <div class="client-license-summary">
          <div class="summary-item">
            <span class="label">Client :</span>
            <div class="client-info-summary">
             <img *ngIf="selectedClient?.ClientLogo" [src]="'data:image/png;base64,' + selectedClient?.ClientLogo"
                   [alt]="selectedClient?.Name" class="client-image">
              <span>{{ selectedClient?.Name }}</span>
            </div>
          </div>
          
          <div class="summary-item">
            <span class="label">Licence :</span>
            <span class="license-name">{{ selectedLicenseForConfirmation?.Name }}</span>
          </div>

          <div class="summary-item">
            <span class="label">Total des options :</span>
            <span class="license-total">
              {{ selectedLicenseForConfirmation ? (getLicenceOptionsTotal(selectedLicenseForConfirmation) | number:'1.0-2') : '0.00' }} €
            </span>
          </div>
        </div>
      </div>
      
      <div class="popup-actions">
        <button class="cancel-btn" (click)="cancelLicenseApplication()">
          Annuler
        </button>
        <button class="confirm-btn" (click)="confirmLicenseApplication()">
          Oui, affecter la licence
        </button>
      </div>
    </div>
  </div>

  <!-- Popup d'annulation de licence -->
  <div class="overlay" *ngIf="showCancelPopup" @fadeInOut>
    <div class="confirmation-popup" @slideDown>
      <div class="popup-header">
        <h3>Confirmer l'annulation de la licence</h3>
        <button class="close-popup-btn" (click)="closeCancelPopup()">
          <span class="material-icons">close</span>
        </button>
      </div>
      <div class="popup-content">
        <div class="confirmation-message">
          <span class="material-icons confirmation-icon">help_outline</span>
          <p>
            Êtes-vous sûr de vouloir annuler la licence
            <strong>{{ licenceToCancel?.Name }}</strong>
            pour le client
            <strong>{{ selectedClient?.Name }}</strong> ?
          </p>
        </div>
        <div class="client-license-summary">
          <div class="summary-item">
            <span class="label">Client :</span>
            <div class="client-info-summary">
              <img *ngIf="selectedClient?.ClientLogo" [src]="'data:image/png;base64,' + selectedClient?.ClientLogo"
                   [alt]="selectedClient?.Name" class="client-image">
              <span>{{ selectedClient?.Name }}</span>
            </div>
          </div>
          <div class="summary-item">
            <span class="label">Licence :</span>
            <span class="license-name">{{ licenceToCancel?.Name }}</span>
          </div>
        </div>
      </div>
      <div class="popup-actions">
        <button class="cancel-btn" (click)="closeCancelPopup()">
          Annuler
        </button>
        <button class="confirm-btn" (click)="confirmCancelLicenceForClient()" style="background: linear-gradient(135deg, #ef4444, red);">
          Oui, annuler la licence
        </button>
      </div>
    </div>
  </div>

  <!-- Notification de succès d'annulation -->
  <div class="success-notification" *ngIf="showCancelNotification" @slideDown>
    <div class="notification-content">
      <span class="material-icons success-icon">check_circle</span>
      <div class="notification-text">
        <h4>Licence annulée avec succès !</h4>
        <p>La licence a été annulée pour le client.</p>
      </div>
      <button class="close-notification-btn" (click)="showCancelNotification = false">
        <span class="material-icons">close</span>
      </button>
    </div>
  </div>

  <!-- Notification de succès d'affectation -->
  <div class="success-notification" *ngIf="showSuccessNotification" @slideDown>
    <div class="notification-content">
      <span class="material-icons success-icon">check_circle</span>
      <div class="notification-text">
        <h4>Licence affectée avec succès !</h4>
        <p>La licence a été affectée avec succès.</p>
      </div>
      <button class="close-notification-btn" (click)="closeSuccessNotification()">
        <span class="material-icons">close</span>
      </button>
    </div>
  </div>

  <!-- Upgrade Confirmation Popup -->
  <div class="overlay" *ngIf="showUpgradePopup" @fadeInOut>
    <div class="confirmation-popup" @slideDown>
      <div class="popup-header">
        <h3>Confirmer l'upgrade de la licence</h3>
        <button class="close-popup-btn" (click)="cancelUpgradePopup()">
          <span class="material-icons">close</span>
        </button>
      </div>
      <div class="popup-content">
        <div class="confirmation-message">
          <span class="material-icons confirmation-icon">upgrade</span>
          <p>
            Êtes-vous sûr de vouloir <strong>upgrader</strong> la licence pour le client
            <strong>{{ selectedClient?.Name }}</strong> ?
          </p>
        </div>
        <div class="client-license-summary">
          <div class="summary-item">
            <span class="label">Client :</span>
            <div class="client-info-summary">
              <img *ngIf="selectedClient?.ClientLogo" [src]="'data:image/png;base64,' + selectedClient?.ClientLogo"
                   [alt]="selectedClient?.Name" class="client-image">
              <span>{{ selectedClient?.Name }}</span>
            </div>
          </div>
          <div class="summary-item">
            <span class="label">Ancienne licence :</span>
            <span class="license-name">{{ oldLicence?.Name }}</span>
            <span class="license-price" *ngIf="oldLicence">
              ({{ getLicenceOptionsTotal(oldLicence) | currency }})
            </span>
          </div>
          <div class="summary-item">
            <span class="label">Nouvelle licence :</span>
            <span class="license-name">{{ selectedLicenseForConfirmation?.Name }}</span>
            <span class="license-price" *ngIf="selectedLicenseForConfirmation">
              ({{ getLicenceOptionsTotal(selectedLicenseForConfirmation) | currency }})
            </span>
          </div>
        </div>
      </div>
      <div class="popup-actions">
        <button class="cancel-btn" (click)="cancelUpgradePopup()">
          Annuler
        </button>
        <button class="confirm-btn" (click)="confirmUpgradeLicence()">
          Oui, upgrader la licence
        </button>
      </div>
    </div>
  </div>
</div>