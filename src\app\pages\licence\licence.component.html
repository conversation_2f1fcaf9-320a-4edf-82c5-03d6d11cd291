<ngx-spinner 
  bdColor="rgba(0,0,0,0.8)" 
  size="medium" 
  color="#fff" 
  type="ball-beat" 
  [fullScreen]="true"
  name="spinner">
  <p style="color: #10b981">Chargement...</p>
</ngx-spinner>

<div class="container">
  <div class="header improved-header">
          <div class="arrow-returning improved-arrow-returning" *ngIf="showCard" style="z-index: 1000; margin-left: 250px; margin-top: 50px;">
  <button class="return-button improved-return-btn" (click)="showCard = false">
    <span class="material-icons">arrow_back</span>
  </button>
</div>
    <h1 class="title improved-title">Gestion des Licence</h1>
    <p class="subtitle improved-subtitle">
      Gérez les licences et options pour vos clients. Sélectionnez un client pour voir les licences disponibles.
    </p>
  </div>

  <!-- Table container is visible only if showCard is false -->
  <div class="table-container" *ngIf="!showCard">
    <div class="class-search header">
      <div class="header-container improved-header-container">
        <div class="container-position">
          <div class="search-bar-table">
            <div class="search-bar-inner improved-search-bar-inner">
              <input
                type="text"
                class="search-input glass improved-search-input"
                [(ngModel)]="searchQuery"
                (keyup.enter)="filterClientsTable()"
                placeholder="Rechercher un client..."
                autocomplete="off"
              />
              <button class="search-btn improved-search-btn" (click)="filterClientsTable()">
                <span class="material-icons">search</span>
              </button>
              <button class="confirm-btn improved-confirm-btn" (click)="ShowLicenceCard()">
                Ajouter une licence
              </button>
            </div>
          </div>
        </div>
        <app-generic-table
          [data]="getActiveTableRows()"  
          [headers]="['Client', 'Licence', 'Date debut', 'Date Fin', 'Statut', 'Prix', 'Frequence de paiement']"
          [keys]="['ClientName', 'LicenceName', 'DateDebut', 'DateFin', 'Status', 'price', 'PaymentFrequency']"
          [actions]="['Modifier', 'Annuler']"
          (actionTriggered)="onTableAction($event)">
        </app-generic-table>
        <div class="table-pagination-controls improved-table-pagination-controls">
          <div class="table-pagination-size">
            <span>Items par page:</span>
            <select [(ngModel)]="tablePageSize" (change)="onTablePageSizeChange()">
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="50">50</option>
            </select>
          </div>
          
          <div class="table-pagination-info">
            Affichage de {{tableFirstItem}} à {{tableLastItem}} sur {{filteredTableRows.length}} éléments
          </div>
          
          <div class="table-pagination-buttons improved-pagination-buttons">
            <button (click)="prevTablePage()" [disabled]="tableCurrentPage === 0">
              &lt;
            </button>
            <span class="table-page-numbers improved-table-page-numbers">
              <span class="pagination-page-number">
                {{ tableCurrentPage + 1 }}
              </span>
            </span>
            <button (click)="nextTablePage()" [disabled]="tableCurrentPage === tableTotalPages - 1">
              &gt;
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Show licence-cards-container only if showCard is true -->
  <div class="licence-cards-container" *ngIf="showCard">
    <div class="client-billing-section">
      <div class="search-container">
        <div class="search-input-container" *ngIf="!selectedClient">
          <div class="search-input-glass-wrapper" *ngIf="!selectedClient">
            <span class="search-input-glass-icon material-icons">search</span>
            <input
              type="text"
              class="search-input glass"
              [ngClass]="{'input-error': showChooseClientError}"
              [(ngModel)]="searchQuery"
              (keyup.enter)="filterClients()"
              (focus)="showDropdown = true; filterClients()"
              placeholder="Rechercher un client..."
              [readonly]="selectedClient !== null"
              autocomplete="off"
            />
            <button
              *ngIf="searchQuery && !selectedClient"
              class="search-input-glass-clear-btn"
              type="button"
              (click)="filterClients()"
              tabindex="-1"
              aria-label="Effacer"
            >
            </button>
            <div class="choose-client-error" *ngIf="showChooseClientError">
              Veuillez choisir un client d'abord.
            </div>
            <div class="dropdown-glass" *ngIf="showDropdown && filteredClients.length" @slideInOut>
              <div 
                *ngFor="let client of filteredClients" 
                class="dropdown-glass-item"
                (click)="selectClient(client)"
              >
                <div class="dropdown-glass-info">
                  <div class="dropdown-glass-name">{{ client.Name }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="search-input-container" *ngIf="selectedClient" style="display: flex; align-items: center; justify-content: center;">
          <div class="selected-client-container improved-client-card" @growIn>
            <div class="client-avatar-section">
              <img *ngIf="selectedClient.ClientLogo" [src]="'data:image/png;base64,' + selectedClient.ClientLogo"
                   [alt]="selectedClient.Name" class="client-image-large">
            </div>
            <div class="client-details-section">
              <div class="client-name-row">
                <span class="client-name-large">{{ selectedClient.Name }}</span>
              </div>
              <div class="client-subtitle">Gérez les licences et options pour ce client</div>
            </div>
            <button class="clear-button improved-clear-btn" 
                    (click)="onClearSelection()" 
                    title="Désélectionner le client">
              <span class="material-icons">close</span>
            </button>
          </div>
        </div>
        <div class="select-type-frequence-payement improved-select-type-frequence-payement">
          <div class="frequence-payement improved-frequence-payement improved-frequence-inline">
            <select class="improved-select" [(ngModel)]="selectedPaymentFrequency">
              <option *ngFor="let freq of paymentFrequencies" [value]="freq.value">
                {{ freq.label }}
              </option>
            </select>
            <ng-container *ngIf="selectedPaymentFrequency === 'Personnalisé'">
              <div class="custom-date-input-wrapper improved-date-inline">
                <label class="date-label">Date fin :</label>
                <input
                  type="date"
                  class="custom-date-input improved-date-input"
                  [(ngModel)]="customDateFin"
                  placeholder="Date fin"
                />
              </div>
            </ng-container>
          </div>
        </div>
      </div>
</div>

<div class="license-grid improved-license-grid"
  [ngClass]="{'single-card': pagedLicences.length === 1}"
  (mousedown)="onTouchStart($event)" (mouseup)="onTouchEnd($event)"
  (touchstart)="onTouchStart($event)" (touchend)="onTouchEnd($event)"
  style="overflow-x: hidden; position: relative;"
>
  <!-- Card for "Ajouter une licence" mode -->
  <ng-container *ngIf="isAffecterCard()">
    <div 
      *ngFor="let licence of pagedLicences; let i = index" 
      class="license-card improved-license-card"
      [ngClass]="{'single-card-inner': pagedLicences.length === 1}"
      [@cardSlide]="{
        value: currentPage,
        params: {
          enterX: cardAnimDirection === 'next' ? 100 : -100,
          leaveX: cardAnimDirection === 'next' ? -100 : 100
        }
      }"
    >
      <div class="card-header improved-card-header">
        <h3 class="improved-title">{{ licence.Name }}</h3>
        <p class="card-description improved-card-description" [title]="licence.Description">
          {{ licence.Description ? (licence.Description | slice:0:80) : '' }}<span *ngIf="licence.Description && licence.Description.length > 80">...</span>
        </p>
      </div>
      <div class="features-list-wrapper">
        <ul class="features-list improved-features-list">
          <li *ngFor="let option of getOptionsForLicence(licence)">
            <div class="feature-item">
              <label class="custom-checkbox">
                <input 
                  type="checkbox"
                  [checked]="isOptionChecked(licence, option.Id)"
                  (change)="toggleOption(licence, option.Id, $event)"
                  [disabled]="!selectedClient"
                />
                <span class="checkmark" [ngClass]="{
                  'checked-green': isOptionChecked(licence, option.Id),
                  'unchecked-red': !isOptionChecked(licence, option.Id)
                }">
                  <span 
                    class="material-icons verified-icon"
                    *ngIf="isOptionChecked(licence, option.Id)"
                    style="color: #10b981; opacity: 1; transform: scale(1);"
                  >check_circle</span>
                  <span 
                    class="material-icons cancel-icon"
                    *ngIf="!isOptionChecked(licence, option.Id)"
                    style="color: #ef4444; opacity: 1; transform: scale(1);"
                  >cancel</span>
                </span>
              </label>
              <div class="feature-details">
                <span class="feature-name">{{ option.Name }}</span>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <div class="license-actions">
        <button class="select-btn"
                (click)="selectLicense(licence)"
                [disabled]="!selectedClient">
          Affecter
        </button>
      </div>
    </div>
  </ng-container>

  <!-- Cards for "Modifier" mode -->
  <ng-container *ngIf="isModifyCard() && clientSubscription">
    <div class="license-grid improved-license-grid"
      [ngClass]="{'single-card': pagedLicences.length === 1}"
      (mousedown)="onTouchStart($event)" (mouseup)="onTouchEnd($event)"
      (touchstart)="onTouchStart($event)" (touchend)="onTouchEnd($event)"
      style="overflow-x: hidden; position: relative;"
    >
      <ng-container *ngFor="let licence of pagedLicences">
        <div 
          class="license-card improved-license-card"
          [ngClass]="{'single-card-inner': pagedLicences.length === 1}"
          [@cardSlide]="{
            value: currentPage,
            params: {
              enterX: cardAnimDirection === 'next' ? 100 : -100,
              leaveX: cardAnimDirection === 'next' ? -100 : 100
            }
          }"
        >
          <div class="card-header improved-card-header">
            <h3 class="improved-title">{{ licence.Name }}</h3>
            <p class="card-description improved-card-description" [title]="licence.Description" style="white-space: normal; max-height: 60px; overflow: hidden;">
              {{ licence.Description }}
            </p>
          </div>
          <div class="features-list-wrapper">
            <ul class="features-list improved-features-list">
              <li *ngFor="let option of getOptionsForLicence(licence)">
                <div class="feature-item">
                  <label class="custom-checkbox">
                    <input 
                      type="checkbox"
                      [checked]="isOptionChecked(licence, option.Id)"
                      (change)="toggleOption(licence, option.Id, $event)"
                      [disabled]="!selectedClient"
                    />
                    <span class="checkmark" [ngClass]="{
                      'checked-green': isOptionChecked(licence, option.Id),
                      'unchecked-red': !isOptionChecked(licence, option.Id)
                    }">
                      <span 
                        class="material-icons verified-icon"
                        *ngIf="isOptionChecked(licence, option.Id)"
                        style="color: #10b981; opacity: 1; transform: scale(1);"
                      >check_circle</span>
                      <span 
                        class="material-icons cancel-icon"
                        *ngIf="!isOptionChecked(licence, option.Id)"
                        style="color: #ef4444; opacity: 1; transform: scale(1);"
                      >cancel</span>
                    </span>
                  </label>
                  <div class="feature-details">
                    <span class="feature-name">{{ option.Name }}</span>
                  </div>
                </div>
              </li>
            </ul>
          </div>
          <div class="license-actions">
            <button 
              *ngIf="isLicenceAssignedToClient(licence) && hasOptionChanges(licence) && !isSubscriptionCancelled(licence)"
              class="select-btn"
              style="background: linear-gradient(135deg, #49b38e, #2c7744);"
              (click)="openModifyPopup(licence)">
              Sauvegarder
            </button>

            <button
              *ngIf="!isLicenceAssignedToClient(licence) && !isSubscriptionCancelled(licence)"
              class="select-btn"
              style="background: linear-gradient(135deg, #49b38e, #2c7744);"
              (click)="upgradeLicenceForClient(licence)">
              Mise à niveau
            </button>
          </div>
        </div>
      </ng-container>
    </div>
  </ng-container>
</div>

  <div class="overlay" *ngIf="showConfirmationPopup && !showUpgradePopup" @fadeInOut>
    <div class="confirmation-popup" @slideDown>
      <div class="popup-header">
        <h3>Confirmer l'affectation de la licence</h3>
        <button class="close-popup-btn" (click)="cancelLicenseApplication()">
          <span class="material-icons">close</span>
        </button>
      </div>
      
      <div class="popup-content">
        <div class="confirmation-message">
          <span class="material-icons confirmation-icon">help_outline</span>
          <p>Êtes-vous sûr de vouloir affecter la licence <strong>{{ selectedLicenseForConfirmation?.Name }}</strong> au client <strong>{{ selectedClient?.Name }}</strong> ?</p>
        </div>
        
        <div class="client-license-summary">
          <div class="summary-item">
            <span class="label">Client :</span>
            <div class="client-info-summary">
              <img *ngIf="selectedClient?.ClientLogo" [src]="'data:image/png;base64,' + selectedClient?.ClientLogo"
                   [alt]="selectedClient?.Name" class="client-image">
              <span>{{ selectedClient?.Name }}</span>
            </div>
          </div>
          
          <div class="summary-item">
            <span class="label">Licence :</span>
            <span class="license-name">{{ selectedLicenseForConfirmation?.Name }}</span>
          </div>

          <div class="summary-item">
            <span class="label">Total des options :</span>
            <span class="license-total">
              {{ selectedLicenseForConfirmation ? (getLicenceOptionsTotal(selectedLicenseForConfirmation) | number:'1.0-2') : '0.00' }} €
            </span>
          </div>

          <div class="summary-item" *ngIf="selectedLicenseForConfirmation">
            <span class="label">Options choisies :</span>
           <ul class="chosen-options-list" style="list-style: none; margin: 0; padding: 0; display: flex; flex-wrap: wrap; gap: 8px;">
              <li *ngFor="let option of getSelectedOptionsForConfirmation()" style="margin: 0; padding: 0; border: none; background: none;">
                <span style="
                  display: inline-block;
                  background: linear-gradient(90deg, #10b981 60%, #34d399 100%);
                  color: #fff;
                  border-radius: 18px;
                  padding: 5px 16px;
                  font-size: 1rem;
                  font-weight: 600;
                  margin-bottom: 2px;
                  letter-spacing: 0.01em;
                  box-shadow: 0 2px 8px rgba(16,185,129,0.10);
                  border: none;
                ">
                  {{ option.Name }}
                </span>
              </li>
            </ul>
          </div>

          <div class="summary-item" *ngIf="selectedLicenseForConfirmation && selectedClient && clientSubscription">
            <span class="label">Début :</span>
            <span>{{ formatDate(clientSubscription.DateDebut) }}</span>
          </div>
          <div class="summary-item" *ngIf="selectedLicenseForConfirmation && selectedClient && clientSubscription">
            <span class="label">Fin :</span>
            <span>{{ formatDate(clientSubscription.DateFin) }}</span>
          </div>
        </div>
      </div>
      
      <div class="popup-actions">
        <button class="cancel-btn" (click)="cancelLicenseApplication()">
          Annuler
        </button>
        <button class="confirm-btn" (click)="confirmLicenseApplication()">
          Oui, affecter la licence
        </button>
      </div>
    </div>
  </div>

  <div class="overlay" *ngIf="showCancelPopup" @fadeInOut>
    <div class="confirmation-popup" @slideDown>
      <div class="popup-header">
        <h3>Confirmer l'annulation de la licence</h3>
        <button class="close-popup-btn" (click)="closeCancelPopup()">
          <span class="material-icons">close</span>
        </button>
      </div>
      <div class="popup-content">
        <div class="confirmation-message">
          <span class="material-icons confirmation-icon">help_outline</span>
          <p>
            Êtes-vous sûr de vouloir annuler la licence
            <strong>{{ licenceToCancel?.Name }}</strong>
            pour le client
            <strong>{{ selectedClient?.Name }}</strong> ?
          </p>
        </div>
        <div class="client-license-summary">
          <div class="summary-item">
            <span class="label">Client :</span>
            <div class="client-info-summary">
              <img *ngIf="selectedClient?.ClientLogo" [src]="'data:image/png;base64,' + selectedClient?.ClientLogo"
                   [alt]="selectedClient?.Name" class="client-image">
              <span>{{ selectedClient?.Name }}</span>
            </div>
          </div>
          <div class="summary-item">
            <span class="label">Licence :</span>
            <span class="license-name">{{ licenceToCancel?.Name }}</span>
          </div>
        </div>
      </div>
      <div class="popup-actions">
        <button class="cancel-btn" (click)="closeCancelPopup()">
          Annuler
        </button>
        <button class="confirm-btn" (click)="confirmCancelLicenceForClient()">
          Confirmer
        </button>
      </div>
    </div>
  </div>

  <div class="success-notification" *ngIf="showCancelNotification" @slideDown>
    <div class="notification-content">
      <span class="material-icons success-icon">check_circle</span>
      <div class="notification-text">
        <h4>Licence annulée avec succès !</h4>
        <p>La licence a été annulée pour le client.</p>
      </div>
      <button class="close-notification-btn" (click)="showCancelNotification = false">
        <span class="material-icons">close</span>
      </button>
    </div>
  </div>

  <div class="success-notification" *ngIf="showSuccessNotification" @slideDown>
    <div class="notification-content">
      <span class="material-icons success-icon">check_circle</span>
      <div class="notification-text">
        <h4>Licence affectée avec succès !</h4>
        <p>La licence a été affectée avec succès.</p>
      </div>
      <button class="close-notification-btn" (click)="closeSuccessNotification()">
        <span class="material-icons">close</span>
      </button>
    </div>
  </div>

  <div class="overlay" *ngIf="showUpgradePopup" @fadeInOut>
    <div class="confirmation-popup" @slideDown>
      <div class="popup-header">
        <h3>Confirmer la mise à niveau de la licence</h3>
        <button class="close-popup-btn" (click)="cancelUpgradePopup()">
          <span class="material-icons">close</span>
        </button>
      </div>
      <div class="popup-content">
        <div class="confirmation-message">
          <span class="material-icons confirmation-icon">upgrade</span>
          <p>
            Êtes-vous sûr de vouloir mettre à niveaula licence <strong>{{ selectedLicenseForConfirmation?.Name }}</strong> pour le client
            <strong>{{ selectedClient?.Name }}</strong> ?
          </p>
        </div>
        <div class="client-license-summary">
          <div class="summary-item">
            <span class="label">Client :</span>
            <div class="client-info-summary">
              <img *ngIf="selectedClient?.ClientLogo" [src]="'data:image/png;base64,' + selectedClient?.ClientLogo"
                   [alt]="selectedClient?.Name" class="client-image">
              <span>{{ selectedClient?.Name }}</span>
            </div>
          </div>
          <div class="summary-item">
            <span class="label">Ancienne licence :</span>
            <span class="license-name">{{ oldLicence?.Name }}</span>
          </div>
          <div class="summary-item">
            <span class="label">Anciennes options :</span>
            <ul class="chosen-options-list" style="list-style: none; margin: 0; padding: 0; display: flex; flex-wrap: wrap; gap: 8px;">
              <li *ngFor="let option of oldOptions" style="margin: 0; padding: 0;">
                <span style="
                  display: inline-block;
                  background: #e5e7eb;
                  color: #374151;
                  border-radius: 18px;
                  padding: 5px 16px;
                  font-size: 1rem;
                  font-weight: 600;
                  margin-bottom: 2px;
                  letter-spacing: 0.01em;
                  border: none;
                ">
                  {{ option.Name }}
                </span>
              </li>
            </ul>
          </div>
          <div class="summary-item">
            <span class="label">Nouvelle licence :</span>
            <span class="license-name">{{ selectedLicenseForConfirmation?.Name }}</span>
          </div>
          <div class="summary-item">
            <span class="label">Nouvelles options :</span>
            <ul class="chosen-options-list" style="list-style: none; margin: 0; padding: 0; display: flex; flex-wrap: wrap; gap: 8px;">
              <li *ngFor="let option of getNewOptionsForUpgrade()" style="margin: 0; padding: 0;">
                <span style="
                  display: inline-block;
                  background: linear-gradient(90deg, #10b981 60%, #34d399 100%);
                  color: #fff;
                  border-radius: 18px;
                  padding: 5px 16px;
                  font-size: 1rem;
                  font-weight: 600;
                  margin-bottom: 2px;
                  letter-spacing: 0.01em;
                  border: none;
                ">
                  {{ option.Name }}
                  <!-- Show prorata if this option is in addedOptionsProrata -->
                  <!-- <ng-container *ngIf="addedOptionsProrata?.length">
                    <ng-container *ngFor="let added of addedOptionsProrata">
                      <ng-container *ngIf="added.Id === option.Id">
                        <span style="font-size:0.95em; color:#ffe082; background:none; font-weight:400; margin-left:8px;">
                          (Prorata: {{ added.prorata | number:'1.0-2' }} € pour {{ added.days }} jours)
                        </span>
                      </ng-container>
                    </ng-container>
                  </ng-container> -->
                </span>
              </li>
            </ul>
          </div>
          <div class="summary-item">
            <span class="label">Ancien total:</span>
            <span class="license-total">{{ proratedOldForUpgrade | number:'1.0-2' }} €</span>
          </div>
          <div class="summary-item" *ngIf="proratedDiferencePay > 0">
            <span class="label">Prix à payer:</span>
            <span class="licence-total">{{ proratedDiferencePay | number:'1.0-2' }} €</span>
          </div>
          <!-- Show "Prix à rembourser" only if greater than 0 -->
          <div class="summary-item" *ngIf="proratedDiferenceReturn > 0">
            <span class="label">Prix à rembourser:</span>
            <span class="licence-total">- {{ proratedDiferenceReturn | number:'1.0-2' }} €</span>
          </div>
          <div class="summary-item">
            <span class="label">Nouveau prix :</span>
            <span class="license-total">{{ proratedTotalForUpgrade | number:'1.0-2' }} €</span>
          </div>
          <!-- In the upgrade popup, show old/new subscription dates if needed -->
        </div>
      </div>
      <div class="popup-actions">
        <button class="cancel-btn" (click)="cancelUpgradePopup()">
          Annuler
        </button>
        <button class="confirm-btn" (click)="confirmUpgradeLicence()">
          Oui, mise a niveau la licence
        </button>
      </div>
    </div>
  </div>

  <!-- Modification Confirmation Popup -->
  <div class="overlay" *ngIf="showModifyPopup" @fadeInOut>
    <div class="confirmation-popup" @slideDown>
      <div class="popup-header">
        <h3>Confirmer la modification de la licence</h3>
        <button class="close-popup-btn" (click)="cancelModifyLicence()">
          <span class="material-icons">close</span>
        </button>
      </div>
      <div class="popup-content">
        <div class="confirmation-message">
          <span class="material-icons confirmation-icon">edit</span>
          <p>
            Êtes-vous sûr de vouloir modifier les options de la licence
            <strong>{{ modifyingLicence?.Name }}</strong>
            pour le client
            <strong>{{ selectedClient?.Name }}</strong> ?
          </p>
        </div>
        <div class="client-license-summary">
          <div class="summary-item">
            <span class="label">Client :</span>
            <div class="client-info-summary">
              <img *ngIf="selectedClient?.ClientLogo" [src]="'data:image/png;base64,' + selectedClient?.ClientLogo"
                   [alt]="selectedClient?.Name" class="client-image">
              <span>{{ selectedClient?.Name }}</span>
            </div>
          </div>
          <div class="summary-item">
            <span class="label">Licence :</span>
            <span class="license-name">{{ modifyingLicence?.Name }}</span>
          </div>
          <div class="summary-item">
            <span class="label">Anciennes options :</span>
            <ul class="chosen-options-list" style="list-style: none; margin: 0; padding: 0; display: flex; flex-wrap: wrap; gap: 8px;">
              <li *ngFor="let option of getOldOptionsForModifying()" style="margin: 0; padding: 0;">
                <span style="
                  display: inline-block;
                  background: #e5e7eb;
                  color: #374151;
                  border-radius: 18px;
                  padding: 5px 16px;
                  font-size: 1rem;
                  font-weight: 600;
                  margin-bottom: 2px;
                  letter-spacing: 0.01em;
                  border: none;
                ">
                  {{ option.Name }}
                </span>
              </li>
            </ul>
          </div>
          <div class="summary-item">
            <span class="label">Nouvelles options :</span>
            <ul class="chosen-options-list" style="list-style: none; margin: 0; padding: 0; display: flex; flex-wrap: wrap; gap: 8px;">
              <li *ngFor="let option of getNewOptionsForModifying()" style="margin: 0; padding: 0;">
                <span style="
                  display: inline-block;
                  background: linear-gradient(90deg, #10b981 60%, #34d399 100%);
                  color: #fff;
                  border-radius: 18px;
                  padding: 5px 16px;
                  font-size: 1rem;
                  font-weight: 600;
                  margin-bottom: 2px;
                  letter-spacing: 0.01em;
                  border: none;
                ">
                  {{ option.Name }}
                </span>
              </li>
            </ul>
          </div>
          <div class="summary-item">
            <span class="label">Ancien total :</span>
            <span class="license-total">{{ getOldTotalForModifying() | number:'1.0-2' }} €</span>
          </div>
          <!-- Show "Prix à payer" if adding, "Prix à rembourser" if removing, or both if both -->
          <div class="summary-item" *ngIf="getModificationType() === 'add'">
            <span class="label">Prix à payer :</span>
            <span class="license-total">{{ getPriceToPayForModification() | number:'1.0-2' }} €</span>
          </div>
          <div class="summary-item" *ngIf="getModificationType() === 'remove'">
            <span class="label">Prix à rembourser :</span>
            <span class="license-total">- {{ getPriceToRefundForModification() | number:'1.0-2' }} €</span>
          </div>
          <div class="summary-item" *ngIf="getModificationType() === 'both'">
            <span class="label">Prix à payer :</span>
            <span class="license-total">{{ getPriceToPayForModification() | number:'1.0-2' }} €</span>
          </div>
          <div class="summary-item" *ngIf="getModificationType() === 'both'">
            <span class="label">Prix à rembourser :</span>
            <span class="license-total">- {{ getPriceToRefundForModification() | number:'1.0-2' }} €</span>
          </div>
          <div class="summary-item">
            <span class="label">Nouveau total :</span>
            <span class="license-total">{{ getNewTotalWithOldForModifying() | number:'1.0-2' }} €</span>
          </div>
        </div>
      </div>
      <div class="popup-actions">
        <button class="cancel-btn" (click)="cancelModifyLicence()">
          Annuler
        </button>
        <button class="confirm-btn" (click)="confirmSaveModifiedLicenceOptions()">
          Oui, sauvegarder les modifications
        </button>
      </div>
    </div>
  </div>

  <!-- Save or Discard Popup for Unsaved Option Changes -->
  <div class="overlay" *ngIf="showSaveOrDiscardPopup" @fadeInOut>
    <div class="confirmation-popup" @slideDown>
      <div class="popup-header">
        <h3>Modifications non sauvegardées</h3>
        <button class="close-popup-btn" (click)="showSaveOrDiscardPopup = false">
          <span class="material-icons">close</span>
        </button>
      </div>
      <div class="popup-content">
        <div class="confirmation-message">
          <span class="material-icons confirmation-icon">warning</span>
          <p>
            Vous avez des modifications non sauvegardées pour la licence
            <strong>{{ licenceWithUnsavedChanges?.Name }}</strong>.
            Voulez-vous sauvegarder les modifications avant de désélectionner le client ?
          </p>
        </div>
      </div>
      <div class="popup-actions">
        <button class="cancel-btn" (click)="discardChangesAndClearSelection()">
          Ne pas sauvegarder
        </button>
        <button class="confirm-btn" (click)="confirmSaveAndClearSelection()">
          Sauvegarder et continuer
        </button>
      </div>
    </div>
  </div>

  <!-- Save notification -->
  <div class="success-notification" *ngIf="showSaveNotification" @slideDown>
    <div class="notification-content">
      <span class="material-icons success-icon">check_circle</span>
      <div class="notification-text">
        <h4>Options sauvegardées avec succès !</h4>
        <p>Les modifications ont été enregistrées.</p>
      </div>
      <button class="close-notification-btn" (click)="showSaveNotification = false">
        <span class="material-icons">close</span>
      </button>
    </div>
  </div>

  <!-- No Option Checked Popup -->
  <div class="overlay" *ngIf="showNoOptionCheckedPopup" @fadeInOut>
    <div class="confirmation-popup" @slideDown>
      <div class="popup-header">
        <h3>Attention</h3>
      </div>
      <div class="popup-content">
        <div class="confirmation-message">
          <span class="material-icons confirmation-icon" style="color:#ef4444;">error_outline</span>
          <p>Vous devez cocher au moins une option.</p>
        </div>
      </div>
      <div class="popup-actions">
        <button class="confirm-btn" (click)="closeNoOptionCheckedPopup()">OK</button>
      </div>
    </div>
  </div>

<!-- Pagination -->
 <div class="pagination-controls improved-pagination-controls">
    <button (click)="prevPage()" [disabled]="currentPage === 0" class="pagination-btn prev-btn improved-pagination-btn">
      &lt;
    </button>
    <span class="pagination-indicator improved-pagination-indicator">
      <span class="pagination-page-number improved-pagination-page-number">
        {{ currentPage + 1 }}
      </span>
    </span>
    <button (click)="nextPage()" [disabled]="currentPage === totalPages - 1" class="pagination-btn next-btn improved-pagination-btn">
      &gt;
    </button>
  </div>
  </div>
</div>