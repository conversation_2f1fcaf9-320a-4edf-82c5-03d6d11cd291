import {
  trigger,
  transition,
  style,
  animate,
  query,
  stagger,
  group,
} from '@angular/animations';

export const logsAnimations = [
  // Fade in animation
  trigger('fadeIn', [
    transition(':enter', [
      style({ opacity: 0, transform: 'translateY(20px)' }),
      animate(
        '300ms ease-out',
        style({ opacity: 1, transform: 'translateY(0)' })
      ),
    ]),
    transition(':leave', [
      animate(
        '200ms ease-in',
        style({ opacity: 0, transform: 'translateY(-10px)' })
      ),
    ]),
  ]),

  // Slide in animation for modal
  trigger('slideIn', [
    transition(':enter', [
      style({
        opacity: 0,
        transform: 'translateY(50px) scale(0.95)',
        filter: 'blur(5px)',
      }),
      animate(
        '400ms cubic-bezier(0.4, 0, 0.2, 1)',
        style({
          opacity: 1,
          transform: 'translateY(0) scale(1)',
          filter: 'blur(0)',
        })
      ),
    ]),
    transition(':leave', [
      animate(
        '200ms ease-in',
        style({
          opacity: 0,
          transform: 'translateY(30px) scale(0.98)',
          filter: 'blur(2px)',
        })
      ),
    ]),
  ]),

  // Stagger animation for log grid
  trigger('staggerIn', [
    transition('* => *', [
      query(
        ':enter',
        [
          style({ opacity: 0, transform: 'translateY(30px) scale(0.95)' }),
          stagger('50ms', [
            animate(
              '400ms cubic-bezier(0.4, 0, 0.2, 1)',
              style({ opacity: 1, transform: 'translateY(0) scale(1)' })
            ),
          ]),
        ],
        { optional: true }
      ),
    ]),
  ]),

  // Individual card animation
  trigger('cardIn', [
    transition(':enter', [
      style({
        opacity: 0,
        transform: 'translateY(30px) scale(0.95) rotateX(10deg)',
        filter: 'blur(3px)',
      }),
      animate(
        '400ms cubic-bezier(0.4, 0, 0.2, 1)',
        style({
          opacity: 1,
          transform: 'translateY(0) scale(1) rotateX(0deg)',
          filter: 'blur(0)',
        })
      ),
    ]),
    transition(':leave', [
      group([
        animate(
          '200ms ease-in',
          style({
            opacity: 0,
            transform: 'scale(0.95)',
          })
        ),
        animate(
          '200ms ease-in',
          style({
            filter: 'blur(2px)',
          })
        ),
      ]),
    ]),
  ]),

  // Hover animation for interactive elements
  trigger('hoverScale', [
    transition(':enter', [style({ transform: 'scale(1)' })]),
    transition('* => hovered', [
      animate('200ms ease-out', style({ transform: 'scale(1.05)' })),
    ]),
    transition('hovered => *', [
      animate('200ms ease-out', style({ transform: 'scale(1)' })),
    ]),
  ]),

  // Loading spinner entrance
  trigger('spinnerIn', [
    transition(':enter', [
      style({ opacity: 0, transform: 'scale(0.8)' }),
      animate('300ms ease-out', style({ opacity: 1, transform: 'scale(1)' })),
    ]),
  ]),

  // Filter form slide down
  trigger('slideDown', [
    transition(':enter', [
      style({
        opacity: 0,
        transform: 'translateY(-20px)',
        maxHeight: '0px',
        overflow: 'hidden',
      }),
      animate(
        '400ms cubic-bezier(0.4, 0, 0.2, 1)',
        style({
          opacity: 1,
          transform: 'translateY(0)',
          maxHeight: '500px',
        })
      ),
    ]),
    transition(':leave', [
      animate(
        '300ms ease-in',
        style({
          opacity: 0,
          transform: 'translateY(-20px)',
          maxHeight: '0px',
        })
      ),
    ]),
  ]),
];

import { CommonModule, NgIf, NgFor, NgClass } from '@angular/common';
import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { LogApiService } from '@app/core/services/administrative/log.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-logs',
  templateUrl: './logs.component.html',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NgIf,
    NgFor,
    NgClass,
    MatIconModule,
  ],
  styleUrls: ['./logs.component.css'],
})
export class LogsComponent implements OnInit, OnDestroy {
  @Input() localId!: string;
  logs: any[] = [];
  filteredLogs: any[] = [];
  logForm!: FormGroup;
  isLoading = false;
  selectedLog: any = null;
  isSummaryLoading = false; // new flag for summary loading
  priorityMetaKeys = [
    'friendly_name',
    'ieee_address',
    'interview_completed',
    'interview_state',
    'interviewing',
    'network_address',
    'supported',
    'type',
    'action',
    'battery',
    'current',
    'detection_interval',
    'device_temperature',
    'illuminance',
    'linkquality',
    'motion_sensitivity',
    'occupancy',
    'operation_mode',
    'power',
    'power_outage_count',
    'side',
    'trigger_indicator',
    'voltage',
  ];

  private refreshSubscription?: Subscription;
  private formSubscription?: Subscription;
  private lastPageSize = 50;
  private isProgrammaticFormChange = false;

  constructor(private fb: FormBuilder, private logService: LogApiService) {}

  ngOnInit(): void {
    this.initForm();
    this.fetchLogs();
  }

  ngOnDestroy(): void {
    this.refreshSubscription?.unsubscribe();
    this.formSubscription?.unsubscribe();
  }

  private initForm(): void {
    this.logForm = this.fb.group({
      logType: ['Tout'],
      textSearch: [''],
      pageSize: [50],
    });

    this.formSubscription = this.logForm.valueChanges.subscribe((formValue) => {
      if (this.isProgrammaticFormChange) return;

      if (formValue.pageSize !== this.lastPageSize) {
        this.lastPageSize = formValue.pageSize;
        this.fetchLogs();
      } else {
        this.filterLogs();
      }
    });
  }

  fetchLogs(): void {
    this.isProgrammaticFormChange = true;
    this.isLoading = true;

    const { pageSize } = this.logForm.value;
    this.lastPageSize = pageSize;

    const payload = {
      pagination: {
        currentPage: 0,
        pageSize: pageSize,
        isLast: true,
        isFirst: true,
        startIndex: 0,
        totalElement: 0,
      },
      filterParams: [
        {
          Column: 'LocalId',
          Value: this.localId,
          Op: 'eq',
          AndOr: 'and',
        },
      ],
    };

    this.logService.gatePage(payload).subscribe({
      next: (res: any) => {
        // Filter out null entries and empty results
        this.logs = (res.Content || [])
          .map((log: any) => this.parseLogMessage(log.Message))
          .filter((log: any) => log !== null); // Remove null entries

        this.filterLogs();
        this.isLoading = false;
        this.isProgrammaticFormChange = false;
      },
      error: (err: any) => {
        console.error('Error loading logs', err);
        this.isLoading = false;
        this.isProgrammaticFormChange = false;
      },
    });
  }

  private parseLogMessage(message: string): any | null {
    // Return null for empty or whitespace-only messages
    if (!message || message.trim() === '') {
      return null;
    }

    // Try to extract JSON inside 'payload ' single quotes
    const payloadRegex = /payload '({.*})'/;
    const match = message.match(payloadRegex);

    if (match && match[1]) {
      let rawJson = match[1];

      // Unescape backslash-escaped quotes
      rawJson = rawJson.replace(/\\"/g, '"');

      try {
        const jsonData = JSON.parse(rawJson);

        // Check if the parsed data has meaningful content
        if (!this.hasValidContent(jsonData, message)) {
          return null;
        }

        return {
          level: jsonData.level ?? 'info',
          message: jsonData.message ?? message,
          timestamp: new Date().toISOString(),
          topic: jsonData.topic,
          payload: jsonData,
          summary: null,
        };
      } catch (err) {
        console.warn('JSON parse error in payload:', err);
        // Return null instead of creating an empty log entry
        return null;
      }
    }

    // fallback original logic for JSON in message braces
    try {
      const jsonStart = message.indexOf('{');
      if (jsonStart === -1) {
        // Check if the plain message has meaningful content
        if (!this.hasValidPlainMessage(message)) {
          return null;
        }

        return {
          level: 'info',
          message,
          timestamp: new Date().toISOString(),
          payload: null,
          summary: null,
        };
      }

      const prefix = message.substring(0, jsonStart).trim();
      const remaining = message.substring(jsonStart);

      let braceCount = 0;
      let endIndex = -1;

      for (let i = 0; i < remaining.length; i++) {
        const char = remaining[i];
        if (char === '{') braceCount++;
        else if (char === '}') braceCount--;

        if (braceCount === 0) {
          endIndex = i;
          break;
        }
      }

      if (endIndex === -1) {
        console.warn('Unbalanced braces in log message JSON part');
        return null;
      }

      const jsonStr = remaining.substring(0, endIndex + 1);
      const jsonData = JSON.parse(jsonStr);

      // Check if the parsed data has meaningful content
      if (!this.hasValidContent(jsonData, prefix || message)) {
        return null;
      }

      return {
        level: jsonData.level ?? 'info',
        message: jsonData.message ?? (prefix || 'Pas de message'),
        timestamp: prefix || new Date().toISOString(),
        topic: jsonData.topic,
        payload: jsonData,
        summary: null,
      };
    } catch (e) {
      console.warn('Could not parse log message as JSON', e);
      return null;
    }
  }

  private hasValidContent(jsonData: any, fallbackMessage: string): boolean {
    // First check if there's a meaningful message
    const message = jsonData.message || fallbackMessage;
    const hasValidMessage =
      message &&
      message.trim() !== '' &&
      message.trim() !== 'Pas de message' &&
      message.trim() !== 'No message' &&
      message.trim() !== 'pas de message' &&
      message.trim() !== 'no message';

    if (hasValidMessage) {
      return true;
    }

    // Check if there's meaningful payload data from our priority list
    if (jsonData && typeof jsonData === 'object') {
      const meaningfulKeys = this.priorityMetaKeys.filter(
        (key) =>
          jsonData[key] !== null &&
          jsonData[key] !== undefined &&
          jsonData[key] !== '' &&
          jsonData[key] !== 'null' &&
          jsonData[key] !== 'undefined'
      );

      // Only consider it valid if there are meaningful priority keys with actual data
      if (meaningfulKeys.length > 0) {
        // Double check that the values are not just empty objects or arrays
        const hasRealData = meaningfulKeys.some((key) => {
          const value = jsonData[key];
          if (typeof value === 'object') {
            return (
              JSON.stringify(value) !== '{}' && JSON.stringify(value) !== '[]'
            );
          }
          return true;
        });

        if (hasRealData) {
          return true;
        }
      }
    }

    // Check if there's a meaningful topic (not just empty string)
    if (
      jsonData.topic &&
      jsonData.topic.trim() !== '' &&
      jsonData.topic.trim() !== 'null' &&
      jsonData.topic.trim() !== 'undefined'
    ) {
      return true;
    }

    return false;
  }

  private hasValidPlainMessage(message: string): boolean {
    const trimmed = message.trim();

    // Filter out common empty/meaningless messages
    const emptyPatterns = [
      'Pas de message',
      'No message',
      'pas de message',
      'no message',
      '',
      'null',
      'undefined',
      '{}',
      '[]',
      'N/A',
      'n/a',
      '-',
      '--',
      '---',
    ];

    // Also check if it's just whitespace or very short meaningless content
    return (
      trimmed.length > 0 &&
      !emptyPatterns.some(
        (pattern) => trimmed.toLowerCase() === pattern.toLowerCase()
      ) &&
      trimmed.length >= 3
    ); // Require at least 3 characters for meaningful content
  }

  getOtherMetaKeys(payload: any): string[] {
    if (!payload || typeof payload !== 'object') return [];
    return Object.keys(payload).filter(
      (key) =>
        !this.priorityMetaKeys.includes(key) &&
        key !== 'level' &&
        key !== 'message' &&
        key !== 'timestamp' &&
        payload[key] !== null &&
        payload[key] !== undefined
    );
  }

  private filterLogs(): void {
    const { logType, textSearch } = this.logForm.value;

    this.filteredLogs = this.logs.filter((log) => {
      if (logType !== 'Tout') {
        if (log.level.toLowerCase() !== logType.toLowerCase()) {
          return false;
        }
      }

      if (textSearch) {
        const searchText = textSearch.toLowerCase();
        if (
          !log.message.toLowerCase().includes(searchText) &&
          !(
            log.payload &&
            JSON.stringify(log.payload).toLowerCase().includes(searchText)
          )
        ) {
          return false;
        }
      }
      return true;
    });
  }

  resetForm(): void {
    this.isProgrammaticFormChange = true;
    this.logForm.reset({
      logType: 'Tout',
      textSearch: '',
      pageSize: 100,
    });
    this.isProgrammaticFormChange = false;
  }

  openLogDetails(log: any): void {
    this.selectedLog = log;
    console.log('Selected log:', this.selectedLog);

    if (!log.summary) {
      this.isSummaryLoading = true;
      this.logService.summarizeLog(log).subscribe({
        next: (summaryText: any) => {
          console.log('Summary received:', summaryText);

          const raw =
            typeof summaryText === 'object' && 'summary' in summaryText
              ? summaryText.summary
              : summaryText?.toString?.() ?? '';

          const lines = raw
            .split('\n')
            .filter((line: string) => line.trim() !== '');
          log.summary = lines.slice(1).join('\n');

          this.selectedLog = log;
          this.isSummaryLoading = false;
        },
        error: (error) => {
          console.error('Error generating summary:', error);
          log.summary = null;
          this.isSummaryLoading = false;
        },
      });
    }
  }

  closeLogDetails(): void {
    this.selectedLog = null;
    this.isSummaryLoading = false;
  }

  getLogLevelClass(level: string): string {
    return level.toLowerCase();
  }

  formatTimestamp(timestamp: string): string {
    try {
      return new Date(timestamp).toLocaleString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      });
    } catch {
      return timestamp;
    }
  }

  getMetaKeys(payload: any): string[] {
    if (!payload || typeof payload !== 'object') return [];
    return Object.keys(payload).filter(
      (key) =>
        key !== 'level' &&
        key !== 'message' &&
        key !== 'timestamp' &&
        payload[key] !== null &&
        payload[key] !== undefined
    );
  }

  getMetaValue(value: any): string {
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    return String(value);
  }

  hasMetaData(payload: any): boolean {
    return this.getMetaKeys(payload).length > 0;
  }
}