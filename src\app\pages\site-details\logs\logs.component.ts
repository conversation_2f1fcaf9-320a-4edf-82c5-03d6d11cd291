import { CommonModule, NgI<PERSON>, Ng<PERSON><PERSON>, Ng<PERSON>lass } from '@angular/common';
import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { LogApiService } from '@app/core/services/administrative/log.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-logs',
  templateUrl: './logs.component.html',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NgIf,
    NgFor,
    NgClass,
    MatIconModule,
  ],
  styleUrls: ['./logs.component.css'],
})
export class LogsComponent implements OnInit, OnDestroy {
  @Input() localId!: string;
  logs: any[] = [];
  filteredLogs: any[] = [];
  logForm!: FormGroup;
  isLoading = false;
  selectedLog: any = null;
  isSummaryLoading = false; // new flag for summary loading

  priorityMetaKeys = [
    'friendly_name',
    'ieee_address',
    'interview_completed',
    'interview_state',
    'interviewing',
    'network_address',
    'supported',
    'type',
  ];

  private refreshSubscription?: Subscription;
  private formSubscription?: Subscription;
  private lastPageSize = 100;
  private isProgrammaticFormChange = false;

  constructor(private fb: FormBuilder, private logService: LogApiService) {}

  ngOnInit(): void {
    this.initForm();
    this.fetchLogs();
  }

  ngOnDestroy(): void {
    this.refreshSubscription?.unsubscribe();
    this.formSubscription?.unsubscribe();
  }

  private initForm(): void {
    this.logForm = this.fb.group({
      logType: ['Tout'],
      textSearch: [''],
      pageSize: [100],
    });

    this.formSubscription = this.logForm.valueChanges.subscribe((formValue) => {
      if (this.isProgrammaticFormChange) return;

      if (formValue.pageSize !== this.lastPageSize) {
        this.lastPageSize = formValue.pageSize;
        this.fetchLogs();
      } else {
        this.filterLogs();
      }
    });
  }

  fetchLogs(): void {
    this.isProgrammaticFormChange = true;
    this.isLoading = true;

    const { pageSize } = this.logForm.value;
    this.lastPageSize = pageSize;

    const payload = {
      pagination: {
        currentPage: 0,
        pageSize: pageSize,
        isLast: true,
        isFirst: true,
        startIndex: 0,
        totalElement: 0,
      },
      filterParams: [
        {
          column: 'LocalId',
          value: this.localId,
          op: 'eq',
          andOr: 'and',
        },
      ],
    };

    this.logService.gatePage(payload).subscribe({
      next: (res: any) => {
        this.logs =
          res.Content?.map((log: any) => this.parseLogMessage(log.Message)) ||
          [];
        this.filterLogs();
        this.isLoading = false;
        this.isProgrammaticFormChange = false;
      },
      error: (err: any) => {
        console.error('Error loading logs', err);
        this.isLoading = false;
        this.isProgrammaticFormChange = false;
      },
    });
  }

  private parseLogMessage(message: string): any {
    if (!message) {
      return {
        level: 'info',
        message: 'Pas de message',
        timestamp: new Date().toISOString(),
        payload: null,
        summary: null,
      };
    }

    // Try to extract JSON inside 'payload ' single quotes
    const payloadRegex = /payload '({.*})'/;
    const match = message.match(payloadRegex);

    if (match && match[1]) {
      let rawJson = match[1];

      // Unescape backslash-escaped quotes
      rawJson = rawJson.replace(/\\"/g, '"');

      try {
        const jsonData = JSON.parse(rawJson);
        return {
          level: jsonData.level ?? 'info',
          message: jsonData.message ?? message,
          timestamp: new Date().toISOString(),
          topic: jsonData.topic,
          payload: jsonData,
          summary: null,
        };
      } catch (err) {
        console.warn('JSON parse error in payload:', err);
        return {
          level: 'info',
          message,
          timestamp: new Date().toISOString(),
          payload: null,
          summary: null,
        };
      }
    }

    // fallback original logic for JSON in message braces
    try {
      const jsonStart = message.indexOf('{');
      if (jsonStart === -1) {
        return {
          level: 'info',
          message,
          timestamp: new Date().toISOString(),
          payload: null,
          summary: null,
        };
      }

      const prefix = message.substring(0, jsonStart).trim();
      const remaining = message.substring(jsonStart);

      let braceCount = 0;
      let endIndex = -1;

      for (let i = 0; i < remaining.length; i++) {
        const char = remaining[i];
        if (char === '{') braceCount++;
        else if (char === '}') braceCount--;

        if (braceCount === 0) {
          endIndex = i;
          break;
        }
      }

      if (endIndex === -1) {
        console.warn('Unbalanced braces in log message JSON part');
        return {
          level: 'info',
          message,
          timestamp: new Date().toISOString(),
          payload: null,
          summary: null,
        };
      }

      const jsonStr = remaining.substring(0, endIndex + 1);
      const jsonData = JSON.parse(jsonStr);

      return {
        level: jsonData.level ?? 'info',
        message: jsonData.message ?? (prefix || 'Pas de message'),
        timestamp: prefix || new Date().toISOString(),
        topic: jsonData.topic,
        payload: jsonData,
        summary: null,
      };
    } catch (e) {
      console.warn('Could not parse log message as JSON', e);
      return {
        level: 'info',
        message,
        timestamp: new Date().toISOString(),
        payload: null,
        summary: null,
      };
    }
  }

  getOtherMetaKeys(payload: any): string[] {
    if (!payload || typeof payload !== 'object') return [];
    return Object.keys(payload).filter(
      (key) =>
        !this.priorityMetaKeys.includes(key) &&
        key !== 'level' &&
        key !== 'message' &&
        key !== 'timestamp' &&
        payload[key] !== null &&
        payload[key] !== undefined
    );
  }

  private filterLogs(): void {
    const { logType, textSearch } = this.logForm.value;

    this.filteredLogs = this.logs.filter((log) => {
      if (logType !== 'Tout') {
        if (log.level.toLowerCase() !== logType.toLowerCase()) {
          return false;
        }
      }

      if (textSearch) {
        const searchText = textSearch.toLowerCase();
        if (
          !log.message.toLowerCase().includes(searchText) &&
          !(
            log.payload &&
            JSON.stringify(log.payload).toLowerCase().includes(searchText)
          )
        ) {
          return false;
        }
      }
      return true;
    });
  }

  resetForm(): void {
    this.isProgrammaticFormChange = true;
    this.logForm.reset({
      logType: 'Tout',
      textSearch: '',
      pageSize: 100,
    });
    this.isProgrammaticFormChange = false;
  }

  openLogDetails(log: any): void {
    this.selectedLog = log;
    console.log('Selected log:', this.selectedLog);

    // If no summary yet, fetch it from the service
    if (!log.summary) {
      this.isSummaryLoading = true;
      this.logService.summarizeLog(log).subscribe({
        next: (summaryText: any) => {
          console.log('Summary received:', summaryText);
          // If summaryText is an object with a summary property, use it; otherwise, use as string
          if (
            summaryText &&
            typeof summaryText === 'object' &&
            'summary' in summaryText
          ) {
            log.summary = summaryText.summary;
          } else {
            log.summary = summaryText?.toString?.() ?? '';
          }
          this.selectedLog = log; // update selectedLog reference to trigger UI update
          this.isSummaryLoading = false;
        },
        error: (error) => {
          console.error('Error generating summary:', error);
          log.summary = null;
          this.isSummaryLoading = false;
        },
      });
    }
  }

  closeLogDetails(): void {
    this.selectedLog = null;
    this.isSummaryLoading = false;
  }

  getLogLevelClass(level: string): string {
    return level.toLowerCase();
  }

  formatTimestamp(timestamp: string): string {
    try {
      return new Date(timestamp).toLocaleString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      });
    } catch {
      return timestamp;
    }
  }

  getMetaKeys(payload: any): string[] {
    if (!payload || typeof payload !== 'object') return [];
    return Object.keys(payload).filter(
      (key) =>
        key !== 'level' &&
        key !== 'message' &&
        key !== 'timestamp' &&
        payload[key] !== null &&
        payload[key] !== undefined
    );
  }

  getMetaValue(value: any): string {
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    return String(value);
  }

  hasMetaData(payload: any): boolean {
    return this.getMetaKeys(payload).length > 0;
  }
}