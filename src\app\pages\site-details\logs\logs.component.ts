// logs.component.ts
import { CommonModule, NgIf, NgF<PERSON>, Ng<PERSON>lass } from '@angular/common';
import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { Log } from '@app/core/models/log';
import { LogApiService } from '@app/core/services/administrative/log.service';
import { Subscription, interval } from 'rxjs';


interface ZigbeeLog {
  level: string;
  message: string;
  timestamp: string;
  topic?: string;
  payload?: any;
}
@Component({
  selector: 'app-logs',
  templateUrl: './logs.component.html',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NgIf,
    NgFor,
    NgClass,
    MatIconModule
  ],
  styleUrl: './logs.component.css'
})
export class LogsComponent implements OnInit, OnDestroy {
  @Input() localId!: string;
  logs?: any[] = [];
  filteredLogs?: any[] = [];
  logForm!: FormGroup;
  isLoading = false;
  private refreshSubscription?: Subscription;
  private formSubscription?: Subscription;
  private lastPageSize: number = 50;

  constructor(
    readonly fb: FormBuilder,
    readonly logService: LogApiService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.fetchLogs();
    this.setupAutoRefresh();
  }

  ngOnDestroy(): void {
    this.refreshSubscription?.unsubscribe();
    this.formSubscription?.unsubscribe();
  }

  private initForm(): void {
    this.logForm = this.fb.group({
      logType: ['Tout'],
      textSearch: [''],
      pageSize: [100]
    });

    this.formSubscription = this.logForm.valueChanges.subscribe((formValue) => {
      if (formValue.pageSize !== this.lastPageSize) {
        this.lastPageSize = formValue.pageSize;
        this.fetchLogs();
      } else {
        this.filterLogs();
      }
    });
  }

  private setupAutoRefresh(): void {
    this.refreshSubscription = interval(10000)
      .subscribe(() => this.fetchLogs());
  }

fetchLogs(): void {
  this.isLoading = true;
  const { pageSize } = this.logForm.value;

  const payload = {
    pagination: {
      currentPage: 0,
      pageSize: 4, // Use the form value
      isLast: true,
      isFirst: true,
      startIndex: 0,
      totalElement: 0
    },
    filterParams: [
      {
        column: 'LocalId',
        value: this.localId,
        op: 'eq',
        andOr: 'and'
      }
    ]
  };

  this.logService.gatePage(payload).subscribe({
    next: (res) => {
      // Parse the logs from the response
      this.filteredLogs =[]
      console.log(res.Content)
      this.logs = res.Content?.map(log => this.parseLogMessage(log.Message));
      this.filterLogs();
      this.isLoading = false;
    },
    error: (err) => {
      console.error('Error loading logs', err);
      this.isLoading = false;
    }
  });
}
private parseLogMessage(message: string): any {
  try {
    // Try to parse the JSON part of the message
    const jsonStart = message.indexOf('{');
    if (jsonStart > -1) {
      const prefix = message.substring(0, jsonStart);
      const jsonStr = message.substring(jsonStart);
      const jsonData = JSON.parse(jsonStr);
      
      return {
        level: jsonData.level ?? 'info',
        message: jsonData.message ?? prefix,
        timestamp: prefix.trim(),
        payload: jsonData
      };
    }
  } catch (e) {
    console.warn('Could not parse log message as JSON', e);
  }
  
  // Fallback to plain message
  return {
    level: 'info',
    message: message,
    timestamp: new Date().toISOString()
  };
}
private filterLogs(): void {
  const { logType, textSearch } = this.logForm.value;
  
  this.filteredLogs = this.logs?.filter(log => {
    // Filter by log type
    if (logType !== 'Tout') {
      if (log.level.toLowerCase() !== logType.toLowerCase()) {
        return false;
      }
    }
    
    // Filter by text search
    if (textSearch) {
      const searchText = textSearch.toLowerCase();
      if (!log.message.toLowerCase().includes(searchText) && 
          !(log.payload && JSON.stringify(log.payload).toLowerCase().includes(searchText))) {
        return false;
      }
    }
    
    return true;
  });
}

  getLogType(log: Log): string {
    if (log.Message.includes('Erreur')) return 'error';
    if (log.Message.includes('Avertissement')) return 'warning';
    if (log.Message.includes('Info')) return 'info';
    return 'other';
  }

  formatLogMessage(message: string): string {
    try {
      // Try to parse the JSON part of the message
      const jsonStart = message.indexOf('{');
      if (jsonStart > -1) {
        const prefix = message.substring(0, jsonStart);
        const jsonStr = message.substring(jsonStart);
        const jsonData = JSON.parse(jsonStr);
        
        // Format the JSON part nicely
        const formattedJson = JSON.stringify(jsonData, null, 2)
          .split('\n')
          .map(line => `<span class="json-line">${line}</span>`)
          .join('\n');
        
        return `${prefix}<div class="json-container">${formattedJson}</div>`;
      }
    } catch (e) {
      console.warn('Could not parse log message as JSON', e);
    }
    
    // Fallback to plain message
    return message;
  }



parseZigbeeLogs(logStrings: string[]): ZigbeeLog[] {
  const logs: any[] = [];

  for (const raw of logStrings) {
    try {
      // Extract timestamp before the JSON
      const timestampMatch = raw.match(/^"Message":\s*"([\d\-T:.+]+).*?\s(zigbee2mqtt.*?)\s({.*})"$/);
      if (!timestampMatch) continue;

      const timestamp = timestampMatch[1];
      const jsonString = timestampMatch[3].replace(/\\"/g, '"'); // Unescape quotes

      const parsed = JSON.parse(jsonString);
      console.log(parsed.message);
      const baseMessage = parsed.message ?? '';

      let topic = '';
      let payload = null;

      // Try extracting topic + payload from inside the message string
      const topicMatch = baseMessage.match(/topic '(.*?)'/);
      const payloadMatch = baseMessage.match(/payload '({.*})'/);

      if (topicMatch) topic = topicMatch[1];
      if (payloadMatch) {
        try {
          payload = JSON.parse(payloadMatch[1]);
        } catch (e) {
          payload = payloadMatch[1]; // fallback to raw string
        }
      }

      logs.push({
        level: parsed.level,
        message: baseMessage,
        timestamp,
        topic,
        payload
      });
    } catch (err) {
      console.warn('Failed to parse log:', raw, err);
    }
  }

  return logs;
}
getMetaKeys(meta: any): string[] {
  return Object.keys(meta);
}


}