.dashboard-container {
  padding: 20px;
  background-color: white;
  min-height: 100vh;
}

.org-name {
  margin: 0 0 20px;
  text-align: center;
  font-size: 18px;
  font-weight: 400;
  color: #2e7d32;
}

/** Header **/
.dashboard-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.04);
  border: 1px solid #e2e8f0;
  margin-bottom: 60px; /** spacing after header **/
}

.dashboard-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

/** Content layout **/
.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 32px; /** spacing between main sections **/
}

/** All Cards Section - Unified grid for all cards **/
.all-cards-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  column-gap: 32px;
  row-gap: 32px;
}

/** Remove the separate main-row-with-chart styling **/
.main-row-with-chart {
  display: contents; /** This makes the container transparent in the grid **/
}

/** Main Metrics Row - Now part of the unified grid **/
.main-metrics-row {
  display: contents; /** Merge with parent grid **/
}

/** Additional Metrics Grid - Now part of the unified grid **/
.additional-metrics-grid {
  display: contents; /** Merge with parent grid **/
}

/** Charts Section - Now appears after all cards **/
.charts-section {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/** Consumption chart - Now treated as a regular chart **/
.consommation-chart {
  height: 500px;
  width: 100%;
  min-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.full-width-chart {
  width: 1125px;
}

/* Map Container - Matches dashboard styling */
.map-container {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.07);
  border: 1px solid #e1e8ed;
  margin-top: 32px;
}

.top-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
}

#map {
  height: 500px;
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

/* Optional: Add a title for the map section */
.map-header {
  margin-bottom: 20px;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  text-align: center;
}

/** Unified Card Styles **/
.card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e1e8ed;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  /* Ensure consistent dimensions */
  min-height: 160px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  flex-shrink: 0; /** Prevent header from shrinking **/
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: rgba(151, 126, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0; /** Consistent icon size **/
}

.card-icon i {
  font-size: 24px;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: #64748b;
  line-height: 1.4;
  flex: 1; /** Allow title to take available space **/
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1; /** Fill remaining space **/
  justify-content: space-between;
}

.card-amount {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.amount-text {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2; /** Consistent line height **/
}

/** Progress bar - Consistent across all cards **/
.card-progress {
  width: 100%;
  margin-top: auto; /** Push to bottom of card **/
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #f1f5f9;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

/** Charts - Consistent styling **/
.chart-container {
  background: white;
  border-radius: 16px;
  padding: 24px;
  height: 500px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.07);
  border: 1px solid #e1e8ed;
}

.large-chart,
.medium-chart {
  height: 500px;
}

.chart-container canvas {
  max-width: 100%;
  height: 100% !important;
}

/** Responsive Design **/
@media (max-width: 1200px) {
  /** Charts section - 2 columns on medium screens **/
  .charts-section {
    grid-template-columns: 1fr 1fr;
  }

  /** Third chart (consumption) goes to next row **/
  .consommation-chart {
    grid-column: 1 / -1; /** Span full width **/
  }

  .large-chart,
  .medium-chart {
    height: 400px;
  }

  #map {
    height: 400px;
  }

  /** Cards maintain responsive grid **/
  .all-cards-section {
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 15px;
  }

  .map-container {
    padding: 20px;
    margin-top: 20px;
  }

  #map {
    height: 300px;
  }

  .map-header {
    font-size: 1.25rem;
    margin-bottom: 16px;
  }

  .dashboard-header h1 {
    font-size: 2rem;
  }

  /** Consistent mobile card styling **/
  .card {
    padding: 20px;
    min-height: 140px; /** Slightly smaller on mobile **/
  }

  .amount-text {
    font-size: 28px;
  }

  .chart-container {
    height: 300px;
    padding: 20px;
  }

  /** Single column for charts on mobile **/
  .charts-section {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  /** Single column for cards on mobile **/
  .all-cards-section {
    grid-template-columns: 1fr;
    column-gap: 20px;
    row-gap: 20px;
  }

  .consommation-chart {
    min-height: 300px;
    height: auto;
  }
}
