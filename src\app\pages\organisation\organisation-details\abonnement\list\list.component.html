<!--* Loading State *-->
<div *ngIf="isLoading" class="loading-message">
  Vos abonnement sont en cours de chargement...
</div>

<!--* Table Container *-->
<div *ngIf="!isLoading" class="table-container">
  <div class="table-header">
    <h2 class="table-title">Abonnements</h2>
  </div>

  <!--* Subscriptions Table *-->
  <table *ngIf="subscriptions.length > 0" class="subscriptions-table">
    <thead>
      <tr>
        <th>Client</th>
        <th>Licence</th>
        <th>Date Début</th>
        <th>Date Fin</th>
        <th>Statut</th>
        <th>Prix</th>
        <th>Fréquence de payment</th>
        <th></th>
      </tr>
    </thead>
    <tbody>
      <tr
        *ngFor="let sub of subscriptions"
        (click)="selectSubscription(sub)"
        [class.selected]="selectedSubscription === sub"
        style="cursor: pointer"
      >
        <td>{{ getClientName(sub.ClientId) }}</td>
        <td>{{ getLicenceName(sub.LicenceId) }}</td>
        <td>{{ sub.DateDebut | date : "dd-MM-yyyy" }}</td>
        <td>{{ sub.DateFin | date : "dd-MM-yyyy" }}</td>
        <td>{{ sub.Status }}</td>
        <td>{{ sub.Price | currency }}</td>
        <td>{{ sub.PaymentFrequency }}</td>

        <!--* Three dots action *-->
        <!-- <td
          class="dropdown-cell"
          style="position: relative"
          (click)="$event.stopPropagation()"
        >
          <div class="dropdown-container">
            <span
              class="dots-menu"
              (click)="toggleDropdown(sub); $event.stopPropagation()"
              title="Actions"
            >
              ⋮
            </span>
            <div
              class="dropdown-content"
              [class.show]="
                showDropdown &&
                selectedSubscription?.ClientId === sub.ClientId &&
                selectedSubscription?.LicenceId === sub.LicenceId
              "
            >
              <button
                class="dropdown-item"
                (click)="openDetailModal(); $event.stopPropagation()"
              >
                📋 Détails
              </button>
            </div>
          </div>
        </td> -->
      </tr>
    </tbody>
  </table>

  <!--* No Subscriptions Message *-->
  <div *ngIf="subscriptions.length === 0" class="no-subscriptions">
    Aucun abonnement trouvé.
  </div>
</div>

<!--* Detail Modal *-->
<div class="modal-overlay" *ngIf="showDetailModal" (click)="closeDetailModal()">
  <div class="modal-container" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h3>Détails de l'Abonnement</h3>
      <button class="close-button" (click)="closeDetailModal()" title="Fermer">
        ×
      </button>
    </div>
    <div class="modal-content">
      <app-detail
        [subscription]="selectedSubscription"
        [licences]="licences"
        (detailsClosed)="closeDetailModal()"
      ></app-detail>
    </div>
  </div>
</div>
