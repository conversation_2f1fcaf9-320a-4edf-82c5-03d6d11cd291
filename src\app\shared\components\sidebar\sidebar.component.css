.sidebar {
  background-color: var(--container-bg);
  color: var(--card-bg);
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  width: 250px;
  transition: all 0.3s ease;
  border-right: 1px solid rgba(0, 0, 0, 0.12);
  overflow-x: hidden;
  overflow-y: auto;
  z-index: 1200;
}

.sidebar.collapsed {
  width: 70px;
  overflow-y: auto;
}

.sidebar-header {
  height: 66px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  display: flex;
  align-items: center;
  padding: 0 16px;
  position: relative;
}

.logo-container {
  display: flex;
  align-items: center;
  width: calc(100% - 16px);
  gap: 12px;
  padding: 0 8px;
  position: relative;
  transition: all 0.3s ease;
}

.logo-wrapper {
  min-width: 40px;
  width: 50px;
  height: 50px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  transition: all 0.3s ease;
  flex-shrink: 0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--sidebar-title);
  white-space: nowrap;
  transition: all 0.3s ease;
  opacity: 1;
  flex-grow: 1;
}

.sidebar.collapsed .logo-wrapper {
  min-width: 32px;
  width: 32px;
  height: 32px;
  border-radius: 6px;
}

.sidebar.collapsed .logo-container {
  justify-content: center;
  padding: 0;
  width: 100%;
}

.sidebar.collapsed .title {
  opacity: 0;
  width: 0;
  margin: 0;
}

.toggle-button {
  position: absolute;
  right: -16px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: white;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  z-index: 1300;
  padding: 0;
}

.toggle-button mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
  color: #666;
}

.sidebar.collapsed .toggle-button mat-icon {
  transform: rotate(180deg);
}

.toggle-button:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.sidebar-content {
  padding: 8px 0;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 66px);
  gap: 2px;
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: var(--card-description);
  text-decoration: none;
  transition: all 0.2s ease;
  border-radius: 0;
  margin: 0;
  position: relative;
  font-weight: 500;
  border-left: 3px solid transparent;
}

.sidebar-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--primary);
  border-left-color: var(--primary-color, #67c987);
}

.sidebar-item.active {
  background-color: rgba(103, 201, 135, 0.1);
  color: var(--primary);
  font-weight: 600;
  border-left-color: var(--primary-color, #67c987);
}

.sidebar-item mat-icon {
  margin-right: 12px;
  font-size: 20px;
  width: 20px;
  height: 20px;
  transition: color 0.2s ease;
  flex-shrink: 0;
}

/* Collapsed sidebar styles */
.sidebar.collapsed .sidebar-item {
  justify-content: center;
  padding: 12px 8px;
  margin: 2px 8px;
  border-radius: 8px;
  border-left: none;
  position: relative;
}

.sidebar.collapsed .sidebar-item:hover {
  background-color: rgba(103, 201, 135, 0.1);
  border-left: none;
}

.sidebar.collapsed .sidebar-item span {
  display: none;
}

.sidebar.collapsed .sidebar-item mat-icon {
  margin-right: 0;
}

/* Tooltip for collapsed items */
.sidebar.collapsed .sidebar-item:hover:after {
  content: attr(data-title);
  position: absolute;
  left: calc(100% + 10px);
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1500;
  pointer-events: none;
}

/* Parent menu styles */
.parent-menu {
  cursor: pointer;
}

.expand-icon {
  margin-left: auto;
  transition: transform 0.2s ease;
  font-size: 18px;
  color: #666;
}

.parent-menu[aria-expanded="true"] .expand-icon {
  transform: rotate(180deg);
}

/* Expanded submenu styles */
.expanded-submenu {
  background-color: rgba(0, 0, 0, 0.02);
  border-left: 2px solid var(--primary-color, #67c987);
  margin: 0;
  overflow: hidden;
  animation: slideDown 0.2s ease;
}

.expanded-submenu .submenu-item {
  padding: 10px 16px 10px 40px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.7);
  border-left: 3px solid transparent;
  margin: 0;
  border-radius: 0;
}

.expanded-submenu .submenu-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--primary);
  border-left-color: var(--primary-color, #67c987);
}

.expanded-submenu .submenu-item mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  margin-right: 12px;
  opacity: 0.7;
}

/* Collapsed parent menu styling */
.sidebar.collapsed .parent-menu {
  position: relative;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  margin: 2px 8px 0 8px;
  border-radius: 8px;
  gap: 4px;
}

.sidebar.collapsed .parent-menu.expanded {
  background-color: rgba(103, 201, 135, 0.1);
  border-radius: 8px;
  margin-bottom: 4px;
}

/* Collapsed submenu styling */
.collapsed-submenu {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 4px 0;
  background-color: rgba(103, 201, 135, 0.05);
  border-radius: 0 0 8px 8px;
  animation: slideDown 0.2s ease;
  gap: 2px;
}

/* Collapsed submenu and nested items */
.sidebar.collapsed .submenu-item,
.sidebar.collapsed .nested-item {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px;
  margin: 0 8px;
  border-radius: 6px;
  background-color: transparent;
  border-left: none;
  position: relative;
  cursor: pointer;
  width: auto;
}

.sidebar.collapsed .submenu-item:hover,
.sidebar.collapsed .nested-item:hover {
  background-color: rgba(103, 201, 135, 0.15);
}

.sidebar.collapsed .submenu-item span,
.sidebar.collapsed .nested-item span {
  display: none;
}

.sidebar.collapsed .submenu-item mat-icon,
.sidebar.collapsed .nested-item mat-icon {
  margin-right: 0;
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Tooltip positioning for submenu items */
.sidebar.collapsed .submenu-item:hover:after,
.sidebar.collapsed .nested-item:hover:after {
  content: attr(data-title);
  position: absolute;
  left: calc(100% + 10px);
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
  z-index: 1500;
  pointer-events: none;
}

/* Hide expand icon in collapsed */
.sidebar.collapsed .parent-menu .expand-icon {
  display: none;
}

/* ===== Hierarchical Nesting for Expanded Paramétrage ===== */

/* Level 1: Client */
.expanded-submenu .parent-submenu {
  padding-left: 16px;
  font-weight: 500;
  border-left: 3px solid transparent;
}

.expanded-submenu .parent-submenu:hover {
  background-color: rgba(0, 0, 0, 0.04);
  border-left-color: var(--primary-color, #67c987);
}

/* Level 2 container: Sites under Client */
.expanded-submenu .nested-submenu {
  margin-left: 16px;
  padding-left: 12px;
  border-left: 2px solid rgba(0, 0, 0, 0.05);
}

/* Level 2 item */
.expanded-submenu .nested-submenu .nested-item {
  padding-left: 32px;
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.75);
  border-left: 3px solid transparent;
}

.expanded-submenu .nested-submenu .nested-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
  border-left-color: var(--primary-color, #67c987);
  color: var(--primary);
}

/* Level 3 container: Locaux under Sites */
.expanded-submenu .deep-nested-submenu {
  margin-left: 24px;
  padding-left: 12px;
  border-left: 2px dashed rgba(0, 0, 0, 0.05);
}

/* Level 3 item */
.expanded-submenu .deep-nested-item {
  padding-left: 40px;
  font-size: 13px;
  color: rgba(0, 0, 0, 0.7);
  border-left: 3px solid transparent;
}

.expanded-submenu .deep-nested-item:hover {
  background-color: rgba(0, 0, 0, 0.03);
  border-left-color: var(--primary-color, #67c987);
  color: var(--primary);
}

/* Smooth transition */
.expanded-submenu .nested-item,
.expanded-submenu .deep-nested-item,
.expanded-submenu .parent-submenu {
  transition: all 0.2s ease;
}


/* Media query for mobile */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(0);
  }
  .sidebar.collapsed {
    transform: translateX(-100%);
  }
}

/* Animation */
@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 500px;
  }
}
