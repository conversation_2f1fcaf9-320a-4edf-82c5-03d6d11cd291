/* Modern Sidebar Styles - Green/White Theme */
.sidebar {
  background: linear-gradient(180deg, var(--sidebar-background) 70%, var(--green-light) 100%);
  color: var(--text-primary);
  height: 100vh;
  width: 280px;
  padding: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-right: 1px solid var(--beige-darl);
  box-shadow: 0 20px 25px -5px var(--box-shadow), 0 10px 10px -5px var(--box-shadow);
  position: fixed;
  z-index: 1200;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.sidebar.collapsed {
  width: 72px;
}

.sidebar-header {
  height: 80px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  background: var(--sidebar-background);
  border-bottom: 1px solid var(--beige-darl);
  position: relative;
  overflow: hidden;
}

.sidebar-header::before {
  display: none;
}

.logo-container {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
  z-index: 1;
}

.logo-wrapper {
  width: 80px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.logo-wrapper::before {
  display: none;
}

.logo {
  width: 80px;
  height: 40px;
  object-fit: contain;
  filter: brightness(1.2);
}

.brand-text {
  margin-left: 12px;
  font-size: 18px;
  font-weight: 700;
  color: var(--primary-dark);
  letter-spacing: -0.025em;
  transition: all 0.3s ease;
}

.sidebar.collapsed .brand-text {
  opacity: 0;
  transform: translateX(-20px);
}

.toggle-button {
  margin-left: auto;
  background: var(--green-light);
  border: 1px solid var(--primary-light);
  border-radius: 10px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.toggle-button:hover {
  background: var(--primary-light);
  border-color: var(--primary-dark);
  transform: scale(1.05);
}

.toggle-button mat-icon {
  color: var(--primary-dark);
  font-size: 20px;
  transition: all 0.3s ease;
}

.toggle-button:hover mat-icon {
  color: var(--primary);
}

.sidebar-content {
  padding: 24px 0;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 80px);
  overflow-y: auto;
  overflow-x: hidden;
  gap: 4px;
}

.sidebar-content::-webkit-scrollbar {
  width: 4px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: var(--green-light);
  border-radius: 2px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: var(--primary-light);
}

/* Navigation Items */
.sidebar-item,
.parent-menu {
  display: flex;
  align-items: center;
  padding: 14px 20px;
  color: var(--text-primary);
  text-decoration: none;
  border-radius: 0;
  margin: 0;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  background: none;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.sidebar-item::before,
.parent-menu::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, var(--primary), var(--primary-light));
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.sidebar-item:hover::before,
.parent-menu:hover::before {
  transform: scaleY(1);
}

.sidebar-item:hover,
.parent-menu:hover {
  background: var(--hover-bg-color);
  color: var(--primary-dark);
  padding-left: 24px;
}

.sidebar-item.active,
.parent-menu.active {
  background: var(--active-item-bg);
  color: var(--primary-dark);
  padding-left: 24px;
}

.sidebar-item.active::before,
.parent-menu.active::before {
  transform: scaleY(1);
}

.sidebar-item mat-icon,
.parent-menu mat-icon {
  margin-right: 16px;
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: var(--primary-light);
  transition: all 0.3s ease;
}

.sidebar-item:hover mat-icon,
.parent-menu:hover mat-icon,
.sidebar-item.active mat-icon,
.parent-menu.active mat-icon {
  color: var(--primary);
  transform: scale(1.1);
}

.sidebar-item span,
.parent-menu span {
  flex: 1;
  transition: all 0.3s ease;
}

.sidebar.collapsed .sidebar-item span,
.sidebar.collapsed .parent-menu span {
  opacity: 0;
  transform: translateX(-20px);
}

.sidebar.collapsed .sidebar-item,
.sidebar.collapsed .parent-menu {
  justify-content: center;
  padding: 14px 0;
  margin: 0 auto;
  width: 48px;
  border-radius: 12px;
}

.sidebar.collapsed .sidebar-item:hover,
.sidebar.collapsed .parent-menu:hover {
  background: var(--primary-light);
  padding: 14px 0;
}

.expand-icon {
  margin-left: auto;
  transition: all 0.3s ease;
  color: var(--primary-light);
}

.expand-icon.rotated {
  transform: rotate(180deg);
  color: var(--primary-dark);
}

/* Expanded Submenu */
.expanded-submenu {
  background: var(--green-light);
  border-radius: 0;
  margin: 0;
  padding: 8px 0;
  animation: slideDown 0.3s ease;
  border-top: 1px solid var(--beige-darl);
  border-bottom: 1px solid var(--beige-darl);
  backdrop-filter: blur(10px);
}

.expanded-submenu .submenu-item {
  display: flex;
  align-items: center;
  padding: 12px 20px 12px 56px;
  color: var(--text-secondary);
  border-radius: 0;
  font-size: 13px;
  transition: all 0.3s ease;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.expanded-submenu .submenu-item::before {
  content: '';
  position: absolute;
  left: 32px;
  top: 50%;
  width: 8px;
  height: 8px;
  background: var(--primary-light);
  border-radius: 50%;
  transform: translateY(-50%);
  transition: all 0.3s ease;
}

.expanded-submenu .submenu-item:hover::before {
  background: var(--primary);
  transform: translateY(-50%) scale(1.2);
}

.expanded-submenu .submenu-item:hover,
.expanded-submenu .submenu-item.active {
  background: var(--primary-light);
  color: var(--primary-dark);
  padding-left: 60px;
}

.expanded-submenu .submenu-item mat-icon {
  margin-right: 12px;
  font-size: 16px;
  width: 16px;
  height: 16px;
  color: var(--primary-light);
  transition: all 0.3s ease;
}

.expanded-submenu .submenu-item:hover mat-icon,
.expanded-submenu .submenu-item.active mat-icon {
  color: var(--primary);
}

/* Collapsed Submenu Popout */
.parent-menu-collapsed {
  position: relative;
  width: 48px; /* Only as wide as the icon */
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}

.collapsed-submenu {
  position: absolute;
  top: 100%; /* Directly below the icon */
  left: 0;
  background: var(--sidebar-background);
  backdrop-filter: blur(16px);
  box-shadow: 0 8px 24px rgba(103, 201, 135, 0.15);
  border-radius: 16px;
  padding: 12px 0;
  min-width: 220px;
  z-index: 4000;
  animation: slideInRight 0.3s ease;
  border: 1px solid var(--beige-darl);
  opacity: 1;
  transform: translateX(0);
  pointer-events: all;
  margin-top: 4px;
}

/* Prevent sidebar from hiding overflowed submenus */
.sidebar.collapsed {
  overflow: visible;
}

.collapsed-submenu .submenu-header {
  padding: 12px 20px;
  font-size: 12px;
  font-weight: 600;
  color: var(--primary-dark);
  border-bottom: 1px solid var(--beige-darl);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.collapsed-submenu .submenu-content .submenu-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: var(--text-primary);
  border-radius: 0;
  font-size: 14px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.collapsed-submenu .submenu-content .submenu-item:hover,
.collapsed-submenu .submenu-content .submenu-item.active {
  background: var(--primary-light);
  color: var(--primary-dark);
}

.collapsed-submenu .submenu-content .submenu-item mat-icon {
  margin-right: 12px;
  font-size: 18px;
  width: 18px;
  height: 18px;
  color: var(--primary-light);
  transition: all 0.3s ease;
}

.collapsed-submenu .submenu-content .submenu-item:hover mat-icon,
.collapsed-submenu .submenu-content .submenu-item.active mat-icon {
  color: var(--primary);
}

/* Animations */
@keyframes slideDown {
  from {
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    max-height: 300px;
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    width: 0;
    min-width: 0;
    transition: width 0.3s ease;
  }
  
  .sidebar.collapsed {
    width: 0;
  }
  
  .sidebar.mobile-open {
    width: 280px;
  }
  
  .sidebar-header,
  .sidebar-content {
    display: none;
  }
  
  .sidebar.mobile-open .sidebar-header,
  .sidebar.mobile-open .sidebar-content {
    display: flex;
    display: block;
  }
}

/* Focus states for accessibility */
.sidebar-item:focus,
.parent-menu:focus,
.submenu-item:focus,
.toggle-button:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Badge styles (if needed) */
.menu-badge {
  background: linear-gradient(135deg, var(--danger), var(--warning));
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  margin-left: auto;
}

/* Hide menu and submenu names when sidebar is collapsed */
.sidebar.collapsed .sidebar-item span,
.sidebar.collapsed .parent-menu span,
.sidebar.collapsed .collapsed-submenu .submenu-header,
.sidebar.collapsed .collapsed-submenu .submenu-item span {
  display: none !important;
}