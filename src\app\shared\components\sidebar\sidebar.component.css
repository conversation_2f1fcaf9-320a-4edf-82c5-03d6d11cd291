.sidebar {
  background-color: var(--container-bg);
  color: var(--card-bg);
  height: 100vh;
  top: 0;
  left: 0;
  width: 250px;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-right: 1px solid rgba(0, 0, 0, 0.12);
  overflow-x: hidden;
  overflow-y: hidden;
  z-index: 1200;
  position: fixed;
}

/* Increased collapsed width */
.sidebar.collapsed {
  width: 120px;
  overflow-y: hidden;
}

.sidebar:not(.collapsed) .logo-container {
  padding: 0 16px;
}

/* Updated Sidebar Header */
.sidebar-header {
  height: 66px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  padding: 0 12px;
  justify-content: space-between;
  position: relative;
  z-index: 1200;
}

/* Update Logo Container positioning */
.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0 16px;
  transition: all 0.3s ease;
  width: 100%;
}

.logo-wrapper {
  min-width: 40px;
  width: 75px;
  height: 60px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--sidebar-title);
  white-space: nowrap;
  transition: all 0.3s ease;
  opacity: 1;
  margin-right: 12px; /* Add space between title and toggle button */
}

.sidebar.collapsed .sidebar-header {
  display: flex;
  align-items: center;
  padding: 0 16px;
  height: 66px;
  justify-content: space-between; /* Changed from center */
  margin-bottom: 8px;
}
/* Collapsed Header Adjustments */
.sidebar.collapsed .logo-wrapper {
  margin: 0; /* Changed from auto */
  width: 40px;
  height: 40px;
}

.sidebar.collapsed .logo-container {
  justify-content: space-between; /* Changed from flex-start */
  padding: 0 8px;
  width: 100%;
}


.sidebar.collapsed .toggle-button {
  position: relative; /* Changed from absolute */
  right: auto;
  top: auto;
  margin-left: 0;
}

.sidebar.collapsed .title {
  opacity: 0;
  width: 0;
  margin: 0;
}

/* Updated Toggle Button - Always visible with consistent styling */
.toggle-button {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  z-index: 1300;
  padding: 0;
  margin-left: auto; /* Push to the right */
  flex-shrink: 0;
}

/* Toggle button hover effects */
.toggle-button:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background-color: white;
  border-color: var(--primary-color, #67c987);
}

/* Toggle button icon styles */
.toggle-button mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #666;
  transition: all 0.3s ease;
}

.toggle-button:hover mat-icon {
  color: var(--primary-color, #67c987);
}

.sidebar-content {
  padding: 8px 0;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 66px);
  gap: 2px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Custom scrollbar for content area only */
.sidebar-content::-webkit-scrollbar {
  width: 4px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Hide scrollbar in collapsed state */
.sidebar.collapsed .sidebar-content::-webkit-scrollbar {
  width: 0px;
}

/* Updated Sidebar Item Base Styles */
.sidebar-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: var(--card-description);
  text-decoration: none;
  transition: all 0.2s ease;
  border-radius: 0;
  margin: 0;
  position: relative;
  font-weight: 500;
  border-left: 3px solid transparent;
}

.sidebar-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--primary);
  border-left-color: var(--primary-color, #67c987);
}

.sidebar-item.active {
  background-color: rgba(103, 201, 135, 0.1);
  color: var(--primary);
  font-weight: 600;
  border-left-color: var(--primary-color, #67c987);
}

.sidebar-item mat-icon {
  margin-right: 12px;
  font-size: 20px;
  width: 20px;
  height: 20px;
  transition: color 0.2s ease;
  flex-shrink: 0;
}

/* Updated Collapsed Sidebar Item */
.sidebar.collapsed .sidebar-item {
  justify-content: center;
  padding: 14px 0;
  margin: 0 auto;
  border-radius: 4px;
  border-left: none;
  width: 44px;
  height: 44px;
}

.sidebar.collapsed .sidebar-item mat-icon {
  margin: 0;
  font-size: 22px;
  width: 22px;
  height: 22px;
}

.sidebar.collapsed .sidebar-item:hover {
  background-color: rgba(103, 201, 135, 0.08);
  border-left: none;
}

.sidebar.collapsed .sidebar-item.active {
  background-color: rgba(103, 201, 135, 0.12);
  border-left: none;
  position: relative;
}

/* Active indicator for collapsed state */
.sidebar.collapsed .sidebar-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--primary-color, #67c987);
}

.sidebar.collapsed .sidebar-item span {
  display: none;
}

/* Collapsed parent menu container for hoverable submenu */
.parent-menu-collapsed {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  z-index: 1000;
}

.parent-menu-collapsed .sidebar-item {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 14px 0;
}

.parent-menu-collapsed .sidebar-item:hover {
  background-color: rgba(103, 201, 135, 0.08);
}

/* Expanded state for the parent menu icon when its submenu is open (on hover) */
.parent-menu-collapsed.expanded .sidebar-item {
  background-color: rgba(103, 201, 135, 0.12);
  position: relative;
}

.parent-menu-collapsed.expanded .sidebar-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--primary-color, #67c987);
}

/* Updated Collapsed Submenu Styles */
.collapsed-submenu {
  position: fixed;
  left: 90px; /* Match collapsed sidebar width */
  top: 66px; /* Below header */
  background-color: var(--container-bg, white);
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
  border-radius: 8px;
  padding: 8px 0;
  min-width: 220px;
  z-index: 3000;
  animation: slideRight 0.2s ease;
  transform-origin: left center;
  max-height: calc(100vh - 70px);
  overflow-y: auto;
}

/* Submenu Header */
.submenu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  font-size: 13px;
  font-weight: 600;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  margin-bottom: 4px;
  color: #666;
}

.sidebar.collapsed .collapsed-submenu .submenu-header {
  color: #666;
  opacity: 0.9;
}

/* Submenu Content Container */
.submenu-content {
  padding: 4px 0;
}

/* Individual Submenu Items */
.submenu-content .submenu-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  color: var(--text-color, #333);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

/* Submenu Item Icons */
.submenu-content .submenu-item mat-icon {
  margin-right: 12px;
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: var(--text-muted, #666);
  transition: color 0.2s ease;
}

/* Submenu Item Text */
.submenu-content .submenu-item span {
  color: var(--text-color, #333);
  transition: color 0.2s ease;
}

/* Hover States */
.submenu-content .submenu-item:hover {
  background-color: rgba(103, 201, 135, 0.08);
}

.submenu-content .submenu-item:hover mat-icon,
.submenu-content .submenu-item:hover span {
  color: var(--primary-color, #67c987);
}

/* Active States */
.submenu-content .submenu-item.active {
  background-color: rgba(103, 201, 135, 0.12);
}

.submenu-content .submenu-item.active mat-icon,
.submenu-content .submenu-item.active span {
  color: var(--primary-color, #67c987);
}

/* Active Indicator */
.submenu-content .submenu-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--primary-color, #67c987);
}

/* Updated Tooltip Styling for collapsed items */
.sidebar.collapsed .sidebar-item:hover::after,
.parent-menu-collapsed:hover::after {
  content: attr(data-title);
  position: absolute;
  left: 125px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  pointer-events: none;
  z-index: 2000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  opacity: 0;
  animation: fadeIn 0.2s forwards;
}

/* Tooltip for collapsed submenu items - disable, as text is visible */
.collapsed-submenu .submenu-item:hover::after {
  content: none;
}

/* Hide expand icon in collapsed */
.sidebar.collapsed .parent-menu .expand-icon,
.sidebar.collapsed .parent-menu-collapsed .expand-icon {
  display: none;
}

/* Expanded submenu styles (for the full expanded sidebar) */
.expanded-submenu {
  background-color: rgba(0, 0, 0, 0.02);
  border-left: 2px solid var(--primary-color, #67c987);
  margin: 0;
  overflow: hidden;
  animation: slideDown 0.2s ease;
}

.expanded-submenu .submenu-item {
  display: flex;
  align-items: center;
  padding: 10px 16px 10px 40px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.7);
  text-decoration: none;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  margin: 0;
  border-radius: 0;
  cursor: pointer;
}

.expanded-submenu .submenu-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--primary);
  border-left-color: var(--primary-color, #67c987);
}

.expanded-submenu .submenu-item mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  margin-right: 12px;
  opacity: 0.7;
  flex-shrink: 0;
}

/* ===== Hierarchical Nesting for Expanded Paramétrage ===== */

/* Level 1: Client */
.expanded-submenu .parent-submenu {
  padding-left: 16px;
  font-weight: 500;
  border-left: 3px solid transparent;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.expanded-submenu .parent-submenu .item-content {
  display: flex;
  align-items: center;
  flex-grow: 1;
  padding: 10px 0;
}

.expanded-submenu .parent-submenu:hover {
  background-color: rgba(0, 0, 0, 0.04);
  border-left-color: var(--primary-color, #67c987);
  color: var(--primary);
}

.expanded-submenu .parent-submenu mat-icon.expand-icon {
  margin-left: auto;
  margin-right: 12px;
  font-size: 18px;
  color: #666;
}

/* Level 2 container: Sites under Client */
.expanded-submenu .nested-submenu {
  margin-left: 16px;
  padding-left: 12px;
  border-left: 2px solid rgba(0, 0, 0, 0.05);
}

/* Level 2 item */
.expanded-submenu .nested-submenu .nested-item {
  padding-left: 32px;
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.75);
  border-left: 3px solid transparent;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.expanded-submenu .nested-submenu .nested-item .item-content {
  display: flex;
  align-items: center;
  flex-grow: 1;
  padding: 10px 0;
}

.expanded-submenu .nested-submenu .nested-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
  border-left-color: var(--primary-color, #67c987);
  color: var(--primary);
}

.expanded-submenu .nested-submenu .nested-item mat-icon.expand-icon {
  margin-left: auto;
  margin-right: 12px;
  font-size: 18px;
  color: #666;
}

/* Level 3 container: Locaux under Sites */
.expanded-submenu .deep-nested-submenu {
  margin-left: 24px;
  padding-left: 12px;
  border-left: 2px dashed rgba(0, 0, 0, 0.05);
}

/* Level 3 item */
.expanded-submenu .deep-nested-item {
  padding-left: 40px;
  font-size: 13px;
  color: rgba(0, 0, 0, 0.7);
  border-left: 3px solid transparent;
  display: flex;
  align-items: center;
}

.expanded-submenu .deep-nested-item:hover {
  background-color: rgba(0, 0, 0, 0.03);
  border-left-color: var(--primary-color, #67c987);
  color: var(--primary);
}

/* Smooth transition for nested items */
.expanded-submenu .nested-item,
.expanded-submenu .deep-nested-item,
.expanded-submenu .parent-submenu {
  transition: all 0.2s ease;
}

.pin-button {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  cursor: pointer;
  color: #666;
  transition: all 0.2s ease;
}

.pin-button:hover {
  color: var(--primary-color, #67c987);
  transform: scale(1.1);
}

.pin-button mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Media query for mobile */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(0);
  }
  .sidebar.collapsed {
    transform: translateX(-100%);
  }
}

/* Animation */
@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 500px;
  }
}

@keyframes slideRight {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.sidebar.collapsed .submenu-item:hover,
.sidebar.collapsed .parent-menu-collapsed:hover {
  background-color: rgba(103, 201, 135, 0.12);
  border-radius: 6px;
}



/* Add these to your existing CSS */
.nav-label {
  margin-left: 8px;
}

.parent-menu.active {
  background-color: rgba(103, 201, 135, 0.1);
  color: var(--primary);
  font-weight: 600;
  border-left-color: var(--primary-color, #67c987);
}

.expanded-submenu {
  padding-left: 16px;
}

.expanded-submenu .submenu-item {
  padding: 10px 16px 10px 40px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.7);
  text-decoration: none;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  margin: 0;
  border-radius: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.expanded-submenu .submenu-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--primary);
  border-left-color: var(--primary-color, #67c987);
}

.expanded-submenu .submenu-item mat-icon {
  margin-right: 12px;
}