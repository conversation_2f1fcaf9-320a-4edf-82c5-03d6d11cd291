<div class="local-management-container">
  <div class="header-section">
    <div class="page-title">
      <h1 class="title">
        <mat-icon class="title-icon">meeting_room</mat-icon> Liste des Locaux
      </h1>
    </div>
  </div>
  
  <div class="popup-overlay" *ngIf="showEditForm" (click)="hideEditLocalForm()">
    <div class="popup-form" (click)="$event.stopPropagation()">
      <div class="popup-header">
        <h3>
          <mat-icon>edit</mat-icon>
          Modifier le Local
        </h3>
        <button class="close-btn" (click)="hideEditLocalForm()">
          <mat-icon>close</mat-icon>
        </button>
      </div>
  
      <form [formGroup]="editLocalForm" (ngSubmit)="submitEditForm()" class="site-form">
        <div class="validation-errors" *ngIf="editLocalForm.invalid && (editLocalForm.touched || editLocalForm.dirty)">
          <div class="validation-errors-title">
            <mat-icon>error_outline</mat-icon>
            Erreurs de validation
          </div>
          <ul class="validation-errors-list">
            <li *ngIf="editLocalForm.get('nom')?.invalid">
              <mat-icon>error</mat-icon>
              Le nom est requis
            </li>
            <li *ngIf="editLocalForm.get('typeLocalId')?.invalid">
              <mat-icon>error</mat-icon>
              Le type de local est requis
            </li>
            <li *ngIf="editLocalForm.get('idSite')?.invalid">
              <mat-icon>error</mat-icon>
              Le site est requis
            </li>
          </ul>
        </div>
  
        <div class="form-grid">
          <div class="form-group">
            <label for="editName">Nom <span class="required">*</span></label>
            <input id="editName" type="text" formControlName="nom" required>
          </div>
  
          <div class="form-group">
            <label for="editTypeLocalId">Type de Local <span class="required">*</span></label>
            <select id="editTypeLocalId" formControlName="typeLocalId" required>
              <option value="">Sélectionnez un type</option>
              <option *ngFor="let type of typeLocals" [value]="type.Id">
                {{ type.Nom }}
              </option>
            </select>
          </div>
  
          <div class="form-group">
            <label for="editIdSite">Site <span class="required">*</span></label>
            <select id="editIdSite" formControlName="idSite" required>
              <option value="">Sélectionnez un site</option>
              <option *ngFor="let site of sites" [value]="site.Id">
                {{ site.Name }}
              </option>
            </select>
          </div>
  
          <div class="form-group">
            <label for="editFloor">Étage</label>
            <input id="editFloor" type="number" formControlName="floor">
          </div>
  
          <div class="form-group">
            <label for="editCapacity">Capacité (personnes)</label>
            <input id="editCapacity" type="number" formControlName="capacitePersonnes">
          </div>
  
          <div class="form-group">
            <label for="editSensorsCount">Nombre de capteurs</label>
            <input id="editSensorsCount" type="number" formControlName="nombreCapteurs" readonly>
          </div>
  
          <div class="form-group full-width">
            <label for="editDescription">Description</label>
            <textarea id="editDescription" formControlName="description" rows="3"></textarea>
          </div>
  
          <!-- <div class="form-group full-width">
            <label>Plan 2D actuel</label>
            <div *ngIf="selectedLocal?.Architecture2DImage" class="current-images-container">
              <div class="current-image-item">
                <img [src]="'data:image/jpeg;base64,' + selectedLocal?.Architecture2DImage" class="current-image-preview">
              </div>
            </div>
            <input type="file" id="editArchitecture2DImage" (change)="onImagesSelected($event, 'Architecture2DImage')" accept="image/*">
          </div> -->
  
          <div class="form-group full-width">
            <label>Image</label>
            <div *ngIf="selectedLocal?.ImageLocal" class="current-images-container">
                <div class="current-image-item">
                    <img [src]="getLocalImageUrl(selectedLocal?.ImageLocal)" 
                         class="current-image-preview"
                         (error)="onImageError($event)"
                         loading="lazy"
                         [class.loaded]="imageLoaded"
                         (load)="onImageLoad()">
                </div>
            </div>
            <input type="file" id="editImageLocal" (change)="onImagesSelected($event, 'ImageLocal')" accept="image/*">
        </div>
        </div>
  
        <div class="form-actions">
          <button type="button" class="btn-cancel" (click)="hideEditLocalForm()">
            Annuler
          </button>
          <button type="submit" class="btn-submit" [disabled]="!editLocalForm.valid || isSubmitting">
            {{ isSubmitting ? 'Modification...' : 'Enregistrer' }}
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Search and Table Section -->
  <div class="search-bar">
    <input
      type="text"
      [(ngModel)]="searchTerm"
      placeholder="Rechercher un local"
      (keyup)="onSearchKeyup($event)"
    />
    <button class="search-button" (click)="filterLocals()">
      <mat-icon>search</mat-icon>
    </button>
  </div>

  <div class="loading-spinner" *ngIf="isLoading">Chargement...</div>

  <!-- Update the table section to match API structure -->
  <div class="table-section" *ngIf="!isLoading">
    <app-generic-table
      [headers]="['Nom', 'Étage', 'Capteurs', 'Capacité', 'Site', 'Type']"
      [keys]="['Name', 'Floor', 'SensorsCount', 'Capacity', 'Site.Name', 'TypeLocal.Nom']"
      [data]="filteredLocals"
      [actions]="['edit', 'view', 'delete']"
      (actionTriggered)="handleAction($event)"
    >
    </app-generic-table>

    <mat-paginator
      [length]="totalCount"
      [pageSize]="pageSize"
      [pageIndex]="currentPage"
      [pageSizeOptions]="[5, 10, 25, 50]"
      (page)="onPageChange($event)"
      aria-label="Select page">
    </mat-paginator>
  </div>
  <ngx-ui-loader></ngx-ui-loader>
  <ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>
</div>