import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { map, Observable } from 'rxjs';
import { environment } from '@app/environments/environment';
import { Controller } from './models/controller';
import { Site } from './models/site';
import { VwCapteurClientSite } from '@app/shared/models/vwCapteurClientSite';

export interface Device {
  Protocol: string;
  Manufacturer: string;
  ModelIdentifier: string;
  FriendlyName: string;
  LastSeen: string;
  IeeeAddress: string;
  NetworkAddress: number;
  Endpoint: number;
  NodeId: number;
  HomeId: number;
  SecurityClasses: string;
  SensorReadings: any;
  Transactions: any;
  IdTypeCapteur: string;
  TypeCapteur: any;
  RowData: string; // JSON string of device state
  Topic: string;
  CreatedAt: string | null;
  Id: string;
  State: string;
  LastUpdatedAt: Date | null;
  Controller: Controller | null;
}

export interface SimpleSite {
  Id: string;
  Name: string;
  ClientId: string;
}
export interface SimpleLocal {
  Id: string;
  Name: string;
  IdSite: string;
}

@Injectable({
  providedIn: 'root',
})
export class SensorsBackEndDataService {
  private apiUrl = environment.host + '/api/capteur';
  constructor(private http: HttpClient) {}

  getAllDevices(): Observable<any[]> {
    return this.http.get<Device[]>(this.apiUrl);
  }

  getSitesByClientId(clientId: string): Observable<SimpleSite[]> {
    const url = environment.host + `/api/site/client/${clientId}`;
    return this.http.get<SimpleSite[]>(url);
  }
  getlocauxBySiteId(IdSite: string): Observable<SimpleLocal[]> {
    const url = environment.host + `/api/local/site/${IdSite}`;
    return this.http.get<SimpleLocal[]>(url);
  }
  getCapteursByClientId(clientId: string): Observable<any[]> {
    const url = `${environment.host}/api/capteur/client/${clientId}`;
    return this.http.get<VwCapteurClientSite[]>(url);
  }

  getCapteursBySiteId(siteId: string): Observable<any[]> {
    const url = `${environment.host}/api/capteur/site/${siteId}`;
    return this.http.get<VwCapteurClientSite[]>(url);
  }
  getCapteursByLocalId(localId: string): Observable<any[]> {
    const url = `${environment.host}/api/capteur/local/${localId}`;

    return this.http.get<VwCapteurClientSite[]>(url);
  }

  getRowDataOnly(): Observable<any[]> {
    return this.http.get<Device[]>(this.apiUrl).pipe(
      map((devices: Device[]) =>
        devices
          .map(device => {
            try {
              const rowData = device.RowData ? JSON.parse(device.RowData) : {};
              const state = device.State ? JSON.parse(device.State) : null;

              // Format LastSeen to French-readable date/time
              const lastSeenDate = device.LastSeen
                ? new Date(device.LastSeen).toLocaleString('fr-FR', {
                    dateStyle: 'medium',
                    timeStyle: 'medium'
                  })
                : null;

              rowData.LastSeen = lastSeenDate;
              rowData.LastSeenOriginal = device.LastSeen;

              return { RowData: rowData, State: state };
            } catch (e) {
              console.error('Failed to parse device data:', device, e);
              return null;
            }
          })
          .filter(item => item !== null)
      )
    );
  }
}
