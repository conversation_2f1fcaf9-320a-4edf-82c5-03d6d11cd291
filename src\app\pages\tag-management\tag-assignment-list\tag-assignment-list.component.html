<!-- Tag Assignment List Component -->
<div class="tag-assignment-list-container">
  <!-- Header Section -->
  <div class="header-card-container">
    <div class="header-section">
      <div class="page-title">
        <h1 class="title">
          <mat-icon class="title-icon">assignment</mat-icon>
          Liste des Affectations
        </h1>
        <p class="subtitle">Consulter et gérer les affectations de tags</p>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="content-container">
    <div class="table-card">
      <div class="table-header">
        <h2>
          <mat-icon>assignment</mat-icon>
          Affectations de Tags
        </h2>
        <div class="table-stats">
          <span class="stats-badge">{{ assignments.length }} affectation(s)</span>
        </div>
      </div>

      <div class="table-container" *ngIf="assignments.length > 0; else noAssignments">
        <table class="data-table">
          <thead>
            <tr>
              <th>Tag</th>
              <th>Type de Cible</th>
              <th>Cible</th>
              <th>Date de Création</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let assignment of assignments">
              <td>
                <div class="tag-cell">
                  <mat-icon class="tag-icon">local_offer</mat-icon>
                  {{ getTagName(assignment) }}
                </div>
              </td>
              <td>
                <span class="target-type-badge" [class]="'type-' + getTargetTypeClass(assignment.TargetType)">
                  {{ getTargetTypeDisplay(assignment.TargetType) }}
                </span>
              </td>
              <td>{{ getTargetName(assignment) }}</td>
              <td>{{ assignment.CreatedAt | date:'dd/MM/yyyy HH:mm' }}</td>
              <td>
                <div class="action-buttons">
                  <button class="btn-action btn-delete" (click)="deleteAssignment(assignment)" title="Supprimer">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <ng-template #noAssignments>
        <div class="empty-state">
          <mat-icon class="empty-icon">assignment</mat-icon>
          <h3>Aucune affectation trouvée</h3>
          <p>Aucune affectation de tag n'a été trouvée dans le système</p>
          <button class="btn-primary" routerLink="/tag-assignment">
            <mat-icon>assignment_add</mat-icon>
            Créer une Affectation
          </button>
        </div>
      </ng-template>
    </div>
  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="isLoading">
    <div class="loading-spinner">
      <mat-icon class="spinning">refresh</mat-icon>
      <p>Chargement...</p>
    </div>
  </div>
</div>
