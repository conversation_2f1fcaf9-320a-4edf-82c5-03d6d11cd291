import { Client } from "./client";
import { ControllerServeur } from "./controllerServeur";
import { Facture } from "./facture";
import { LicenceOption } from "./licenceOption";
import { AuditModel } from "./models-audit/audit-model";

export class Licence extends AuditModel {
    Name!: string;
    Description?: string | null;
    DateDebut?: Date | null;
    DateFin?: Date | null;
    Status?: string | null;
    Factures?: Facture[];
    ControllerServeurs?: ControllerServeur[];
    LicenceOptions?: LicenceOption[];
}

