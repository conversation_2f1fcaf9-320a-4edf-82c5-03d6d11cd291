<div class="controller-section">
  <div class="section-header">
    <h3 class="section-title">Contrôleurs</h3>
    <button
      class="create-button"
      (click)="addNewController()"
      *ngIf="!showCreateForm && !showDetailsModal"
    >
      <i class="material-icons">add</i> Ajouter un contrôleur
    </button>
  </div>

  <!-- Loading state -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="spinner"></div>
    <p>Chargement des contrôleurs...</p>
  </div>

  <div class="modal-overlay" *ngIf="showCreateForm">
    <div class="modal-container">
      <div class="modal-header">
        <h3>
          {{
            isEditMode
              ? "Modifier le Contrôleur Serveur"
              : "Ajouter un Contrôleur Serveur"
          }}
        </h3>
        <button class="close-button" (click)="onFormClosed()">
          <i class="material-icons">close</i>
        </button>
      </div>

      <div class="modal-content">
        <app-form
          [controller]="selectedControllerEdit"
          [isEditMode]="isEditMode"
          (formClosed)="onFormClosed()"
          (controllerCreated)="onControllerCreated($event)"
          (controllerUpdated)="onControllerUpdated($event)"
        >
        </app-form>
        <!-- <app-form
          [controller]="selectedController"
          [isEditMode]="isEditMode"
          (formClosed)="onFormClosed()"
        > 
        </app-form>-->
      </div>
    </div>
  </div>

  <!-- Details Modal -->
  <div class="modal-overlay" *ngIf="showDetailsModal">
    <div class="modal-container details-modal">
      <div class="modal-header">
        <h3>Détails du Contrôleur</h3>
        <button class="close-button" (click)="onDetailsClosed()">
          <i class="material-icons">close</i>
        </button>
      </div>

      <div class="modal-content">
        <app-details
          [controller]="selectedController"
          (detailsClosed)="onDetailsClosed()"
        >
        </app-details>
      </div>
    </div>
  </div>

  <!-- Controllers content -->
  <div class="controllers-content" *ngIf="!isLoading">
    <!-- Table View -->
    <div class="table-container" *ngIf="controllers.length > 0">
      <app-generic-table
        [data]="paginatedControllers"
        [headers]="controllerHeaders"
        [keys]="controllerKeys"
        [actions]="['view', 'edit', 'delete']"
        (actionTriggered)="handleTableAction($event)"
      >
      </app-generic-table>

      <!-- Pagination -->
      <div class="pagination-container">
        <mat-paginator
          [length]="totalControllers"
          [pageSize]="pageSize"
          [pageIndex]="currentPage"
          [pageSizeOptions]="[5, 10, 25, 50]"
          (page)="onPageChange($event)"
          aria-label="Select page"
        >
        </mat-paginator>
      </div>
    </div>

    <!-- No controllers message -->
    <div class="no-controllers-message" *ngIf="controllers.length === 0">
      <i class="material-icons">device_hub</i>
      <p>Aucun contrôleur trouvé pour cette organisation</p>
      <!-- <div class="empty-state-actions">
        <button class="btn btn-secondary" (click)="linkExistingController()">
          <i class="material-icons">link</i> Lier un contrôleur existant
        </button>
        <button class="btn btn-primary" (click)="addNewController()">
          <i class="material-icons">add</i> Créer un nouveau contrôleur
        </button>
      </div> -->
    </div>
  </div>
</div>
