<ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>
<div class="client-edit-container">
  <div class="breadcrumb-nav">
    <button class="back-button" (click)="goBack()">
      <i class="material-icons">arrow_back</i>
    </button>
    <span class="breadcrumb-text">Modification de {{client?.Name}}</span>
  </div>

  <div class="loading-container" *ngIf="isLoading">
    <div class="spinner"></div>
    <p>Chargement des données...</p>
  </div>

  <div class="edit-form-container" *ngIf="!isLoading && client">
    <div class="form-card">
      <h2 class="form-title">Modifier {{client.Name}}</h2>

      <div class="form-invalid" *ngIf="showErrorMessages.length > 0">
        <div class="alert alert-danger" [ngClass]="{'animated fadeIn': showErrorMessages}"
          style="margin-bottom: 0px;border-radius: 0px;padding: 15px 30px;">
          <button aria-hidden="true" data-dismiss="alert" class="close" type="button">×</button>
          <h4>Formulaire invalide</h4>
          <ul>
            <li *ngFor="let error of showErrorMessages"> {{error}} </li>
          </ul>
        </div>
      </div>

      <!-- <form > -->
      <div class="form-grid">
        <div class="form-group">
          <label for="idOrganisation" class="required">Organisation</label>
          <select id="idOrganisation" [(ngModel)]="client.IdOrganisation" [value]="client.IdOrganisation" class="form-select" required>
            <option value="">Sélectionnez une organisation</option>
            <option *ngFor="let org of organisations" [value]="org.Id">
              {{ org.Nom }}
            </option>
          </select>
        </div>
        <div class="form-group">
          <label for="raisonSociale" class="required">Raison Sociale</label>
          <input id="raisonSociale" type="text" [(ngModel)]="client.Name" [value]="client.Name" class="form-control">
        </div>

        <div class="form-group">
          <label for="Address" class="required">Adresse</label>
          <input id="Address" type="text" [(ngModel)]="client.Address" [value]="client.Address" class="form-control">
        </div>
        <div class="form-group">
          <label for="PhoneNumber" class="required">Téléphone</label>
          <input id="PhoneNumber" type="tel" [(ngModel)]="client.PhoneNumber" [value]="client.PhoneNumber"
            class="form-control">
        </div>
        <div class="form-group">
          <label for="dateCreation" class="required">Date de création d'entreprise</label>
          <input id="dateCreation" type="date" [(ngModel)]="date"
 required />
        </div>
        <div class="form-group">
          <label for="LegalForm" class="required">Forme Juridique</label>
          <select id="LegalForm" class="form-select" [(ngModel)]="client.LegalForm" [value]="client.LegalForm" required>
            <!-- <option value="" selected>Sélectionnez une forme juridique</option> -->
            <option value="SARL">SARL - Société à Responsabilité Limitée</option>
            <option value="SA">SA - Société Anonyme</option>
            <option value="SAS">SAS - Société par Actions Simplifiée</option>
            <option value="EURL">
              EURL - Entreprise Unipersonnelle à Responsabilité Limitée
            </option>
            <option value="EIRL">
              EIRL - Entreprise Individuelle à Responsabilité Limitée
            </option>
            <option value="SASU">
              SASU - Société par Actions Simplifiée Unipersonnelle
            </option>
            <option value="SNC">SNC - Société en Nom Collectif</option>
            <option value="SCS">SCS - Société en Commandite Simple</option>
            <option value="SCA">SCA - Société en Commandite par Actions</option>
            <option value="SCOP">
              SCOP - Société Coopérative et Participative
            </option>
            <option value="SCE">SCE - Société Coopérative Européenne</option>
            <option value="GIE">GIE - Groupement d'Intérêt Économique</option>
            <option value="Micro-entreprise">
              Micro-entreprise (auto-entrepreneur)
            </option>
            <option value="LLC">LLC - Limited Liability Company (USA)</option>
            <option value="Inc.">Inc. - Incorporated (USA, Canada)</option>
            <option value="PLC">PLC - Public Limited Company (UK)</option>
            <option value="GmbH">
              GmbH - Gesellschaft mit beschränkter Haftung (Allemagne, Suisse)
            </option>
            <option value="Sàrl">
              Sàrl - Société à responsabilité limitée (Suisse)
            </option>
            <option value="EI">EI - Entreprise Individuelle</option>
          </select>
        </div>
        <div class="form-group">
          <label for="businessSector" class="required">Secteur d'activité</label>
          <input id="businessSector" type="text" [(ngModel)]="client.BusinessSector" [value]="client.BusinessSector"
            required />
        </div>
        <div class="form-group">
          <label for="RC" class="required">RC</label>
          <input id="RC" type="text" placeholder="Registre de Commerce" [(ngModel)]="client.RC" [value]="client.RC"
            required />
        </div>
        <div class="form-group">
          <label for="IF" class="required">IF</label>
          <input id="IF" type="text" placeholder="Identifiant Fiscal" [(ngModel)]="client.IF" [value]="client.IF"
            required />
        </div>
        <div class="form-group">
          <label for="ICE" class="required">ICE</label>
          <input id="ICE" type="text" [(ngModel)]="client.ICE" [value]="client.ICE"
            placeholder="Identifiant Commun de l'Entreprise" required />
        </div>
        <div class="form-group">
          <label for="Patente" class="required">Patente</label>
          <input id="Patente" type="text" [(ngModel)]="client.Patente" [value]="client.Patente" required />
        </div>
        <div class="form-group">
          <label for="SIRET" class="required">SIRET</label>
          <input id="SIRET" type="text" [(ngModel)]="client.SIRET" [value]="client.SIRET" required />
        </div>
        <div class="form-group">
          <label for="SIREN" class="required">SIREN</label>
          <input id="SIREN" type="text" [(ngModel)]="client.SIREN" [value]="client.SIREN" required />
        </div>

        <div class="form-grid" style="grid-column: 1/-1" *ngIf="showMoreFields">
          <div class="form-group">
            <label for="marque">Marque</label>
            <input id="marque" [(ngModel)]="client.Brand" [value]="client.Brand" type="text" />
          </div>
          <div class="form-group">
            <label for="filiale">Filiale</label>
            <input id="filiale" [(ngModel)]="client.Filiale" [value]="client.Filiale" type="text" />
          </div>
          <div class="form-group">
            <label for="pays">Pays</label>
            <select id="pays" [(ngModel)]="client.Country" [value]="client.Country" class="form-select">
              <!-- <option value="" selected>Sélectionnez un pays</option> -->
              <option value="Albanie">Albanie</option>
              <option value="Allemagne">Allemagne</option>
              <option value="Andorre">Andorre</option>
              <option value="Autriche">Autriche</option>
              <option value="Belgique">Belgique</option>
              <option value="Biélorussie">Biélorussie</option>
              <option value="Bosnie-Herzégovine">Bosnie-Herzégovine</option>
              <option value="Bulgarie">Bulgarie</option>
              <option value="Chypre">Chypre</option>
              <option value="Croatie">Croatie</option>
              <option value="Danemark">Danemark</option>
              <option value="Espagne">Espagne</option>
              <option value="Estonie">Estonie</option>
              <option value="Finlande">Finlande</option>
              <option value="France">France</option>
              <option value="Grèce">Grèce</option>
              <option value="Hongrie">Hongrie</option>
              <option value="Irlande">Irlande</option>
              <option value="Islande">Islande</option>
              <option value="Italie">Italie</option>
              <option value="Kosovo">Kosovo</option>
              <option value="Lettonie">Lettonie</option>
              <option value="Liechtenstein">Liechtenstein</option>
              <option value="Lituanie">Lituanie</option>
              <option value="Luxembourg">Luxembourg</option>
              <option value="Macédoine du Nord">Macédoine du Nord</option>
              <option value="Malte">Malte</option>
              <option value="Moldavie">Moldavie</option>
              <option value="Monaco">Monaco</option>
              <option value="Monténégro">Monténégro</option>
              <option value="Norvège">Norvège</option>
              <option value="Pays-Bas">Pays-Bas</option>
              <option value="Pologne">Pologne</option>
              <option value="Portugal">Portugal</option>
              <option value="République tchèque">République tchèque</option>
              <option value="Roumanie">Roumanie</option>
              <option value="Royaume-Uni">Royaume-Uni</option>
              <option value="Russie">Russie</option>
              <option value="Saint-Marin">Saint-Marin</option>
              <option value="Serbie">Serbie</option>
              <option value="Slovaquie">Slovaquie</option>
              <option value="Slovénie">Slovénie</option>
              <option value="Suède">Suède</option>
              <option value="Suisse">Suisse</option>
              <option value="Ukraine">Ukraine</option>
              <option value="Vatican">Vatican</option>
            </select>
          </div>
          <div class="form-group">
            <label for="region">Région</label>
            <input id="region" [(ngModel)]="client.Region" [value]="client.Region" type="text" />
          </div>
          <div class="form-group">
            <label for="ville">Ville</label>
            <input id="ville" [(ngModel)]="client.City" [value]="client.City" type="text" />
          </div>
          <div class="form-group">
            <label for="nomContact">Nom du contact</label>
            <input id="nomContact" [(ngModel)]="client.ContactName" [value]="client.ContactName" type="text" />
          </div>
          <div class="form-group">
            <label for="emailContact">Email du contact</label>
            <input id="emailContact" [(ngModel)]="client.ContactEmail" [value]="client.ContactEmail" type="text" />
          </div>

          <div class="form-group">
            <label for="addressContact">Adresse du contact</label>
            <input id="addressContact" [(ngModel)]="client.ContactAddress" [value]="client.ContactAddress" type="text" />
          </div>
          <div class="form-group">
            <label for="typeEntreprise">Type d'entreprise</label>
            <select id="typeEntreprise" [(ngModel)]="client.CompanyType" [value]="client.CompanyType" class="form-select">
              <!-- <option value="" selected>Sélectionnez un type d'entreprise</option> -->
              <option value="commerciale">Entreprise commerciale</option>
              <option value="industrielle">Entreprise industrielle</option>
              <option value="artisanale">Entreprise artisanale</option>
              <option value="agricole">Entreprise agricole</option>
              <option value="services">Entreprise de services</option>
              <option value="transport">Entreprise de transport</option>
              <option value="btp">
                Entreprise de BTP (bâtiment et travaux publics)
              </option>
              <option value="startup">Start-up</option>
              <option value="cooperative">Coopérative</option>
              <option value="ong">
                ONG (organisation non gouvernementale)
              </option>
              <option value="sociale">Entreprise sociale ou solidaire</option>
              <option value="holding">Holding</option>
              <option value="franchise">Franchise</option>
              <option value="import_export">Société d'import/export</option>
              <option value="conseil">Société de conseil</option>
              <option value="dev_info">
                Société de développement informatique
              </option>
              <option value="communication">Agence de communication</option>
              <option value="formation">Centre de formation</option>
              <option value="publique">Entreprise publique</option>
              <option value="privee">Entreprise privée</option>
            </select>
          </div>
          <div class="form-group">
            <label for="tailleEntreprise">Taille d'entreprise</label>
            <input id="tailleEntreprise" [(ngModel)]="client.CompanySize" [value]="client.CompanySize" type="text" />
          </div>
          <div class="form-group">
            <label for="ActiveEquipement">Nombre d'equipement Actif</label>
            <input id="ActiveEquipement" [(ngModel)]="client.ActiveEquipment" [value]="client.ActiveEquipment" type="text" />
          </div>
          <div class="form-group">
            <label for="InactiveEquipement">Nombre d'equipement Inactif</label>
            <input id="InactiveEquipement" [(ngModel)]="client.InactiveEquipment" [value]="client.InactiveEquipment" type="text" />
          </div>
          <div class="form-group">
            <label for="logo">Logo</label>
            <div class="file-input-container">
              <button
                type="button"
                class="file-button"
                (click)="fileInput.click()"
              >
                <i class="material-icons">upload_file</i> Choisir un logo
              </button>
              <input
                hidden
                type="file"
                #fileInput
                accept="image/*"
                (change)="onFileSelected($event)"
              />
              <span *ngIf="uploadedLogo?.name" class="file-info">
                {{uploadedLogo?.name}}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div class="form-actions">
        <div>
          <button class="show-more-buttons" (click)="showMoreFields = !showMoreFields">
            <span>{{
              showMoreFields ? "Afficher Moins" : "Afficher Plus"
              }}</span>
            <i class="material-icons">
              {{ showMoreFields ? "expand_less" : "expand_more" }}
            </i>
          </button>
        </div>
        <div class="form-actions-buttons">
          <button type="button" class="cancel-button" (click)="goBack()">
            Annuler
          </button>
          <button type="submit" class="submit-button" (click)="onSubmit()">
            Enregistrer
          </button>
        </div>
      </div>
      <!-- </form> -->
      <!-- <div class="current-logo" *ngIf="client.logoClient">
        <img [src]="client.logoClient" alt="Logo actuel" class="logo-preview">
        <p class="logo-caption">Logo actuel</p>
      </div> -->
    </div>
  </div>

  <div class="error-container" *ngIf="!isLoading && !client">
    <i class="material-icons error-icon">error</i>
    <p>Impossible de trouver les détails du client</p>
    <button class="btn-primary" (click)="navigateToList()">Retour à la liste des clients</button>
  </div>
</div>