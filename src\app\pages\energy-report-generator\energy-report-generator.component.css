/* Energy Report Generator Component Styles */

.report-generator-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: white; /* Fond blanc au lieu du dégradé vert */
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header Section */
.header-section {
  text-align: center;
  margin-bottom: 3rem;
  color: black; /* Couleur sombre pour le texte */
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: slideDown 0.8s ease-out;
}

.page-title mat-icon {
  font-size: 3rem;
  width: 3rem;
  height: 3rem;
  animation: pulse 2s infinite;
}

.page-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0;
  animation: fadeIn 1s ease-out 0.3s both;
}

/* Form Container */
.form-container {
  animation: slideUp 0.8s ease-out;
}

.form-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.form-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 2rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #e2e8f0;
}

.section-title mat-icon {
  color: #49B38E;
  font-size: 1.75rem;
  width: 1.75rem;
  height: 1.75rem;
}

/* Form Sections */
.form-section {
  margin-bottom: 2.5rem;
}

.form-label {
  display: block;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.75rem;
  font-size: 1rem;
}

/* Search Container */
.search-container {
  position: relative;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 1rem;
  color: #718096;
  z-index: 2;
}

.search-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.search-input:focus {
  outline: none;
  border-color: #49B38E;
  box-shadow: 0 0 0 3px rgba(73, 179, 142, 0.1);
  transform: translateY(-2px);
}

/* Dropdown */
.dropdown-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  animation: dropDown 0.3s ease-out;
}

.dropdown-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f7fafc;
}

.dropdown-item:hover {
  background: #f7fafc;
  transform: translateX(5px);
}

.dropdown-item:last-child {
  border-bottom: none;
}

.org-info .org-name {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
}

.org-info .org-details {
  font-size: 0.875rem;
  color: #718096;
}

.select-icon {
  color: #49B38E;
}

.no-results {
  padding: 1.5rem;
  text-align: center;
  color: #718096;
  font-style: italic;
}

/* Selected Organization */
.selected-org {
  margin-top: 1rem;
  animation: fadeIn 0.5s ease-out;
}

.org-card-mini {
  background: linear-gradient(135deg, #49B38E, #2E8B6B);
  border-radius: 16px;
  padding: 1.5rem;
  color: white;
  box-shadow: 0 8px 20px rgba(73, 179, 142, 0.3);
}


.org-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.org-icon {
  font-size: 2rem;
  width: 2rem;
  height: 2rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  padding: 0.5rem;
}

.org-header .org-info h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.org-header .org-info p {
  margin: 0;
  opacity: 0.8;
  font-size: 0.9rem;
}

.clear-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: auto;
}

.clear-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.clear-btn mat-icon {
  color: white;
  font-size: 1.25rem;
}

.org-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.75rem;
  border-radius: 8px;
  transition: transform 0.2s ease;
}

.stat-item:hover {
  transform: scale(1.05);
}

.stat-icon {
  font-size: 1.25rem;
  opacity: 0.8;
}

/* Date Range */
.date-range-container {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 1rem;
  align-items: center;
}

.date-field {
  width: 100%;
}

.date-separator {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #49B38E;
  font-weight: bold;
}

.date-separator mat-icon {
  font-size: 1.5rem;
}

/* Format Selection */
.format-selection {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.format-option {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  position: relative;
  overflow: hidden;
}

.format-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(37, 97, 169, 0.1), transparent);
  transition: left 0.5s ease;
}

.format-option:hover::before {
  left: 100%;
}

.format-option:hover {
  border-color: #49B38E;
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(73, 179, 142, 0.2);
}

.format-option.selected {
  border-color: #49B38E;
  background: linear-gradient(135deg, #49B38E, #2E8B6B);
  color: white;
  transform: scale(1.02);
}

.format-icon {
  font-size: 2rem;
  width: 2rem;
  height: 2rem;
}

.format-option.selected .format-icon {
  color: white;
}

.format-info h4 {
  margin: 0 0 0.25rem 0;
  font-weight: 600;
}

.format-info p {
  margin: 0;
  font-size: 0.875rem;
  opacity: 0.8;
}

.check-icon {
  margin-left: auto;
  color: #F29E4C;
  font-size: 1.5rem;
}

.format-option.selected .check-icon {
  color: white;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e2e8f0;
}

/* Buttons */
.btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: width 0.3s ease, height 0.3s ease;
  transform: translate(-50%, -50%);
}

.btn:hover::before {
  width: 300px;
  height: 300px;
}

.btn-primary {
  background: linear-gradient(135deg, #49B38E, #2E8B6B);
  color: white;
  box-shadow: 0 4px 15px rgba(73, 179, 142, 0.4);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(73, 179, 142, 0.5);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background: #f7fafc;
  color: #4a5568;
  border: 2px solid #e2e8f0;
}

.btn-secondary:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
  transform: translateY(-2px);
}

/* Preview Container */
.preview-container {
  animation: slideUp 0.8s ease-out;
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1.5rem 2rem;
  border-radius: 16px;
  margin-bottom: 2rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.preview-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 1rem;
}

/* Report Preview */
.report-preview {
  background: white;
  border-radius: 16px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.8s ease-out;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 3px solid #2561A9;
}

.report-title h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #49B38E, #2E8B6B);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}


.report-period {
  font-size: 1.1rem;
  color: #718096;
  font-weight: 500;
}

.report-logo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-icon {
  font-size: 4rem;
  width: 4rem;
  height: 4rem;
  color: #49B38E; /* Vert au lieu d'orange */
  background: linear-gradient(135deg, #49B38E, #8FD3C9); /* Dégradé vert */
  border-radius: 50%;
  padding: 1rem;
  box-shadow: 0 8px 20px rgba(73, 179, 142, 0.3); /* Ombre verte */
}

/* Report Sections */
.report-section {
  margin-bottom: 3rem;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 1.5rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #e2e8f0;
}

.section-header mat-icon {
  color: #49B38E;
  font-size: 1.75rem;
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 1rem;
  background: #E8F5F2; /* Vert clair au lieu d'orange */
  background: linear-gradient(135deg, #E8F5F2, #C8F0E4); /* Dégradé vert clair */
  border-radius: 8px;
  transition: transform 0.2s ease;
  color: #2E8B6B; /* Texte vert foncé */
}

.info-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.info-item label {
  font-weight: 600;
  color: #4A5566;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item span {
  font-size: 1rem;
  color: #4A5566;
  font-weight: 600;
}

.sites-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.site-address {
  font-size: 0.875rem;
  color: #718096;
  margin-left: 1rem;
}

/* Summary Grid */
.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.summary-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  background: linear-gradient(135deg, #f7fafc, #edf2f7);
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #49B38E, #2E8B6B);
}


.summary-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
}

.summary-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  flex-shrink: 0;
}

.summary-icon.positive {
  background: linear-gradient(135deg, #49B38E, #8FD3C9); /* Dégradé vert */
  color: white;
}
.summary-icon.warning {
  background: linear-gradient(135deg, #49B38E, #2E8B6B);
  color: white;
}

.summary-icon.negative {
  background: linear-gradient(135deg, #4A5566, #2d3748);
  color: white;
}

.summary-icon mat-icon {
  font-size: 1.5rem;
}

.summary-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.25rem;
}

.summary-label {
  font-size: 0.875rem;
  color: #718096;
  font-weight: 500;
}

/* Data Table */
.data-table {
  overflow-x: auto;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.data-table thead {
  background: linear-gradient(135deg, #49B38E, #2E8B6B);
  color: white;
}


.data-table th,
.data-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

.data-table th {
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.875rem;
}

.data-table tbody tr {
  transition: background-color 0.2s ease;
}

.data-table tbody tr:hover {
  background: #f7fafc;
}

.data-table tbody tr:last-child td {
  border-bottom: none;
}

/* Savings Section */
.savings-highlight {
  text-align: center;
  padding: 3rem;
  background: linear-gradient(135deg, #49B38E, #8FD3C9); /* Dégradé vert */
  border-radius: 16px;
  color: white; /* Texte blanc pour contraste */
  position: relative;
  overflow: hidden;
}
.savings-highlight::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
}

.savings-amount {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  position: relative;
  z-index: 1;
}

.savings-description {
  font-size: 1.2rem;
  opacity: 0.9;
  position: relative;
  z-index: 1;
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes dropDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .report-generator-container {
    padding: 1rem;
  }
  
  .form-card,
  .report-preview {
    padding: 1.5rem;
  }
  
  .page-title {
    font-size: 2rem;
  }
  
  .date-range-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .date-separator {
    transform: rotate(90deg);
  }
  
  .format-selection {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .preview-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .action-buttons {
    width: 100%;
    justify-content: center;
  }
  
  .report-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .summary-grid {
    grid-template-columns: 1fr;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .savings-amount {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .report-generator-container {
    padding: 0.5rem;
  }
  
  .form-card,
  .report-preview {
    padding: 1rem;
  }
  
  .page-title {
    font-size: 1.5rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .org-stats {
    grid-template-columns: 1fr;
  }
  
  .data-table {
    font-size: 0.875rem;
  }
  
  .data-table th,
  .data-table td {
    padding: 0.5rem;
  }
}