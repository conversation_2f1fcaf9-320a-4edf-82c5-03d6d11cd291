// sidebar.component.ts
import { Component, EventEmitter, HostListener, Input, OnInit, Output } from '@angular/core';
import { Router, RouterLink, RouterLinkActive } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { AuthService } from '@app/core/services/auth.service';
import { trigger, state, style, animate, transition } from '@angular/animations';
import { CommonModule } from '@angular/common';
import { Menu, menu } from './menu.model';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [RouterLink, RouterLinkActive, MatIconModule, CommonModule],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css'],
  animations: [
    trigger('toggleIconAnimation', [
      state('expanded', style({ transform: 'rotate(0deg)' })),
      state('collapsed', style({ transform: 'rotate(180deg)' })),
      transition('expanded <=> collapsed', [animate('0.3s cubic-bezier(0.4, 0, 0.2, 1)')])
    ]),
    trigger('dropdownAnimation', [
      state('void', style({ opacity: 0, transform: 'translateX(-10px)' })),
      state('*', style({ opacity: 1, transform: 'translateX(0)' })),
      transition('void <=> *', animate('200ms ease-in-out'))
    ])
  ]
})
export class SidebarComponent implements OnInit {
  @Input() isCollapsed = false;
  @Output() sidebarToggled = new EventEmitter<boolean>();
  menuItems: Menu[] = menu;
  userRoles: string[] = [];

  constructor(readonly authService: AuthService, private router: Router) {}

  ngOnInit(): void {
    this.userRoles = this.authService.getRoles();
  }

  hasAuthority(authority?: string[]): boolean {
    if (!authority) return true;
    return this.userRoles.some(role => authority.includes(role));
  }

  activeRoute(starts?: string[]): boolean {
    if (!starts) return false;
    return starts.some(route => this.router.url.startsWith(route));
  }

  toggleSidebar(): void {
    this.isCollapsed = !this.isCollapsed;
    this.sidebarToggled.emit(this.isCollapsed);
  }
}