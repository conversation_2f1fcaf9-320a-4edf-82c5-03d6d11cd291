import { Component, EventEmitter, HostListener, Input, OnInit, Output } from '@angular/core';
import { Router, RouterLink, RouterLinkActive } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { AuthService } from '@app/core/services/auth.service';
import { trigger, state, style, animate, transition } from '@angular/animations';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [RouterLink, RouterLinkActive, MatIconModule, CommonModule],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css'],
  animations: [
    trigger('toggleIconAnimation', [
      state('expanded', style({ transform: 'rotate(0deg)' })),
      state('collapsed', style({ transform: 'rotate(180deg)' })),
      transition('expanded <=> collapsed', [animate('0.3s cubic-bezier(0.4, 0, 0.2, 1)')])
    ]),
    trigger('dropdownAnimation', [
      state('void', style({ opacity: 0, transform: 'translateX(-10px)' })),
      state('*', style({ opacity: 1, transform: 'translateX(0)' })),
      transition('void <=> *', animate('200ms ease-in-out'))
    ])
  ]
})
export class SidebarComponent implements OnInit {
  @Input() isCollapsed = false;
  @Output() sidebarToggled = new EventEmitter<boolean>();
  isAdmin = false;
  isSuperAdmin = false;

  showParametrage = false;
  showClient = false;
  showSites = false;
  parametragePinned = false;

  constructor(readonly authService: AuthService, private router: Router) {}

  ngOnInit(): void {
    const roles = this.authService.getRoles();
    this.isAdmin = roles.includes('Admin');
    this.isSuperAdmin = roles.includes('SuperAdmin');
  }

  @HostListener('document:click', ['$event'])
  handleClickOutside(event: Event) {
    const target = event.target as HTMLElement;
    
    // Only close if clicking outside both the menu and the button
    if (!target.closest('.parent-menu-collapsed') && 
        !target.closest('.collapsed-submenu')) {
      if (this.isCollapsed && !this.parametragePinned) {
        this.showParametrage = false;
      }
    }
  }

  toggleSidebar(): void {
    this.isCollapsed = !this.isCollapsed;
    this.sidebarToggled.emit(this.isCollapsed);
    // When collapsing, ensure all submenus are closed
    if (this.isCollapsed) {
      this.showParametrage = false;
      this.showClient = false;
      this.showSites = false;
    }
  }

  // This method will now primarily be used by the expanded sidebar for click-to-toggle behavior
  toggleParametrage(event?: Event): void {
    if (!this.isCollapsed) { // Only toggle by click if sidebar is expanded
      if (event) {
        event.stopPropagation(); // Prevent document:click from immediately closing it
      }
      this.showParametrage = !this.showParametrage;
      // When the main parametrage menu is closed, ensure nested submenus are also closed
      if (!this.showParametrage) {
        this.showClient = false;
        this.showSites = false;
      }
    }
  }

  toggleParametrageCollapse(event: Event): void {
    event.stopPropagation();
    this.parametragePinned = !this.parametragePinned;
    this.showParametrage = this.parametragePinned;
    
    // Close other submenus when pinning this one
    if (this.parametragePinned) {
      this.showClient = false;
      this.showSites = false;
    }
  }
  
  // Added this method to handle mouseleave from the collapsed parent menu
  closeParametrageSubmenu(): void {
    if (this.isCollapsed) {
      this.showParametrage = false;
    }
  }

  toggleClient(): void {
    this.showClient = !this.showClient;
    if (!this.showClient) {
      this.showSites = false;
    }
  }

  toggleSites(): void {
    this.showSites = !this.showSites;
  }

  handleSubmenuClick(route: string) {
    this.navigateTo(route);
    // Always close the collapsed submenu after navigating
    if (this.isCollapsed) {
      this.showParametrage = false;
    }
  }

  navigateTo(route: string) {
    this.router.navigate([route]);
  }
}