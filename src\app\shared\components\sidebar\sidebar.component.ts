// sidebar.component.ts
import { Component, EventEmitter, HostListener, Input, OnInit, Output } from '@angular/core';
import { Router, RouterLink, RouterLinkActive } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { AuthService } from '@app/core/services/auth.service';
import {
  trigger,
  state,
  style,
  animate,
  transition,
} from '@angular/animations';
import { CommonModule } from '@angular/common';
import { Menu, menu } from './menu.model';
import { ClientApiService } from '@app/core/services/administrative/client.service';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [RouterLink, RouterLinkActive, MatIconModule, CommonModule],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css'],
  animations: [
    trigger('toggleIconAnimation', [
      state('expanded', style({ transform: 'rotate(0deg)' })),
      state('collapsed', style({ transform: 'rotate(180deg)' })),
      transition('expanded <=> collapsed', [
        animate('0.3s cubic-bezier(0.4, 0, 0.2, 1)'),
      ]),
    ]),
    trigger('dropdownAnimation', [
      state('void', style({ 
        opacity: 0, 
        transform: 'translateY(-10px)',
        maxHeight: '0px'
      })),
      state('*', style({ 
        opacity: 1, 
        transform: 'translateY(0)',
        maxHeight: '300px'
      })),
      transition('void <=> *', animate('300ms cubic-bezier(0.4, 0, 0.2, 1)')),
    ]),
  ],
})
export class SidebarComponent implements OnInit {
  @Input() isCollapsed = false;
  @Output() sidebarToggled = new EventEmitter<boolean>();
  
  menuItems: Menu[] = menu;
  userRoles: string[] = [];
  expandedMenus: Set<string> = new Set();
  openCollapsedMenu: string | null = null;
  
  constructor(
    readonly authService: AuthService, 
    readonly router: Router,
    private clientApiService: ClientApiService
  ) {}

// In sidebar.component.ts
ngOnInit(): void {
  this.userRoles = this.authService.getRoles();
  console.log('Initialized user roles:', this.userRoles); // Debug line
  
  // If roles are empty but user is logged in, try to get them from current user
  if (this.userRoles.length === 0 && this.authService.isLoggedIn()) {
    const currentUser = this.authService.getCurrentUser();
    if (currentUser?.user?.roles) {
      this.userRoles = currentUser.user.roles;
      console.log('Falling back to current user roles:', this.userRoles);
    }
 console.log('currentUser', currentUser);
    if (/*currentUser?.user.roles.includes('CLIENT')*/true) {
      console.log('CLIENT user detected, redirecting to organization details...');

      this.clientApiService.getClientByUserId(currentUser!.user.id).subscribe({
        next: (client) => {
          console.log('Client found:', client.Id);
          this.router.navigate([`/organisation-details/${client.Id}`]);
        },
        error: (error) => {
          console.error('Error finding client:', error);
          // Stay on accueil page if client not found
        }
      });
    }
  }
  
  this.initializeExpandedMenus();
}

  /**
   * Initialize expanded menus based on current route
   */
  private initializeExpandedMenus(): void {
    const currentUrl = this.router.url;
    this.menuItems.forEach(menuItem => {
      if (menuItem.subMenus && menuItem.subMenus.length > 0) {
        const hasActiveSubmenu = menuItem.subMenus.some(subItem => 
          currentUrl.startsWith(subItem.url ?? '')
        );
        if (hasActiveSubmenu) {
          this.expandedMenus.add(menuItem.name ?? '');
        }
      }
    });
  }

  /**
   * Check if user has required authority
   */
// In sidebar.component.ts, update the hasAuthority method:
hasAuthority(authority?: string[]): boolean {
  if (!authority || authority.length === 0) return true;
  const hasAuth = this.userRoles.some(role => authority.includes(role));
  return hasAuth;
}

  /**
   * Check if current route matches any of the provided starts
   */
  activeRoute(starts?: string[]): boolean {
    if (!starts || starts.length === 0) return false;
    const currentUrl = this.router.url;
    return starts.some(route => currentUrl.startsWith(route));
  }

  /**
   * Toggle sidebar collapsed state
   */
  toggleSidebar(): void {
    this.isCollapsed = !this.isCollapsed;
    this.sidebarToggled.emit(this.isCollapsed);

    if (this.isCollapsed) {
      this.expandedMenus.clear();
    } else {
      // Always re-initialize expanded menus based on current route
      setTimeout(() => this.initializeExpandedMenus(), 0);
    }
  }

  /**
   * Toggle submenu expansion
   */
  toggleSubmenu(menuName: string): void {
    if (this.isCollapsed) return;
    
    if (this.expandedMenus.has(menuName)) {
      this.expandedMenus.delete(menuName);
    } else {
      this.expandedMenus.add(menuName);
    }
  }

  /**
   * Toggle collapsed menu
   */
  toggleCollapsedMenu(menuName: string): void {
    this.openCollapsedMenu = this.openCollapsedMenu === menuName ? null : menuName;
  }

  /**
   * Check if submenu is expanded
   */
  isSubmenuExpanded(menuName: string): boolean {
    if (this.isCollapsed) return false;
    return this.expandedMenus.has(menuName);
  }

  /**
   * Handle keyboard events for submenu toggle
   */
  onSubmenuKeydown(event: KeyboardEvent, menuName: string): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.toggleSubmenu(menuName);
    }
  }

  /**
   * Track by function for menu items performance optimization
   */
  trackByFn(index: number, item: Menu): string {
    return item.name || index.toString();
  }

  /**
   * Track by function for submenu items performance optimization
   */
  trackBySubFn(index: number, item: Menu): string {
    return item.name || index.toString();
  }

  /**
   * Handle window resize to auto-collapse sidebar on mobile
   */
  @HostListener('window:resize', ['$event'])
  onResize(event: Event): void {
    const target = event.target as Window;
    if (target.innerWidth <= 768 && !this.isCollapsed) {
      this.toggleSidebar();
    }
  }

  /**
   * Close expanded menus when clicking outside (for mobile)
   */
  @HostListener('document:click', ['$event'])



  // Add this method to your sidebar.component.ts

/**
   * Close expanded menus when clicking outside
   */
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    
    // Close collapsed submenu if clicking outside
    if (!target.closest('.parent-menu-collapsed') && !target.closest('.collapsed-submenu')) {
      this.openCollapsedMenu = null;
    }
    
    // Close expanded menus on mobile
    if (!target.closest('.sidebar') && window.innerWidth <= 768) {
      this.expandedMenus.clear();
    }
  }

  /**
   * Handle collapsed menu positioning
   */
  onCollapsedMenuClick(event: MouseEvent, menuName: string): void {
    event.stopPropagation();
    this.openCollapsedMenu = this.openCollapsedMenu === menuName ? null : menuName;
  }
}