// src/app/shared/components/sidebar/sidebar.component.ts (updated)
import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { Router, RouterLink, RouterLinkActive } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { AuthService } from '@app/core/services/auth.service';
import { trigger, state, style, animate, transition } from '@angular/animations';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [RouterLink, RouterLinkActive, MatIconModule, CommonModule],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css'],
  animations: [
    trigger('toggleIconAnimation', [
      state('expanded', style({ transform: 'rotate(0deg)' })),
      state('collapsed', style({ transform: 'rotate(180deg)' })),
      transition('expanded <=> collapsed', [animate('0.3s cubic-bezier(0.4, 0, 0.2, 1)')])
    ])
  ]
})
export class SidebarComponent implements OnInit {
  @Output() sidebarToggled = new EventEmitter<boolean>();
  isCollapsed = false;
  isAdmin = false;
  isSuperAdmin = false;

  // Hierarchical menu state
  showParametrage = false;
  showClient = false;  // Changed from showOrganisation to showClient
  showSites = false;

  constructor(readonly authService: AuthService, private router: Router) {}

  ngOnInit(): void {
    const roles = this.authService.getRoles();
    this.isAdmin = roles.includes('Admin');
    this.isSuperAdmin = roles.includes('SuperAdmin');
  }

  toggleSidebar(): void {
    this.isCollapsed = !this.isCollapsed;
    this.sidebarToggled.emit(this.isCollapsed);
  }

  toggleParametrage(): void {
    this.showParametrage = !this.showParametrage;
    if (!this.showParametrage) {
      // Close all nested submenus when paramétrage is closed
      this.showClient = false;
      this.showSites = false;
    }
  }

  toggleClient(): void {  // Changed from toggleOrganisation to toggleClient
    this.showClient = !this.showClient;
    if (!this.showClient) {
      // Close nested submenus when client is closed
      this.showSites = false;
    }
  }

  toggleSites(): void {
    this.showSites = !this.showSites;
    // No need to close anything as Locaux is the final level
  }

  handleSubmenuClick(route: string) {
    if (this.isCollapsed) {
      // When collapsed, navigate directly
      this.navigateTo(route);
    } else {
      // When expanded, handle submenu logic
      this.navigateTo(route);
    }
  }

  navigateTo(route: string) {
    this.router.navigate([route]);
  }
}
