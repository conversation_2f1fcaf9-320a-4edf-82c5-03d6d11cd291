/* IoT Rules Generator - Clean version without drag & drop */

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--background);
  color: var(--text-primary);
  min-height: 100vh;
}

.max-w-4xl {
  max-width: 56rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.p-6 {
  padding: 1.5rem;
}

.min-h-screen {
  min-height: 100vh;
}

/* Main container */
.bg-white {
  background: var(--surface);
  border: 1px solid var(--card-border);
  box-shadow: var(--shadow-lg);
}

.rounded-lg {
  border-radius: 0.75rem;
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

/* Typography */
h1 {
  color: var(--primary);
  font-size: 1.875rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-shadow: 0 0 10px var(--highlight-color);
}

h2, h3 {
  color: var(--text-primary);
  font-weight: 600;
}

h2 {
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

h3 {
  font-size: 1.125rem;
  margin-bottom: 0.75rem;
}

/* Grid layouts */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-1 {
  gap: 0.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.ml-4 {
  margin-left: 1rem;
}

.p-1 {
  padding: 0.25rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .md\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
}

/* Form elements */
.block {
  display: block;
}

.text-sm {
  font-size: 0.875rem;
}

.text-xs {
  font-size: 0.75rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.text-gray-700,
.text-gray-800 {
  color: var(--text-primary);
}

.text-gray-600 {
  color: var(--primary);
}

.text-gray-500 {
  color: var(--grey-light);
}

.text-blue-600 {
  color: var(--primary);
}

.w-full {
  width: 100%;
}

.w-3 {
  width: 0.75rem;
}

.w-4 {
  width: 1rem;
}

.w-5 {
  width: 1.25rem;
}

.w-6 {
  width: 1.5rem;
}

.h-3 {
  height: 0.75rem;
}

.h-4 {
  height: 1rem;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-12 {
  height: 3rem;
}

/* Input fields */
input[type="text"],
input[type="time"],
select {
  width: 100%;
  padding: 0.75rem;
  background: var(--surface);
  border: 2px solid var(--card-border);
  border-radius: 0.5rem;
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

input[type="text"]:focus,
input[type="time"]:focus,
select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--highlight-color);
  background: var(--beige-light);
}

input[type="text"]::placeholder {
  color: var(--grey-light);
}

/* Select dropdown */
select option {
  background: var(--surface);
  color: var(--text-primary);
}

/* Small selects for groups */
select.p-1 {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  min-width: 4rem;
}

/* Dialog styling */
.mat-dialog-container {
  width: 50vw !important;
  max-width: 800px !important;
  min-width: 600px !important;
}

.dialog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 85vh;
  width: 100%;
  max-width: none;
  min-width: auto;
  position: relative;
  overflow: hidden;
}

.dialog-header {
  flex-shrink: 0;
  padding: 16px 16px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  background: var(--surface, #ffffff);
  z-index: 10;
  border-bottom: 1px solid var(--card-border);
}

.dialog-header h1 {
  margin-bottom: 0;
}

.dialog-content {
  flex-grow: 1;
  overflow-y: auto;
  padding: 16px;
  scrollbar-width: none;
  -ms-overflow-style: none;
  transition: opacity 0.3s ease, filter 0.3s ease;
}

.dialog-content::-webkit-scrollbar {
  display: none;
}

.dialog-footer {
  flex-shrink: 0;
  padding: 16px;
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  position: sticky;
  bottom: 0;
  background: var(--surface, #ffffff);
  border-top: 1px solid var(--card-border);
}

.dialog-footer button {
  min-width: 100px;
  color: white;
  justify-content: center;
}

.mat-dialog-content {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.mat-dialog-content::-webkit-scrollbar {
  display: none !important;
}

.close-button {
  background: transparent !important;
  border: none !important;
  padding: 8px !important;
  border-radius: 50% !important;
  width: 40px !important;
  height: 40px !important;
  min-width: 40px !important;
  color: var(--grey-light) !important;
  box-shadow: none !important;
  transform: none !important;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: var(--card-border) !important;
  color: var(--danger) !important;
  transform: none !important;
  box-shadow: none !important;
}

.close-button[disabled] {
  opacity: 0.4;
  cursor: not-allowed;
}

.close-button[disabled]:hover {
  background: transparent !important;
  color: var(--grey-light) !important;
  transform: none !important;
}

/* Confirmation Dialog Panel Override */
::ng-deep .confirmation-dialog-panel {
  max-width: 500px !important;
  max-height: 90vh !important;
}

::ng-deep .confirmation-dialog-panel .mat-dialog-container {
  padding: 0 !important;
  background: transparent !important;
  box-shadow: none !important;
  border-radius: 16px !important;
  overflow: visible !important;
}

/* Enhanced button styling for confirmations */
.cancel-button,
.download-button {
  background: var(--grey-light) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--card-border) !important;
  transition: all 0.3s ease;
}

.cancel-button:hover,
.download-button:hover {
  background: var(--card-border) !important;
  color: var(--text-primary) !important;
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-md) !important;
}

.download-button {
  background: var(--secondary) !important;
  color: var(--white) !important;
}

.download-button:hover {
  background: var(--grey-dark) !important;
  color: var(--white) !important;
}

/* Checkboxes */
input[type="checkbox"] {
  width: 1.25rem;
  height: 1.25rem;
  background: var(--surface);
  border: 2px solid var(--card-border);
  border-radius: 0.25rem;
  appearance: none;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

input[type="checkbox"]:checked {
  background: var(--primary);
  border-color: var(--primary);
  box-shadow: 0 0 10px var(--highlight-color);
}

input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--white);
  font-weight: bold;
  font-size: 0.875rem;
}

/* Enhanced Save Button */
.save-button {
  background: #2F7D33 !important;
  color: #fff !important;
  border: none !important;
}

.save-button:disabled {
  background: var(--grey-light) !important;
  color: var(--text-primary) !important;
  cursor: not-allowed;
  opacity: 0.7;
}

.save-button:hover:not(:disabled) {
  background: #256029 !important;
  color: #fff !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);
}

.save-button[disabled]:hover {
  transform: none;
  box-shadow: none;
}

.save-button .animate-spin {
  border-width: 2px;
  border-color: white;
  border-top-color: transparent;
}

/* Buttons */
button {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  position: relative;
  overflow: hidden;
  background: var(--primary);
  color: var(--white);
}

button:hover:not(:disabled) {
  background: var(--primary-dark);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

button:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Specific button colors using app color scheme */
.bg-blue-600 {
  background: var(--primary) !important;
}

.bg-blue-600:hover {
  background: var(--primary-dark) !important;
}

.bg-green-600,
.bg-green-500 {
  background: var(--success) !important;
}

.bg-green-600:hover,
.bg-green-500:hover {
  background: var(--primary-dark) !important;
}

.bg-purple-600,
.bg-purple-700 {
  background: var(--secondary) !important;
}

.bg-purple-600:hover,
.bg-purple-700:hover {
  background: var(--grey-dark) !important;
}

.bg-orange-600,
.bg-orange-700 {
  background: var(--warning) !important;
}

.bg-orange-600:hover,
.bg-orange-700:hover {
  background: var(--danger) !important;
}

.bg-indigo-600,
.bg-indigo-700 {
  background: var(--audit) !important;
}

.bg-indigo-600:hover,
.bg-indigo-700:hover {
  background: var(--primary-dark) !important;
}

/* Delete buttons with confirmation feedback */
.text-red-600 {
  color: var(--white) !important;
  background: var(--danger) !important;
  border-radius: 0.5rem;
  padding: 0.5rem;
  transition: all 0.3s ease;
  position: relative;
}

.text-red-600:hover {
  color: var(--white) !important;
  background: var(--black) !important;
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

/* Destructive action confirmation */
.text-red-600:active {
  transform: scale(0.95);
  box-shadow: 0 0 15px rgba(220, 38, 38, 0.5);
}

/* Cards and containers */
.bg-gray-50 {
  background: var(--beige-light);
  border: 1px solid var(--card-border);
  border-radius: 0.5rem;
  padding: 1rem;
  position: relative;
  overflow: hidden;
}

.bg-gray-50::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary), var(--primary-dark));
  opacity: 0.12;
}

/* Condition Groups - Enhanced styling */
.bg-gray-50.border-2 {
  border: 2px solid var(--primary);
  border-radius: 0.75rem;
  box-shadow: 0 4px 12px var(--box-shadow);
  position: relative;
  margin-bottom: 1rem;
}

.bg-gray-50.border-2::before {
  background: linear-gradient(90deg, var(--primary), var(--success));
  height: 3px;
  opacity: 0.6;
}

/* Individual conditions within groups */
.bg-white.border-gray-200 {
  background: var(--surface);
  border: 1px solid var(--card-border);
  border-radius: 0.5rem;
  padding: 0.75rem;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.bg-white.border-gray-200:hover {
  border-color: var(--primary);
  box-shadow: var(--card-shadow);
  transform: translateY(-1px);
}

.border-gray-200 {
  border-color: var(--card-border, #eeeeee);
}

.border-2 {
  border-width: 2px;
}

.rounded-md {
  border-radius: 0.5rem;
}

/* Topics display area */
.bg-gray-50.min-h-\[40px\] {
  background: #f9fafb;
  border: 2px dashed var(--card-border, #eeeeee);
  min-height: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  transition: all 0.3s ease;
}

.min-h-\[40px\] {
  min-height: 40px;
}

/* Flexbox utilities */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.items-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: flex-end;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-1 > * + * {
  margin-top: 0.25rem;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

/* SVG icons */
svg {
  transition: all 0.3s ease;
}

button:hover svg {
  transform: scale(1.1);
}

/* Icon colors */
.text-blue-600,
.text-yellow-600 {
  color: var(--primary, #2F7D33);
  filter: none;
}

/* JSON output */
pre {
  background: var(--beige-light);
  color: var(--text-primary);
  padding: 1.5rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  overflow-x: auto;
  border: 1px solid var(--card-border);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  line-height: 1.6;
  position: relative;
  transition: opacity 0.3s ease;
}

pre::before {
  content: 'JSON';
  position: absolute;
  top: 0.5rem;
  right: 0.75rem;
  background: var(--primary);
  color: var(--white);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: bold;
}

pre.opacity-50 {
  opacity: 0.5;
  position: relative;
}

pre.opacity-50::after {
  content: 'Aperçu mis à jour après sauvegarde';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  white-space: nowrap;
}

/* Overflow handling */
.overflow-x-auto {
  overflow-x: auto;
}

.max-h-48 {
  max-height: 12rem;
}

/* Scrollbar styling for general content */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--white);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--primary, #2F7D33);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark, #3F6433);
}

/* Group labels and operators */
.text-sm.font-semibold {
  color: var(--primary, #2F7D33);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

/* Enhanced hover effects for interactive elements */
.bg-gray-50:hover {
  background: #f0fdf4;
}

/* Animations */
@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--highlight-color, rgba(46, 125, 50, 0.08));
  }
  50% {
    box-shadow: 0 0 20px var(--highlight-color, rgba(46, 125, 50, 0.12));
  }
}

.bg-white {
  animation: glow 3s ease-in-out infinite;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bg-gray-50.border-2 {
  animation: slideIn 0.3s ease-out;
}

/* Danger state animations for destructive actions */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.text-red-600.confirming {
  animation: shake 0.5s ease-in-out;
  background: var(--warning) !important;
}

/* Loading state for confirmation actions */
button.loading {
  pointer-events: none;
  opacity: 0.7;
  position: relative;
}

button.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid currentColor;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* ENHANCED LOADING STATES */

/* Global loading overlay */
.absolute {
  position: absolute;
}

.inset-0 {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.bg-white.bg-opacity-75 {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(2px);
}

.z-50 {
  z-index: 50;
}

/* Enhanced loading spinner */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Loading dialog box */
.bg-white.p-6.rounded-lg.shadow-lg {
  background: var(--surface);
  border: 2px solid var(--primary);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.max-w-sm {
  max-width: 24rem;
}

.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}

/* Progress indicators */
.w-2 {
  width: 0.5rem;
}

.h-2 {
  height: 0.5rem;
}

.rounded-full {
  border-radius: 9999px;
}

.bg-primary {
  background-color: var(--primary, #2F7D33);
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.bg-green-500 {
  background-color: #10b981;
}

.bg-gray-300 {
  background-color: #d1d5db;
}

.text-primary {
  color: var(--primary, #2F7D33);
}

.text-green-600 {
  color: #059669;
}

.text-gray-500 {
  color: #6b7280;
}

.text-center {
  text-align: center;
}

/* Header loading indicator */
.dialog-header .animate-spin {
  border-color: var(--primary, #2F7D33);
  border-top-color: transparent;
}

/* Disabled states when busy */
.pointer-events-none {
  pointer-events: none;
}

.opacity-50 {
  opacity: 0.5;
}

/* Form elements disabled state enhancement */
input[disabled], 
select[disabled], 
button[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #f9fafb;
}

/* Enhanced feedback for loading states */
.loading-feedback {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: var(--surface);
  border: 1px solid var(--primary);
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.loading-feedback .spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--primary);
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Status message styling */
.status-message {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

/* Progress bar for loading states */
.progress-bar {
  width: 100%;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  margin-top: 0.5rem;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--success));
  border-radius: 2px;
  animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
  0% {
    width: 0%;
  }
  50% {
    width: 70%;
  }
  100% {
    width: 100%;
  }
}

/* Blur effect for background content when loading */
.dialog-content.pointer-events-none {
  filter: blur(1px);
  transition: filter 0.3s ease;
}

/* Loading state for different sections */
.conditions-loading,
.actions-loading {
  position: relative;
}

.conditions-loading::before,
.actions-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(1px);
  z-index: 10;
  border-radius: 0.5rem;
}

/* Success state after loading */
.loading-success {
  border-color: #10b981 !important;
  background: #f0fdf4 !important;
}

.loading-success .spinner {
  border-color: #10b981;
  border-top-color: transparent;
}

/* Error state after loading */
.loading-error {
  border-color: #ef4444 !important;
  background: #fef2f2 !important;
}

.loading-error .spinner {
  border-color: #ef4444;
  border-top-color: transparent;
}

/* Utility classes for proper spacing */
.selected {
  background: var(--highlight-color, rgba(46, 125, 50, 0.1)) !important;
  border-color: var(--primary, #2F7D33) !important;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .mat-dialog-container {
    width: 60vw !important;
  }
}

@media (max-width: 900px) {
  .mat-dialog-container {
    width: 80vw !important;
    min-width: 400px !important;
  }
}

@media (max-width: 768px) {
  .p-6 {
    padding: 1rem;
  }
  h1 {
    font-size: 1.5rem;
  }
  .grid {
    gap: 0.75rem;
  }
  button {
    padding: 0.75rem;
    font-size: 0.75rem;
  }
  
  /* Stack grid columns on mobile */
  .md\:grid-cols-5 {
    grid-template-columns: 1fr;
  }
  
  .md\:grid-cols-4 {
    grid-template-columns: 1fr;
  }
  
  .md\:grid-cols-3 {
    grid-template-columns: 1fr;
  }
  
  /* Adjust condition group styling for mobile */
  .bg-gray-50.border-2 {
    padding: 0.75rem;
  }
  
  .bg-white.border-gray-200 {
    padding: 0.5rem;
  }

  /* Mobile responsive loading states */
  .absolute.inset-0 .bg-white.p-6 {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .loading-feedback {
    bottom: 10px;
    right: 10px;
    left: 10px;
    padding: 0.75rem;
  }
  
  .progress-bar {
    height: 3px;
  }
}

@media (max-width: 600px) {
  .mat-dialog-container {
    width: 95vw !important;
    min-width: 320px !important;
  }
}