/* User Management Component Styles */

.user-management-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* Breadcrumb Navigation */
.breadcrumb-nav {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 0;
}

.breadcrumb-text {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  gap: 8px;
}

.title-icon {
  color: #2E7D32;
}

/* Users Section */
.users-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-bottom: 20px;
}

/* Search Section */
.search-section {
  margin-bottom: 24px;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 12px;
  max-width: 500px;
}

.search-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #2E7D32;
}

.search-button {
  padding: 12px;
  background: linear-gradient(45deg, #2E7D32, #81C784);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

.search-button:hover {
  background: linear-gradient(45deg, #81C784, #2E7D32);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.search-button i {
  font-size: 20px;
}

/* Section Header */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.add-user-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(45deg, #2E7D32, #81C784);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

.add-user-btn:hover {
  background: linear-gradient(45deg, #81C784, #2E7D32);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.add-user-btn i {
  font-size: 18px;
}

/* Loading Container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #666;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #2E7D32;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Table Container */
.table-container {
  margin-bottom: 24px;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

/* No Users Message */
.no-users-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #666;
  text-align: center;
}

.no-users-message i {
  font-size: 64px;
  color: #ccc;
  margin-bottom: 16px;
}

.no-users-message p {
  font-size: 16px;
  margin: 0;
}

/* Popup Overlay */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

/* Popup Form */
.popup-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Popup Header */
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 16px;
  border-bottom: 1px solid #e0e0e0;
}

.popup-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.close-btn:hover {
  background: #f5f5f5;
}

.close-btn mat-icon {
  color: #666;
}

/* Validation Errors */
.validation-errors {
  margin: 24px 24px 0;
  padding: 16px;
  background: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 8px;
}

.error-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  color: #c62828;
}

.validation-errors-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.validation-errors-list li {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  color: #d32f2f;
  font-size: 14px;
}

.validation-errors-list li:last-child {
  margin-bottom: 0;
}

.validation-errors-list mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Form Styles */
.user-form {
  padding: 24px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
}

.required {
  color: #d32f2f;
}

.form-group input,
.form-group select {
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #2E7D32;
}

.form-group input::placeholder {
  color: #999;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;
}

.btn-cancel,
.btn-submit {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-cancel {
  background: #f5f5f5;
  color: #666;
}

.btn-cancel:hover {
  background: #eeeeee;
}

.btn-submit {
  background: linear-gradient(45deg, #2E7D32, #81C784);
  color: white;
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

.btn-submit:hover {
  background: linear-gradient(45deg, #81C784, #2E7D32);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.btn-submit:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-management-container {
    padding: 16px;
  }

  .section-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .search-container {
    max-width: none;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .popup-form {
    width: 95%;
    margin: 20px;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn-cancel,
  .btn-submit {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .breadcrumb-text {
    font-size: 1.25rem;
  }

  .section-title {
    font-size: 1.25rem;
  }

  .popup-header h3 {
    font-size: 1.1rem;
  }
}

/* Material Design Overrides */
::ng-deep .mat-mdc-paginator {
  background: transparent !important;
}

::ng-deep .mat-mdc-paginator .mat-mdc-paginator-range-label {
  margin: 0 16px !important;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 6px;
}

.btn-primary {
  background: linear-gradient(45deg, #2E7D32, #81C784);
  color: white;
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(45deg, #81C784, #2E7D32);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.btn-primary:active {
  transform: translateY(0);
}
/* Material Design Form Field Styles */
.form-field {
  width: 100%;
}

.form-field.full-width {
  grid-column: 1 / -1;
}

/* Role option styling */
.role-option {
  display: flex;
  flex-direction: column;
  padding: 4px 0;
}

.role-label {
  font-weight: 500;
  color: #333;
}

.role-description {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

/* Material Design Form Field Overrides */
::ng-deep .mat-mdc-form-field {
  width: 100%;
}

::ng-deep .mat-mdc-form-field .mat-mdc-form-field-subscript-wrapper {
  margin-top: 4px;
}

::ng-deep .mat-mdc-select-panel {
  max-height: 300px;
}

::ng-deep .mat-mdc-option {
  min-height: 60px !important;
  height: auto !important;
  padding: 8px 16px !important;
}

::ng-deep .mat-mdc-option .role-option {
  width: 100%;
}

/* Update form grid for Material Design */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}
