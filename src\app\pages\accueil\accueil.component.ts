import { Component, ViewChild } from '@angular/core';
import { TopPartComponent } from "./top-part/top-part.component";
import { StatsComponent } from "./stats/stats.component";
import { ClientsComponent } from "./clients/clients.component";

@Component({
  selector: 'app-accueil',
  imports: [TopPartComponent, StatsComponent, ClientsComponent],
  templateUrl: './accueil.component.html',
  styleUrl: './accueil.component.css'
})
export class AccueilComponent {
  @ViewChild(ClientsComponent) clientsComponent!: ClientsComponent;

  onOrganisationSelected(organisationId: string): void {
    // Filter clients by organisation ID and scroll to section
    if (this.clientsComponent) {
      this.clientsComponent.filterClientsByOrganisation(organisationId);
      this.clientsComponent.scrollToSection();
    }
  }
}
