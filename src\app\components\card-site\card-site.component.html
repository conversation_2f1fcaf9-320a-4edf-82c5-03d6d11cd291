<div
  class="site-card"
  [@cardHover]="cardState"
  (mouseenter)="onMouseEnter()"
  (mouseleave)="onMouseLeave()"
>
  <div class="card-image-container">
    <div class="image-container">
      <!-- Loading state -->
      <div *ngIf="isLoadingImage" class="loading-container">
        <i class="material-icons loading-icon">hourglass_empty</i>
        <span>Chargement...</span>
      </div>

      <!-- Image display -->
      <img *ngIf="!isLoadingImage && imageUrl && !imageError"
        [src]="imageUrl"
        [alt]="site.Name"
        class="site-image"
        (error)="handleImageError()"
      />

      <!-- No image placeholder -->
      <div *ngIf="!isLoadingImage && (!imageUrl || imageError)" class="no-logo">
        <i class="material-icons">location_city</i>
      </div>
    </div>

  </div>

  <div class="card-header">
    <h3 class="site-title">{{ site.Name }}</h3>
  <div
  class="site-type"
  [ngClass]="{
    'status-actif': site.Status === 'Actif',
    'status-inactif': site.Status === 'Inactif',
    'status-maintenance': site.Status === 'En maintenance'
  }"
  *ngIf="site.Status"
  >
  {{ site.Status | titlecase }}
</div>
  </div>

  <div class="card-content">
    <div class="site-details">
      <div class="detail-item" *ngIf="site.AddressComplement">
        <i class="material-icons">location_on</i>
        <span>{{ site.AddressComplement }}</span>
      </div>
      <div class="detail-item">
  <i class="material-icons">location_city</i>
  <span>
    {{
      site.EmployeesCount === 0
        ? 'aucun local'
        : site.EmployeesCount === 1
          ? '1 local'
          : site.EmployeesCount + ' locaux'
    }}
  </span>
</div>
      <div class="detail-item" *ngIf="site.Contact">
        <i class="material-icons">person</i>
        <span>{{ site.Contact }}</span>
      </div>
      <div class="detail-item" *ngIf="site.PhoneNumber">
        <i class="material-icons">phone</i>
        <span>{{ site.PhoneNumber }}</span>
      </div>
      <div class="detail-item" *ngIf="site.Email">
        <i class="material-icons">email</i>
        <span>{{ site.Email }}</span>
      </div>
    </div>

    <!-- <div class="stats-section">
      <div class="stat-item" *ngIf="site.LocalsCount !== null">
        <span class="stat-value">{{ site.LocalsCount }}</span>
        <span class="stat-label">Locaux</span>
      </div>
      <div class="stat-item" *ngIf="site.EmployeesCount !== null">
        <span class="stat-value">{{ site.EmployeesCount }}</span>
        <span class="stat-label">Employés</span>
      </div>
      <div class="stat-item" *ngIf="site.Surface !== null">
        <span class="stat-value">{{ site.Surface }}m²</span>
        <span class="stat-label">Surface</span>
      </div>
    </div> -->
  </div>

  <div class="card-actions">
    <button class="btn btn-primary" [routerLink]="['/site-locals', site.Id]">
      <i class="material-icons">visibility</i>
    </button>
    <button
      class="btn btn-accent"
      (click)="onEdit($event); $event.stopPropagation()"
    >
      <i class="material-icons">edit</i>
    </button>
    <button
      class="btn btn-danger"
      (click)="onDelete($event); $event.stopPropagation()"
    >
      <i class="material-icons">delete</i>
    </button>
  </div>
</div>