import {
  Component,
  Input,
  OnInit,
  OnChanges,
  SimpleChanges,
  EventEmitter,
  Output,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { ControllerApiService } from '@app/core/services/administrative/controller.service';
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { ClientLicenceControllerView } from '@app/shared/models/clientLicenceControllerView';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { DetailsComponent } from '../details/details.component';
import { FormComponent } from '../../controller/form/form.component';
import { Controller } from '@app/core/models/controller';
import { MatIconModule } from '@angular/material/icon';
import { FormsModule } from '@angular/forms';
import { NgToastService } from 'ng-angular-popup';

@Component({
  selector: 'app-controller',
  standalone: true,
  imports: [
    CommonModule,
    GenericTableComponent,
    MatPaginatorModule,
    DetailsComponent,
    FormComponent,
    MatIconModule,
    FormsModule,
    MatIconModule,
    FormsModule,
  ],
  templateUrl: './controller.component.html',
  styleUrl: './controller.component.css',
})
export class ControllerComponent implements OnInit, OnChanges {
  @Input() clientId: string = '';
  @Output() controllerDeleted = new EventEmitter<string>();

  controllers: ClientLicenceControllerView[] = [];
  allControllersData: ClientLicenceControllerView[] = []; // Store all data for search
  displayedControllers: ClientLicenceControllerView[] = [];
  selectedControllerEdit: Controller | null = null;
  selectedController: ClientLicenceControllerView | null = null;
  showDetailsModal: boolean = false;
  showCreateForm: boolean = false;
  isEditMode: boolean = false;
  searchParam: string = '';
  hasSearchFilter: boolean = false;

  isLoading = false;

  // Pagination properties
  currentPage = 0;
  pageSize = 5;
  totalControllers = 0;

  // Table configuration
  controllerHeaders: string[] = [
    'Nom',
    'Modèle',
    'Numéro de série',
    'Adresse MAC',
    'Adresse IP',
    'État',
    'Serveur',
    'Licence',
  ];
  controllerKeys: string[] = [
    'ControllerName',
    'ControllerModel',
    'ControllerSerialNumber',
    'ControllerMacAddress',
    'ControllerIpAddress',
    'ControllerStateDisplay',
    'ControllerServeurName',
    'LicenceName',
  ];

  constructor(
    private clientService: ClientApiService,
    private controllerService: ControllerApiService,
    private dialog: MatDialog,
    readonly toast: NgToastService
  ) {}

  public showSuccess(message: string, title: string) {
    this.toast.info(message, title, 3000, false);
  }
  public showError(message: string, title: string) {
    this.toast.warning(message, title, 3000, false);
  }

  ngOnInit() {
    if (this.clientId) {
      this.loadControllers();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['clientId']) {
      if (this.clientId) {
        this.loadControllers();
      } else {
        this.controllers = [];
        this.allControllersData = [];
        this.updateDisplayedData();
      }
    }
  }

  loadControllers() {
    if (!this.clientId) {
      this.controllers = [];
      this.allControllersData = [];
      this.updateDisplayedData();
      return;
    }

    this.isLoading = true;

    this.clientService.getClientControllers(this.clientId).subscribe({
      next: (controllers: ClientLicenceControllerView[]) => {
        console.log('Controllers loaded:', controllers);
        // Transform the data to display proper state labels
        const transformedControllers = controllers.map((controller) => ({
          ...controller,
          ControllerStateDisplay: this.getControllerStateDisplay(
            controller.ControllerState
          ),
        }));

        // Store all data for search functionality
        this.allControllersData = transformedControllers;

        // Apply search filter if exists
        this.applySearchFilter();

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading controllers:', error);
        this.controllers = [];
        this.allControllersData = [];
        this.updateDisplayedData();
        this.isLoading = false;
      },
    });
  }

  private applySearchFilter(): void {
    let filteredData = [...this.allControllersData];

    if (this.searchParam && this.searchParam.trim() !== '') {
      const keyword = this.searchParam.trim().toLowerCase();
      this.hasSearchFilter = true;

      filteredData = filteredData.filter((controller) => {
        return controller.ControllerName?.toLowerCase().includes(keyword);
      });

      console.log('Search applied with keyword:', keyword);
      console.log(
        'Filtered results:',
        filteredData.map((c) => c.ControllerName)
      );
    } else {
      this.hasSearchFilter = false;
    }

    this.controllers = filteredData;
    this.totalControllers = filteredData.length;
    this.currentPage = 0; // Reset to first page
    this.updatePaginatedData();
  }

  private updateDisplayedData(): void {
    this.totalControllers = this.controllers.length;
    this.updatePaginatedData();
  }

  private updatePaginatedData(): void {
    const startIndex = this.currentPage * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.displayedControllers = this.controllers.slice(startIndex, endIndex);
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadControllers(); // Reload data for new page
  }

  handleTableAction(event: { action: string; row: any }): void {
    const { action, row } = event;

    switch (action) {
      case 'view':
        this.viewControllerDetails(row);
        break;
      case 'edit':
        this.editController(row);
        break;
      case 'delete':
        this.deleteController(row);
        break;
      default:
        console.warn('Unknown action:', action);
    }
  }

  viewControllerDetails(controller: ClientLicenceControllerView): void {
    console.log('Viewing controller details:', controller);
    this.selectedController = controller;
    this.showDetailsModal = true;
    document.body.classList.add('modal-open');
  }

  editController(controllerView: ClientLicenceControllerView): void {
    console.log('Editing controller:', controllerView);

    const controllerId = controllerView.ControllerId || controllerView.Id;
    if (!controllerId) {
      console.error('No controller ID found');
      return;
    }

    // Fetch the full Controller object by ID
    this.controllerService.getById(controllerId).subscribe({
      next: (controller: Controller) => {
        console.log('Controller fetched for editing:', controller);
        this.selectedControllerEdit = controller;
        this.isEditMode = true;
        this.showCreateForm = true;
        document.body.classList.add('modal-open');
      },
      error: (error) => {
        console.error('Error fetching controller for edit:', error);
        this.dialog.open(ConfirmationDialogComponent, {
          width: '400px',
          data: {
            title: 'Erreur',
            message: 'Erreur lors de la récupération du contrôleur',
            confirmText: 'OK',
            cancelText: '',
            type: 'danger',
          },
        });
      },
    });
  }

  deleteController(controllerView: ClientLicenceControllerView): void {
    const controllerName = controllerView.ControllerName || 'ce contrôleur';
    const controllerId = controllerView.ControllerId || controllerView.Id;

    if (!controllerId) {
      console.error('No controller ID found for deletion');
      return;
    }

    // Confirmation dialog
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirmation de suppression',
        message: `Êtes-vous sûr de vouloir supprimer "${controllerName}" ?`,
        type: 'danger',
        icon: 'warning',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.performDelete(controllerId, controllerName);
      }
    });
  }

  private performDelete(controllerId: string, controllerName: string): void {
    this.controllerService.delete(controllerId).subscribe({
      next: () => {
        // Remove from all data arrays
        this.allControllersData = this.allControllersData.filter(
          (c) => (c.ControllerId || c.Id) !== controllerId
        );

        // Reapply search filter
        this.applySearchFilter();

        // Emit deletion event
        this.controllerDeleted.emit(controllerId);
        this.showSuccess('Contrôleur supprimé avec succès', 'Information');
      },
      error: (err) => {
        console.error('Error deleting controller:', err);

        // Error dialog
        // this.dialog.open(ConfirmationDialogComponent, {
        //   width: '400px',
        //   data: {
        //     title: 'Erreur',
        //     message: 'Erreur lors de la suppression du contrôleur',
        //     confirmText: 'OK',
        //     cancelText: '',
        //     type: 'danger',
        //   },
        // });
        this.showError(
          'Erreur lors de la suppression du contrôleur, Essayer ultérieurement',
          'Erreur'
        );
      },
    });
  }

  addNewController(): void {
    console.log('Adding new controller for client:', this.clientId);
    this.selectedControllerEdit = null;
    this.isEditMode = false;
    this.showCreateForm = true;
    document.body.classList.add('modal-open');
  }

  closeAllModals(): void {
    this.showCreateForm = false;
    this.showDetailsModal = false;
    this.isEditMode = false;
    this.selectedController = null;
    this.selectedControllerEdit = null;
    document.body.classList.remove('modal-open');
  }

  linkExistingController(): void {
    console.log('Linking existing controller to client:', this.clientId);
    // TODO: Implement link existing controller functionality
  }

  // Getter for the paginated data used in the template
  get paginatedControllers(): ClientLicenceControllerView[] {
    return this.displayedControllers;
  }

  // Close details modal
  onDetailsClosed(): void {
    this.showDetailsModal = false;
    this.selectedController = null;
    document.body.classList.remove('modal-open');
  }

  onControllerCreated(newController: Controller): void {
    console.log('New controller created:', newController);

    // Reload the controllers list to get the updated view with all relations
    this.loadControllers();
    this.onFormClosed();
  }

  onControllerUpdated(updatedController: Controller): void {
    console.log('Controller updated:', updatedController);

    // Reload the controllers list to get the updated view with all relations
    this.loadControllers();
    this.onFormClosed();
  }

  // Helper method to transform controller state for display
  private getControllerStateDisplay(state: string | boolean | null): string {
    if (state === null || state === undefined) return 'Inconnu';

    // Handle boolean values directly
    if (typeof state === 'boolean') {
      return state ? 'Actif' : 'Inactif';
    }

    // Handle string values
    if (typeof state === 'string') {
      // Handle boolean-like strings
      if (state.toLowerCase() === 'true' || state === '1') {
        return 'Actif';
      }
      if (state.toLowerCase() === 'false' || state === '0') {
        return 'Inactif';
      }

      // Handle other possible state values
      switch (state.toLowerCase()) {
        case 'active':
        case 'actif':
        case 'online':
        case 'connected':
          return 'Actif';
        case 'inactive':
        case 'inactif':
        case 'offline':
        case 'disconnected':
          return 'Inactif';
        case 'maintenance':
          return 'Maintenance';
        case 'error':
        case 'erreur':
          return 'Erreur';
        default:
          return state; // Return original value if no mapping found
      }
    }

    return 'Inconnu'; // Fallback for any other type
  }

  onFormClosed(): void {
    this.showCreateForm = false;
    this.isEditMode = false;
    this.selectedController = null;
    this.selectedControllerEdit = null;
    document.body.classList.remove('modal-open');
  }

  searchControllers(): void {
    console.log('Search triggered with param:', this.searchParam);
    this.applySearchFilter();
  }

  clearSearch(): void {
    console.log('Clearing search');
    this.searchParam = '';
    this.hasSearchFilter = false;
    this.applySearchFilter();
  }

  // Helper methods for template
  getTotalActiveControllers(): number {
    return this.allControllersData.filter((c) => c.ControllerState === 'Actif')
      .length;
  }

  getTotalInactiveControllers(): number {
    return this.allControllersData.filter(
      (c) => c.ControllerState === 'Inactif'
    ).length;
  }
}