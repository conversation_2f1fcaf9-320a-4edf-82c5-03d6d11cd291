import {
  Component,
  Input,
  OnInit,
  OnChanges,
  SimpleChanges,
  EventEmitter,
  Output,
  HostListener,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { ControllerApiService } from '@app/core/services/administrative/controller.service';
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { ClientLicenceControllerView } from '@app/shared/models/clientLicenceControllerView';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { DetailsComponent } from '../details/details.component';
import { FormComponent } from '../../controller/form/form.component';
import { Controller } from '@app/core/models/controller';
import { MatIconModule } from '@angular/material/icon';
import { FormsModule } from '@angular/forms';
import { FilterParam, Lister, Pagination } from '@app/core/models/util/page';

@Component({
  selector: 'app-controller',
  standalone: true,
  imports: [
    CommonModule,
    GenericTableComponent,
    MatPaginatorModule,
    DetailsComponent,
    FormComponent,
    MatIconModule,
    FormsModule
  ],
  templateUrl: './controller.component.html',
  styleUrl: './controller.component.css',
})
export class ControllerComponent implements OnInit, OnChanges {
  @Input() clientId: string = '';
  @Output() controllerDeleted = new EventEmitter<string>();

  controllers: ClientLicenceControllerView[] = [];
  displayedControllers: ClientLicenceControllerView[] = [];
  selectedControllerEdit: Controller | null = null;
  selectedController: ClientLicenceControllerView | null = null;
  showDetailsModal: boolean = false;
  showCreateForm: boolean = false;
  isEditMode: boolean = false;

  isLoading = false;

  // Search properties
  searchParam: string = '';
  hasSearchFilter: boolean = false;

  // Pagination properties
  currentPage = 0;
  pageSize = 5;
  totalControllers = 0;

  // Table configuration
  controllerHeaders: string[] = [
    'Nom',
    'Modèle',
    'Numéro de série',
    'Adresse MAC',
    'Adresse IP',
    'État',
    'Serveur',
    'Licence',
  ];
  controllerKeys: string[] = [
    'ControllerName',
    'ControllerModel',
    'ControllerSerialNumber',
    'ControllerMacAddress',
    'ControllerIpAddress',
    'ControllerStateDisplay',
    'ControllerServeurName',
    'LicenceName',
  ];

  constructor(
    private clientService: ClientApiService,
    private controllerService: ControllerApiService,
    private dialog: MatDialog
  ) {}

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;

    if (
      this.showDetailsModal &&
      target &&
      target.classList.contains('modal-overlay')
    ) {
      this.closeAllModals();
    }
  }

  ngOnInit() {
    if (this.clientId) {
      this.loadControllers();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['clientId']) {
      if (this.clientId) {
        this.loadControllers();
      } else {
        this.controllers = [];
        this.displayedControllers = [];
        this.totalControllers = 0;
      }
    }
  }

  loadControllers() {
    if (!this.clientId) {
      this.controllers = [];
      this.displayedControllers = [];
      this.totalControllers = 0;
      return;
    }

    this.isLoading = true;

    const pagination: Pagination = {
      CurrentPage: this.currentPage + 1, // API expects 1-based pagination
      PageSize: this.pageSize,
      totalElement: 0,
    };

    const filterParams: FilterParam[] = [
      {
        Column: 'ClientId',
        Value: this.clientId,
        Op: 'eq',
        AndOr: 'AND',
      },
    ];

    // Add search filter if searchParam is not empty
    if (this.searchParam && this.searchParam.trim() !== '') {
      this.hasSearchFilter = true;
      filterParams.push({
        Column: 'ClientName',
        Value: this.searchParam.trim(),
        Op: 'Contains',
        AndOr: 'AND',
      });
    } else {
      this.hasSearchFilter = false;
    }

    const lister: Lister = {
      pagination: pagination,
      filterParams: filterParams,
    };

    this.clientService.gatePageClientControllers(lister).subscribe({
      next: (response) => {
        console.log('Controllers loaded:', response);

        // Transform the data to display proper state labels
        this.controllers = (response.Content || []).map((controller) => ({
          ...controller,
          ControllerStateDisplay: this.getControllerStateDisplay(
            controller.ControllerState
          ),
        }));

        this.displayedControllers = [...this.controllers];

        // Update total count from response
        if (response.Lister?.pagination?.totalElement !== undefined) {
          this.totalControllers = response.Lister.pagination.totalElement;
        } else {
          this.totalControllers = this.controllers.length;
        }

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading controllers:', error);
        this.controllers = [];
        this.displayedControllers = [];
        this.totalControllers = 0;
        this.isLoading = false;
      },
    });
  }

  // Search methods
  onSearch(): void {
    this.currentPage = 0; // Reset to first page when searching
    this.loadControllers();
  }

  clearSearch(): void {
    this.searchParam = '';
    this.hasSearchFilter = false;
    this.currentPage = 0; // Reset to first page when clearing search
    this.loadControllers();
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadControllers(); // Reload data for new page
  }

  handleTableAction(event: { action: string; row: any }): void {
    const { action, row } = event;

    switch (action) {
      case 'view':
        this.viewControllerDetails(row);
        break;
      case 'edit':
        this.editController(row);
        break;
      case 'delete':
        this.deleteController(row);
        break;
      default:
        console.warn('Unknown action:', action);
    }
  }

  viewControllerDetails(controller: ClientLicenceControllerView): void {
    console.log('Viewing controller details:', controller);
    this.selectedController = controller;
    this.showDetailsModal = true;
    document.body.classList.add('modal-open');
  }

  editController(controllerView: ClientLicenceControllerView): void {
    console.log('Editing controller:', controllerView);
    
    const controllerId = controllerView.ControllerId || controllerView.Id;
    if (!controllerId) {
      console.error('No controller ID found');
      return;
    }

    // Fetch the full Controller object by ID
    this.controllerService.getById(controllerId).subscribe({
      next: (controller: Controller) => {
        console.log('Controller fetched for editing:', controller);
        this.selectedControllerEdit = controller;
        this.isEditMode = true;
        this.showCreateForm = true;
        document.body.classList.add('modal-open');
      },
      error: (error) => {
        console.error('Error fetching controller for edit:', error);
        alert('Erreur lors de la récupération du contrôleur');
      }
    });
  }

  deleteController(controllerView: ClientLicenceControllerView): void {
    const controllerName = controllerView.ControllerName || 'ce contrôleur';
    const controllerId = controllerView.ControllerId || controllerView.Id;

    if (!controllerId) {
      console.error('No controller ID found for deletion');
      return;
    }

    // Confirmation dialog
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirmation de suppression',
        message: `Êtes-vous sûr de vouloir supprimer "${controllerName}" ?`,
        type: 'danger',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.performDelete(controllerId, controllerName);
      }
    });
  }

  private performDelete(controllerId: string, controllerName: string): void {
    this.controllerService.delete(controllerId).subscribe({
      next: () => {
        // Reload controllers to get updated data
        this.loadControllers();
        
        // Emit deletion event
        this.controllerDeleted.emit(controllerId);

        // Success dialog
        this.dialog.open(ConfirmationDialogComponent, {
          width: '400px',
          data: {
            title: 'Succès',
            message: 'Contrôleur supprimé avec succès',
            confirmText: 'OK',
            cancelText: '',
            type: 'success',
          },
        });
      },
      error: (err) => {
        console.error('Error deleting controller:', err);
        
        // Error dialog
        this.dialog.open(ConfirmationDialogComponent, {
          width: '400px',
          data: {
            title: 'Erreur',
            message: 'Erreur lors de la suppression du contrôleur',
            confirmText: 'OK',
            cancelText: '',
            type: 'danger',
          },
        });
      },
    });
  }

  addNewController(): void {
    console.log('Adding new controller for client:', this.clientId);
    this.selectedControllerEdit = null;
    this.isEditMode = false;
    this.showCreateForm = true;
    document.body.classList.add('modal-open');
  }

  closeAllModals(): void {
    this.showCreateForm = false;
    this.showDetailsModal = false;
    this.isEditMode = false;
    this.selectedController = null;
    this.selectedControllerEdit = null;
    document.body.classList.remove('modal-open');
  }

  linkExistingController(): void {
    console.log('Linking existing controller to client:', this.clientId);
    // TODO: Implement link existing controller functionality
  }

  // Getter for the paginated data used in the template
  get paginatedControllers(): ClientLicenceControllerView[] {
    return this.displayedControllers;
  }

  // Close details modal
  onDetailsClosed(): void {
    this.showDetailsModal = false;
    this.selectedController = null;
    document.body.classList.remove('modal-open');
  }

  onControllerCreated(newController: Controller): void {
    console.log('New controller created:', newController);
    
    // Reload the controllers list to get the updated view with all relations
    this.loadControllers();
    this.onFormClosed();
  }

  onControllerUpdated(updatedController: Controller): void {
    console.log('Controller updated:', updatedController);
    
    // Reload the controllers list to get the updated view with all relations
    this.loadControllers();
    this.onFormClosed();
  }

  // Helper method to transform controller state for display
  private getControllerStateDisplay(state: string | boolean | null): string {
    if (state === null || state === undefined) return 'Inconnu';

    // Handle boolean values directly
    if (typeof state === 'boolean') {
      return state ? 'Actif' : 'Inactif';
    }

    // Handle string values
    if (typeof state === 'string') {
      // Handle boolean-like strings
      if (state.toLowerCase() === 'true' || state === '1') {
        return 'Actif';
      }
      if (state.toLowerCase() === 'false' || state === '0') {
        return 'Inactif';
      }

      // Handle other possible state values
      switch (state.toLowerCase()) {
        case 'active':
        case 'actif':
        case 'online':
        case 'connected':
          return 'Actif';
        case 'inactive':
        case 'inactif':
        case 'offline':
        case 'disconnected':
          return 'Inactif';
        case 'maintenance':
          return 'Maintenance';
        case 'error':
        case 'erreur':
          return 'Erreur';
        default:
          return state; // Return original value if no mapping found
      }
    }

    return 'Inconnu'; // Fallback for any other type
  }

  onFormClosed(): void {
    this.showCreateForm = false;
    this.isEditMode = false;
    this.selectedController = null;
    this.selectedControllerEdit = null;
    document.body.classList.remove('modal-open');
  }
}