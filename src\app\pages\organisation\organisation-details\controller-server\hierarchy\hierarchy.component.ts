// Robust hierarchy.component.ts with ViewChild fix
import { Component, OnInit, ElementRef, ViewChild, Input, OnDestroy, OnChanges, SimpleChanges, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import * as d3 from 'd3';
import { GroupedControllerServer } from '@app/shared/models/ControllerServerHierarchyDto';

interface HierarchyNode {
  id: string;
  name: string;
  type: 'server' | 'controller';
  status?: string;
  details?: any;
  x?: number;
  y?: number;
  fx?: number;
  fy?: number;
}

interface HierarchyLink {
  source: HierarchyNode | string;
  target: HierarchyNode | string;
}

@Component({
  selector: 'app-hierarchy',
  standalone: true,
  imports: [CommonModule, MatIconModule, MatButtonModule],
  templateUrl: './hierarchy.component.html',
  styleUrl: './hierarchy.component.css'
})
export class HierarchyComponent implements OnInit, AfterViewInit, OnD<PERSON>roy, OnChanges {
  @ViewChild('svg', { static: false }) svgElement!: ElementRef<SVGElement>;
  @ViewChild('tooltip', { static: false }) tooltipElement!: ElementRef<HTMLElement>;

  @Input() hierarchyData: GroupedControllerServer[] = [];

  private svg: any;
  private simulation: any;
  private nodes: HierarchyNode[] = [];
  private links: HierarchyLink[] = [];
  private tooltip: any;
  private viewInitialized = false;

  // Component state
  layoutType: 'force' | 'tree' = 'force';
  isLoading = false;
  error: string | null = null;

  // Statistics
  serverCount = 0;
  controllerCount = 0;
  connectionCount = 0;
  activeControllerCount = 0;

  private resizeObserver?: ResizeObserver;

  ngOnInit() {
    console.log('Hierarchy component initialized with data:', this.hierarchyData);
  }

  ngAfterViewInit() {
    console.log('AfterViewInit - ViewChild elements should be available now');
    this.viewInitialized = true;
    
    // Small delay to ensure DOM is fully rendered
    setTimeout(() => {
      this.initializeVisualization();
      if (this.hierarchyData && this.hierarchyData.length > 0) {
        this.loadHierarchyData();
      }
    }, 50);
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['hierarchyData'] && !changes['hierarchyData'].firstChange) {
      console.log('Hierarchy data changed:', changes['hierarchyData'].currentValue);
      
      if (this.viewInitialized) {
        // Wait a bit to ensure any pending operations complete
        setTimeout(() => {
          this.loadHierarchyData();
        }, 50);
      }
    }
  }

  ngOnDestroy() {
    if (this.simulation) {
      this.simulation.stop();
    }
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }

  private initializeVisualization() {
    console.log('Initializing visualization...');
    
    // Check if ViewChild elements are available
    if (!this.svgElement) {
      console.error('SVG ViewChild not available');
      this.error = 'SVG ViewChild not available';
      return;
    }

    if (!this.tooltipElement) {
      console.error('Tooltip ViewChild not available');
      this.error = 'Tooltip ViewChild not available';
      return;
    }

    const svgNativeElement = this.svgElement.nativeElement;
    const tooltipNativeElement = this.tooltipElement.nativeElement;

    if (!svgNativeElement || !tooltipNativeElement) {
      console.error('Native elements not available');
      this.error = 'Native elements not available';
      return;
    }

    console.log('SVG element:', svgNativeElement);
    console.log('Tooltip element:', tooltipNativeElement);
    
    // Clear any existing content
    d3.select(svgNativeElement).selectAll('*').remove();
    
    this.svg = d3.select(svgNativeElement);
    this.tooltip = d3.select(tooltipNativeElement);

    // Set initial size
    const container = svgNativeElement.parentElement;
    if (container) {
      const width = Math.max(800, container.clientWidth);
      const height = Math.max(600, container.clientHeight || 600);
      
      console.log('Setting SVG dimensions:', width, height);
      
      this.svg
        .attr('width', width)
        .attr('height', height)
        .style('border', '1px solid #e5e7eb')
        .style('background-color', '#fafafa');
    }

    // Setup zoom behavior
    const zoom = d3.zoom()
      .scaleExtent([0.1, 4])
      .on('zoom', (event) => {
        this.svg.select('.main-group')
          .attr('transform', event.transform);
      });

    this.svg.call(zoom);

    // Add definitions for gradients and patterns
    this.setupSVGDefinitions();

    // Add main group for all visualization elements
    this.svg.append('g').attr('class', 'main-group');

    // Setup resize observer
    this.setupResizeObserver();
    
    // Clear any previous error
    this.error = null;
    
    console.log('Visualization initialized successfully');
  }

  private setupSVGDefinitions() {
    const defs = this.svg.append('defs');

    // Server gradient
    const serverGradient = defs.append('linearGradient')
      .attr('id', 'serverGradient')
      .attr('x1', '0%').attr('y1', '0%')
      .attr('x2', '100%').attr('y2', '100%');
    
    serverGradient.append('stop')
      .attr('offset', '0%')
      .attr('stop-color', '#667eea');
    
    serverGradient.append('stop')
      .attr('offset', '100%')
      .attr('stop-color', '#764ba2');

    // Active controller gradient
    const activeControllerGradient = defs.append('linearGradient')
      .attr('id', 'activeControllerGradient')
      .attr('x1', '0%').attr('y1', '0%')
      .attr('x2', '100%').attr('y2', '100%');
    
    activeControllerGradient.append('stop')
      .attr('offset', '0%')
      .attr('stop-color', '#48bb78');
    
    activeControllerGradient.append('stop')
      .attr('offset', '100%')
      .attr('stop-color', '#38a169');

    // Inactive controller gradient
    const inactiveControllerGradient = defs.append('linearGradient')
      .attr('id', 'inactiveControllerGradient')
      .attr('x1', '0%').attr('y1', '0%')
      .attr('x2', '100%').attr('y2', '100%');
    
    inactiveControllerGradient.append('stop')
      .attr('offset', '0%')
      .attr('stop-color', '#e53e3e');
    
    inactiveControllerGradient.append('stop')
      .attr('offset', '100%')
      .attr('stop-color', '#c53030');

    // Add drop shadow filter
    const filter = defs.append('filter')
      .attr('id', 'drop-shadow')
      .attr('x', '-50%')
      .attr('y', '-50%')
      .attr('width', '200%')
      .attr('height', '200%');

    filter.append('feDropShadow')
      .attr('dx', 2)
      .attr('dy', 2)
      .attr('stdDeviation', 3)
      .attr('flood-opacity', 0.3);
  }

  private setupResizeObserver() {
    if (typeof ResizeObserver === 'undefined' || !this.svgElement?.nativeElement) {
      console.warn('ResizeObserver not available or SVG element not found');
      return;
    }

    this.resizeObserver = new ResizeObserver(() => {
      this.handleResize();
    });
    
    const container = this.svgElement.nativeElement.parentElement;
    if (container) {
      this.resizeObserver.observe(container);
    }
  }

  private handleResize() {
    if (this.simulation && this.svgElement?.nativeElement) {
      const container = this.svgElement.nativeElement.parentElement;
      if (container) {
        const width = Math.max(800, container.clientWidth);
        const height = Math.max(600, container.clientHeight || 600);
        
        this.svg
          .attr('width', width)
          .attr('height', height);

        this.simulation
          .force('center', d3.forceCenter(width / 2, height / 2))
          .alpha(0.3)
          .restart();
      }
    }
  }

  loadHierarchyData() {
    console.log('Loading hierarchy data:', this.hierarchyData);
    
    this.isLoading = true;
    this.error = null;

    try {
      // Check if visualization is initialized
      if (!this.svg) {
        console.log('SVG not initialized, initializing now...');
        this.initializeVisualization();
        
        // If still not available, set error
        if (!this.svg) {
          this.error = 'Failed to initialize SVG';
          this.isLoading = false;
          return;
        }
      }

      this.processHierarchyData();
      
      if (this.svg && this.svgElement?.nativeElement) {
        console.log('Creating visualization with nodes:', this.nodes.length, 'links:', this.links.length);
        this.createVisualization();
      } else {
        console.error('SVG not ready for visualization creation');
        this.error = 'SVG not ready for visualization creation';
      }
      
      this.isLoading = false;
    } catch (error) {
      console.error('Error processing hierarchy data:', error);
      this.error = 'Erreur lors du traitement des données: ' + error;
      this.isLoading = false;
    }
  }

  private processHierarchyData() {
    console.log('Processing hierarchy data...');
    
    this.nodes = [];
    this.links = [];

    if (!this.hierarchyData || this.hierarchyData.length === 0) {
      console.log('No hierarchy data available');
      this.updateStatistics();
      return;
    }

    console.log('Processing grouped hierarchy data:', this.hierarchyData);

    this.hierarchyData.forEach((server: GroupedControllerServer) => {
      console.log('Processing server:', server.Name);
      
      // Add server node
      const serverNode: HierarchyNode = {
        id: server.Id,
        name: server.Name,
        type: 'server',
        status: server.Status,
        details: {
          maxControllers: server.MaxControllers,
          maxSensors: server.MaxSensors,
          geographicZone: server.GeographicZone,
          commercialCondition: server.CommercialCondition,
          licence: server.Licence?.Name || '',
          triggerType: server.TriggerType,
          actionType: server.ActionType,
          eventType: server.EventType,
          createdAt: server.CreatedAt,
          lastUpdatedAt: server.LastUpdatedAt
        }
      };
      this.nodes.push(serverNode);
      console.log('Added server node:', serverNode);

      // Add controller nodes and links
      if (server.ControllerServerControllers && Array.isArray(server.ControllerServerControllers)) {
        console.log(`Processing ${server.ControllerServerControllers.length} controllers for server ${server.Name}`);
        
        server.ControllerServerControllers.forEach((relation) => {
          if (relation.Controller) {
            const controller = relation.Controller;
            const controllerId = controller.Id;
            
            console.log('Processing controller:', controller.HostName, controllerId);
            
            // Check if controller already exists (avoid duplicates across servers)
            let controllerNode = this.nodes.find(n => n.id === controllerId);
            
            if (!controllerNode) {
              controllerNode = {
                id: controllerId,
                name: controller.HostName || 'Contrôleur Inconnu',
                type: 'controller',
                status: controller.State ? 'active' : 'inactive',
                details: {
                  model: controller.Model || '',
                  serialNumber: controller.SerialNumber || '',
                  macAddress: controller.MacAddress || '',
                  ipAddress: controller.IpAddress || '',
                  baseTopic: controller.BaseTopic || '',
                  lastConnection: controller.LastConnection || null,
                  state: controller.State
                }
              };
              this.nodes.push(controllerNode);
              console.log('Added controller node:', controllerNode);
            }

            // Add link between server and controller
            const link = {
              source: serverNode.id,
              target: controllerNode.id
            };
            this.links.push(link);
            console.log('Added link:', link);
          }
        });
      } else {
        console.log(`No controllers found for server ${server.Name}`);
      }
    });

    console.log(`Processed hierarchy: ${this.nodes.length} nodes, ${this.links.length} links`);
    console.log('Final nodes:', this.nodes);
    console.log('Final links:', this.links);

    this.updateStatistics();
  }

  private updateStatistics() {
    this.serverCount = this.nodes.filter(n => n.type === 'server').length;
    this.controllerCount = this.nodes.filter(n => n.type === 'controller').length;
    this.activeControllerCount = this.nodes.filter(n => n.type === 'controller' && n.status === 'active').length;
    this.connectionCount = this.links.length;
    
    console.log('Updated statistics:', {
      serverCount: this.serverCount,
      controllerCount: this.controllerCount,
      activeControllerCount: this.activeControllerCount,
      connectionCount: this.connectionCount
    });
  }

  private createVisualization() {
    console.log('Creating visualization with layout:', this.layoutType);
    
    if (this.layoutType === 'force') {
      this.createForceLayout();
    } else {
      this.createTreeLayout();
    }
  }

  private createForceLayout() {
    console.log('Creating force layout...');
    
    if (!this.svgElement?.nativeElement) {
      console.error('SVG element not available for force layout');
      this.error = 'SVG element not available for force layout';
      return;
    }

    const container = this.svgElement.nativeElement.parentElement;
    if (!container) {
      console.error('SVG container not found');
      this.error = 'SVG container not found';
      return;
    }

    const width = Math.max(800, container.clientWidth);
    const height = Math.max(600, container.clientHeight || 600);

    console.log('Force layout dimensions:', width, height);

    // Clear previous visualization
    this.svg.select('.main-group').selectAll('*').remove();

    // If no nodes, show a message
    if (this.nodes.length === 0) {
      console.log('No nodes to display, showing no data message');
      this.showNoDataMessage();
      return;
    }

    console.log('Creating force simulation with', this.nodes.length, 'nodes and', this.links.length, 'links');

    // Create force simulation
    this.simulation = d3.forceSimulation(this.nodes)
      .force('link', d3.forceLink(this.links).id((d: any) => d.id).distance(120))
      .force('charge', d3.forceManyBody().strength(-400))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius(40));

    const g = this.svg.select('.main-group');

    // Create links (only if there are links)
    let link: any;
    if (this.links.length > 0) {
      console.log('Creating', this.links.length, 'links');
      
      link = g.append('g')
        .attr('class', 'links')
        .selectAll('line')
        .data(this.links)
        .enter().append('line')
        .attr('class', 'link')
        .attr('stroke', '#94a3b8')
        .attr('stroke-width', 2)
        .attr('stroke-opacity', 0.6);

      this.addLinkEventHandlers(link);
      console.log('Links created successfully');
    }

    // Create nodes
    console.log('Creating', this.nodes.length, 'nodes');
    
    const node = g.append('g')
      .attr('class', 'nodes')
      .selectAll('g')
      .data(this.nodes)
      .enter().append('g')
      .attr('class', 'node')
      .call(this.drag());

    // Add node circles
    node.append('circle')
      .attr('r', (d: HierarchyNode) => d.type === 'server' ? 30 : 25)
      .attr('fill', (d: HierarchyNode) => {
        if (d.type === 'server') return 'url(#serverGradient)';
        return d.status === 'active' ? 'url(#activeControllerGradient)' : 'url(#inactiveControllerGradient)';
      })
      .attr('stroke', '#fff')
      .attr('stroke-width', 3)
      .attr('filter', 'url(#drop-shadow)')
      .style('cursor', 'pointer');

    // Add node labels
    node.append('text')
      .attr('class', 'node-label')
      .attr('text-anchor', 'middle')
      .attr('dy', '0.35em')
      .style('font-family', 'Roboto, sans-serif')
      .style('font-size', '12px')
      .style('font-weight', '500')
      .style('fill', '#fff')
      .style('pointer-events', 'none')
      .text((d: HierarchyNode) => this.truncateText(d.name, 10));

    // Add status indicators for controllers
    node.filter((d: HierarchyNode) => d.type === 'controller')
      .append('circle')
      .attr('r', 8)
      .attr('cx', 20)
      .attr('cy', -20)
      .attr('fill', (d: HierarchyNode) => d.status === 'active' ? '#48bb78' : '#e53e3e')
      .attr('stroke', '#fff')
      .attr('stroke-width', 2);

    // Add event handlers
    this.addNodeEventHandlers(node);

    console.log('Nodes created successfully');

    // Update positions in simulation tick
    this.simulation.on('tick', () => {
      if (link) {
        link
          .attr('x1', (d: any) => d.source.x)
          .attr('y1', (d: any) => d.source.y)
          .attr('x2', (d: any) => d.target.x)
          .attr('y2', (d: any) => d.target.y);
      }

      node.attr('transform', (d: any) => `translate(${d.x},${d.y})`);
    });

    console.log('Force layout created successfully');
  }

  private showNoDataMessage() {
    if (!this.svgElement?.nativeElement) return;
    
    const container = this.svgElement.nativeElement.parentElement;
    if (!container) return;

    const width = Math.max(800, container.clientWidth);
    const height = Math.max(600, container.clientHeight || 600);

    const g = this.svg.select('.main-group');
    
    g.append('text')
      .attr('x', width / 2)
      .attr('y', height / 2)
      .attr('text-anchor', 'middle')
      .attr('dominant-baseline', 'middle')
      .style('font-family', 'Roboto, sans-serif')
      .style('font-size', '18px')
      .style('fill', '#6b7280')
      .text('Aucune donnée de hiérarchie disponible');
      
    console.log('No data message displayed');
  }

  private createTreeLayout() {
    // Keep existing tree layout implementation
    if (!this.svgElement?.nativeElement) {
      console.error('SVG element not available for tree layout');
      return;
    }

    const container = this.svgElement.nativeElement.parentElement;
    if (!container) return;

    const width = Math.max(800, container.clientWidth);
    const height = Math.max(600, container.clientHeight || 600);

    this.svg.select('.main-group').selectAll('*').remove();

    if (this.nodes.length === 0) {
      this.showNoDataMessage();
      return;
    }

    const hierarchy = this.createHierarchyStructure();
    const tree = d3.tree().size([height - 100, width - 200]);
    const root = d3.hierarchy(hierarchy);
    tree(root);

    const g = this.svg.select('.main-group').attr('transform', 'translate(100, 50)');

    g.selectAll('.link')
      .data(root.links())
      .enter().append('path')
      .attr('class', 'link')
      .attr('fill', 'none')
      .attr('stroke', '#94a3b8')
      .attr('stroke-width', 2)
      .attr('stroke-opacity', 0.6)
      .attr('d', d3.linkHorizontal().x((d: any) => d.y).y((d: any) => d.x));

    const node = g.selectAll('.node')
      .data(root.descendants())
      .enter().append('g')
      .attr('class', 'node')
      .attr('transform', (d: any) => `translate(${d.y},${d.x})`);

    node.append('circle')
      .attr('r', (d: any) => d.data.type === 'server' ? 30 : 25)
      .attr('fill', (d: any) => {
        if (d.data.type === 'server') return 'url(#serverGradient)';
        return d.data.status === 'active' ? 'url(#activeControllerGradient)' : 'url(#inactiveControllerGradient)';
      })
      .attr('stroke', '#fff')
      .attr('stroke-width', 3)
      .attr('filter', 'url(#drop-shadow)');

    node.append('text')
      .attr('class', 'node-label')
      .attr('dx', (d: any) => d.children ? -35 : 35)
      .attr('dy', '0.35em')
      .attr('text-anchor', (d: any) => d.children ? 'end' : 'start')
      .style('font-family', 'Roboto, sans-serif')
      .style('font-size', '12px')
      .style('font-weight', '500')
      .style('fill', '#374151')
      .text((d: any) => d.data.name);

    this.addNodeEventHandlers(node);
  }

  private createHierarchyStructure(): any {
    const root = {
      name: 'Système de Contrôle',
      type: 'root',
      children: [] as any[]
    };

    const servers = this.nodes.filter(n => n.type === 'server');
    
    servers.forEach(server => {
      const serverChildren = this.links
        .filter(l => (l.source as any) === server.id || (l.source as HierarchyNode).id === server.id)
        .map(l => {
          const targetId = typeof l.target === 'string' ? l.target : l.target.id;
          return this.nodes.find(n => n.id === targetId);
        })
        .filter(Boolean);
      
      root.children.push({
        ...server,
        children: serverChildren
      });
    });

    return root;
  }

  private addNodeEventHandlers(nodeSelection: any) {
    nodeSelection
      .on('mouseover', (event: any, d: any) => this.showTooltip(event, d))
      .on('mouseout', () => this.hideTooltip())
      .on('click', (event: any, d: any) => this.handleNodeClick(event, d));
  }

  private addLinkEventHandlers(linkSelection: any) {
    linkSelection
      .on('mouseover', function(this: any) {
        d3.select(this)
          .attr('stroke', '#667eea')
          .attr('stroke-width', 3)
          .attr('stroke-opacity', 1);
      })
      .on('mouseout', function(this: any) {
        d3.select(this)
          .attr('stroke', '#94a3b8')
          .attr('stroke-width', 2)
          .attr('stroke-opacity', 0.6);
      });
  }

  private showTooltip(event: any, d: any) {
    const nodeData = d.data || d;
    
    let content = `<div class="tooltip-title">${nodeData.name}</div>`;
    content += `<div class="tooltip-type">${nodeData.type === 'server' ? 'Contrôleur Serveur' : 'Contrôleur'}</div>`;
    
    if (nodeData.details) {
      content += '<div class="tooltip-details">';
      if (nodeData.type === 'server') {
        content += `<div><strong>Max Contrôleurs:</strong> ${nodeData.details.maxControllers}</div>`;
        content += `<div><strong>Max Capteurs:</strong> ${nodeData.details.maxSensors}</div>`;
        if (nodeData.details.geographicZone) {
          content += `<div><strong>Zone:</strong> ${nodeData.details.geographicZone}</div>`;
        }
        if (nodeData.details.licence) {
          content += `<div><strong>Licence:</strong> ${nodeData.details.licence}</div>`;
        }
        content += `<div><strong>Statut:</strong> ${nodeData.status}</div>`;
      } else {
        content += `<div><strong>Statut:</strong> ${nodeData.status === 'active' ? 'Actif' : 'Inactif'}</div>`;
        if (nodeData.details.ipAddress) {
          content += `<div><strong>IP:</strong> ${nodeData.details.ipAddress}</div>`;
        }
        if (nodeData.details.model) {
          content += `<div><strong>Modèle:</strong> ${nodeData.details.model}</div>`;
        }
        if (nodeData.details.serialNumber) {
          content += `<div><strong>Série:</strong> ${nodeData.details.serialNumber}</div>`;
        }
        if (nodeData.details.lastConnection) {
          const lastConnection = new Date(nodeData.details.lastConnection).toLocaleString('fr-FR');
          content += `<div><strong>Dernière connexion:</strong> ${lastConnection}</div>`;
        }
      }
      content += '</div>';
    }

    this.tooltip
      .style('opacity', 1)
      .html(content)
      .style('left', (event.pageX + 10) + 'px')
      .style('top', (event.pageY - 10) + 'px');
  }

  private hideTooltip() {
    this.tooltip.style('opacity', 0);
  }

  private handleNodeClick(event: any, d: any) {
    const nodeData = d.data || d;
    this.highlightConnectedNodes(nodeData.id);
  }

  private highlightConnectedNodes(nodeId: string) {
    this.svg.selectAll('.node circle').style('opacity', 0.3);
    this.svg.selectAll('.link').style('opacity', 0.1);

    const connectedNodeIds = new Set([nodeId]);
    
    this.links.forEach(link => {
      const sourceId = typeof link.source === 'string' ? link.source : (link.source as HierarchyNode).id;
      const targetId = typeof link.target === 'string' ? link.target : (link.target as HierarchyNode).id;
      
      if (sourceId === nodeId || targetId === nodeId) {
        connectedNodeIds.add(sourceId);
        connectedNodeIds.add(targetId);
      }
    });

    this.svg.selectAll('.node')
      .filter((d: any) => {
        const id = d.data ? d.data.id : d.id;
        return connectedNodeIds.has(id);
      })
      .select('circle')
      .style('opacity', 1);

    this.svg.selectAll('.link')
      .filter((d: any) => {
        const sourceId = typeof d.source === 'string' ? d.source : d.source.id;
        const targetId = typeof d.target === 'string' ? d.target : d.target.id;
        return sourceId === nodeId || targetId === nodeId;
      })
      .style('opacity', 1);

    setTimeout(() => {
      this.svg.selectAll('.node circle').style('opacity', 1);
      this.svg.selectAll('.link').style('opacity', 0.6);
    }, 3000);
  }

  private drag() {
    return d3.drag()
      .on('start', (event: any, d: any) => {
        if (!event.active && this.simulation) {
          this.simulation.alphaTarget(0.3).restart();
        }
        d.fx = d.x;
        d.fy = d.y;
      })
      .on('end', (event: any, d: any) => {
        if (!event.active && this.simulation) {
          this.simulation.alphaTarget(0);
        }
        d.fx = null;
        d.fy = null;
      });
  }

  private truncateText(text: string, maxLength: number): string {
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }

  // Public methods for controls
  resetView() {
    if (!this.svgElement?.nativeElement) {
      console.warn('SVG element not available for reset view');
      return;
    }

    const container = this.svgElement.nativeElement.parentElement;
    if (!container) return;

    const width = Math.max(800, container.clientWidth);
    const height = Math.max(600, container.clientHeight || 600);
    
    this.svg.transition().duration(750).call(
      d3.zoom().transform,
      d3.zoomIdentity.translate(0, 0).scale(1)
    );

    if (this.simulation) {
      this.simulation
        .force('center', d3.forceCenter(width / 2, height / 2))
        .alpha(0.3)
        .restart();
    }
  }

  toggleLayout() {
    this.layoutType = this.layoutType === 'force' ? 'tree' : 'force';
    
    if (this.simulation) {
      this.simulation.stop();
    }
    
    this.createVisualization();
  }

  exportSVG() {
    if (!this.svgElement?.nativeElement) {
      console.warn('SVG element not available for export');
      return;
    }

    const svgElement = this.svgElement.nativeElement;
    const serializer = new XMLSerializer();
    const svgString = serializer.serializeToString(svgElement);
    
    const blob = new Blob([svgString], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `hierarchy-${new Date().getTime()}.svg`;
    link.click();
    
    URL.revokeObjectURL(url);
  }

  // Force reload method for debugging
  forceReload() {
    console.log('Force reloading hierarchy...');
    this.viewInitialized = false;
    this.error = null;
    
    setTimeout(() => {
      this.viewInitialized = true;
      this.initializeVisualization();
      this.loadHierarchyData();
    }, 100);
  }
}