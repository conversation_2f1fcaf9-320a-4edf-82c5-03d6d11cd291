import { CommonModule } from '@angular/common';
import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { Router } from '@angular/router';
import { Client } from '@app/core/models/client';
import { Organisation } from '@app/core/models/organisation';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { OrganisationApiService } from '@app/core/services/administrative/organisation.service';

@Component({
  selector: 'app-top-part',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './top-part.component.html',
  styleUrl: './top-part.component.css',
})
export class TopPartComponent implements OnInit {

  organisations: Organisation[] = [];
  clientCountsByOrg: { [orgId: string]: number } = {};
  initialDisplayCount = 5;
  showAllCategories = false;

  @Output() organisationSelected = new EventEmitter<Organisation>();

  get visibleOrganisations(): Organisation[] {
    return this.showAllCategories
      ? this.organisations
      : this.organisations.slice(0, this.initialDisplayCount);
  }

  constructor(
    readonly organisationService: OrganisationApiService,
    readonly clientService: ClientApiService,
    public router: Router
  ) {}

  ngOnInit(): void {
    this.loadOrganisationsAndClients();
    console.log(this.organisations);
  }

  toggleCategoriesDisplay(): void {
    this.showAllCategories = !this.showAllCategories;
  }

  loadOrganisationsAndClients(): void {
    this.organisationService.getAll().subscribe({
      next: (orgs) => {
        this.organisations = orgs;
        this.loadClientCounts(); // Load after organisations
      },
      error: (err) => console.error('Error loading organisations:', err),
    });
  }

  loadClientCounts(): void {
    this.clientService.getAll().subscribe({
      next: (clients: Client[]) => {
        // Count clients by organisation ID
        for (const client of clients) {
          const orgId = client.IdOrganisation;
          if (orgId) {
            this.clientCountsByOrg[orgId] =
              (this.clientCountsByOrg[orgId] || 0) + 1;
          }
        }
      },
      error: (err) => console.error('Error loading clients:', err),
    });
  }

  loadOrganisations(): void {
    this.organisationService.getAll().subscribe({
      next: (data) => {
        this.organisations = data;
      },
      error: (err) => {
        console.error('Failed to load organisations:', err);
      },
    });
    console.log(this.organisations);
  }

  getClientCount(org: Organisation): number {
    return this.clientCountsByOrg[org.Id!] || 0;
  }

  hasLogo(org: Organisation): boolean {
    return !!(org.LogoOrganisation && org.LogoOrganisation.trim());
  }

  getLogoUrl(org: Organisation): string {
    return  `data:image/jpeg;base64,${org.LogoOrganisation}`;

  }

  getIcon(org: Organisation): string {
    const name = org.Nom.toLowerCase();
    if (name.includes('école') || name.includes('ecole')) return 'school';
    if (name.includes('hôpital') || name.includes('hopital'))
      return 'local_hospital';
    if (name.includes('entrepôt') || name.includes('entrepot'))
      return 'warehouse';
    if (name.includes('bureau')) return 'business_center';
    if (name.includes('usine')) return 'factory';
    if (name.includes('magasin')) return 'store';
    if (name.includes('résidence') || name.includes('residence')) return 'home';
    if (name.includes('centre commercial')) return 'shopping_bag';
    if (name.includes('restaurant')) return 'restaurant';
    if (name.includes('hôtel') || name.includes('hotel')) return 'hotel';
    if (name.includes('maison')) return 'house';
    return 'business';
  }

  getColor(org: Organisation): string {
    const colors = [
      '#4CAF50',
      '#2196F3',
      '#FF9800',
      '#9C27B0',
      '#F44336',
      '#00BCD4',
      '#795548',
      '#607D8B',
      '#E91E63',
      '#3F51B5',
      '#009688',
      '#FF5722',
    ];
    let hash = 0;
    for (let i = 0; i < org.Nom.length; i++) {
      const char = org.Nom.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash;
    }
    return colors[Math.abs(hash) % colors.length];
  }


  onCardClick(org: Organisation): void {
    this.organisationSelected.emit(org);
    // Plus de navigation ici, juste l'émission de l'événement
  }

  onDetailsClick(event: Event, org: Organisation): void {
    event.stopPropagation();
    console.log('Details requested for:', org);
    
    // Navigate to the statistics page
    this.router.navigate(['/organisation-statistics', org.Id]);
  }
}