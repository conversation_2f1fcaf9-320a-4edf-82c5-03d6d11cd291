// navigation.service.ts
import { Injectable } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { filter, pairwise } from 'rxjs/operators';

@Injectable({ providedIn: 'root' })
export class NavigationService {
  private history: { url: string, position: number }[] = [];

  constructor(private router: Router) {
    this.router.events
      .pipe(
        filter((event): event is NavigationEnd => event instanceof NavigationEnd),
        pairwise()
      )
      .subscribe(([previous, current]) => {
        this.history.push({
          url: previous.urlAfterRedirects,
          position: window.scrollY
        });
      });
  }

  public getPrevious(): { url: string, position: number } | null {
    return this.history.length > 0 ? this.history[this.history.length - 1] : null;
  }
}
