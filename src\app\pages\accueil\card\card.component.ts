import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Client } from '@app/core/models/client';

@Component({
  selector: 'app-card',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './card.component.html',
  styleUrls: ['./card.component.css'],
})
export class CardComponent {
  @Input() client!: Client;
  @Output() view = new EventEmitter<string>();
  @Output() edit = new EventEmitter<string>();
  @Output() delete = new EventEmitter<string>();

  // get imageUrl() {
  //   if (this.client?.ClientLogo) {
  //     //console.log('Client logo found:', this.client.ClientLogo.toString());
  //     return `data:image/jpeg;base64,${this.client.ClientLogo}`;
  //   }
  // }

  onDetailsClick(): void {
    this.view.emit(this.client.Id);
  }

  onEditClick(event: Event): void {
    event.stopPropagation();
    this.edit.emit(this.client.Id);
  }

  onDeleteClick(event: Event): void {
    event.stopPropagation();
    this.delete.emit(this.client.Id);
  }
}
