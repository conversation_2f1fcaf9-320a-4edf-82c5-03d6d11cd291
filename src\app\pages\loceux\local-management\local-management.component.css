.local-management-container {
  width: 95%;
  margin: 30px auto;
  padding: 25px;
  background: linear-gradient(145deg, #ffffff, #f9f9f9);
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

/* Update popup overlay styles */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
  padding: 20px;
  overflow-y: auto;
}

/* Update popup form styles */
.popup-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 95%;
  max-width: 800px;
  max-height: calc(100vh - 40px);
  overflow-y: auto;
  animation: slideIn 0.3s ease-out;
  position: relative;
  margin: auto;
  margin-top: 50px;
  padding: 20px;
}

/* Update form grid for better responsiveness */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
  align-items: end;
}

/* Add responsive styles */
@media (max-width: 1024px) {
  .popup-form {
    width: 90%;
    margin: 20px auto;
  }

  .form-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .popup-overlay {
    padding: 15px;
  }

  .popup-form {
    width: 95%;
    max-height: calc(100vh - 30px);
    margin: 15px auto;
    padding: 15px;
  }

  .popup-header {
    padding: 15px 20px;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
    gap: 10px;
  }

  .form-actions button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .popup-overlay {
    padding: 10px;
  }

  .popup-form {
    width: 100%;
    margin: 10px auto;
    padding: 10px;
    border-radius: 8px;
  }

  .popup-header {
    padding: 12px 15px;
  }

  .popup-header h3 {
    font-size: 18px;
  }
}

/* Add styles for zoom handling */
@media (min-width: 2000px) {
  .popup-form {
    max-width: 1200px;
  }

  .form-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

/* Add smooth scrollbar for the popup */
.popup-form {
  scrollbar-width: thin;
}

.popup-form::-webkit-scrollbar {
  width: 8px;
}

.popup-form::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.popup-form::-webkit-scrollbar-thumb {
  background: #4CAF50;
  border-radius: 4px;
}

.popup-form::-webkit-scrollbar-thumb:hover {
  background: #3e8e41;
}

.form-group input[readonly] {
  cursor: not-allowed;
  background-color: #f5f5f5;
}

/* Ensure form inputs are properly sized */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  color: #4a5568;
  background-color: white;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #4CAF50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  color: #1a202c;
  background-color: white;
  transition: border-color 0.2s;
}

.form-group select:focus {
  border-color: #4CAF50;
  outline: none;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

.form-group select:invalid {
  color: #718096;
}

.form-group select option {
  color: #1a202c;
}

.form-group input[type="file"] {
  padding: 8px;
  height: auto;
  border: 2px dashed #e2e8f0;
  background-color: #f8fafc;
  cursor: pointer;
}

.form-group input[type="file"]:hover {
  border-color: #4CAF50;
  background-color: #f0fff4;
}

.form-group input[type="checkbox"] {
  width: auto;
  height: auto;
  margin-top: 5px;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e2e8f0;
  border-radius: 12px 12px 0 0;
}

.popup-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #2E7D32;
  display: flex;
  align-items: center;
  gap: 10px;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;
}

.close-btn:hover {
  background: #f0f0f0;
}

.current-images-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
  margin-top: 8px;
}

.current-image-item {
  position: relative;
  aspect-ratio: 1;
}

.current-image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

/* Animation keyframes */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Search Bar (copied and adapted from site-management) */
.search-bar {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  align-items: center;
}

.search-bar input {
  flex: 1;
  max-width: 450px;
  padding: 12px 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #f8fafc;
  color: #4a5568;
}

.search-bar input:focus {
  outline: none;
  border-color: #4CAF50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.search-bar input::placeholder {
  color: #a0aec0;
}

.search-button {
  background: linear-gradient(45deg, #4CAF50, #81C784);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.search-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
  background: linear-gradient(45deg, #81C784, #4CAF50);
}

.search-button mat-icon {
  font-size: 18px;
  height: 18px;
  width: 18px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  animation: slideIn 0.5s ease-out;
  width: 95%;
}

.page-title {
  margin: 0;
}

.title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.title-icon {
  font-size: 30px;
  color: #4CAF50;
  background: linear-gradient(45deg, #4CAF50, #81C784);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.actions {
  display: flex;
  gap: 15px;
}

.create-button {
  background: linear-gradient(45deg, #4CAF50, #81C784);
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.create-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(76, 175, 80, 0.5);
  background: linear-gradient(45deg, #81C784, #4CAF50);
}

.create-form-card,
.edit-form-container {
  margin-bottom: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.4s ease-in;
  background: white;
  padding: 25px;
  width: 95%;
}

.create-form-card form,
.edit-form-container form {
  width: 100%;
}

.edit-form-container h2 {
  font-size: 24px;
  color: #2d3748;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.edit-form-container h2 mat-icon {
  color: #4CAF50;
}

.form-group textarea {
  height: auto;
  min-height: 100px;
  resize: vertical;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.form-actions button {
  padding: 10px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.form-actions button[type="button"] {
  background-color: #fff;
  border: 1px solid #e2e8f0;
  color: #4a5568;
}

.form-actions button[type="submit"] {
  background: linear-gradient(45deg, #4CAF50, #81C784);
  border: none;
  color: white;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.2);
}

.form-actions button[type="button"]:hover {
  background-color: #f8fafc;
  border-color: #cbd5e0;
}

.form-actions button[type="submit"]:hover {
  background: linear-gradient(45deg, #81C784, #4CAF50);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(76, 175, 80, 0.3);
}

.form-actions button[type="submit"]:disabled {
  background: #e2e8f0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  font-size: 18px;
  color: #4CAF50;
}

.table-view {
  width: 100%;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.mat-paginator {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.current-images-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
  margin-top: 8px;
}

.current-image-item {
  position: relative;
  aspect-ratio: 1;
}

.current-image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.remove-image-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0;
}

.validation-errors {
  background-color: #fef2f2;
  border-left: 4px solid #ef4444;
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 4px;
  animation: fadeIn 0.3s ease-out;
}

.validation-errors-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ef4444;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.validation-errors-list {
  margin: 0;
  padding-left: 20px;
  list-style-type: none;
}

.validation-errors-list li {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ef4444;
  font-size: 13px;
  margin-bottom: 4px;
}

.validation-errors-list li mat-icon,
.validation-errors-title mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

.required {
  color: #ef4444;
  margin-left: 4px;
}

.error-message {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.form-group.error input,
.form-group.error select,
.form-group.error textarea {
  border-color: #ef4444;
  background-color: #fff5f5;
}

.form-group.error label {
  color: #ef4444;
}

.error-message {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
}

/* Update the edit form popup styles */
.edit-form-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.edit-form-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideIn 0.3s ease-out;
  margin-top: 60px;
  padding: 25px;
}

/* Update form header to match local style */
.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8f9fa;
  border-radius: 12px 12px 0 0;
  margin: -25px -25px 20px -25px;
}

.form-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #2E7D32;
  display: flex;
  align-items: center;
  gap: 10px;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;
}

.close-button:hover {
  background: #f0f0f0;
}

/* Update form validation errors style */
.form-errors {
  background-color: #fef2f2;
  border-left: 4px solid #ef4444;
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 4px;
  animation: fadeIn 0.3s ease-out;
}

.form-errors ul {
  margin: 0;
  padding-left: 20px;
  list-style-type: none;
}

.form-errors li {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ef4444;
  font-size: 13px;
  margin-bottom: 4px;
}

.form-errors li mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

@media (max-width: 768px) {
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .actions {
    width: 100%;
    flex-direction: column;
    gap: 10px;
  }

  .create-button {
    width: 100%;
    padding: 12px;
    justify-content: center;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 24px;
  }

  .title-icon {
    font-size: 26px;
  }

  .search-bar input {
    max-width: 100%;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .form-actions button {
    width: 100%;
  }
}

.form-group .mat-error {
  font-size: 12px;
  color: #ef4444;
  margin-top: 4px;
}

.form-group .mat-form-field {
  width: 100%;
}

.form-group .mat-select {
  width: 100%;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 20px;
    margin-right: 10px;
}

/* Add these styles for the confirmation dialog */
::ng-deep .mat-dialog-container {
  padding: 0 !important;
  border-radius: 12px !important;
  overflow: hidden !important;
}

::ng-deep .confirmation-dialog .mat-dialog-content {
  margin: 0 !important;
  padding: 20px !important;
}

::ng-deep .confirmation-dialog .mat-dialog-actions {
  margin: 0 !important;
  padding: 20px !important;
  border-top: 1px solid #e2e8f0;
}