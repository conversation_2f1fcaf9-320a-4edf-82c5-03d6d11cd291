import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiService } from '../api.service';
import { SubscribedOptions } from '@app/core/models/subscribedoptions';
@Injectable({ providedIn: 'root' })
export class SubscribedOptionsApiService extends ApiService<SubscribedOptions> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint("subscribedOptions");
  }
}
