import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { HttpClient } from '@angular/common/http';
import { ControllerServeur } from '@app/core/models/controllerServeur';
import { Observable } from 'rxjs';
import { ControllerServerViewData } from '@app/shared/models/ControllerServerViewData';

@Injectable({ providedIn: 'root' })
export class ControllerServeurApiService extends ApiService<ControllerServeur> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint('controller-serveur');
  }

  updateMaxValues(): Observable<any> {
    return this.http.post<any>(
      `${this.baseUrl}controller-serveur/update-all-max-values`,
      {}
    );
  }

  getAllLight(): Observable<any> {
    return this.http.get<any>(
      `${this.baseUrl}controller-serveur/get-all-light`,
      {}
    );
  }

  getAllLightByClient(clientId: string): Observable<any> {
    return this.http.get<any>(
      `${this.baseUrl}controller-serveur/get-light-by-client/${clientId}`,
      {}
    );
  }

  getClientControllerServers(
    clientId: string
  ): Observable<ControllerServerViewData[]> {
    const url = `${this.baseUrl}controller-serveur/client/${clientId}`;
    return this.http.get<ControllerServerViewData[]>(url);
  }

  generateName(): Observable<string> {
    return this.http.get<string>(
      `${this.baseUrl}controller-serveur/generate-code`
    );
  }

  canAdd(subscriptionId: string) {
    return this.http.get<boolean>(
      `${this.baseUrl}controller-serveur/subscription/${subscriptionId}/can-add`
    );
  }
}
