import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { HttpClient } from '@angular/common/http';
import { ControllerServeur } from '@app/core/models/controllerServeur';
import { Observable } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class ControllerServeurApiService extends ApiService<ControllerServeur> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint('controller-serveur');
  }

  updateMaxValues(): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}controller-serveur/update-all-max-values`, {});
  }

  getAllLight(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}controller-serveur/get-all-light`, {});
  }

}