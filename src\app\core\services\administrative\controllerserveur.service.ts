import { Injectable } from "@angular/core";
import { ApiService } from "../api.service";
import { HttpClient } from "@angular/common/http";
import { ControllerServeur } from "@app/core/models/controllerServeur";

@Injectable({ providedIn: 'root' })
export class ControllerServeurApiService extends ApiService<ControllerServeur> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint("controller-serveur");
  }
}