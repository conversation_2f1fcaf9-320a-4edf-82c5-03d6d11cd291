import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { Site } from '../../core/models/site';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-card-site',
  standalone: true,
  imports: [CommonModule, RouterModule, MatIconModule],
  templateUrl: './card-site.component.html',
  styleUrls: ['./card-site.component.css'],
  animations: [
    trigger('cardHover', [
      state(
        'initial',
        style({
          transform: 'scale(1)',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
        })
      ),
      state(
        'hovered',
        style({
          transform: 'scale(1.02)',
          boxShadow: '0 8px 20px rgba(0, 0, 0, 0.15)',
        })
      ),
      transition('initial <=> hovered', animate('0.2s ease-in-out')),
    ]),
  ],
})
export class CardSiteComponent implements OnInit {
  @Input() site!: any;
  @Output() viewDetails = new EventEmitter<Site>();
  @Output() edit = new EventEmitter<Site>();
  @Output() delete = new EventEmitter<string>();
  @Output() addSite = new EventEmitter<void>();

  cardState = 'initial';
  imageError = false;

  constructor(public router: Router) {}

  ngOnInit() {
    console.log('CardSite initialized for site:', {
      siteId: this.site.id,
      hasImage: !!this.site.image
    });
  }

  get imageUrl(): string {
    if (this.imageError || !this.site.Image) {
      return 'assets/images/default-organisation.png'; // Add a placeholder image in your assets
    }
    return `data:image/jpeg;base64,${this.site.Image}`;
  }

  handleImageError(event: Event): void {
    this.imageError = true;
    (event.target as HTMLImageElement).src = 'assets/images/placeholder-building.png';
  }

  onMouseEnter(): void {
    this.cardState = 'hovered';
  }

  onMouseLeave(): void {
    this.cardState = 'initial';
  }

  onView(event?: Event): void {
    if (event) event.stopPropagation();
    this.router.navigate(['/site-details', this.site.id]);
  }

  onEdit(event?: Event): void {
    if (event) event.stopPropagation();
    this.edit.emit(this.site);
  }

onDelete(event?: Event): void {
  if (event) event.stopPropagation();
  this.delete.emit(this.site.Id);
}

  onAdd(): void {
    this.addSite.emit();
  }
}