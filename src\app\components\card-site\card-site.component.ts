import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { Site } from '../../core/models/site';
import { MatIconModule } from '@angular/material/icon';
import { SiteApiService } from '../../core/services/administrative/site.service';

@Component({
  selector: 'app-card-site',
  standalone: true,
  imports: [CommonModule, RouterModule, MatIconModule],
  templateUrl: './card-site.component.html',
  styleUrls: ['./card-site.component.css'],
  animations: [
    trigger('cardHover', [
      state(
        'initial',
        style({
          transform: 'scale(1)',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
        })
      ),
      state(
        'hovered',
        style({
          transform: 'scale(1.02)',
          boxShadow: '0 8px 20px rgba(0, 0, 0, 0.15)',
        })
      ),
      transition('initial <=> hovered', animate('0.2s ease-in-out')),
    ]),
  ],
})
export class CardSiteComponent implements OnInit {
  @Input() site!: any;
  @Output() viewDetails = new EventEmitter<Site>();
  @Output() edit = new EventEmitter<Site>();
  @Output() delete = new EventEmitter<string>();
  @Output() addSite = new EventEmitter<void>();

  cardState = 'initial';
  imageError = false;
  imageUrl: string = '';
  isLoadingImage = false;

  constructor(
    public router: Router,
    private readonly siteService: SiteApiService
  ) {}

  ngOnInit() {
    console.log('CardSite initialized for site:', {
      siteId: this.site.Id,
      hasImage: !!this.site.Image
    });

    // Load image using the downloadImage service
    this.loadSiteImage();
  }

  private loadSiteImage(): void {
    if (!this.site.Id) {
      this.imageUrl = 'assets/images/default-organisation.png';
      return;
    }

    // Check if site already has image data
    if (this.site.Image) {
      this.imageUrl = `data:image/jpeg;base64,${this.site.Image}`;
      return;
    }

    // Load image using downloadImage service
    this.isLoadingImage = true;
    this.siteService.downloadImage(this.site.Id).subscribe({
      next: (response) => {
        this.isLoadingImage = false;
        if (response && response.imageData) {
          this.imageUrl = `data:image/jpeg;base64,${response.imageData}`;
        } else {
          this.imageUrl = 'assets/images/default-organisation.png';
        }
      },
      error: (error) => {
        this.isLoadingImage = false;
        console.error('Error loading site image:', error);
        this.imageUrl = 'assets/images/default-organisation.png';
      }
    });
  }

  handleImageError(): void {
    this.imageError = true;
    this.imageUrl = 'assets/images/default-organisation.png';
  }

  onMouseEnter(): void {
    this.cardState = 'hovered';
  }

  onMouseLeave(): void {
    this.cardState = 'initial';
  }

  onView(event?: Event): void {
    if (event) event.stopPropagation();
    this.router.navigate(['/site-details', this.site.id]);
  }

  onEdit(event?: Event): void {
    if (event) event.stopPropagation();
    this.edit.emit(this.site);
  }

onDelete(event?: Event): void {
  if (event) event.stopPropagation();
  this.delete.emit(this.site.Id);
}

  onAdd(): void {
    this.addSite.emit();
  }
}