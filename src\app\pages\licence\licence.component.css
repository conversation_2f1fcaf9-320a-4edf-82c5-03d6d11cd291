:host {
  display: block;
  font-family: '<PERSON><PERSON>', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  color: #2d3748;
  background-color: #f9fafb;
  min-height: 100vh;
  height: 100%;
}

.container {
  max-width: none;
  width: 100%;
  margin: 0;
  padding: 0;
  height: 120%;
  display: flex;
  flex-direction: column;
}

.client-billing-section {
  padding: 1.5rem;
  background: white;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.client-search-section {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.05);
}

.search-container {
  display: flex;
  justify-content: flex-start; /* Changed from space-between to flex-start */
  gap: 1.5rem;
  flex-wrap: wrap;
}

.search-input-container {
  position: relative;
  flex: 0 1 auto; /* Changed from flex: 1 to prevent growing */
  min-width: 300px;
  transition: all 0.3s ease;
  margin-right: auto; /* This will push other items to the right */
}

.search-input {
  width: 100%; /* Changed from 40% to take full width of its container */
  padding: 0.85rem 1rem 0.85rem 3rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  background: white;
  height: 38px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.03);
}

.search-input:focus {
  outline: none;
  border-color: #49b38e;
  box-shadow: 0 0 0 3px rgba(73, 179, 142, 0.2);
}

.search-icon {
  position: absolute;
  left: 1.25rem; /* Adjusted position */
  top: 50%;
  transform: translateY(-50%);
  color: #a0aec0;
  font-size: 1.5rem; /* Larger icon */
}

.selected-client-container {
  display: flex;
  align-items: center;
  background: #f0fdf4;
  border-radius: 14px; /* Larger radius */
  padding: 0.75rem 1rem; /* Increased padding */
  border: 1px solid #bbf7d0;
  min-width: 260px; /* Wider container */
  transition: all 0.3s ease;
}

.selected-client-container.improved-client-card {
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
}

.client-info {
  display: flex;
  align-items: center;
  gap: 1rem; /* Increased gap */
}

.client-image {
  width: 44px; /* Larger image */
  height: 44px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid #d1fae5;
}

.client-name {
  font-weight: 600;
  font-size: 1.05rem; /* Larger font */
  color: #065f46;
}

.clear-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px; /* Larger button */
  height: 32px;
  border-radius: 50%;
  border: none;
  background: #ecfdf5;
  color: #34d399;
  cursor: pointer;
  transition: all 0.2s;
  margin-left: 12px; /* Increased margin */
}

.clear-button:hover {
  background: #dcfce7;
  color: #10b981;
}

.dropdown {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  width: 100%; /* Changed from 45% to match input width */
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  margin-top: 0.25rem;
  max-height: 300px;
  overflow-y: auto;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  z-index: 1000;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem; /* Increased padding */
  gap: 1.25rem; /* Increased gap */
  cursor: pointer;
  transition: background 0.2s;
}

.dropdown-item:hover {
  background-color: #f8fafc;
}

.client-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem; /* Added gap between elements */
}

.client-site {
  font-size: 0.95rem; /* Larger font */
  color: #64748b;
}

.license-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin: 2rem auto;
  max-width: 1200px;
  padding: 0 1.5rem;
  width: 100%;
  overflow: visible !important; /* prevent scroll */
}

.license-card {
  position: relative;
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-top: 30px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0,0,0,0.04);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), 
              transform 0.4s cubic-bezier(.4,2,.6,1);
  will-change: transform, opacity;
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.licenses-container {
  width: 100%;
  padding: 2rem 0;
  background-color: #f9fafb;
}

.license-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%; 
  height: 6px;
  background: #e2e8f0; 
}

.license-card:nth-child(3n+1)::before {
  background: #49b38e; /* Green for first card in each row */
}

.license-card:nth-child(3n+2)::before {
  background: #3b82f6; /* Blue for second card in each row */
}

.license-card:nth-child(3n+3)::before {
  background: #8b5cf6; /* Purple for third card in each row */
}

/* For popular and featured cards (if you want to keep those classes) */
.license-card.popular::before {
  background: #3b82f6; /* Blue for popular */
}

.license-card.featured::before {
  background: #8b5cf6; /* Purple for featured */
}

.license-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 28px rgba(0,0,0,0.1);
}

.license-card.popular {
  border-top: 4px solid #3b82f6;
}

.license-card.featured {
  border-top: 4px solid #8b5cf6;
}

.card-header {
  margin-bottom: 1.5rem;
  text-align: center;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f1f5f9;
}

.card-header h3 {
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
  color: #1e293b;
}

.card-description {
  color: #64748b;
  font-size: 0.95rem;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0 0 1rem 0;
  flex-grow: 1;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f1f5f9;
  gap: 0.75rem;
}

.custom-checkbox {
  display: block;
  position: relative;
  cursor: pointer;
  user-select: none;
  margin-right: 0.5rem;
}

.custom-checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.custom-checkbox input:disabled {
  background: #e5e7eb !important;
  color: #a1a1aa !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
  opacity: 0.7;
}

.checkmark {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 20px;
  width: 20px;
  border-radius: 5px;
  background-color: #f1f5f9;
  transition: all 0.2s;
}

.custom-checkbox input:checked ~ .checkmark {
  background-color: #10b981;
  border-color: #10b981;
}

.custom-checkbox input:checked ~ .checkmark .verified-icon {
  opacity: 1;
  transform: scale(1);
}

.custom-checkbox input:not(:checked) ~ .checkmark .verified-icon {
  opacity: 0;
  transform: scale(0.8);
}

.custom-checkbox input:disabled ~ .checkmark {
  background-color: #e2e8f0;
  cursor: not-allowed;
}

.verified-icon, .cancel-icon {
  font-size: 18px;
  color: white;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.2s;
}

.custom-checkbox input:checked ~ .checkmark .verified-icon,
.custom-checkbox input:not(:checked) ~ .checkmark .cancel-icon {
  opacity: 1;
  transform: scale(1);
}

.feature-details {
  flex-grow: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.feature-name {
  font-size: 1.2rem; /* Slightly larger font */
  color: #334155;
}

.select-btn {
  width: 100%;
  padding: 0.75rem;
  border: none;
  border-radius: 10px;
  background: linear-gradient(135deg, #49b38e, #2c7744);
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  margin-top: auto;
  font-size: 0.95rem;
}

.select-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 18px rgba(73, 179, 142, 0.3);
}

.select-btn:disabled {
  background: #e5e7eb !important;
  color: #a1a1aa !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
  opacity: 0.7;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.confirmation-popup {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  position: relative;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f1f5f9;
}

.popup-header h3 {
  font-size: 1.4rem;
  color: #1e293b;
  margin: 0;
}

.close-popup-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background: #f8fafc;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s;
}

.close-popup-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.popup-content {
  margin-bottom: 2rem;
}

.confirmation-message {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.confirmation-icon {
  color: #f59e0b;
  font-size: 2rem;
  margin-top: 0.2rem;
}

.confirmation-message p {
  font-size: 1rem;
  color: #475569;
  line-height: 1.6;
  margin: 0;
}

.client-logo {
  width: 52px;
  height: 52px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid #d1fae5;
}

.client-license-summary {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.25rem;
  border: 1px solid #e2e8f0;
}

.summary-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.delete-option-inputs-wrapper {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem 0;
}

.delete-option-inputs-wrapper .improved-form-group {
  margin-bottom: 0;
}

.delete-option-inputs-wrapper .improved-input {
  width: 100%;
  padding: 0.8rem;
  border-radius: 12px;
  border: 1.5px solid #e0f2fe;
  background: white;
  font-size: 1.08rem;
  color: #065f46;
  font-weight: 500;
  transition: border 0.2s, box-shadow 0.2s;
  outline: none;
  box-shadow: 0 2px 8px rgba(16,185,129,0.07);
}

.delete-option-inputs-wrapper .improved-input:focus {
  border: 2px solid #10b981;
  box-shadow: 0 0 0 3px rgba(16,185,129,0.13);
}

.delete-option-popup-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #f1f5f9;
}

.delete-option-popup-actions .cancel-btn,
.delete-option-popup-actions .confirm-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 0.95rem;
  border: none;
}

.delete-option-popup-actions .cancel-btn {
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.delete-option-popup-actions .cancel-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.delete-option-popup-actions .confirm-btn {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.delete-option-popup-actions .confirm-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 18px rgba(239, 68, 68, 0.3);
}

.delete-option-popup-actions .confirm-btn:disabled {
  background: #e5e7eb !important;
  color: #a1a1aa !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
  opacity: 0.7;
}

.label {
  font-weight: 600;
  color: #374151;
  font-size: 1.2rem;
}

.client-info-summary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #065f46;
}

.license-name {
  font-weight: 600;
  color: #1e293b;
  background: linear-gradient(135deg, #49b38e, #2c7744);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.popup-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.cancel-btn, .confirm-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 0.95rem;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cancel-btn {
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.cancel-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.confirm-btn {
  background: linear-gradient(135deg, #49b38e, #2c7744);
  color: white;
}

.confirm-btn:disabled {
  background: #e5e7eb !important;
  color: #a1a1aa !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
  opacity: 0.7;
}

.confirm-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 18px rgba(73, 179, 142, 0.3);
}

.success-notification {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #10b981;
  z-index: 3000;
  max-width: 400px;
  width: 90%;
}

.notification-content {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.success-icon {
  color: #10b981;
  font-size: 2rem;
  margin-top: 0.2rem;
}

.notification-text {
  flex: 1;
}

.notification-text h4 {
  font-size: 1.1rem;
  color: #065f46;
  margin: 0 0 0.25rem 0;
  font-weight: 700;
}

.notification-text p {
  font-size: 0.9rem;
  color: #374151;
  margin: 0;
  line-height: 1.5;
}

.close-notification-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: none;
  background: #f0fdf4;
  color: #10b981;
  cursor: pointer;
  transition: all 0.2s;
  margin-left: auto;
  flex-shrink: 0;
}

.close-notification-btn:hover {
  background: #dcfce7;
  color: #059669;
}

.input-error {
  border: 2px solid #ef4444 !important;
  background: #fef2f2 !important;
}

.choose-client-error {
  color: #ef4444;
  font-size: 0.97rem;
  margin-top: 0.35rem;
  margin-left: 0.2rem;
  font-weight: 600;
  letter-spacing: 0.01em;
}

.checked-green {
  background-color: #d1fae5 !important;
  border: 2px solid #10b981 !important;
}
.unchecked-red {
  background-color: #fee2e2 !important;
  border: 2px solid #ef4444 !important;
}

.checked-icon {
  color: #10b981 !important;
  opacity: 1 !important;
  transform: scale(1) !important;
}

.unchecked-icon {
  color: #ef4444 !important;
  opacity: 1 !important;
  transform: scale(1) !important;
}

.improved-client-card {
  display: flex;
  align-items: center;
  background: #edfff9;
  border-radius: 12px;
  padding: 0.75rem 1rem;
  box-shadow: 0 4px 16px rgba(16,185,129,0.08);
  border: 1.5px solid #10b981;
  min-width: 220px;
  position: relative;
  gap: 0.75rem;
}

.client-avatar-section {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.client-image-large {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #10b981;
}

.client-details-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
}

.client-name-row {
  display: flex;
  align-items: center;
  gap: 0.7rem;
  margin-bottom: 0.15rem;
}

.client-name-large {
  font-weight: 700;
  font-size: 1.13rem;
  color: #065f46;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.client-badge {
  background: linear-gradient(90deg, #10b981 60%, #34d399 100%);
  color: #fff;
  font-size: 0.85rem;
  font-weight: 600;
  border-radius: 12px;
  padding: 2px 12px;
  margin-left: 2px;
  letter-spacing: 0.01em;
  box-shadow: 0 1px 4px rgba(16,185,129,0.10);
}

.client-subtitle {
  font-size: 0.93rem;
  color: #64748b;
  margin-top: 2px;
  font-weight: 500;
  letter-spacing: 0.01em;
}

.improved-clear-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #f0fdf4;
  color: #10b981;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  box-shadow: 0 1px 4px rgba(16,185,129,0.08);
  transition: background 0.2s, color 0.2s;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.improved-clear-btn:hover {
  background: #dcfce7;
  color: #059669;
}

.search-input-glass-wrapper {
  position: relative;
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  z-index: 10;
}

.search-input.glass {
  width: 100%;
  padding: 0.95rem 2.7rem 0.95rem 3.3rem; /* increased left padding for more space */
  border: none;
  border-radius: 18px;
  font-size: 1.12rem;
  background: rgba(255,255,255,0.25);
  box-shadow: 0 8px 32px 0 rgba(31,38,135,0.10);
  color: black;
  font-weight: 500;
  letter-spacing: 0.01em;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1.5px solid rgba(16,185,129,0.18);
  transition: box-shadow 0.2s, border 0.2s, background 0.2s;
  outline: none;
}

.search-input.glass:focus {
  border: 1.5px solid rgba(16,185,129,0.18);
  background: transparent;
  box-shadow: none;
}

.search-input-glass-icon {
  position: absolute;
  left: 1.1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-size: 1.6rem;
  pointer-events: none;
  z-index: 2;
}

.search-input-glass-clear-btn {
  position: absolute;
  right: 1.1rem;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: #a0aec0;
  font-size: 1.35rem;
  cursor: pointer;
  padding: 0;
  z-index: 2;
  transition: color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-input-glass-clear-btn:hover {
  color: #ef4444;
}

.dropdown-glass {
  position: absolute;
  top: calc(100% + 1px);
  left: 0;
  width: 120%;
  min-width: 260px;
  background: rgba(255,255,255,0.85);
  border-radius: 18px;
  box-shadow: 0 8px 32px 0 rgba(31,38,135,0.13);
  border: 1.5px solid #e0f2fe;
  padding: 0.5rem 0;
  z-index: 1001;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  animation: dropdownGlassFadeIn 0.25s cubic-bezier(.4,2,.6,1) both;
  max-height: 320px; /* 4 items * ~80px each, adjust as needed */
  overflow-y: auto;
}

@keyframes dropdownGlassFadeIn {
  from { opacity: 0; transform: translateY(-10px) scale(0.98);}
  to   { opacity: 1; transform: translateY(0) scale(1);}
}

.dropdown-glass-item {
  display: flex;
  align-items: center;
  gap: 1.1rem;
  padding: 0.7rem 1.3rem;
  cursor: pointer;
  border-radius: 12px;
  transition: background 0.18s, box-shadow 0.18s;
  position: relative;
}

.dropdown-glass-item:hover {
  background: transparent;
  box-shadow: none;
}

.dropdown-glass-avatar {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  overflow: hidden;
  background: #e0f2fe;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border: 2px solid #bbf7d0;
  box-shadow: 0 1px 4px rgba(16,185,129,0.07);
}

.dropdown-glass-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.dropdown-glass-avatar-fallback {
  font-size: 1.25rem;
  font-weight: 700;
  color: #10b981;
  background: #e0f2fe;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.dropdown-glass-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

.dropdown-glass-name {
  font-weight: 600;
  font-size: 1.04rem;
  color: #065f46;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: 0.01em;
}

.dropdown-glass-selected {
  color: #10b981;
  font-size: 1.4rem;
  margin-left: 0.5rem;
  opacity: 0.85;
}

.improved-buttons-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 1.2rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.add-option-inputs-wrapper,
.delete-option-inputs-wrapper {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}

.improved-form-group {
  margin-bottom: 0;
}

.input-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.improved-input {
  width: 100%;
  padding: 0.8rem  0.8rem ;
  border-radius: 12px;
  border: 1.5px solid #e0f2fe;
  font-size: 1.08rem;
  color: #065f46;
  font-weight: 500;
  transition: border 0.2s, box-shadow 0.2s, background 0.2s;
  outline: none;
}

/* .improved-input:focus {
  border: 2px solid #10b981;
} */

.input-clear-btn {
  position: absolute;
  right: 1rem;
  background: transparent;
  border: none;
  color: #a0aec0;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-clear-btn:hover {
  color: #ef4444;
}

.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.2rem;
  margin: 6rem 0 2.5rem 0; /* Changed from 1.5rem to 3rem for top margin */
  user-select: none;
}

.pagination-btn {
  background: #f0fdf4;
  border: none;
  border-radius: 50%;
  width: 38px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  color: #10b981;
  font-size: 1.7rem;
  cursor: pointer;
  transition: background 0.18s, color 0.18s, box-shadow 0.18s;
  box-shadow: 0 2px 8px rgba(16,185,129,0.07);
  visibility: visible !important;
  opacity: 1 !important;
}

.pagination-btn:disabled {
  background: #e5e7eb;
  color: #a1a1aa;
  cursor: not-allowed;
  opacity: 0.7;
}
.pagination-btn:hover:not(:disabled) {
  background: #bbf7d0;
  color: #059669;
}

.pagination-indicator {
  display: flex;
  gap: 0.5rem;
}
.pagination-dot {
  width: 13px;
  height: 13px;
  border-radius: 50%;
  background: #e0e7ef;
  border: none;
  margin: 0 2px;
  cursor: pointer;
  transition: background 0.2s, transform 0.2s;
  outline: none;
  box-shadow: 0 1px 4px rgba(16,185,129,0.07);
}
.pagination-dot.active,
.pagination-dot:focus {
  background: linear-gradient(90deg, #10b981 60%, #34d399 100%);
  transform: scale(1.18);
  box-shadow: 0 2px 8px rgba(16,185,129,0.13);
}

/* Search bar styles */
.search-bar-table {
  display: flex;
  flex-direction: column;
  margin-bottom: 1.5rem;
  margin-top: 1rem;
}

/* Table Pagination Styles */
.table-pagination-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1rem 0;
  padding: 0.75rem 1rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.table-pagination-size {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.table-pagination-size select {
  padding: 0.5rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  background: white;
}

.table-pagination-info {
  color: #64748b;
  font-size: 0.9rem;
}

.table-pagination-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.table-pagination-buttons button {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.table-pagination-buttons button:hover:not(:disabled) {
  background: #f0fdf4;
  border-color: #bbf7d0;
  color: #065f46;
}

.table-pagination-buttons button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.table-page-numbers button {
  min-width: 32px;
  height: 32px;
}

.table-page-numbers button.active {
  background: #10b981;
  color: white;
  border-color: #10b981;
}

.search-bar-table {
  margin-bottom: 1rem;
}

.search-bar-inner {
  display: flex;
  gap: 0.5rem;
}

.search-btn {
  background: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-btn:hover {
  background: #059669;
}

.search-bar-inner {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
}
.search-bar-inner .search-input.glass {
  flex: 1 1 300px;
  min-width: 220px;
  max-width: 400px;
}
.search-bar-inner .search-btn {
  background: #10b981;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.6rem 1.2rem;
  font-size: 1.1rem;
  cursor: pointer;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}
.search-bar-inner .search-btn:hover {
  background: #059669;
}
.search-bar-inner .confirm-btn {
  margin-left: auto;
}

/* Dropdown for actions in the table */
.table-action-dropdown {
  position: relative;
  display: inline-block;
}
.table-action-dropdown .dropdown-btn {
  background: transparent;
  border: none;
  cursor: pointer;
  color: #374151;
  font-size: 1.5rem;
  padding: 0.2rem 0.5rem;
  border-radius: 6px;
  transition: background 0.15s;
}
.table-action-dropdown .dropdown-btn:hover {
  background: #f0fdf4;
}
.table-action-dropdown .dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  top: 110%;
  min-width: 160px;
  background: #fff;
  border: 1px solid #e2e8f0;
  border-radius: 10px;
  box-shadow: 0 8px 32px 0 rgba(31,38,135,0.10);
  z-index: 100;
  padding: 0.5rem 0;
}
.table-action-dropdown .dropdown-content.show {
  display: block;
}
.table-action-dropdown .dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.7rem;
  padding: 0.7rem 1.3rem;
  cursor: pointer;
  border-radius: 8px;
  transition: background 0.18s;
  color: #374151;
  font-size: 1rem;
  background: none;
  border: none;
  width: 100%;
  text-align: left;
}
.table-action-dropdown .dropdown-item:hover {
  background: #f0fdf4;
  color: #10b981;
}
.table-action-dropdown .dropdown-item .material-icons {
  font-size: 1.2rem;
}

/* Header improvements */
.improved-header {
  background: linear-gradient(90deg, #f0fdf4 60%, #e0f2fe 100%);
  border-radius: 0 0 24px 24px;
  padding: 2.5rem 2rem 2rem 2rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 24px rgba(16,185,129,0.07);
  text-align: center;
}

.improved-header-container {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(16,185,129,0.07);
  padding: 1.5rem 2rem 1.2rem 2rem;
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
}

.custom-date-input-wrapper {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-left: 1.2rem;
  padding: 0.6rem 1rem;
  background: #f0fdf4;
  border-radius: 12px;
  border: 1.5px solid #bbf7d0;
  box-shadow: 0 2px 8px rgba(16,185,129,0.07);
}

.date-label {
  font-family: 'Lato', sans-serif;
  font-size: 1rem;
  font-weight: 600;
  color: #065f46;
  white-space: nowrap;
  margin: 0;
}

.custom-date-input {
  font-family: 'Lato', sans-serif;
  font-size: 1rem;
  padding: 0.6rem 0.8rem;
  border-radius: 8px;
  border: 1.5px solid #e0f2fe;
  background: white;
  color: #065f46;
  font-weight: 500;
  transition: border 0.2s, box-shadow 0.2s;
  outline: none;
  min-width: 150px;
  box-shadow: 0 1px 3px rgba(16,185,129,0.05);
}

.custom-date-input:focus {
  border: 2px solid #10b981;
  box-shadow: 0 0 0 3px rgba(16,185,129,0.13);
}

.custom-date-input::-webkit-calendar-picker-indicator {
  color: #10b981;
  cursor: pointer;
}

.custom-date-input::-webkit-datetime-edit {
  color: #065f46;
}

.custom-date-input::-webkit-datetime-edit-fields-wrapper {
  color: #065f46;
}

.custom-date-input::-webkit-datetime-edit-text {
  color: #065f46;
  padding: 0 0.2rem;
}

.custom-date-input::-webkit-datetime-edit-month-field,
.custom-date-input::-webkit-datetime-edit-day-field,
.custom-date-input::-webkit-datetime-edit-year-field {
  color: #065f46;
  font-weight: 500;
}

.improved-frequence-payement {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  gap: 1rem;
}

.improved-frequence-payement .improved-select {
  min-width: 200px;
}

.improved-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 2.2rem;
  font-weight: 800;
  color: #10b981;
  margin-bottom: 0.5rem;
  letter-spacing: 0.01em;
}
.improved-subtitle {
  font-family: 'Lato', sans-serif;
  font-size: 1.1rem;
  color: #64748b;
  margin-bottom: 0;
  font-weight: 500;
  letter-spacing: 0.01em;
}

/* Arrow returning improvements */
.improved-arrow-returning {
  position: absolute;
  top: 2.2rem;
  left: 2.2rem;
  z-index: 10;
}
.improved-return-btn {
  background: #f0fdf4;
  color: #10b981;
  border: none;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  box-shadow: 0 2px 8px rgba(16,185,129,0.10);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.18s, color 0.18s, box-shadow 0.18s;
  font-size: 2rem;
}
.improved-return-btn:hover {
  background: #bbf7d0;
  color: #059669;
  box-shadow: 0 4px 16px rgba(16,185,129,0.13);
}

/* Pagination improvements */
.improved-pagination-controls,
.improved-table-pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  margin: 2.5rem 0 1.5rem 0;
  user-select: none;
}
.improved-pagination-btn {
  background: #f0fdf4;
  color: #10b981;
  border: none;
  border-radius: 50%;
  width: 38px;
  height: 38px;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.18s, color 0.18s, box-shadow 0.18s;
}
.improved-pagination-btn:disabled {
  background: #e5e7eb;
  color: #a1a1aa;
  cursor: not-allowed;
  opacity: 0.7;
}
.improved-pagination-btn:hover:not(:disabled) {
  background: #bbf7d0;
  color: #059669;
}
.improved-pagination-indicator {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.25rem;
  color: #10b981;
  font-weight: 700;
  letter-spacing: 0.03em;
}
.improved-pagination-page-number {
  padding: 0 0.7rem;
  font-size: 1.25rem;
  font-family: 'Montserrat', sans-serif;
  color: #10b981;
  font-weight: 700;
  border-radius: 8px;
  background: #f0fdf4;
  border: 1.5px solid #bbf7d0;
  min-width: 36px;
  text-align: center;
  display: inline-block;
}

/* Select improvements */
.improved-select-type-frequence-payement {
  margin-top: 1.2rem;
}
.improved-frequence-payement {
  display: flex;
  align-items: center;
  gap: 1rem;
}
.improved-select {
  font-family: 'Lato', sans-serif;
  font-size: 1.08rem;
  padding: 0.7rem 1.2rem;
  border-radius: 12px;
  border: 1.5px solid #bbf7d0;
  background: #f0fdf4;
  color: #065f46;
  font-weight: 600;
  outline: none;
  transition: border 0.2s, box-shadow 0.2s;
  min-width: 180px;
  box-shadow: 0 2px 8px rgba(16,185,129,0.07);
}
.improved-select:focus {
  border: 2px solid #10b981;
  background: #edfff9;
}

/* Card grid improvements */
.improved-license-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-start;
  gap: 2.5rem;
  margin: 2rem auto;
  max-width: 1200px;
  padding: 0 1.5rem;
  width: 100%;
}
.improved-license-grid.single-card {
  justify-content: center;
  align-items: center;
  min-height: 350px;
}
.improved-license-card {
  width: 350px;
  max-width: 100%;
  min-width: 320px;
  margin: 0 auto;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 20px rgba(0,0,0,0.04);
  border: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), 
              transform 0.4s cubic-bezier(.4,2,.6,1);
  height: 100%;
  min-height: 350px;
  justify-content: flex-start;
}
.improved-license-card.single-card-inner {
  margin: 0 auto;
  min-width: 350px;
  max-width: 400px;
  width: 100%;
}

/* Card header and description improvements */
.improved-card-header {
  margin-bottom: 1.2rem;
  text-align: center;
  padding-bottom: 0.7rem;
  border-bottom: 1px solid #f1f5f9;
}
.improved-card-header .improved-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.3rem;
  font-weight: 700;
  color: #10b981;
  margin-bottom: 0.3rem;
  letter-spacing: 0.01em;
}
.improved-card-description {
  font-family: 'Lato', sans-serif;
  color: #64748b;
  font-size: 0.97rem;
  margin-bottom: 0;
  line-height: 1.4;
  min-height: 36px;
  max-height: 36px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

/* Features list always at the same position */
.features-list-wrapper {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  min-height: 120px;
}
.improved-features-list {
  margin-top: 0.5rem;
  margin-bottom: 1.2rem;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.improved-search-bar-inner {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
  padding: 0.5rem 0;
}

.improved-search-input {
  flex: 1 1 300px;
  width: 40%;
  height: 30%;
  font-size: 1.08rem;
  border-radius: 12px;
  border: 1.5px solid #e0f2fe;
  background: #fff;
  color: #065f46;
  font-weight: 500;
  padding: 0 1.2rem;
  box-shadow: 0 1px 3px rgba(16,185,129,0.07);
  transition: border 0.2s, box-shadow 0.2s, background 0.2s;
  outline: none;
}

.improved-search-input:focus {
  border: 2px solid #10b981;
  background: #edfff9;
}

.improved-search-btn,
.improved-confirm-btn {
  height: 44px;
  min-width: 44px;
  padding: 0 1.3rem;
  border-radius: 12px;
  font-size: 1.08rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  transition: background 0.2s, color 0.2s;
  box-shadow: 0 1px 3px rgba(16,185,129,0.07);
}

.improved-search-btn {
  background: #10b981;
  color: #fff;
  margin-left: 0.5rem;
}

.improved-search-btn:hover {
  background: #059669;
}

.improved-confirm-btn {
  background: linear-gradient(135deg, #49b38e, #2c7744);
  color: #fff;
  margin-left: 0.5rem;
}

.improved-confirm-btn:hover {
  background: linear-gradient(135deg, #2c7744, #49b38e);
}

.improved-frequence-inline {
  flex-direction: row !important;
  align-items: center !important;
  gap: 1.2rem;
}

.improved-date-inline {
  display: flex;
  align-items: center;
  gap: 0.7rem;
  margin: 0;
  padding: 0;
  background: none;
  border: none;
  box-shadow: none;
}

.improved-date-input {
  border-radius: 8px;
  border: 1.5px solid #10b981;
  background: #f0fdf4;
  color: #0000;
  font-size: 1.08rem;
  font-weight: 600;
  padding: 0.5rem 1rem;
  min-width: 150px;
  box-shadow: 0 2px 8px rgba(16,185,129,0.07);
  transition: border 0.2s, box-shadow 0.2s, background 0.2s;
  outline: none;
}

.improved-date-input:focus {
  border: 2px solid #10b981;
  background: #edfff9;
}

.improved-date-input::-webkit-input-placeholder {
  color: #a0aec0;
  opacity: 1;
}
.improved-date-input:-ms-input-placeholder {
  color: #a0aec0;
  opacity: 1;
}
.improved-date-input::placeholder {
  color: #a0aec0;
  opacity: 1;
}