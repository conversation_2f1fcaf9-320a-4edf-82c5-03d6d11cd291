/* Removed unused styles: subtitle, billing-toggle-section, client-site, 
   billing-toggle, savings-badge, card-badge, price, amount, period, 
   feature-cost, yearly-savings, client-image-small, and media query for header-section */

:host {
  display: block;
  font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #2d3748;
  background-color: #f9fafb;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
}

.client-billing-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-bottom: 3rem;
}

.client-search-section {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.05);
}

.search-container {
  display: flex;
  justify-content: space-between;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.search-input-container {
  position: relative;
  flex: 1;
  min-width: 300px;
  transition: all 0.3s ease;
}

.search-input {
  width: 40%;
  padding: 0.85rem 1rem 0.85rem 3rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  background: white;
  height: 38px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.03);
}

.search-input:focus {
  outline: none;
  border-color: #49b38e;
  box-shadow: 0 0 0 3px rgba(73, 179, 142, 0.2);
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #a0aec0;
  font-size: 1.25rem;
}

.selected-client-container {
  display: flex;
  align-items: center;
  background: #f0fdf4;
  border-radius: 12px;
  padding: 0.5rem 0.75rem;
  border: 1px solid #bbf7d0;
  min-width: 220px;
  transition: all 0.3s ease;
}

.selected-client-container.improved-client-card {
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
}

.client-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.client-image {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid #d1fae5;
}

.client-name {
  font-weight: 600;
  font-size: 0.9rem;
  color: #065f46;
}

.clear-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: none;
  background: #ecfdf5;
  color: #34d399;
  cursor: pointer;
  transition: all 0.2s;
  margin-left: 8px;
}

.clear-button:hover {
  background: #dcfce7;
  color: #10b981;
}

.dropdown {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  width: 45%;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  margin-top: 0.25rem;
  max-height: 300px;
  overflow-y: auto;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  z-index: 1000;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 0.65rem 1.25rem;
  gap: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}

.dropdown-item:hover {
  background-color: #f8fafc;
}

.client-text {
  display: flex;
  flex-direction: column;
}

.license-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  margin-top: 1rem;
}

.license-card {
  position: relative;
  background: white;
  border-radius: 16px;
  padding: 1.75rem;
  box-shadow: 0 8px 20px rgba(0,0,0,0.04);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.license-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.08);
}

.license-card.popular {
  border-top: 4px solid #3b82f6;
}

.license-card.featured {
  border-top: 4px solid #8b5cf6;
}

.card-header {
  margin-bottom: 1.25rem;
  text-align: center;
  padding-bottom: 1.25rem;
  border-bottom: 1px solid #f1f5f9;
}

.card-header h3 {
  font-size: 1.6rem;
  margin-bottom: 0.5rem;
  color: #1e293b;
}

.card-description {
  color: #64748b;
  font-size: 0.9rem;
  margin-bottom: 1.25rem;
  line-height: 1.6;
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0 0 1.75rem 0;
  flex-grow: 1;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 0.85rem 0;
  border-bottom: 1px solid #f1f5f9;
}

.custom-checkbox {
  display: block;
  position: relative;
  cursor: pointer;
  user-select: none;
  margin-right: 0.75rem;
}

.custom-checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.custom-checkbox input:disabled {
  background: #e5e7eb !important;
  color: #a1a1aa !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
  opacity: 0.7;
}

.checkmark {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  width: 24px;
  border-radius: 6px;
  background-color: #f1f5f9;
  transition: all 0.2s;
}

.custom-checkbox input:checked ~ .checkmark {
  background-color: #10b981;
  border-color: #10b981;
}

.custom-checkbox input:checked ~ .checkmark .verified-icon {
  opacity: 1;
  transform: scale(1);
}

.custom-checkbox input:not(:checked) ~ .checkmark .verified-icon {
  opacity: 0;
  transform: scale(0.8);
}

.custom-checkbox input:disabled ~ .checkmark {
  background-color: #e2e8f0;
  cursor: not-allowed;
}

.verified-icon, .cancel-icon {
  font-size: 18px;
  color: white;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.2s;
}

.custom-checkbox input:checked ~ .checkmark .verified-icon,
.custom-checkbox input:not(:checked) ~ .checkmark .cancel-icon {
  opacity: 1;
  transform: scale(1);
}

.feature-details {
  flex-grow: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.feature-name {
  font-size: 0.95rem;
  color: #334155;
}

.select-btn {
  width: 100%;
  padding: 1rem;
  border: none;
  border-radius: 10px;
  background: linear-gradient(135deg, #49b38e, #2c7744);
  color: white;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  margin-top: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  letter-spacing: 0.5px;
}

.select-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 18px rgba(73, 179, 142, 0.3);
}

.select-btn:disabled {
  background: #e5e7eb !important;
  color: #a1a1aa !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
  opacity: 0.7;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.confirmation-popup {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  position: relative;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f1f5f9;
}

.popup-header h3 {
  font-size: 1.4rem;
  color: #1e293b;
  margin: 0;
}

.close-popup-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background: #f8fafc;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s;
}

.close-popup-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.popup-content {
  margin-bottom: 2rem;
}

.confirmation-message {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.confirmation-icon {
  color: #f59e0b;
  font-size: 2rem;
  margin-top: 0.2rem;
}

.confirmation-message p {
  font-size: 1rem;
  color: #475569;
  line-height: 1.6;
  margin: 0;
}

.client-logo {
  width: 52px;
  height: 52px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid #d1fae5;
}

.client-license-summary {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.25rem;
  border: 1px solid #e2e8f0;
}

.summary-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.client-info-summary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #065f46;
}

.license-name {
  font-weight: 600;
  color: #1e293b;
  background: linear-gradient(135deg, #49b38e, #2c7744);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.popup-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.cancel-btn, .confirm-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 0.95rem;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cancel-btn {
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.cancel-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.confirm-btn {
  background: linear-gradient(135deg, #49b38e, #2c7744);
  color: white;
}

.confirm-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 18px rgba(73, 179, 142, 0.3);
}

.success-notification {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #10b981;
  z-index: 3000;
  max-width: 400px;
  width: 90%;
}

.notification-content {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.success-icon {
  color: #10b981;
  font-size: 2rem;
  margin-top: 0.2rem;
}

.notification-text {
  flex: 1;
}

.notification-text h4 {
  font-size: 1.1rem;
  color: #065f46;
  margin: 0 0 0.25rem 0;
  font-weight: 700;
}

.notification-text p {
  font-size: 0.9rem;
  color: #374151;
  margin: 0;
  line-height: 1.5;
}

.close-notification-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: none;
  background: #f0fdf4;
  color: #10b981;
  cursor: pointer;
  transition: all 0.2s;
  margin-left: auto;
  flex-shrink: 0;
}

.close-notification-btn:hover {
  background: #dcfce7;
  color: #059669;
}

.input-error {
  border: 2px solid #ef4444 !important;
  background: #fef2f2 !important;
}

.choose-client-error {
  color: #ef4444;
  font-size: 0.97rem;
  margin-top: 0.35rem;
  margin-left: 0.2rem;
  font-weight: 600;
  letter-spacing: 0.01em;
}

.checked-green {
  background-color: #d1fae5 !important;
  border: 2px solid #10b981 !important;
}
.unchecked-red {
  background-color: #fee2e2 !important;
  border: 2px solid #ef4444 !important;
}

.checked-icon {
  color: #10b981 !important;
  opacity: 1 !important;
  transform: scale(1) !important;
}

.unchecked-icon {
  color: #ef4444 !important;
  opacity: 1 !important;
  transform: scale(1) !important;
}

.improved-client-card {
  display: flex;
  align-items: center;
  background: linear-gradient(90deg, #f0fdf4 80%, #e0f2fe 100%);
  border-radius: 18px;
  padding: 1rem 1.5rem;
  box-shadow: 0 4px 24px rgba(16,185,129,0.08);
  border: 1.5px solid #bbf7d0;
  min-width: 260px;
  position: relative;
  gap: 1.2rem;
  margin-bottom: 0.5rem;
}

.client-avatar-section {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.client-image-large {
  width: 54px;
  height: 54px;
  border-radius: 50%;
  object-fit: cover;
  border: 2.5px solid #10b981;
  box-shadow: 0 2px 8px rgba(16,185,129,0.10);
  background: #fff;
}

.client-details-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
}

.client-name-row {
  display: flex;
  align-items: center;
  gap: 0.7rem;
  margin-bottom: 0.15rem;
}

.client-name-large {
  font-weight: 700;
  font-size: 1.13rem;
  color: #065f46;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.client-badge {
  background: linear-gradient(90deg, #10b981 60%, #34d399 100%);
  color: #fff;
  font-size: 0.85rem;
  font-weight: 600;
  border-radius: 12px;
  padding: 2px 12px;
  margin-left: 2px;
  letter-spacing: 0.01em;
  box-shadow: 0 1px 4px rgba(16,185,129,0.10);
}

.client-subtitle {
  font-size: 0.93rem;
  color: #64748b;
  margin-top: 2px;
  font-weight: 500;
  letter-spacing: 0.01em;
}

.improved-clear-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #f0fdf4;
  color: #10b981;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  box-shadow: 0 1px 4px rgba(16,185,129,0.08);
  transition: background 0.2s, color 0.2s;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.improved-clear-btn:hover {
  background: #dcfce7;
  color: #059669;
}

.search-input-glass-wrapper {
  position: relative;
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  z-index: 10;
}

.search-input.glass {
  width: 100%;
  padding: 0.95rem 2.7rem 0.95rem 3.3rem; /* increased left padding for more space */
  border: none;
  border-radius: 18px;
  font-size: 1.12rem;
  background: rgba(255,255,255,0.25);
  box-shadow: 0 8px 32px 0 rgba(31,38,135,0.10);
  color: #065f46;
  font-weight: 500;
  letter-spacing: 0.01em;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1.5px solid rgba(16,185,129,0.18);
  transition: box-shadow 0.2s, border 0.2s, background 0.2s;
  outline: none;
}

.search-input.glass:focus {
  box-shadow: 0 0 0 4px rgba(16,185,129,0.18);
  border: 2px solid #10b981;
  background: rgba(224,242,254,0.45);
}

.search-input-glass-icon {
  position: absolute;
  left: 1.1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-size: 1.6rem;
  pointer-events: none;
  z-index: 2;
}

.search-input-glass-clear-btn {
  position: absolute;
  right: 1.1rem;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: #a0aec0;
  font-size: 1.35rem;
  cursor: pointer;
  padding: 0;
  z-index: 2;
  transition: color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-input-glass-clear-btn:hover {
  color: #ef4444;
}

.dropdown-glass {
  position: absolute;
  top: calc(100% + 1px);
  left: 0;
  width: 120%;
  min-width: 260px;
  background: rgba(255,255,255,0.85);
  border-radius: 18px;
  box-shadow: 0 8px 32px 0 rgba(31,38,135,0.13);
  border: 1.5px solid #e0f2fe;
  padding: 0.5rem 0;
  z-index: 1001;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  animation: dropdownGlassFadeIn 0.25s cubic-bezier(.4,2,.6,1) both;
  max-height: 320px; /* 4 items * ~80px each, adjust as needed */
  overflow-y: auto;
}

@keyframes dropdownGlassFadeIn {
  from { opacity: 0; transform: translateY(-10px) scale(0.98);}
  to   { opacity: 1; transform: translateY(0) scale(1);}
}

.dropdown-glass-item {
  display: flex;
  align-items: center;
  gap: 1.1rem;
  padding: 0.7rem 1.3rem;
  cursor: pointer;
  border-radius: 12px;
  transition: background 0.18s, box-shadow 0.18s;
  position: relative;
}

.dropdown-glass-item:hover {
  background: linear-gradient(90deg, #f0fdf4 60%, #e0f2fe 100%);
  box-shadow: 0 2px 12px rgba(16,185,129,0.08);
}

.dropdown-glass-avatar {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  overflow: hidden;
  background: #e0f2fe;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border: 2px solid #bbf7d0;
  box-shadow: 0 1px 4px rgba(16,185,129,0.07);
}

.dropdown-glass-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.dropdown-glass-avatar-fallback {
  font-size: 1.25rem;
  font-weight: 700;
  color: #10b981;
  background: #e0f2fe;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.dropdown-glass-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

.dropdown-glass-name {
  font-weight: 600;
  font-size: 1.04rem;
  color: #065f46;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: 0.01em;
}

.dropdown-glass-selected {
  color: #10b981;
  font-size: 1.4rem;
  margin-left: 0.5rem;
  opacity: 0.85;
}

@media (max-width: 768px) {
  .container {
    padding: 1.5rem 1rem;
  }
  
  .search-container {
    flex-direction: column;
  }
  
  .search-input-container {
    max-width: 100%;
  }
  
  .selected-client-container {
    width: 100%;
  }
  
  .license-grid {
    grid-template-columns: 1fr;
  }
  
  .confirmation-popup {
    width: 95%;
    padding: 1.5rem;
  }
  
  .popup-actions {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .cancel-btn, .confirm-btn {
    width: 100%;
    justify-content: center;
  }
  
  .success-notification {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    width: auto;
    max-width: none;
  }
}

@media (max-width: 480px) {
  .amount {
    font-size: 2.5rem;
  }
  
  .license-card {
    padding: 1.5rem;
  }
  
  .confirmation-popup {
    padding: 1.25rem;
  }
  
  .popup-header h3 {
    font-size: 1.2rem;
  }
  
  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}