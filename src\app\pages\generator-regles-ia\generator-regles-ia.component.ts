import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';
import { trigger, state, style, transition, animate } from '@angular/animations';

// Import NgToast
import { NgToastModule, NgToastService, TOAST_POSITIONS } from 'ng-angular-popup';

// Import components
import { RuleFormComponent } from '../../components/rule-form/rule-form.component';
import { ConfirmationDialogComponent } from '../../components/confirmation-dialog/confirmation-dialog.component';
import { AiRuleGeneratorDialogComponent } from '@app/components/ai-rule-generator-dialog/ai-rule-generator-dialog.component';

// Import models
import { RuleContent } from "@app/shared/models/rule/RuleContent";
import { RuleComprehensive } from "@app/shared/models/rule/RuleComprehensive";
import { RuleClientHierarchy } from "@app/shared/models/rule/RuleClientHierarchy";
import { Lister } from "@app/shared/models/rule/Lister";
import { ClientForRule } from "@app/shared/models/rule/ClientForRule";
import { ControllerForRule } from "@app/shared/models/rule/ControllerForRule";
import { Controller } from "@app/core/models/controller";
import { Action } from "@app/shared/models/rule/Action";
import { Condition } from "@app/shared/models/rule/Condition";
import { RawDataBackendStructure } from "@app/shared/models/rule/RawDataBackendStructure";
import { WhereParams } from "@app/shared/models/rule/WhereParams";
import { Sorting } from "@app/shared/models/rule/Sorting";

import { RulesApiService } from '../../core/services/administrative/rules.service';
import { forkJoin, map, debounceTime, distinctUntilChanged, Subject } from 'rxjs';
import { ControllerApiService } from '@app/core/services/administrative/controller.service';
import { RuleTransactionDetail } from '@app/shared/models/rule/RuleTransactionDetail';

@Component({
  selector: 'app-generator-regles-ia',
  templateUrl: './generator-regles-ia.component.html',
  styleUrls: ['./generator-regles-ia.component.css'],
  standalone: true,
  animations: [
    trigger('slideInOut', [
      transition(':enter', [
        style({ 
          height: '0px', 
          opacity: 0, 
          transform: 'translateY(-10px)',
          overflow: 'hidden' 
        }),
        animate('300ms ease-in-out', style({ 
          height: '*', 
          opacity: 1, 
          transform: 'translateY(0px)' 
        }))
      ]),
      transition(':leave', [
        style({ 
          height: '*', 
          opacity: 1, 
          transform: 'translateY(0px)',
          overflow: 'hidden' 
        }),
        animate('300ms ease-in-out', style({ 
          height: '0px', 
          opacity: 0, 
          transform: 'translateY(-10px)' 
        }))
      ])
    ])
  ],
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatDialogModule,
    MatTooltipModule,
    NgxUiLoaderModule,
    NgToastModule,
  ]
})
export class GeneratorReglesIaComponent implements OnInit {
  // Core data properties
  rules: RuleContent[] = [];
  filteredRules: RuleContent[] = [];

  // Search and pagination
  searchTerm: string = '';
  currentPage: number = 1;
  pageSize: number = 10;
  totalElements: number = 0;
  pageCount: number = 0;

  // UI state
  activeTab: 'hierarchy' | 'rawdata' | 'summary' = 'summary';
  expandedRuleId: string | null = null;
  selectedControllerId: string | null = null;

  // Loading states
  private isDeleting = new Set<string>();
  private isLoading = false;
  isSearching: boolean = false;
  isSearchMode: boolean = false;

  // Search functionality
  private searchSubject = new Subject<string>();

  // Data caches
  private rawDataCache = new Map<string, string>();
  private summaryCache = new Map<string, string>();
  private controllersCache = new Map<string, Controller>();

  // === SORTING AND FILTERING PROPERTIES ===
  showAdvancedFilters: boolean = false;
  currentSortColumn: string = 'Priority';
  currentSortDirection: string = 'ASC';
  appliedFilters: WhereParams[] = [];

  // Available sort options
  sortOptions = [
    { value: 'Priority', label: 'Priorité' },
    { value: 'RuleCreatedAt', label: 'Date de création' },
    { value: 'RuleLastUpdatedAt', label: 'Dernière modification' },
    { value: 'Status', label: 'Statut' },
    { value: 'TotalApplications', label: 'Nombre d\'applications' },
    { value: 'LastTriggered', label: 'Dernier déclenchement' }
  ];

  // Available filter options
  filterOptions = [
    { column: 'Status', label: 'Statut', type: 'select', options: [
      { value: 'active', label: 'Actif' },
      { value: 'inactive', label: 'Inactif' }
    ]},
    { column: 'Priority', label: 'Priorité', type: 'number' },
    { column: 'RuleCreatedAt', label: 'Date de création', type: 'date' },
    { column: 'TotalApplications', label: 'Nombre d\'applications', type: 'number' },
    { column: 'RawData', label: 'Contenu de la règle', type: 'text' },
    { column: 'TagsString', label: 'Tags', type: 'text' }
  ];

  TOAST_POSITIONS = TOAST_POSITIONS;

  constructor(
    private dialog: MatDialog,
    private rulesApiService: RulesApiService,
    private controllerApiService: ControllerApiService,
    private toast: NgToastService,
    private ngxUiLoaderService: NgxUiLoaderService
  ) {}

  ngOnInit(): void {
    this.currentPage = 1;
    this.loadRules();
    this.setupSearch();
  }

  // === SORTING AND FILTERING METHODS ===

  /**
   * Toggles the advanced filters panel
   */
  toggleAdvancedFilters(): void {
    this.showAdvancedFilters = !this.showAdvancedFilters;
  }

  /**
   * Updates the current sort column and direction
   */
  updateSort(column: string, direction?: string): void {
    if (this.currentSortColumn === column && !direction) {
      // Toggle direction if same column clicked
      this.currentSortDirection = this.currentSortDirection === 'ASC' ? 'DESC' : 'ASC';
    } else {
      this.currentSortColumn = column;
      this.currentSortDirection = direction || 'ASC';
    }

    this.currentPage = 1; // Reset to first page when sorting changes
    this.applySortAndFilters();
  }

  /**
   * Adds a new filter
   */
  addFilter(column: string, operator: string, value: any): void {
    // Remove existing filter for the same column
    this.appliedFilters = this.appliedFilters.filter(f => f.Column !== column);
    
    // Add new filter
    if (value !== null && value !== undefined && value !== '') {
      if (this.validateFilterValue(column, value, operator)) {
        this.appliedFilters.push({
          Column: column,
          Value: value.toString(), // Convert to string as per interface
          Operand: operator
        });
      }
    }

    this.currentPage = 1; // Reset to first page when filters change
    this.applySortAndFilters();
  }

  /**
   * Removes a specific filter
   */
  removeFilter(column: string): void {
    this.appliedFilters = this.appliedFilters.filter(f => f.Column !== column);
    this.currentPage = 1;
    this.applySortAndFilters();
  }

  /**
   * Clears all filters
   */
  clearAllFilters(): void {
    this.appliedFilters = [];
    this.currentPage = 1;
    this.applySortAndFilters();
  }

  /**
   * Gets the current filter value for a column
   */
  getFilterValue(column: string): any {
    const filter = this.appliedFilters.find(f => f.Column === column);
    return filter ? filter.Value : null;
  }

  /**
   * Gets the current filter operator for a column
   */
  getFilterOperator(column: string): string {
    const filter = this.appliedFilters.find(f => f.Column === column);
    return filter ? filter.Operand : 'eq';
  }

  /**
   * Checks if a filter is applied for a column
   */
  hasFilter(column: string): boolean {
    return this.appliedFilters.some(f => f.Column === column);
  }

  /**
   * Applies current sort and filters to the data
   */
  private applySortAndFilters(): void {
    if (this.isSearchMode && this.searchTerm.trim()) {
      this.performSearch(this.searchTerm);
    } else {
      this.loadRules();
    }
  }

  /**
   * Gets sort icon for a column
   */
  getSortIcon(column: string): string {
    if (this.currentSortColumn !== column) {
      return 'unfold_more';
    }
    return this.currentSortDirection === 'ASC' ? 'keyboard_arrow_up' : 'keyboard_arrow_down';
  }

  /**
   * Checks if a column is currently being sorted
   */
  isSortedBy(column: string): boolean {
    return this.currentSortColumn === column;
  }

  /**
   * Gets display text for a filter
   */
  getFilterDisplayText(filter: WhereParams): string {
    const option = this.filterOptions.find(opt => opt.column === filter.Column);
    const label = option?.label || filter.Column;
    
    let operatorText = '';
    switch (filter.Operand) {
      case 'eq': operatorText = '='; break;
      case 'neq': operatorText = '≠'; break;
      case 'lt': operatorText = '<'; break;
      case 'gt': operatorText = '>'; break;
      case 'lte': operatorText = '≤'; break;
      case 'gte': operatorText = '≥'; break;
      case 'contains': operatorText = 'contient'; break;
      case 'startswith': operatorText = 'commence par'; break;
      case 'endswith': operatorText = 'finit par'; break;
      default: operatorText = filter.Operand;
    }

    return `${label} ${operatorText} ${filter.Value}`;
  }

  /**
   * Creates a quick filter for common scenarios
   */
  createQuickFilter(column: string, value: any, operator: string = 'eq'): void {
    this.addFilter(column, operator, value);
  }

  /**
   * Sets up predefined filter combinations
   */
  applyPredefinedFilter(filterType: string): void {
    this.clearAllFilters();
    
    switch (filterType) {
      case 'active-rules':
        this.createQuickFilter('Status', 'active', 'eq');
        break;
      case 'recent-rules':
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        this.createQuickFilter('RuleCreatedAt', oneWeekAgo.toISOString().split('T')[0], 'gte');
        break;
      case 'high-priority':
        this.createQuickFilter('Priority', '5', 'lt');
        break;
      case 'frequently-used':
        this.createQuickFilter('TotalApplications', '10', 'gt');
        break;
      default:
        break;
    }
  }

  /**
   * Validates filter values before applying
   */
  private validateFilterValue(column: string, value: any, operator: string): boolean {
    if (value === null || value === undefined || value === '') {
      return false;
    }

    // Additional validation based on column type
    const option = this.filterOptions.find(opt => opt.column === column);
    if (!option) return true;

    switch (option.type) {
      case 'number':
        return !isNaN(Number(value));
      case 'date':
        return !isNaN(Date.parse(value));
      default:
        return true;
    }
  }

  /**
   * Gets the total number of active filters
   */
  getActiveFiltersCount(): number {
    return this.appliedFilters.length;
  }

  /**
   * Checks if any advanced features are active
   */
  hasAdvancedFeaturesActive(): boolean {
    return this.appliedFilters.length > 0 || 
           this.currentSortColumn !== 'Priority' || 
           this.currentSortDirection !== 'ASC';
  }

  /**
   * Handles select change events for filters
   */
  onFilterOperatorChange(event: Event, column: string): void {
    const target = event.target as HTMLSelectElement;
    if (target?.value) {
      this.addFilter(column, target.value, this.getFilterValue(column) || '');
    }
  }

  /**
   * Handles input change events for filters
   */
  onFilterValueChange(event: Event, column: string): void {
    const target = event.target as HTMLInputElement;
    if (target) {
      this.addFilter(column, this.getFilterOperator(column), target.value || '');
    }
  }

  /**
   * Handles select change events for filter values
   */
  onFilterSelectChange(event: Event, column: string): void {
    const target = event.target as HTMLSelectElement;
    if (target) {
      this.addFilter(column, 'eq', target.value || '');
    }
  }

  /**
   * Resets all sorting and filtering to defaults
   */
  resetToDefaults(): void {
    this.currentSortColumn = 'Priority';
    this.currentSortDirection = 'ASC';
    this.appliedFilters = [];
    this.searchTerm = '';
    this.showAdvancedFilters = false;
    this.isSearchMode = false;
    this.currentPage = 1;
    
    this.loadRules();
    this.toast.info('Tri et filtres réinitialisés', 'Info', 2000, false);
  }

  // === CORE DATA LOADING (UPDATED) ===

  private loadRules(): void {
    this.ngxUiLoaderService.start();
    this.isLoading = true;
    this.isSearching = false;

    if (this.currentPage < 1) {
      this.currentPage = 1;
    }

    const lister: Lister<RuleComprehensive> = {
      Pagination: {
        CurrentPage: this.currentPage,
        PageSize: this.pageSize,
        PageCount: 0,
        IsLast: false,
        IsFirst: this.currentPage === 1,
        StartIndex: (this.currentPage - 1) * this.pageSize,
        TotalElement: 0
      },
      SortParams: [{ 
        Column: this.currentSortColumn, 
        Sort: this.currentSortDirection 
      }],
      FilterParams: [...this.appliedFilters] // Include applied filters
    };

    // Use advanced endpoint if we have complex filters (simplified since no AndOr)
    const hasComplexFilters = this.appliedFilters.length > 3;

    const request$ = hasComplexFilters 
      ? this.rulesApiService.getRulesComprehensiveAdvanced({
          page: this.currentPage,
          pageSize: this.pageSize,
          sortParams: lister.SortParams,
          filterParams: this.appliedFilters
        })
      : this.rulesApiService.getRulesComprehensive(lister);

    request$.pipe(
      map(response => {
        this.totalElements = response.Lister.Pagination.TotalElement || 0;
        this.pageCount = response.Lister.Pagination.PageCount || Math.ceil(this.totalElements / this.pageSize);

        if (this.currentPage > this.pageCount && this.pageCount > 0) {
          this.currentPage = this.pageCount;
          this.loadRules();
          return [];
        }

        return response.Content.map(dto => {
          if (dto.RawData) {
            this.rawDataCache.set(dto.RuleId, dto.RawData);
          }
          if (dto.RuleSummary) {
            this.summaryCache.set(dto.RuleId, dto.RuleSummary);
          }
          return this.mapDtoToRuleContent(dto);
        });
      })
    ).subscribe({
      next: (mappedRules) => {
        this.rules = mappedRules;
        this.filteredRules = [...this.rules];
        this.loadHierarchyForAllRules();
        this.isLoading = false;
        this.ngxUiLoaderService.stop();
      },
      error: (error) => {
        console.error('Error loading rules:', error);
        this.isLoading = false;
        this.ngxUiLoaderService.stop();
        this.showErrorDialog('Erreur lors du chargement des règles. Veuillez vérifier votre connexion et réessayer.');
      }
    });
  }

  private loadHierarchyForAllRules(): void {
    const hierarchyObservables = this.rules.map(rule =>
      forkJoin({
        clientHierarchy: this.rulesApiService.getRuleClientHierarchy(rule.id),
        transactionDetails: this.rulesApiService.getRuleTransactionDetails(rule.id)
      }).pipe(
        map(data => ({
          ruleId: rule.id,
          clientHierarchy: data.clientHierarchy,
          transactionDetails: data.transactionDetails
        }))
      )
    );

    if (hierarchyObservables.length > 0) {
      forkJoin(hierarchyObservables).subscribe({
        next: (results) => {
          results.forEach(result => {
            const rule = this.rules.find(r => r.id === result.ruleId);
            if (rule) {
              rule.clients = this.aggregateClientHierarchyWithControllers(
                result.clientHierarchy,
                result.transactionDetails
              );

              const filteredRule = this.filteredRules.find(r => r.id === result.ruleId);
              if (filteredRule) {
                filteredRule.clients = rule.clients;
              }
            }
          });

          this.loadControllerDetails();
        },
        error: (error) => {
          console.error('Error loading hierarchy with controllers:', error);
        }
      });
    }
  }

  private aggregateClientHierarchyWithControllers(
    hierarchy: RuleClientHierarchy[],
    transactionDetails: RuleTransactionDetail[]
  ): ClientForRule[] {
    const clientsMap = new Map<string, ClientForRule>();

    // Build the basic client/site/local hierarchy
    hierarchy.forEach(item => {
      if (!clientsMap.has(item.ClientId)) {
        clientsMap.set(item.ClientId, {
          id: item.ClientId,
          name: item.ClientRC || item.ClientSIREN || 'Unknown Client',
          email: '',
          company: '',
          status: 'active',
          sites: []
        });
      }
      const client = clientsMap.get(item.ClientId)!;

      let site = client.sites.find(s => s.id === item.SiteId);
      if (!site) {
        site = {
          id: item.SiteId,
          name: item.SiteName,
          address: item.SiteAddress,
          clientId: item.ClientId,
          locations: []
        };
        client.sites.push(site);
      }

      let location = site.locations.find(l => l.id === item.LocalId);
      if (!location) {
        location = {
          id: item.LocalId,
          name: item.LocalName,
          type: 'room',
          controllers: []
        };
        site.locations.push(location);
      }
    });

    // Add controllers from transaction details
    transactionDetails.forEach(detail => {
      const actualControllerId = detail.ControllerIdController || detail.ControllerId;

      if (!actualControllerId) {
        return;
      }

      clientsMap.forEach(client => {
        client.sites.forEach(site => {
          site.locations.forEach(location => {
            if (location.id === detail.ControllerLocalId) {
              if (!location.controllers.find(c => c.id === actualControllerId)) {
                const controllerForRule: ControllerForRule = {
                  id: actualControllerId,
                  name: `Controller ${actualControllerId.substring(0, 8)}`,
                  model: 'Loading...',
                  lastSeen: detail.TransactionCreatedAt,
                  status: detail.ControllerInControl,
                  applications: [],
                  performanceAnalytics: []
                };
                location.controllers.push(controllerForRule);
              }
            }
          });
        });
      });
    });

    return Array.from(clientsMap.values());
  }

  private loadControllerDetails(): void {
    const controllerIds = new Set<string>();

    this.rules.forEach(rule => {
      rule.clients.forEach(client => {
        client.sites.forEach(site => {
          site.locations.forEach(location => {
            location.controllers.forEach(controller => {
              controllerIds.add(controller.id);
            });
          });
        });
      });
    });

    const controllerObservables = Array.from(controllerIds).map(controllerId =>
      this.controllerApiService.getById(controllerId).pipe(
        map(controller => ({
          controllerId,
          controller
        }))
      )
    );

    if (controllerObservables.length > 0) {
      forkJoin(controllerObservables).subscribe({
        next: (results) => {
          results.forEach(result => {
            if (result.controller) {
              this.controllersCache.set(result.controllerId, result.controller);
            }
          });

          this.updateRulesWithControllerData();
        },
        error: (error) => {
          console.error('Error loading controller details:', error);
        }
      });
    }
  }

  private updateRulesWithControllerData(): void {
    [this.rules, this.filteredRules].forEach(rulesList => {
      rulesList.forEach(rule => {
        rule.clients.forEach(client => {
          client.sites.forEach(site => {
            site.locations.forEach(location => {
              location.controllers.forEach(controller => {
                const cachedController = this.controllersCache.get(controller.id);
                if (cachedController) {
                  controller.name = cachedController.HostName || `Controller ${controller.id.substring(0, 8)}`;
                  controller.model = cachedController.Model || 'Unknown Model';
                  controller.lastSeen = cachedController.LastConnection ?
                    cachedController.LastConnection.toString() : controller.lastSeen;
                  controller.status = cachedController.State ?? false;
                }
              });
            });
          });
        });
      });
    });
  }

  // === SEARCH FUNCTIONALITY (UPDATED) ===

  private setupSearch(): void {
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(searchTerm => {
      this.performSearch(searchTerm);
    });
  }

  submitSearch(): void {
    this.currentPage = 1;
    this.performSearch(this.searchTerm);
  }

  private performSearch(searchTerm: string): void {
    if (!searchTerm.trim()) {
      this.currentPage = 1;
      this.filteredRules = [...this.rules];
      this.isSearching = false;
      this.isSearchMode = false;
      this.loadRules();
      return;
    }

    this.isSearching = true;
    this.isSearchMode = true;

    const lister: Lister<RuleComprehensive> = {
      Pagination: {
        CurrentPage: this.currentPage,
        PageSize: this.pageSize,
        PageCount: 0,
        IsLast: false,
        IsFirst: this.currentPage === 1,
        StartIndex: (this.currentPage - 1) * this.pageSize,
        TotalElement: 0
      },
      SortParams: [{ 
        Column: this.currentSortColumn, 
        Sort: this.currentSortDirection 
      }],
      FilterParams: [...this.appliedFilters] // Include applied filters
    };

    // Use advanced search if we have complex filters (simplified since no AndOr)
    const hasComplexFilters = this.appliedFilters.length > 3;

    const request$ = hasComplexFilters
      ? this.rulesApiService.searchRulesAdvanced({
          searchTerm: searchTerm.trim(),
          includeInactive: false,
          page: this.currentPage,
          pageSize: this.pageSize,
          sortParams: lister.SortParams,
          filterParams: this.appliedFilters
        })
      : this.rulesApiService.searchRules(searchTerm.trim(), lister);

    request$.pipe(
      map(response => {
        this.totalElements = response.Lister.Pagination.TotalElement || 0;
        this.pageCount = response.Lister.Pagination.PageCount || Math.ceil(this.totalElements / this.pageSize);

        if (this.currentPage > this.pageCount && this.pageCount > 0) {
          this.currentPage = this.pageCount;
        }

        return response.Content.map(dto => {
          if (dto.RawData) {
            this.rawDataCache.set(dto.RuleId, dto.RawData);
          }
          if (dto.RuleSummary) {
            this.summaryCache.set(dto.RuleId, dto.RuleSummary);
          }
          return this.mapDtoToRuleContent(dto);
        });
      })
    ).subscribe({
      next: (searchedRules) => {
        this.filteredRules = searchedRules;
        this.loadHierarchyForSearchResults(searchedRules);
        this.isSearching = false;
      },
      error: (error) => {
        console.error('Error performing search:', error);
        this.isSearching = false;
        this.performLocalSearch(searchTerm);
        this.toast.info('Erreur lors de la recherche, filtrage local utilisé', 'Info', 3000, false);
      }
    });
  }

  private loadHierarchyForSearchResults(searchResults: RuleContent[]): void {
    const rulesToLoadHierarchy = searchResults.filter(searchRule => {
      const existingRule = this.rules.find(r => r.id === searchRule.id);
      return !existingRule || existingRule.clients.length === 0;
    });

    if (rulesToLoadHierarchy.length > 0) {
      const hierarchyObservables = rulesToLoadHierarchy.map(rule =>
        forkJoin({
          clientHierarchy: this.rulesApiService.getRuleClientHierarchy(rule.id),
          transactionDetails: this.rulesApiService.getRuleTransactionDetails(rule.id)
        }).pipe(
          map(data => ({
            ruleId: rule.id,
            clientHierarchy: data.clientHierarchy,
            transactionDetails: data.transactionDetails
          }))
        )
      );

      forkJoin(hierarchyObservables).subscribe({
        next: (results) => {
          results.forEach(result => {
            const searchRule = this.filteredRules.find(r => r.id === result.ruleId);
            if (searchRule) {
              searchRule.clients = this.aggregateClientHierarchyWithControllers(
                result.clientHierarchy,
                result.transactionDetails
              );
            }

            const mainRule = this.rules.find(r => r.id === result.ruleId);
            if (mainRule) {
              mainRule.clients = this.aggregateClientHierarchyWithControllers(
                result.clientHierarchy,
                result.transactionDetails
              );
            }
          });
        },
        error: (error) => {
          console.error('Error loading hierarchy for search results:', error);
        }
      });
    }
  }

  private performLocalSearch(searchTerm: string): void {
    const term = searchTerm.toLowerCase().trim();
    this.filteredRules = this.rules.filter(rule => {
      return rule.name.toLowerCase().includes(term) ||
             rule.tags.some(tag => tag.toLowerCase().includes(term)) ||
             rule.actions.some(action =>
               action.type.toLowerCase().includes(term) ||
               action.action.toLowerCase().includes(term) ||
               action.value.toLowerCase().includes(term) ||
               action.target.toLowerCase().includes(term)
             ) ||
             rule.conditions.some(condition =>
               condition.type.toLowerCase().includes(term) ||
               condition.operator.toLowerCase().includes(term) ||
               condition.value.toLowerCase().includes(term)
             ) ||
             this.rawDataCache.get(rule.id)?.toLowerCase().includes(term) ||
             this.summaryCache.get(rule.id)?.toLowerCase().includes(term);
    });
  }

  clearSearch(): void {
    if (this.searchTerm.trim() && this.filteredRules.length > 0) {
      this.performClearSearch();
    }
  }

  private performClearSearch(): void {
    this.searchTerm = '';
    this.currentPage = 1;
    this.isSearchMode = false;
    this.isSearching = false;
    this.filteredRules = [];
    this.loadRules();
  }

  // === RULE MANAGEMENT ===

  openRuleFormDialog(rule?: any): void {
    if (this.isSearching) {
      this.toast.info('Veuillez attendre la fin de la recherche en cours','Info',3000,false);
      return;
    }

    const dialogRef = this.dialog.open(RuleFormComponent, {
      width: '95vw',
      maxWidth: '800px',
      panelClass: 'custom-rule-form-dialog',
      data: rule ? { rule: rule } : {},
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.action === 'saved') {
        this.toast.success('Règle sauvegardée avec succès!','Succès',3000,true);
        this.loadRules();
      }
    });
  }

  editRuleById(ruleId: string): void {
    if (this.isDeleting.has(ruleId)) {
      this.toast.info('Impossible de modifier une règle en cours de suppression','Info',3000,false);
      return;
    }

    const rawData = this.rawDataCache.get(ruleId);

    if (rawData) {
      try {
        const ruleForEdit = {
          id: ruleId,
          isEdit: true,
          RawData: rawData,
          ...JSON.parse(rawData)
        };

        const dialogRef = this.dialog.open(RuleFormComponent, {
          width: '95vw',
          maxWidth: '800px',
          panelClass: 'custom-rule-form-dialog',
          data: { rule: ruleForEdit },
          disableClose: true
        });

        dialogRef.afterClosed().subscribe(result => {
          if (result && result.action === 'saved') {
            this.toast.success('Règle mise à jour avec succès','Succès',3000,false);
            this.loadRules();
          }
        });
      } catch (error) {
        console.error('Error parsing raw data for edit:', error);
        this.showErrorDialog('Erreur lors de l\'analyse des données de la règle. Les données pourraient être corrompues.');
      }
    } else {
      this.rulesApiService.getRuleComprehensiveById(ruleId).subscribe({
        next: (ruleDto) => {
          if (ruleDto && ruleDto.RawData) {
            this.rawDataCache.set(ruleId, ruleDto.RawData);
            if (ruleDto.RuleSummary) {
              this.summaryCache.set(ruleId, ruleDto.RuleSummary);
            }
            this.editRuleById(ruleId);
          } else {
            this.showErrorDialog('Impossible de charger les données de la règle pour la modification.');
          }
        },
        error: (error) => {
          console.error('Error fetching rule for edit:', error);
          this.showErrorDialog('Erreur lors du chargement de la règle. Veuillez réessayer.');
        }
      });
    }
  }

  deleteRule(ruleId: string): void {
    if (this.isDeleting.has(ruleId)) {
      return;
    }

    const rule = this.filteredRules.find(r => r.id === ruleId);
    const ruleName = rule?.name || 'cette règle';

    let confirmationMessage = `Voulez-vous vraiment supprimer la règle "${ruleName}" ?`;

    if (rule) {
      const details = [];
      if (rule.totalApplications > 0) {
        details.push(`${rule.totalApplications} application(s) enregistrée(s)`);
      }
      if (rule.clients.length > 0) {
        const totalControllers = this.getTotalControllersForRule(rule);
        details.push(`${rule.clients.length} client(s) et ${totalControllers} contrôleur(s) associé(s)`);
      }
      if (rule.actions.length > 0) {
        details.push(`${rule.actions.length} action(s) configurée(s)`);
      }

      if (details.length > 0) {
        confirmationMessage += `\n\nCette règle contient :\n• ${details.join('\n• ')}`;
      }

      confirmationMessage += '\n\nCette action est irréversible et supprimera définitivement toutes les données associées.';
    }

    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Supprimer la règle',
        message: confirmationMessage,
        icon: 'delete_forever'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.performDeleteRule(ruleId);
      }
    });
  }

  private performDeleteRule(ruleId: string): void {
    this.isDeleting.add(ruleId);

    this.rulesApiService.delete(ruleId).subscribe({
      next: (success) => {
        this.isDeleting.delete(ruleId);

        if (success) {
          this.toast.success('Règle supprimée avec succès','Succès',3000,false);

          this.rawDataCache.delete(ruleId);
          this.summaryCache.delete(ruleId);

          if (this.expandedRuleId === ruleId) {
            this.expandedRuleId = null;
            this.selectedControllerId = null;
          }

          this.loadRules();
        } else {
          this.showErrorDialog('Échec de la suppression de la règle. Veuillez réessayer.');
        }
      },
      error: (error) => {
        this.isDeleting.delete(ruleId);
        console.error('Error deleting rule:', error);
        this.showErrorDialog('Erreur lors de la suppression de la règle. Veuillez vérifier votre connexion et réessayer.');
      }
    });
  }

  // === UI STATE MANAGEMENT ===

  toggleRuleDetails(ruleId: string): void {
    this.expandedRuleId = this.expandedRuleId === ruleId ? null : ruleId;
    this.selectedControllerId = null;
    this.activeTab = 'summary';

    if (this.expandedRuleId) {
      const rule = this.filteredRules.find(r => r.id === this.expandedRuleId);
      if (rule && rule.clients.length > 0) {
        this.populateControllerApplicationsAndPerformance(rule);
      }
    }
  }

  setActiveTab(tab: 'hierarchy' | 'rawdata' | 'summary'): void {
    this.activeTab = tab;
  }

  selectController(controllerId: string): void {
    this.selectedControllerId = this.selectedControllerId === controllerId ? null : controllerId;
  }

  // === PAGINATION ===

  goToPage(page: number): void {
    if (page >= 1 && page <= this.pageCount && page !== this.currentPage && !this.hasPendingOperations()) {
      this.currentPage = page;

      if (this.isSearchMode && this.searchTerm.trim()) {
        this.performSearch(this.searchTerm);
      } else {
        this.loadRules();
      }
    }
  }

  nextPage(): void {
    if (this.currentPage < this.pageCount && !this.hasPendingOperations()) {
      this.currentPage++;

      if (this.isSearchMode && this.searchTerm.trim()) {
        this.performSearch(this.searchTerm);
      } else {
        this.loadRules();
      }
    }
  }

  previousPage(): void {
    if (this.currentPage > 1 && !this.hasPendingOperations()) {
      this.currentPage--;

      if (this.isSearchMode && this.searchTerm.trim()) {
        this.performSearch(this.searchTerm);
      } else {
        this.loadRules();
      }
    }
  }

  getPagesArray(): number[] {
    if (this.pageCount <= 0) {
      return [];
    }
    return Array.from({ length: this.pageCount }, (_, i) => i + 1);
  }

  shouldShowPagination(): boolean {
    return this.totalElements > this.pageSize || this.pageCount > 1;
  }

  // === DATA UTILITY METHODS ===

  private mapDtoToRuleContent(dto: RuleComprehensive): RuleContent {
    let parsedRawData: RawDataBackendStructure | null = null;
    try {
      if (dto.RawData) {
        parsedRawData = JSON.parse(dto.RawData) as RawDataBackendStructure;
      }
    } catch (e) {
      console.error('Error parsing RawData for rule:', dto.RuleId, e);
    }

    const priority: number = parsedRawData?.priority ?? dto.Priority;
    const status: string = parsedRawData?.enabled == true ? 'active' : 'inactive';

    const conditions: Condition[] = [];
    if (parsedRawData && parsedRawData.conditions?.groups?.length > 0) {
      parsedRawData.conditions.groups[0].conditions.forEach((cond, index) => {
        conditions.push({
          id: index + 1,
          type: cond.type,
          operator: cond.operator,
          value: cond.value,
          deviceId: cond.device,
          propertyName: cond.key
        });
      });
    }

    const actions: Action[] = [];
    if (parsedRawData && parsedRawData.actions?.length > 0) {
      parsedRawData.actions.forEach((act, index) => {
        let actionValue = '';
        let actionName = act.type;
        if (act.payload) {
          const payloadKeys = Object.keys(act.payload);
          if (payloadKeys.length > 0) {
            actionName = payloadKeys[0];
            actionValue = String(act.payload[payloadKeys[0]]);
          }
        }

        actions.push({
          id: index + 1,
          type: act.type,
          action: actionName,
          value: actionValue,
          target: act.topic
        });
      });
    }

    const name: string = parsedRawData?.rule_name || `Rule ${dto.RuleId.substring(0, 8)}`;
    const tagsArray: string[] = dto.TagsString ? dto.TagsString.split(', ').filter(t => t.length > 0) : [];
    const tagStatus: { [key: string]: string } = {};
    tagsArray.forEach(tag => tagStatus[tag] = 'active');

    return {
      id: dto.RuleId,
      name: name,
      priority: priority,
      status: status == 'active' ? 'active' : 'inactive',
      tags: tagsArray,
      conditions: conditions,
      actions: actions,
      tagStatus: tagStatus,
      totalApplications: dto.TotalApplications,
      lastTriggered: dto.LastTriggered || '',
      clients: []
    };
  }

  // === CONTROLLER UTILITY METHODS ===

  private populateControllerApplicationsAndPerformance(rule: RuleContent): void {
    const allControllersInRule: { controller: ControllerForRule, localId: string, localName: string }[] = [];
    rule.clients.forEach(client => {
      client.sites.forEach(site => {
        site.locations.forEach(location => {
          location.controllers.forEach(controller => {
            allControllersInRule.push({
              controller,
              localId: location.id,
              localName: location.name
            });
          });
        });
      });
    });

    if (allControllersInRule.length > 0) {
      // Load applications and performance data once for the rule
      forkJoin({
        apps: this.rulesApiService.getRuleRecentApplications(rule.id, 10),
        performance: this.rulesApiService.getRulePerformance(rule.id, 7)
      }).subscribe({
        next: (data) => {
          // Distribute applications to controllers based on LocalName
          allControllersInRule.forEach(({ controller, localId, localName }) => {
            // Filter applications by LocalName (since that's what matches your API response)
            controller.applications = data.apps
              .filter(app => app.LocalName === localName)
              .map(app => ({
                timestamp: app.ApplicationTimestamp,
                success: app.ApplicationSuccess,
                executionTime: 0 // This field doesn't exist in your API response
              }));

            // Filter performance data by LocalId
            controller.performanceAnalytics = data.performance
              .filter(perf => perf.LocalId === localId);
          });
        },
        error: (error) => console.error('Error populating controller applications and performance:', error)
      });
    }
  }

  getControllerStatusIcon(controller: ControllerForRule): string {
    const cachedController = this.controllersCache.get(controller.id);
    const status = cachedController?.State ?? controller.status;

    switch (status) {
      case true: return 'check_circle';
      case false: return 'error';
      default: return 'help';
    }
  }

  getControllerStatusText(controller: ControllerForRule): string {
    const cachedController = this.controllersCache.get(controller.id);
    const status = cachedController?.State ?? controller.status;

    switch (status) {
      case true: return 'En ligne';
      case false: return 'Hors ligne';
      default: return 'Inconnu';
    }
  }

  getControllerStatusClass(controller: ControllerForRule): string {
    const cachedController = this.controllersCache.get(controller.id);
    const status = cachedController?.State ?? controller.status;

    switch (status) {
      case true: return 'online';
      case false: return 'offline';
      default: return 'unknown';
    }
  }

  // === SUMMARY FUNCTIONALITY ===

  getRuleSummary(ruleId: string): string {
    return this.summaryCache.get(ruleId) || 'Résumé non disponible';
  }

  hasRuleSummary(ruleId: string): boolean {
    const summary = this.summaryCache.get(ruleId);
    return summary != null && summary.trim().length > 0 && summary !== 'Résumé non disponible';
  }

  // === RAW DATA FUNCTIONALITY ===

  formatRawData(ruleId: string): string {
    const rawData = this.rawDataCache.get(ruleId);
    if (!rawData) {
      return '{\n  "error": "Données brutes non disponibles"\n}';
    }

    try {
      const parsed = JSON.parse(rawData);
      return JSON.stringify(parsed, null, 2);
    } catch (e) {
      return rawData;
    }
  }

  copyRawData(ruleId: string): void {
    const rawData = this.formatRawData(ruleId);
    navigator.clipboard.writeText(rawData).then(() => {
      this.toast.success('Données copiées dans le presse-papiers','Succès',3000,false);
    }).catch(err => {
      console.error('Error copying to clipboard:', err);
      this.toast.warning('Erreur lors de la copie','Erreur',3000,false);
    });
  }

  downloadRawData(rule: RuleContent): void {
    const rawData = this.formatRawData(rule.id);
    const blob = new Blob([rawData], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `rule-${rule.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}-${rule.id.substring(0, 8)}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    this.toast.success('Téléchargement démarré','Succès',3000,false);
  }

  // === UTILITY METHODS ===

  isSimpleRule(rule: RuleContent): boolean {
    const hasMinimalData = (
      rule.clients.length === 0 &&
      rule.tags.length === 0 &&
      rule.totalApplications === 0 &&
      (!rule.conditions || rule.conditions.length <= 1) &&
      (!rule.actions || rule.actions.length <= 1)
    );

    const isNewRule = (
      !rule.lastTriggered || rule.lastTriggered === '' || rule.lastTriggered === 'Jamais'
    );

    return hasMinimalData && isNewRule;
  }

  isRuleDeleting(ruleId: string): boolean {
    return this.isDeleting.has(ruleId);
  }

  getLoadingState(): boolean {
    return this.isLoading;
  }

  hasPendingOperations(): boolean {
    return this.isLoading || this.isSearching || this.isDeleting.size > 0;
  }

  getTotalClientsForRule(rule: RuleContent): number {
    return rule.clients.length;
  }

  getTotalControllersForRule(rule: RuleContent): number {
    return rule.clients.reduce((total, client) => {
      return total + client.sites.reduce((siteAcc, site) => {
        return siteAcc + site.locations.reduce((locAcc, location) => {
          return locAcc + location.controllers.length;
        }, 0);
      }, 0);
    }, 0);
  }

  formatTimestamp(timestamp: string): string {
    if (!timestamp) return 'N/A';
    const date = new Date(timestamp);
    return date.toLocaleString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  refreshData(): void {
    if (this.isLoading || this.isSearching || this.isDeleting.size > 0) {
      this.toast.info('Une opération est en cours, veuillez patienter','Info',3000,false);
      return;
    }

    this.confirmRefresh();
  }

  private confirmRefresh(): void {
    if (this.expandedRuleId || this.isSearching) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: 'Actualiser les données',
          message: 'Actualiser les données fermera les détails ouverts et réinitialisera les filtres. Continuer ?',
          icon: 'refresh'
        },
        disableClose: true,
        panelClass: 'confirmation-dialog-panel'
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.performRefresh();
        }
      });
    } else {
      this.performRefresh();
    }
  }

  private performRefresh(): void {
    this.currentPage = 1;
    this.searchTerm = '';
    this.isSearchMode = false;
    this.expandedRuleId = null;
    this.selectedControllerId = null;
    this.controllersCache.clear();
    // Note: We don't reset filters and sorting on refresh unless explicitly requested
    this.loadRules();
  }

  private showErrorDialog(message: string): void {
    this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Erreur',
        message: message,
        icon: 'error'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    });
  }

  openAiRuleGeneratorDialog(): void {
    if (this.isSearching) {
      this.toast.info('Veuillez attendre la fin de la recherche en cours','Info',3000,false);
      return;
    }

    const dialogRef = this.dialog.open(AiRuleGeneratorDialogComponent, {
      width: '600px',
      panelClass: 'ai-rule-generator-dialog-panel',
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      // Check if the dialog closed with a 'confirm' action, indicating a successful save
      if (result && result.action === 'confirm') {
        this.toast.success('Règle générée par IA et sauvegardée avec succès!','Succès',3000,false);
        this.loadRules(); // Refresh the list of rules
      }
    });
  }
}