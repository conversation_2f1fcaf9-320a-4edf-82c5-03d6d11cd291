import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';

// Import components
import { RuleFormComponent } from '../../components/rule-form/rule-form.component';
import { ConfirmationDialogComponent } from '../../components/confirmation-dialog/confirmation-dialog.component';

// Import models
import { RuleContent } from "@app/shared/models/rule/RuleContent";
import { RuleComprehensive } from "@app/shared/models/rule/RuleComprehensive";
import { RuleClientHierarchy } from "@app/shared/models/rule/RuleClientHierarchy";
import { Lister } from "@app/shared/models/rule/Lister";
import { ClientForRule } from "@app/shared/models/rule/ClientForRule";
import { ControllerForRule } from "@app/shared/models/rule/ControllerForRule";
import { Action } from "@app/shared/models/rule/Action";
import { Condition } from "@app/shared/models/rule/Condition";
import { RawDataBackendStructure } from "@app/shared/models/rule/RawDataBackendStructure";

import { RulesApiService } from '../../core/services/administrative/rules.service';
import { forkJoin, map, debounceTime, distinctUntilChanged, Subject } from 'rxjs';

@Component({
  selector: 'app-generator-regles-ia',
  templateUrl: './generator-regles-ia.component.html',
  styleUrls: ['./generator-regles-ia.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatDialogModule,
    MatTooltipModule,
    NgxUiLoaderModule,
    MatSnackBarModule
  ]
})
export class GeneratorReglesIaComponent implements OnInit {
  // Core data properties
  rules: RuleContent[] = [];
  filteredRules: RuleContent[] = [];
  
  // Search and pagination
  searchTerm: string = '';
  currentPage: number = 1;
  pageSize: number = 10;
  totalElements: number = 0;
  pageCount: number = 0;
  
  // UI state
  activeTab: 'hierarchy' | 'rawdata' | 'summary' = 'summary';
  expandedRuleId: string | null = null;
  selectedControllerId: string | null = null;
  
  // Loading states
  private isDeleting = new Set<string>();
  private isLoading = false;
  isSearching: boolean = false;
  isSearchMode: boolean = false;
  
  // Search functionality
  private searchSubject = new Subject<string>();
  
  // Data caches
  private rawDataCache = new Map<string, string>();
  private summaryCache = new Map<string, string>();

  constructor(
    private dialog: MatDialog,
    private rulesApiService: RulesApiService,
    private snackBar: MatSnackBar,
    private ngxUiLoaderService: NgxUiLoaderService
  ) {}

  ngOnInit(): void {
    // Ensure we start with page 1
    this.currentPage = 1;
    this.loadRules();
    this.setupSearch();
  }

  // === CORE DATA LOADING ===

  private loadRules(): void {
    this.ngxUiLoaderService.start();
    this.isLoading = true;
    this.isSearching = false;
    
    // Ensure currentPage is at least 1
    if (this.currentPage < 1) {
      this.currentPage = 1;
    }
    
    const lister: Lister<RuleComprehensive> = {
      Pagination: {
        CurrentPage: this.currentPage,
        PageSize: this.pageSize,
        PageCount: 0,
        IsLast: false,
        IsFirst: this.currentPage === 1,
        StartIndex: (this.currentPage - 1) * this.pageSize,
        TotalElement: 0
      },
      SortParams: [{ Column: "Priority", Sort: "ASC" }],
      FilterParams: []
    };

    console.log('🚀 Component: Loading rules with pagination:', {
      CurrentPage: lister.Pagination.CurrentPage,
      PageSize: lister.Pagination.PageSize,
      StartIndex: lister.Pagination.StartIndex,
      IsFirst: lister.Pagination.IsFirst
    });

    this.rulesApiService.getRulesComprehensive(lister).pipe(
      map(response => {
        console.log('📨 Component: API Response received:', {
          responseCurrentPage: response.Lister.Pagination.CurrentPage,
          responseStartIndex: response.Lister.Pagination.StartIndex,
          responseTotalElements: response.Lister.Pagination.TotalElement,
          responsePageCount: response.Lister.Pagination.PageCount,
          contentLength: response.Content.length
        });
        
        // Validate that we got the expected page
        const expectedPage = this.currentPage;
        const actualPage = response.Lister.Pagination.CurrentPage;
        
        if (expectedPage !== actualPage) {
          console.warn(`⚠️ Page mismatch! Expected: ${expectedPage}, Got: ${actualPage}`);
        }
        
        this.totalElements = response.Lister.Pagination.TotalElement || 0;
        this.pageCount = response.Lister.Pagination.PageCount || Math.ceil(this.totalElements / this.pageSize);
        
        // Ensure current page doesn't exceed page count
        if (this.currentPage > this.pageCount && this.pageCount > 0) {
          console.log(`🔄 Adjusting current page from ${this.currentPage} to ${this.pageCount}`);
          this.currentPage = this.pageCount;
          // Reload with the corrected page
          this.loadRules();
          return [];
        }
        
        console.log(`✅ Successfully loaded ${response.Content.length} rules for page ${this.currentPage} of ${this.pageCount}`);
        
        return response.Content.map(dto => {
          if (dto.RawData) {
            this.rawDataCache.set(dto.RuleId, dto.RawData);
          }
          if (dto.RuleSummary) {
            this.summaryCache.set(dto.RuleId, dto.RuleSummary);
          }
          return this.mapDtoToRuleContent(dto);
        });
      })
    ).subscribe({
      next: (mappedRules) => {
        this.rules = mappedRules;
        this.filteredRules = [...this.rules];
        this.loadHierarchyForAllRules();
        this.isLoading = false;
        this.ngxUiLoaderService.stop();
        
        console.log(`📄 Final result: Displaying ${this.filteredRules.length} rules on page ${this.currentPage}`);
      },
      error: (error) => {
        console.error('❌ Error loading rules:', error);
        this.isLoading = false;
        this.ngxUiLoaderService.stop();
        this.showErrorDialog('Erreur lors du chargement des règles. Veuillez vérifier votre connexion et réessayer.');
      }
    });
  }

  private loadHierarchyForAllRules(): void {
    const hierarchyObservables = this.rules.map(rule => 
      this.rulesApiService.getRuleClientHierarchy(rule.id).pipe(
        map(clientHierarchy => ({
          ruleId: rule.id,
          hierarchy: clientHierarchy
        }))
      )
    );

    if (hierarchyObservables.length > 0) {
      forkJoin(hierarchyObservables).subscribe({
        next: (results) => {
          results.forEach(result => {
            const rule = this.rules.find(r => r.id === result.ruleId);
            if (rule) {
              rule.clients = this.aggregateClientHierarchy(result.hierarchy);
              const filteredRule = this.filteredRules.find(r => r.id === result.ruleId);
              if (filteredRule) {
                filteredRule.clients = rule.clients;
              }
            }
          });
        },
        error: (error) => {
          console.error('Error loading hierarchy for rules:', error);
        }
      });
    }
  }

  // === SEARCH FUNCTIONALITY ===

  private setupSearch(): void {
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(searchTerm => {
      this.performSearch(searchTerm);
    });
  }

  // onSearchChange(): void {
  //   this.searchSubject.next(this.searchTerm);
  // }

  submitSearch(): void {
    this.currentPage = 1;
    this.performSearch(this.searchTerm);
  }

  private performSearch(searchTerm: string): void {
    if (!searchTerm.trim()) {
      this.currentPage = 1;
      this.filteredRules = [...this.rules];
      this.isSearching = false;
      this.isSearchMode = false;
      this.loadRules(); // Reload to get fresh data from page 1
      return;
    }

    this.isSearching = true;
    this.isSearchMode = true;

    const lister: Lister<RuleComprehensive> = {
      Pagination: {
        CurrentPage: this.currentPage,
        PageSize: this.pageSize,
        PageCount: 0,
        IsLast: false,
        IsFirst: this.currentPage === 1,
        StartIndex: (this.currentPage - 1) * this.pageSize,
        TotalElement: 0
      },
      SortParams: [{ Column: "Priority", Sort: "ASC" }],
      FilterParams: []
    };

    console.log('🔍 Component: Performing search for:', searchTerm, 'with pagination:', {
      CurrentPage: lister.Pagination.CurrentPage,
      StartIndex: lister.Pagination.StartIndex
    });

    this.rulesApiService.searchRules(searchTerm.trim(), lister).pipe(
      map(response => {
        console.log('🔍 Component: Search API Response:', {
          searchTerm: searchTerm,
          responseCurrentPage: response.Lister.Pagination.CurrentPage,
          responseTotalElements: response.Lister.Pagination.TotalElement,
          contentLength: response.Content.length
        });
        
        this.totalElements = response.Lister.Pagination.TotalElement || 0;
        this.pageCount = response.Lister.Pagination.PageCount || Math.ceil(this.totalElements / this.pageSize);
        
        if (this.currentPage > this.pageCount && this.pageCount > 0) {
          this.currentPage = this.pageCount;
        }
        
        return response.Content.map(dto => {
          if (dto.RawData) {
            this.rawDataCache.set(dto.RuleId, dto.RawData);
          }
          if (dto.RuleSummary) {
            this.summaryCache.set(dto.RuleId, dto.RuleSummary);
          }
          return this.mapDtoToRuleContent(dto);
        });
      })
    ).subscribe({
      next: (searchedRules) => {
        console.log(`🔍 Search completed: Found ${searchedRules.length} rules for "${searchTerm}" on page ${this.currentPage}`);
        this.filteredRules = searchedRules;
        this.loadHierarchyForSearchResults(searchedRules);
        this.isSearching = false;
      },
      error: (error) => {
        console.error('❌ Error performing search:', error);
        this.isSearching = false;
        this.performLocalSearch(searchTerm);

        this.snackBar.open('Erreur lors de la recherche, filtrage local utilisé', 'Fermer', {
          duration: 3000,
          horizontalPosition: 'right',
          verticalPosition: 'top'
        });
      }
    });
  }

  private loadHierarchyForSearchResults(searchResults: RuleContent[]): void {
    const rulesToLoadHierarchy = searchResults.filter(searchRule => {
      const existingRule = this.rules.find(r => r.id === searchRule.id);
      return !existingRule || existingRule.clients.length === 0;
    });

    if (rulesToLoadHierarchy.length > 0) {
      const hierarchyObservables = rulesToLoadHierarchy.map(rule => 
        this.rulesApiService.getRuleClientHierarchy(rule.id).pipe(
          map(clientHierarchy => ({
            ruleId: rule.id,
            hierarchy: clientHierarchy
          }))
        )
      );

      forkJoin(hierarchyObservables).subscribe({
        next: (results) => {
          results.forEach(result => {
            const searchRule = this.filteredRules.find(r => r.id === result.ruleId);
            if (searchRule) {
              searchRule.clients = this.aggregateClientHierarchy(result.hierarchy);
            }
            
            const mainRule = this.rules.find(r => r.id === result.ruleId);
            if (mainRule) {
              mainRule.clients = this.aggregateClientHierarchy(result.hierarchy);
            }
          });
        },
        error: (error) => {
          console.error('Error loading hierarchy for search results:', error);
        }
      });
    }
  }

  private performLocalSearch(searchTerm: string): void {
    const term = searchTerm.toLowerCase().trim();
    this.filteredRules = this.rules.filter(rule => {
      return rule.name.toLowerCase().includes(term) ||
             rule.tags.some(tag => tag.toLowerCase().includes(term)) ||
             rule.actions.some(action => 
               action.type.toLowerCase().includes(term) ||
               action.action.toLowerCase().includes(term) ||
               action.value.toLowerCase().includes(term) ||
               action.target.toLowerCase().includes(term)
             ) ||
             rule.conditions.some(condition => 
               condition.type.toLowerCase().includes(term) ||
               condition.operator.toLowerCase().includes(term) ||
               condition.value.toLowerCase().includes(term)
             ) ||
             this.rawDataCache.get(rule.id)?.toLowerCase().includes(term) ||
             this.summaryCache.get(rule.id)?.toLowerCase().includes(term);
    });
  }

  clearSearch(): void {
    if (this.searchTerm.trim() && this.filteredRules.length > 0) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: 'Effacer la recherche',
          message: `Vous avez actuellement ${this.filteredRules.length} résultat(s) de recherche pour "${this.searchTerm}". Voulez-vous effacer la recherche et revenir à la liste complète ?`,
          icon: 'clear'
        },
        disableClose: true,
        panelClass: 'confirmation-dialog-panel'
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.performClearSearch();
        }
      });
    } else {
      this.performClearSearch();
    }
  }

  private performClearSearch(): void {
    this.searchTerm = '';
    this.currentPage = 1;
    this.isSearchMode = false;
    this.isSearching = false;
    
    // Clear the filtered rules to force a reload
    this.filteredRules = [];
    this.loadRules();
  }

  // === RULE MANAGEMENT ===

  openRuleFormDialog(rule?: any): void {
    if (this.isSearching) {
      this.snackBar.open('Veuillez attendre la fin de la recherche en cours', 'Fermer', {
        duration: 3000,
        horizontalPosition: 'right',
        verticalPosition: 'top'
      });
      return;
    }

    const dialogRef = this.dialog.open(RuleFormComponent, {
      width: '95vw',
      maxWidth: '800px',
      panelClass: 'custom-rule-form-dialog',
      data: rule ? { rule: rule } : {},
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.action === 'saved') {
        this.snackBar.open('Règle sauvegardée avec succès!', 'Fermer', {
          duration: 3000,
          horizontalPosition: 'right',
          verticalPosition: 'top'
        });
        this.loadRules();
      }
    });
  }

  editRuleById(ruleId: string): void {
    if (this.isDeleting.has(ruleId)) {
      this.snackBar.open('Impossible de modifier une règle en cours de suppression', 'Fermer', {
        duration: 3000,
        horizontalPosition: 'right',
        verticalPosition: 'top'
      });
      return;
    }

    const rawData = this.rawDataCache.get(ruleId);
    
    if (rawData) {
      try {
        const ruleForEdit = {
          id: ruleId,
          isEdit: true,
          RawData: rawData,
          ...JSON.parse(rawData)
        };

        const dialogRef = this.dialog.open(RuleFormComponent, {
          width: '95vw',
          maxWidth: '800px',
          panelClass: 'custom-rule-form-dialog',
          data: { rule: ruleForEdit },
          disableClose: true
        });

        dialogRef.afterClosed().subscribe(result => {
          if (result && result.action === 'saved') {
            this.snackBar.open('Règle mise à jour avec succès', 'Fermer', {
              duration: 3000,
              horizontalPosition: 'right',
              verticalPosition: 'top'
            });
            this.loadRules();
          }
        });
      } catch (error) {
        console.error('Error parsing raw data for edit:', error);
        this.showErrorDialog('Erreur lors de l\'analyse des données de la règle. Les données pourraient être corrompues.');
      }
    } else {
      this.rulesApiService.getRuleComprehensiveById(ruleId).subscribe({
        next: (ruleDto) => {
          if (ruleDto && ruleDto.RawData) {
            this.rawDataCache.set(ruleId, ruleDto.RawData);
            if (ruleDto.RuleSummary) {
              this.summaryCache.set(ruleId, ruleDto.RuleSummary);
            }
            this.editRuleById(ruleId);
          } else {
            this.showErrorDialog('Impossible de charger les données de la règle pour la modification.');
          }
        },
        error: (error) => {
          console.error('Error fetching rule for edit:', error);
          this.showErrorDialog('Erreur lors du chargement de la règle. Veuillez réessayer.');
        }
      });
    }
  }

  deleteRule(ruleId: string): void {
    if (this.isDeleting.has(ruleId)) {
      return;
    }

    const rule = this.filteredRules.find(r => r.id === ruleId);
    const ruleName = rule?.name || 'cette règle';
    
    let confirmationMessage = `Voulez-vous vraiment supprimer la règle "${ruleName}" ?`;
    
    if (rule) {
      const details = [];
      if (rule.totalApplications > 0) {
        details.push(`${rule.totalApplications} application(s) enregistrée(s)`);
      }
      if (rule.clients.length > 0) {
        const totalControllers = this.getTotalControllersForRule(rule);
        details.push(`${rule.clients.length} client(s) et ${totalControllers} contrôleur(s) associé(s)`);
      }
      if (rule.actions.length > 0) {
        details.push(`${rule.actions.length} action(s) configurée(s)`);
      }
      
      if (details.length > 0) {
        confirmationMessage += `\n\nCette règle contient :\n• ${details.join('\n• ')}`;
      }
      
      confirmationMessage += '\n\nCette action est irréversible et supprimera définitivement toutes les données associées.';
    }

    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Supprimer la règle',
        message: confirmationMessage,
        icon: 'delete_forever'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.performDeleteRule(ruleId);
      }
    });
  }

  private performDeleteRule(ruleId: string): void {
    this.isDeleting.add(ruleId);

    this.rulesApiService.delete(ruleId).subscribe({
      next: (success) => {
        this.isDeleting.delete(ruleId);
        
        if (success) {
          this.snackBar.open('Règle supprimée avec succès', 'Fermer', {
            duration: 3000,
            horizontalPosition: 'right',
            verticalPosition: 'top'
          });
          
          this.rawDataCache.delete(ruleId);
          this.summaryCache.delete(ruleId);
          
          if (this.expandedRuleId === ruleId) {
            this.expandedRuleId = null;
            this.selectedControllerId = null;
          }
          
          this.loadRules();
        } else {
          this.showErrorDialog('Échec de la suppression de la règle. Veuillez réessayer.');
        }
      },
      error: (error) => {
        this.isDeleting.delete(ruleId);
        console.error('Error deleting rule:', error);
        this.showErrorDialog('Erreur lors de la suppression de la règle. Veuillez vérifier votre connexion et réessayer.');
      }
    });
  }

  // === UI STATE MANAGEMENT ===

  toggleRuleDetails(ruleId: string): void {
    this.expandedRuleId = this.expandedRuleId === ruleId ? null : ruleId;
    this.selectedControllerId = null;
    this.activeTab = 'summary';

    if (this.expandedRuleId) {
      const rule = this.filteredRules.find(r => r.id === this.expandedRuleId);
      if (rule && rule.clients.length > 0) {
        this.populateControllerApplicationsAndPerformance(rule);
      }
    }
  }

  setActiveTab(tab: 'hierarchy' | 'rawdata' | 'summary'): void {
    this.activeTab = tab;
  }

  selectController(controllerId: string): void {
    this.selectedControllerId = this.selectedControllerId === controllerId ? null : controllerId;
  }

  // === PAGINATION ===

  goToPage(page: number): void {
    if (page >= 1 && page <= this.pageCount && page !== this.currentPage && !this.hasPendingOperations()) {
      console.log(`📄 Navigating from page ${this.currentPage} to page ${page}`);
      this.currentPage = page;
      
      if (this.isSearchMode && this.searchTerm.trim()) {
        this.performSearch(this.searchTerm);
      } else {
        this.loadRules();
      }
    }
  }

  nextPage(): void {
    if (this.currentPage < this.pageCount && !this.hasPendingOperations()) {
      console.log(`📄 Moving to next page: ${this.currentPage + 1}`);
      this.currentPage++;
      
      if (this.isSearchMode && this.searchTerm.trim()) {
        this.performSearch(this.searchTerm);
      } else {
        this.loadRules();
      }
    }
  }

  previousPage(): void {
    if (this.currentPage > 1 && !this.hasPendingOperations()) {
      console.log(`📄 Moving to previous page: ${this.currentPage - 1}`);
      this.currentPage--;
      
      if (this.isSearchMode && this.searchTerm.trim()) {
        this.performSearch(this.searchTerm);
      } else {
        this.loadRules();
      }
    }
  }

  getPagesArray(): number[] {
    if (this.pageCount <= 0) {
      return [];
    }
    return Array.from({ length: this.pageCount }, (_, i) => i + 1);
  }

  shouldShowPagination(): boolean {
    return this.totalElements > this.pageSize || this.pageCount > 1;
  }

  // === DATA UTILITY METHODS ===

  private mapDtoToRuleContent(dto: RuleComprehensive): RuleContent {
    let parsedRawData: RawDataBackendStructure | null = null;
    try {
      if (dto.RawData) {
        parsedRawData = JSON.parse(dto.RawData) as RawDataBackendStructure;
      }
    } catch (e) {
      console.error('Error parsing RawData for rule:', dto.RuleId, e);
    }

    // Extract from raw data if available, fallback to model if not
    const priority: number = parsedRawData?.priority ?? dto.Priority;
    const status: string = parsedRawData?.enabled == true ? 'active' : 'inactive';

    const conditions: Condition[] = [];
    if (parsedRawData && parsedRawData.conditions?.groups?.length > 0) {
      parsedRawData.conditions.groups[0].conditions.forEach((cond, index) => {
        conditions.push({
          id: index + 1,
          type: cond.type,
          operator: cond.operator,
          value: cond.value,
          deviceId: cond.device,
          propertyName: cond.key
        });
      });
    }

    const actions: Action[] = [];
    if (parsedRawData && parsedRawData.actions?.length > 0) {
      parsedRawData.actions.forEach((act, index) => {
        let actionValue = '';
        let actionName = act.type;
        if (act.payload) {
          const payloadKeys = Object.keys(act.payload);
          if (payloadKeys.length > 0) {
            actionName = payloadKeys[0];
            actionValue = String(act.payload[payloadKeys[0]]);
          }
        }

        actions.push({
          id: index + 1,
          type: act.type,
          action: actionName,
          value: actionValue,
          target: act.topic
        });
      });
    }

    const name: string = parsedRawData?.rule_name || `Rule ${dto.RuleId.substring(0, 8)}`;
    const tagsArray: string[] = dto.TagsString ? dto.TagsString.split(', ').filter(t => t.length > 0) : [];
    const tagStatus: { [key: string]: string } = {};
    tagsArray.forEach(tag => tagStatus[tag] = 'active');

    return {
      id: dto.RuleId,
      name: name,
      priority: priority,
      status: status == 'active' ? 'active' : 'inactive',
      tags: tagsArray,
      conditions: conditions,
      actions: actions,
      tagStatus: tagStatus,
      totalApplications: dto.TotalApplications,
      lastTriggered: dto.LastTriggered || '',
      clients: []
    };
  }

  private aggregateClientHierarchy(hierarchy: RuleClientHierarchy[]): ClientForRule[] {
    const clientsMap = new Map<string, ClientForRule>();

    hierarchy.forEach(item => {
      if (!clientsMap.has(item.ClientId)) {
        clientsMap.set(item.ClientId, {
          id: item.ClientId,
          name: item.ClientRC || item.ClientSIREN || 'Unknown Client',
          email: '',
          company: '',
          status: 'active',
          sites: []
        });
      }
      const client = clientsMap.get(item.ClientId)!;

      let site = client.sites.find(s => s.id === item.SiteId);
      if (!site) {
        site = {
          id: item.SiteId,
          name: item.SiteName,
          address: item.SiteAddress,
          clientId: item.ClientId,
          locations: []
        };
        client.sites.push(site);
      }

      let location = site.locations.find(l => l.id === item.LocalId);
      if (!location) {
        location = {
          id: item.LocalId,
          name: item.LocalName,
          type: 'room',
          controllers: []
        };
        site.locations.push(location);
      }

      let controller = location.controllers.find(c => c.id === item.SiteId);
      if (!controller) {
        controller = {
          id: item.SiteId,
          name: `Controller ${item.SiteId.substring(0, 4)}`,
          model: '',
          lastSeen: '',
          status: 'unknown',
          applications: [],
          performanceAnalytics: []
        };
        location.controllers.push(controller);
      }
    });

    return Array.from(clientsMap.values());
  }

  private populateControllerApplicationsAndPerformance(rule: RuleContent): void {
    const allControllersInRule: { controller: ControllerForRule, localId: string }[] = [];
    rule.clients.forEach(client => {
      client.sites.forEach(site => {
        site.locations.forEach(location => {
          location.controllers.forEach(controller => {
            allControllersInRule.push({ controller, localId: location.id });
          });
        });
      });
    });

    const observables = allControllersInRule.map(({ controller, localId }) => {
      const recentApps$ = this.rulesApiService.getRuleRecentApplications(rule.id, 5);
      const performance$ = this.rulesApiService.getRulePerformance(rule.id, 7);

      return forkJoin({
        apps: recentApps$,
        performance: performance$
      }).pipe(
        map(data => ({
          controllerId: controller.id,
          localId: localId,
          apps: data.apps,
          performance: data.performance
        }))
      );
    });

    if (observables.length > 0) {
      forkJoin(observables).subscribe({
        next: (results) => {
          results.forEach(result => {
            for (const client of rule.clients) {
              for (const site of client.sites) {
                for (const location of site.locations) {
                  const targetController = location.controllers.find(c => c.id === result.controllerId);
                  if (targetController) {
                    targetController.applications = result.apps
                      .filter(app => app.TransactionId === targetController.id)
                      .map(app => ({
                        timestamp: app.ApplicationTimestamp,
                        success: app.ApplicationSuccess,
                        executionTime: 0
                      }));

                    targetController.performanceAnalytics = result.performance
                      .filter(perf => perf.LocalId === result.localId);
                    break;
                  }
                }
              }
            }
          });
        },
        error: (error) => console.error('Error populating controller applications and performance:', error)
      });
    }
  }

  // === SUMMARY FUNCTIONALITY ===

  getRuleSummary(ruleId: string): string {
    return this.summaryCache.get(ruleId) || 'Résumé non disponible';
  }

  hasRuleSummary(ruleId: string): boolean {
    const summary = this.summaryCache.get(ruleId);
    return summary != null && summary.trim().length > 0 && summary !== 'Résumé non disponible';
  }

 
  // === RAW DATA FUNCTIONALITY ===

  formatRawData(ruleId: string): string {
    const rawData = this.rawDataCache.get(ruleId);
    if (!rawData) {
      return '{\n  "error": "Données brutes non disponibles"\n}';
    }

    try {
      const parsed = JSON.parse(rawData);
      return JSON.stringify(parsed, null, 2);
    } catch (e) {
      return rawData;
    }
  }

  copyRawData(ruleId: string): void {
    const rawData = this.formatRawData(ruleId);
    navigator.clipboard.writeText(rawData).then(() => {
      this.snackBar.open('Données copiées dans le presse-papiers', 'Fermer', {
        duration: 3000,
        horizontalPosition: 'right',
        verticalPosition: 'top'
      });
    }).catch(err => {
      console.error('Error copying to clipboard:', err);
      this.snackBar.open('Erreur lors de la copie', 'Fermer', {
        duration: 3000,
        horizontalPosition: 'right',
        verticalPosition: 'top'
      });
    });
  }

  downloadRawData(rule: RuleContent): void {
    const rawData = this.formatRawData(rule.id);
    const blob = new Blob([rawData], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `rule-${rule.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}-${rule.id.substring(0, 8)}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    this.snackBar.open('Téléchargement démarré', 'Fermer', {
      duration: 3000,
      horizontalPosition: 'right',
      verticalPosition: 'top'
    });
  }

  // === UTILITY METHODS ===

  isSimpleRule(rule: RuleContent): boolean {
    const hasMinimalData = (
      rule.clients.length === 0 &&
      rule.tags.length === 0 &&
      rule.totalApplications === 0 &&
      (!rule.conditions || rule.conditions.length <= 1) &&
      (!rule.actions || rule.actions.length <= 1)
    );

    const isNewRule = (
      !rule.lastTriggered || rule.lastTriggered === '' || rule.lastTriggered === 'Jamais'
    );

    return hasMinimalData && isNewRule;
  }

  isRuleDeleting(ruleId: string): boolean {
    return this.isDeleting.has(ruleId);
  }

  getLoadingState(): boolean {
    return this.isLoading;
  }

  hasPendingOperations(): boolean {
    return this.isLoading || this.isSearching || this.isDeleting.size > 0;
  }

  getTotalClientsForRule(rule: RuleContent): number {
    return rule.clients.length;
  }

  getTotalControllersForRule(rule: RuleContent): number {
    return rule.clients.reduce((total, client) => {
      return total + client.sites.reduce((siteAcc, site) => {
        return siteAcc + site.locations.reduce((locAcc, location) => {
          return locAcc + location.controllers.length;
        }, 0);
      }, 0);
    }, 0);
  }

  getControllerStatusIcon(status: string): string {
    switch (status) {
      case 'online': return 'check_circle';
      case 'offline': return 'error';
      case 'warning': return 'warning';
      default: return 'help';
    }
  }

  getControllerStatusClass(status: string): string {
    switch (status) {
      case 'online': return 'status-online';
      case 'offline': return 'status-offline';
      case 'warning': return 'status-warning';
      default: return 'status-unknown';
    }
  }

  formatTimestamp(timestamp: string): string {
    if (!timestamp) return 'N/A';
    const date = new Date(timestamp);
    return date.toLocaleString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  refreshData(): void {
    if (this.isLoading || this.isSearching || this.isDeleting.size > 0) {
      this.snackBar.open('Une opération est en cours, veuillez patienter', 'Fermer', {
        duration: 3000,
        horizontalPosition: 'right',
        verticalPosition: 'top'
      });
      return;
    }

    this.confirmRefresh();
  }

  private confirmRefresh(): void {
    if (this.expandedRuleId || this.isSearching) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: 'Actualiser les données',
          message: 'Actualiser les données fermera les détails ouverts et réinitialisera les filtres. Continuer ?',
          icon: 'refresh'
        },
        disableClose: true,
        panelClass: 'confirmation-dialog-panel'
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.performRefresh();
        }
      });
    } else {
      this.performRefresh();
    }
  }

  private performRefresh(): void {
    // Reset to first page and clear any search
    this.currentPage = 1;
    this.searchTerm = '';
    this.isSearchMode = false;
    this.expandedRuleId = null;
    this.selectedControllerId = null;
    
    console.log('🔄 Refreshing data - resetting to page 1');
    this.loadRules();
  }

  private showErrorDialog(message: string): void {
    this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Erreur',
        message: message,
        icon: 'error'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    });
  }
}