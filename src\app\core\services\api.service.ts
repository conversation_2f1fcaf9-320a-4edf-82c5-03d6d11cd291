import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { IApiService } from './i-api.service';
import { Lister, Page } from '../models/util/page';

@Injectable()
export class ApiService<T> implements IApiService<T> {
  baseUrl = environment.host.endsWith('/')
    ? environment.host + 'api/'
    : environment.host + '/api/';
  resource = '';
  httpOptions = {
    headers: new HttpHeaders({ 'Content-Type': 'application/json' }),
  };

  constructor(public http: HttpClient) {}

  setEndpoint(resource: string) {
    this.resource = resource;
  }

  // Fixed URL restructuring method
  restructureUrl(url: string): string {
    if (url == undefined || url == null) return url;

    // Ensure the protocol (http or https) is included
    if (!/^https?:\/\//i.test(url)) {
      url = 'https://' + url;
    }

    // Don't replace slashes after protocol - only replace multiple consecutive slashes in path
    const parts = url.split('://');
    if (parts.length === 2) {
      const protocol = parts[0];
      const rest = parts[1];
      // Only fix multiple slashes in the path part, not in the domain:port part
      const pathIndex = rest.indexOf('/');
      if (pathIndex > -1) {
        const domainPart = rest.substring(0, pathIndex);
        const pathPart = rest.substring(pathIndex);
        const fixedPath = pathPart.replace(/\/{2,}/g, '/');
        url = protocol + '://' + domainPart + fixedPath;
      }
    }

    // Ensure trailing slash
    if (!url.endsWith('/')) {
      url = url + '/';
    }

    return url;
  }

  // Simplified getFullUrl method
  getFullUrl(endpoint?: string): string {
    let url: string;

    if (endpoint) {
      // Ensure endpoint starts with /
      const cleanEndpoint = endpoint.startsWith('/')
        ? endpoint
        : '/' + endpoint;
      url = `${this.baseUrl}${this.resource}${cleanEndpoint}`;
    } else {
      url = `${this.baseUrl}${this.resource}`;
    }

    // Clean up the URL
    url = this.restructureUrl(url);

    // Fix the https:/ issue (should be https://)
    url = url.replace(/^https?:\/(?!\/)/, (match) => match + '/');

    return url;
  }

  getResourceUrl(resource: string) {
    return `${this.baseUrl}${resource}`;
  }

  get(endpoint: string) {
    return this.http.get<any>(this.getFullUrl(endpoint)).pipe();
  }

  getById(id: string): Observable<T> {
    return this.http.get<T>(this.getFullUrl(`/${id}`)).pipe();
  }

  getOne(endpoint: string): Observable<T> {
    return this.http.get<T>(this.getFullUrl(endpoint)).pipe();
  }

  getAll(endpoint?: string): Observable<T[]> {
    return this.http.get<T[]>(this.getFullUrl(endpoint)).pipe();
  }

  create(data: any): Observable<T> {
    return this.post(null, data);
  }

  update(data: any): Observable<T> {
    return this.put('', data);
  }

  post(endpoint: any, data: any): Observable<T> {
    return this.http
      .post<T>(this.getFullUrl(endpoint), data, this.httpOptions)
      .pipe();
  }

  put(endpoint: string, data: any): Observable<T> {
    return this.http.put<T>(
      this.getFullUrl(endpoint),
      data,
      this.httpOptions
    );
  }

  delete(id: string | undefined): Observable<{ success: boolean }> {
    return this.http.delete<{ success: boolean }>(
      this.getFullUrl() + `?id=${id}`
    );
  }

  gatePage(lister: Lister): Observable<Page<T>> {
    return this.http
      .post<Page<T>>(this.getFullUrl() + 'search', lister, this.httpOptions)
      .pipe();
  }

  count(endpoint?: string) {
    return this.http.get<number>(this.getFullUrl(endpoint) + 'count').pipe();
  }

  generateReference() {
    return this.http
      .get<string>(this.getFullUrl() + 'generateReference')
      .pipe();
  }

  getDescriptor(descriptors: string) {
    return this.http
      .get<any>(`${this.getFullUrl()}descriptor?param=${descriptors}`)
      .pipe();
  }

  getDescriptorByResource(resource: string, descriptors: string) {
    return this.http
      .get<any>(
        `${this.getResourceUrl(resource)}descriptor?param=${descriptors}`
      )
      .pipe();
  }
}

@Injectable({
  providedIn: 'root',
})
export class NotificationService {
  readonly audio: HTMLAudioElement;
  private audioEnabled = false;

  constructor() {
    // One-time listener to unlock audio on first click anywhere
    document.addEventListener(
      'click',
      () => {
        this.audio.load();
        this.audioEnabled = true;
        console.log('Audio preloaded and enabled Oualid ABDELLAOUI');
      },
      { once: true }
    );

    this.audio = new Audio('assets/soundeffects/notication.mp3');
  }

  notify() {
    // Play sound if unlocked
    if (this.audioEnabled) {
      this.audio.play().catch((err) => {
        console.warn('Audio play blocked:', err);
      });
    }
  }
}