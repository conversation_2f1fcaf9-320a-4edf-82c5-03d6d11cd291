import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import {
  FormsModule,
  ReactiveFormsModule,
  FormGroup,
  FormBuilder,
  Validators,
} from '@angular/forms';
import {
  trigger,
  state,
  style,
  transition,
  animate,
} from '@angular/animations';
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { Client } from '@app/core/models/client';
import { Organisation } from '@app/core/models/organisation';
import { OrganisationApiService } from '@app/core/services/administrative/organisation.service';
import { id } from '@swimlane/ngx-charts';
import { FilterParam, Lister, Pagination } from '@app/core/models/util/page';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { SuccessComponent } from "../../../shared/components/success/success/success.component";
import { FormaValidationService } from '@app/shared/services/forma-validation.service';
import { formElements } from './form-element';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { NgToastComponent, NgToastService, TOAST_POSITIONS } from 'ng-angular-popup';
import { MatIconModule } from '@angular/material/icon';
import { ClientFormComponent } from '../client-form/client-form.component';
import { MatButtonModule } from '@angular/material/button';
import { ClientFormService } from '@app/shared/services/client-form.service';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';

// Define the interface for the API request
@Component({
  selector: 'app-organisation-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    GenericTableComponent,
    MatPaginatorModule,
    SuccessComponent,
    MatDialogModule,
    MatButtonModule,
    NgToastComponent,
    MatIconModule,
    NgxUiLoaderModule
  ],
  templateUrl: './organisation-management.component.html',
  styleUrls: ['./organisation-management.component.css'],
  animations: [
    trigger('fadeIn', [
      state('void', style({ opacity: 0 })),
      transition(':enter', [animate('300ms ease-out', style({ opacity: 1 }))]),
    ]),
  ],
})
export class OrganisationManagementComponent implements OnInit {
  TOAST_POSITIONS = TOAST_POSITIONS;
  clientData: Client = new Client();
  @ViewChild('SuccessComponent') successComponent!: SuccessComponent;
  clients: Client[] = [];
  filteredClients: Client[] = [];
  showErrorMessages: string[] = [];
  organisations: Organisation[] = [];
  pageSize: number = 5;
  currentPage: number = 0;
  totalClients: number = 0;
  uploadedLogo: File | undefined;
  showMoreFields: boolean = false;
  base64Image: string = "";

  constructor(
    readonly clientApiService: ClientApiService,
    readonly organisationApiService: OrganisationApiService,
    readonly fb: FormBuilder,
    readonly router: Router,
    readonly route: ActivatedRoute,
    private formValidationService: FormaValidationService,
    private clientFormService: ClientFormService,
    private dialog: MatDialog,
    private toast: NgToastService,
    private ngxUiLoaderService: NgxUiLoaderService
  ) { }

  // Table configuration for clients
  headers: string[] = [
    'Raison Sociale',
    'Email',
    'Téléphone',
    'Organisation',
    'Équipements',
    'Nombre Locaux',
    'Statut',
  ];

  keys: string[] = [
    'Name',
    'ContactEmail',
    'PhoneNumber',
    'ContactAddress',
    'RC',
    'IF',
    'ClientStatus'
  ];

  isLoading: boolean = false;
  searchTerm: string = "";
  showCreateForm: boolean = false;


  ngOnInit(): void {
    this.loadOrganisations();
    this.loadClients();
  }

  loadOrganisations(): void {
    this.isLoading = true;
    this.organisationApiService.getAll().subscribe({
      next: (organisations: Organisation[]) => {
        this.organisations = organisations;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading organisations:', error);
        this.isLoading = false;
      },
    });
  }

  loadClients(): void {
    this.ngxUiLoaderService.start()
    const pagination: Pagination = {
      currentPage: this.currentPage,
      pageSize: this.pageSize
    };

    const request: Lister = {
      pagination: pagination
    };

    const filterParams: FilterParam[] = [];

    if (this.searchTerm) {
      console.log(this.searchTerm);
      this.keys.forEach(k => {
        filterParams.push({
          column: k,
          op: 'contains',
          value: this.searchTerm,
          andOr: "OR"
        })
      });
      request.filterParams = filterParams
    }


    this.isLoading = true;
    this.clientApiService.gatePage(request).subscribe({
      next: (result: any) => {
        console.log("gate page data", result);
        // this.filteredClients = clients.Content ?? [];
        this.clients = result.Content ?? [];
        // this.filterClients();
        this.isLoading = false;
        this.ngxUiLoaderService.stop();
      },
      error: (error) => {
        console.error('Error loading paginated clients:', error);
        this.isLoading = false;
        this.ngxUiLoaderService.stop();
      },
    });
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadClients();
  }

  filterClients(): void {
    this.loadClients();
    // if (!this.searchTerm) {
    //   this.filteredClients = [...this.clients];
    //   return;
    // }

    // const lowerCaseSearchTerm = this.searchTerm.toLowerCase();
    // this.filteredClients = this.clients.filter((client) => {
    //   return (
    //     (client.Name && client.Name.toLowerCase().includes(lowerCaseSearchTerm)) ||
    //     (client.PhoneNumber && client.PhoneNumber.toLowerCase().includes(lowerCaseSearchTerm)) ||
    //     (client.Organisation?.Nom && client.Organisation.Nom.toLowerCase().includes(lowerCaseSearchTerm)) ||
    //     (client.Address && client.Address.toLowerCase().includes(lowerCaseSearchTerm)) ||
    //     (client.City && client.City.toLowerCase().includes(lowerCaseSearchTerm)) ||
    //     (client.Region && client.Region.toLowerCase().includes(lowerCaseSearchTerm))
    //   );
    // });
  }

  onSearchChange(): void {
    this.filterClients();
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.filterClients();
  }

  showClientForm(): void {
    const dialogRef = this.clientFormService.openCreateClientDialog(this.organisations)
      .then(result => {
        this.viewClientDetails(result);
      });
  }

  handleAction(event: { action: string; row: Client }): void {
    switch (event.action) {
      case 'view':
        this.viewClientDetails(event.row);
        break;
      case 'edit':
        this.editClient(event.row);
        break;
      case 'delete':
        this.deleteClient(event.row);
        break;
      // case 'toggle':
      //   this.toggleClientActivation(event.row);
      //   break;
      default:
        console.warn('Unknown action:', event.action);
    }
  }

  viewClientDetails(client: Client | undefined): void {
    this.router.navigate(['organisation-details/', client?.Id]);
  }

  editClient(client: Client): void {
    const dialogRef = this.clientFormService.openEditClientDialog(client, this.organisations)
      .then(result => {
        if (result) {
          this.loadClients();
        }
      });
  }

  deleteClient(client: Client): void {
    if (!client.Id) {
      this.showError("Erreur lors de la suppression de client, Essayer ultérieurement", "Erreur");
      return;
    }

    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirmation de suppression',
        message: `Êtes-vous sûr de vouloir supprimer définitivement le client ${client.Name} ?\n\n` +
          `Cette action supprimera toutes les données associées et est irréversible.`,
        icon: 'warning'
      }
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.isLoading = true;

        this.clientApiService.delete(client.Id).subscribe({
          next: () => {
            this.clients = this.clients.filter(c => c.Id !== client.Id);
            this.filteredClients = this.filteredClients.filter(c => c.Id !== client.Id);
            this.totalClients--;

            this.showSuccess("Le Client a été suprimé avec succes", "Information");
          },
          error: (error) => {
            this.showError("Erreur lors de la suppression de client, Essayer ultérieurement", "Erreur");
          },
          complete: () => {
            this.isLoading = false;
          }
        });
      }
    });
  }

  onFileSelected(event: any): void {
    const file: File = event.target.files[0];
    if (file) {
      this.uploadedLogo = file;
      console.log('File selected:', file.name);

      const reader = new FileReader();
      reader.onload = () => {
        var base64String = reader.result as string;

        this.base64Image = base64String.split(",")[1];
        console.log('Base64 string:', this.base64Image);
      };
      reader.onerror = (error) => {
        console.error('Error reading file:', error);
      };

      reader.readAsDataURL(file);
    } else {
      this.uploadedLogo = undefined;
    }
  }

  openDialog() {
    this.dialog.open(ClientFormComponent, {
      panelClass: 'custom-dialog',
      position: {
        top: '50%',
        bottom: '50%',
        left: '50%',
        right: '50%'
      },
      disableClose: true
    });
  }

  public showSuccess(message: string, title: string) {
    this.toast.info(message, title, 3000, false);
  }

  public showError(message: string, title: string) {
    this.toast.warning(message, title, 3000, false);
  }
}