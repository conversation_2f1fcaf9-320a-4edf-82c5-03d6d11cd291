// import { Component } from '@angular/core';
// import { FormBuilder, FormGroup, Validators } from '@angular/forms';
// import { AuthService } from '../../../core/services/auth.service';
// import { Router } from '@angular/router';
// import { trigger, transition, style, animate } from '@angular/animations';
// import { CommonModule } from '@angular/common';
// import { ReactiveFormsModule } from '@angular/forms';
// import { RouterModule } from '@angular/router';

// // PrimeNG Imports
// import { InputTextModule } from 'primeng/inputtext';
// import { PasswordModule } from 'primeng/password';
// import { ButtonModule } from 'primeng/button';
// import { CheckboxModule } from 'primeng/checkbox';
// import { CardModule } from 'primeng/card';
// import { RippleModule } from 'primeng/ripple';
// import { ToastModule } from 'primeng/toast';
// import { MessageService } from 'primeng/api';
// import { MatIconModule } from '@angular/material/icon';

// @Component({
//   selector: 'app-login',
//   templateUrl: './login.component.html',
//   styleUrls: ['./login.component.css'],
//   standalone: true,
//   imports: [
//     CommonModule,
//     ReactiveFormsModule,
//     RouterModule,
//     InputTextModule,
//     PasswordModule,
//     ButtonModule,
//     CheckboxModule,
//     CardModule,
//     RippleModule,
//     ToastModule,
//     MatIconModule
//   ],
//   providers: [MessageService],
//   animations: [
//     trigger('fadeIn', [
//       transition(':enter', [
//         style({ opacity: 0, transform: 'translateY(-20px)' }),
//         animate('600ms cubic-bezier(0.35, 0, 0.25, 1)', style({ opacity: 1, transform: 'translateY(0)' }))
//       ])
//     ]),
//     trigger('slideInRight', [
//       transition(':enter', [
//         style({ opacity: 0, transform: 'translateX(-20px)' }),
//         animate('400ms 200ms cubic-bezier(0.35, 0, 0.25, 1)', style({ opacity: 1, transform: 'translateX(0)' }))
//       ])
//     ])
//   ]
// })
// export class LoginComponent {
//   loginForm: FormGroup;
//   rememberMe: boolean = false;
//   // loading: boolean = false;

//   constructor(
//     private fb: FormBuilder,
//     private authService: AuthService,
//     private router: Router,
//     private messageService: MessageService
//   ) {
//     this.loginForm = this.fb.group({
//       userName: ['', Validators.required],
//       password: ['', Validators.required]
//     });
//   }

//   onSubmit(): void {
//     if (this.loginForm.invalid) {
//       this.messageService.add({
//         severity: 'error',
//         summary: 'Error',
//         detail: 'Please fill in all required fields'
//       });
//       return;
//     }

//     // this.loading = true;
//     const { userName, password } = this.loginForm.value;

//     this.authService.login(userName, password).subscribe({
//       next: () => {
//         this.messageService.add({
//           severity: 'success',
//           summary: 'Success',
//           detail: 'Login successful'
//         });
//         setTimeout(() => {
//           this.router.navigate(['/organisations']);
//         }, 1000);
//       },
//       error: (err) => {
//         // this.loading = false;
//         this.messageService.add({
//           severity: 'error',
//           summary: 'Error',
//           detail: err.error || 'Invalid credentials'
//         });
//       }
//     });
//   }
// }
import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AuthService } from '../../../core/services/auth.service';
import { Router } from '@angular/router';
import { trigger, transition, style, animate } from '@angular/animations';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

// PrimeNG Imports
import { InputTextModule } from 'primeng/inputtext';
import { PasswordModule } from 'primeng/password';
import { ButtonModule } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { CardModule } from 'primeng/card';
import { RippleModule } from 'primeng/ripple';
import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    InputTextModule,
    PasswordModule,
    ButtonModule,
    CheckboxModule,
    CardModule,
    RippleModule,
    ToastModule,
    MatIconModule,
    MatIconModule,
  ],
  providers: [MessageService],
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(-20px)' }),
        animate(
          '600ms cubic-bezier(0.35, 0, 0.25, 1)',
          style({ opacity: 1, transform: 'translateY(0)' })
        ),
      ]),
    ]),
    trigger('slideInRight', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateX(-20px)' }),
        animate(
          '400ms 200ms cubic-bezier(0.35, 0, 0.25, 1)',
          style({ opacity: 1, transform: 'translateX(0)' })
        ),
      ]),
    ]),
  ],
})
export class LoginComponent {
  loginForm: FormGroup;
  rememberMe: boolean = false;
  //loading: boolean = false;
  loginError: boolean = false;
  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private messageService: MessageService
  ) {
    this.loginForm = this.fb.group({
      userName: ['', Validators.required],
      password: ['', Validators.required],
    });
  }

 onSubmit(): void {
    if (this.loginForm.invalid) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Please fill in all required fields'
      });
      return;
    }

    //this.loading = true;
    this.loginError = false;
    const { userName, password } = this.loginForm.value;

    this.authService.login(userName, password).subscribe({
      next: () => {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Login successful'
        });
        setTimeout(() => {
          this.router.navigate(['/organisations']);
        }, 1000);
      },
      error: (err) => {
        //this.loading = false;
        this.loginError = true;
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: err.error || 'Invalid credentials'
        });
      }
    });
  }
}