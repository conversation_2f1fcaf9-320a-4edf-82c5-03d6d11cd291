import { Injectable } from '@angular/core';
import mqtt from 'mqtt';

@Injectable({
  providedIn: 'root'
})
export class PublishRuleService {
  private client: any;

  constructor() {
    this.client = mqtt.connect('ws://146.59.198.243:8083');

    this.client.on('connect', () => {
      console.log('MQTT connected');
    });

    this.client.on('error', (err: any) => {
      console.error('MQTT error:', err);
      this.client.end();
    });
  }

  /**
   * Publish a message to topic idlocal/idcontroller/rule
   * @param idLocal ID of the local
   * @param idController ID of the controller
   * @param payload JSON object to send
   */
  publishRule(idRule: string,idLocal: string, idController: string, payload: Record<string, any>): void {
    const topic = `${idLocal}/${idController}/rule`;
    const message = JSON.stringify(
      
      {
        id:idRule,
        rawData:payload});


    this.client.publish(topic, message, { qos: 1 }, (err: any) => {
      if (err) {
        console.error('Failed to publish:', err);
      } else {
        console.log(`Message published to ${topic}:`, message);
      }
    });
  }
}