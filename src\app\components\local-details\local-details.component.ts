import { Component, HostListener, Input } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Local } from '@app/core/models/local';
import { Site } from '@app/core/models/site';
import { LocalApiService } from '@app/core/services/administrative/local.service';
import { SiteApiService } from '@app/core/services/administrative/site.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTabsModule } from '@angular/material/tabs';
import { LocalRuleComponent } from '@app/pages/site-details/local-rule/local-rule.component';
import { AppliedRulesListComponent } from '@app/components/applied-rules-list/applied-rules-list.component'; // Import the new component
import { MatDialog } from '@angular/material/dialog';
import * as fabric from 'fabric';
import { TransactionApiService } from '@app/core/services/administrative/transaction.service';
import { Transaction } from '@app/core/models/transaction';
import { CapteurApiService } from '@app/core/services/administrative/capteur.service';

@Component({
  selector: 'app-local-details',
  standalone: true,
  imports: [
    CommonModule, 
    MatIconModule, 
    MatButtonModule, 
    MatTabsModule,
    AppliedRulesListComponent // Add to imports
  ],
  templateUrl: './local-details.component.html',
  styleUrls: ['./local-details.component.css']
})
export class LocalDetailsComponent {
  @Input() local?: Local;
  @Input() site?: Site;

  canvas!: fabric.Canvas;
  canvasInitialized = false;
  isLoading: boolean = true;
  localId: string = '';
  currentSite?: Site;

  capteurCount = 0;
  capteurNames: string[] = [];
  showCapteurNames = false;

  showSensorPopup: boolean = false;
  
  // Add tab management
  selectedTabIndex: number = 0;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly localService: LocalApiService,
    private readonly siteService: SiteApiService,
    private readonly snackBar: MatSnackBar,
    private dialog: MatDialog,
    private transactionService: TransactionApiService,
    private capteurService: CapteurApiService
  ) {}

  ngOnInit(): void {
    this.localId = this.route.snapshot.paramMap.get('id') || '';
    if (this.local) {
      this.loadSiteForLocal(this.local);
      this.loadSensorCountForLocal();
      this.loadCapteurCountAndNames();

      this.isLoading = false;
    } else if (this.localId) {
      this.loadLocalDetails();
    } else {
      this.isLoading = false;
      this.showErrorNotification('Aucun local trouvé');
    }
  }

  
  // Keep all existing methods...
  private loadSiteForLocal(local: Local): void {
    if (local.IdSite) {
      this.siteService.getById(local.IdSite).subscribe({
        next: (site) => {
          const transformedSite = {
            ...site,
            name: (site as any).Name ?? site.Name,
            adress: (site as any).Address ?? site.Address
          };
          this.currentSite = transformedSite;
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading site:', error);
          this.showErrorNotification('Erreur lors du chargement du site');
          this.isLoading = false;
        }
      });
    } else {
      this.isLoading = false;
    }
  }

  loadCapteurCountAndNames(): void {
    if (!this.local?.Id) return;
  
    this.transactionService.getAll().subscribe({
      next: (transactions) => {
        const localTransactions = transactions.filter(t => t.IdLocal === this.local?.Id);
        const capteurIds = [...new Set(localTransactions.map(t => t.IdCapteur))];
  
        this.capteurCount = capteurIds.length;
  
        this.capteurService.getAll().subscribe({
          next: (capteurs) => {
            this.capteurNames = capteurs
              .filter(c => capteurIds.includes(c.Id))
              .map(c => c.FriendlyName || c.TypeCapteur?.DisplayName || 'Capteur inconnu');
          },
          error: err => console.error('Erreur chargement capteurs:', err)
        });
      },
      error: err => console.error('Erreur chargement transactions:', err)
    });
  }
  ngAfterViewInit(): void {
    this.initializeCanvas();
    if (this.local?.Architecture2DImage) {
      this.initializeArchitecturePlan();
    }
  }

  updateToolbar(): void{}

  onCanvasMouseDown(event: any) {
    const pointer = this.canvas.getPointer(event.e);
    const objects = this.canvas.getObjects();

    const clickedObject = this.canvas.findTarget(event.e);
    if (clickedObject && clickedObject.get('type') === 'capteur') {
      const capteurData = clickedObject.get('capteurData');
    }
  }

  initializeCanvas(): void {
    this.canvas = new fabric.Canvas('canvas', {
      backgroundColor: '#FAFAFA',
      width: 900,
      height: 500,
      selection: true
    });

    this.canvasInitialized = true;
    this.canvas.on('selection:created', () => this.updateToolbar());
    this.canvas.on('selection:cleared', () => this.updateToolbar());
    this.canvas.on('mouse:down', (e) => this.onCanvasMouseDown(e));
  }

  private initializeArchitecturePlan(): void {
    if (this.local?.Architecture2DImage) {
      try {
        const jsonData = JSON.parse(this.local.Architecture2DImage);
        this.canvas.loadFromJSON(jsonData, () => {
          setTimeout(() => {
            this.fitCanvasContent();
            this.lockPlanAsBackground();
            this.canvas.renderAll();
            console.log('✅ Plan JSON imported, centered and resized');
          }, 100);
        });
      } catch (error) {
        console.error('Error loading architecture plan:', error);
        this.showErrorNotification('Erreur lors du chargement du plan d\'architecture');
      }
    }
  }

  private loadCapteursForLocal() {
    this.transactionService.getAll().subscribe({
      next: (transactions: Transaction[]) => {
        const localTransactions = transactions.filter(t => t.IdLocal === this.localId);
        const uniqueCapteurIds = [...new Set(localTransactions.map(t => t.IdCapteur))];
  
        this.capteurCount = uniqueCapteurIds.length;
  
        // Charger les noms
        this.capteurService.getAll().subscribe({
          next: (capteurs) => {
            this.capteurNames = capteurs
              .filter(c => uniqueCapteurIds.includes(c.Id))
              .map(c => c.FriendlyName || c.TypeCapteur?.DisplayName || 'Capteur inconnu');
          },
          error: err => console.error('Erreur chargement capteurs:', err)
        });
      },
      error: err => console.error('Erreur chargement transactions:', err)
    });
  }

  private loadSensorCountForLocal(): void {
    if (!this.local?.Id) return;
  
    this.transactionService.getAll().subscribe({
      next: (transactions: Transaction[]) => {
        const count = transactions.filter(t => t.IdLocal === this.local!.Id)
                                  .map(t => t.IdCapteur)
                                  .filter((id, index, self) => id && self.indexOf(id) === index)
                                  .length;
  
        this.local!.SensorsCount = count;
      },
      error: err => {
        console.error('Erreur lors du chargement des transactions pour le local:', err);
      }
    });
  }
  
  private getObjectsBounds(objects: fabric.Object[]): { width: number; height: number } {
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    objects.forEach(obj => {
      const bound = obj.getBoundingRect();
      minX = Math.min(minX, bound.left);
      minY = Math.min(minY, bound.top);
      maxX = Math.max(maxX, bound.left + bound.width);
      maxY = Math.max(maxY, bound.top + bound.height);
    });

    return {
      width: maxX - minX,
      height: maxY - minY
    };
  }

  private lockPlanAsBackground(): void {
    this.canvas.getObjects().forEach(obj => {
      obj.selectable = false;
      obj.hoverCursor = 'default';
    });
    this.canvas.renderAll();
  }

  loadLocalDetails(): void {
    this.isLoading = true;

    this.localService.getById(this.localId).subscribe({
      next: (local: Local) => {
        this.local = local;
        this.loadSensorCountForLocal();
        if (local.IdSite) {
          this.loadSiteForLocal(local);
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading local details:', error);
        this.showErrorNotification('Erreur lors du chargement du local');
        this.isLoading = false;
      }
    });
  }

  loadSiteDetails(siteId: string): void {
    this.siteService.getById(siteId).subscribe({
      next: (site: Site) => {
        this.site = site;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading site details:', error);
        this.isLoading = false;
      }
    });
  }

  goBack(): void {
    if (this.local?.IdSite) {
      this.router.navigate(['/site-locals', this.local.IdSite]);
    } else if (this.site?.Id) {
      this.router.navigate(['/site-locals', this.site.Id]);
    } else {
      this.router.navigate(['/accueil']);
    }
  }

  private showSuccessNotification(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 4000,
      panelClass: ['success-snackbar'],
      horizontalPosition: 'right',
      verticalPosition: 'top'
    });
  }

  onImageError(event: any): void {
    event.target.src = 'assets/images/default-image.jpg';
  }

  private showErrorNotification(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar'],
      horizontalPosition: 'right',
      verticalPosition: 'top'
    });
  }

  fitCanvasContent() {
    const objects = this.canvas.getObjects();
 
    if (objects.length === 0) {
      console.log('Aucun objet à redimensionner');
      return;
    }
 
    let minX = Infinity;
    let minY = Infinity;
    let maxX = -Infinity;
    let maxY = -Infinity;
 
    objects.forEach(obj => {
      const bounds = obj.getBoundingRect();
      minX = Math.min(minX, bounds.left);
      minY = Math.min(minY, bounds.top);
      maxX = Math.max(maxX, bounds.left + bounds.width);
      maxY = Math.max(maxY, bounds.top + bounds.height);
    });
 
    const contentWidth = maxX - minX;
    const contentHeight = maxY - minY;
    const contentCenterX = minX + contentWidth / 2;
    const contentCenterY = minY + contentHeight / 2;
 
    console.log('Dimensions du contenu:', {
      width: contentWidth,
      height: contentHeight,
      minX, minY, maxX, maxY
    });
 
    const canvasWidth = this.canvas.getWidth();
    const canvasHeight = this.canvas.getHeight();
    const canvasCenterX = canvasWidth / 2;
    const canvasCenterY = canvasHeight / 2;
 
    const margin = 50;
    const scaleX = (canvasWidth - margin * 2) / contentWidth;
    const scaleY = (canvasHeight - margin * 2) / contentHeight;
    const scale = Math.min(scaleX, scaleY, 1);
 
    console.log('Échelle calculée:', scale);
 
    const deltaX = canvasCenterX - contentCenterX;
    const deltaY = canvasCenterY - contentCenterY;
 
    objects.forEach(obj => {
      const currentLeft = obj.left || 0;
      const currentTop = obj.top || 0;
      const currentScaleX = obj.scaleX || 1;
      const currentScaleY = obj.scaleY || 1;
 
      const newLeft = (currentLeft - contentCenterX) * scale + canvasCenterX;
      const newTop = (currentTop - contentCenterY) * scale + canvasCenterY;
 
      obj.set({
        left: newLeft,
        top: newTop,
        scaleX: currentScaleX * scale,
        scaleY: currentScaleY * scale
      });
 
      obj.setCoords();
    });
 
    console.log(`✅ ${objects.length} objets repositionnés et redimensionnés`);
  }
 
  toggleSensorPopup(): void {
    this.showSensorPopup = !this.showSensorPopup;
  }
  
  closeSensorPopup(): void {
    this.showSensorPopup = false;
  }
  
  // Optional: Close popup on Escape key
  @HostListener('document:keydown.escape', ['$event'])
  onEscapeKey(event: KeyboardEvent): void {
    if (this.showSensorPopup) {
      this.closeSensorPopup();
    }
  }
  
}