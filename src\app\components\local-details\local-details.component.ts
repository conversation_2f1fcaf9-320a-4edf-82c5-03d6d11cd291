import { Component, Input } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Local } from '@app/core/models/local';
import { Site } from '@app/core/models/site';
import { LocalApiService } from '@app/core/services/administrative/local.service';
import { SiteApiService } from '@app/core/services/administrative/site.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';

@Component({
  selector: 'app-local-details',
  standalone: true,
  imports: [CommonModule, MatIconModule, MatButtonModule, NgxUiLoaderModule],
  templateUrl: './local-details.component.html',
  styleUrls: ['./local-details.component.css']
})
export class LocalDetailsComponent {
  @Input() local?: Local;
  @Input() site?: Site;

  isLoading: boolean = true;
  localId: string = '';
  currentSite?: Site;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly localService: LocalApiService,
    private readonly siteService: SiteApiService,
    private readonly snackBar: MatSnackBar,
    private ngxUiLoaderService: NgxUiLoaderService
  ) {}

  ngOnInit(): void {
    this.localId = this.route.snapshot.paramMap.get('id') || '';
    if (this.local) {

      this.loadSiteForLocal(this.local);
      this.isLoading = false;
    } else if (this.localId) {

      this.loadLocalDetails();
    } else {

      this.isLoading = false;
      this.showErrorNotification('Aucun local trouvé');
    }
  }

  private loadSiteForLocal(local: Local): void {
    if (local.IdSite) {
      this.siteService.getById(local.IdSite).subscribe({
        next: (site) => {
          // Cast the response to handle uppercase properties
          const transformedSite = {
            ...site,
            name: (site as any).Name ?? site.Name,
            adress: (site as any).Address ?? site.Address
          };
          this.currentSite = transformedSite;
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading site:', error);
          this.showErrorNotification('Erreur lors du chargement du site');
          this.isLoading = false;
        }
      });
    } else {
      this.isLoading = false;
    }
  }

  loadLocalDetails(): void {
    this.isLoading = true;
    
    this.localService.getById(this.localId).subscribe({
      next: (local: Local) => {
        this.local = local;
        if (local.IdSite) {
          this.loadSiteForLocal(local);
        }
        this.isLoading = false; 
      },
      error: (error) => {
        console.error('Error loading local details:', error);
        this.showErrorNotification('Erreur lors du chargement du local');
        this.isLoading = false;
      }
    });
  }

  loadSiteDetails(siteId: string): void {
    this.siteService.getById(siteId).subscribe({
      next: (site: Site) => {
        this.site = site;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading site details:', error);
        this.isLoading = false;
      }
    });
  }

  goBack(): void {
    if (this.local?.IdSite) {

      this.router.navigate(['/site-locals', this.local.IdSite]);
    } else if (this.site?.Id) {
      this.router.navigate(['/site-locals', this.site.Id]);
    } else {
      this.router.navigate(['/accueil']);
    }
  }

  private showSuccessNotification(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 4000,
      panelClass: ['success-snackbar'],
      horizontalPosition: 'right',
      verticalPosition: 'top'
    });
  }

  onImageError(event: any): void {
    event.target.src = 'assets/images/default-image.jpg';
  }

  private showErrorNotification(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar'],
      horizontalPosition: 'right',
      verticalPosition: 'top'
    });
  }
}
