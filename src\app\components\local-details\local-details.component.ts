import { Component, Input } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Local } from '@app/core/models/local';
import { Site } from '@app/core/models/site';
import { LocalApiService } from '@app/core/services/administrative/local.service';
import { SiteApiService } from '@app/core/services/administrative/site.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { LocalRuleComponent } from '@app/pages/site-details/local-rule/local-rule.component';
import { MatDialog } from '@angular/material/dialog';
import * as fabric from 'fabric';

@Component({
  selector: 'app-local-details',
  standalone: true,
  imports: [CommonModule, MatIconModule, MatButtonModule],
  templateUrl: './local-details.component.html',
  styleUrls: ['./local-details.component.css']
})
export class LocalDetailsComponent {
  @Input() local?: Local;
  @Input() site?: Site;

  canvas!: fabric.Canvas;
  canvasInitialized = false;
  isLoading: boolean = true;
  localId: string = '';
  currentSite?: Site;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly localService: LocalApiService,
    private readonly siteService: SiteApiService,
    private readonly snackBar: MatSnackBar,
    private dialog: MatDialog,

  ) {}

  ngOnInit(): void {
    this.localId = this.route.snapshot.paramMap.get('id') || '';
    if (this.local) {

      this.loadSiteForLocal(this.local);
      this.isLoading = false;
    } else if (this.localId) {

      this.loadLocalDetails();
      // if (this.canvasInitialized) {
      //   this.initializeArchitecturePlan();
      // }
    } else {

      this.isLoading = false;
      this.showErrorNotification('Aucun local trouvé');
    }
  }
openRuleFormDialog(rule?: any): void {
  console.log('Id Local:', this.localId);
  const dialogRef = this.dialog.open(LocalRuleComponent, {
    width: '95vw',
    maxWidth: '800px',
    panelClass: 'custom-local-rule-dialog',
    data: rule ? {
      rule: rule,
      idLocal: this.localId
    } : {
      rule: null,
      idLocal: this.localId
    },
    disableClose: true
  });

  dialogRef.afterClosed().subscribe(result => {
    if (result && result.action === 'saved') {
      // Handle saved rule
    }
  });
}
  private loadSiteForLocal(local: Local): void {
    if (local.IdSite) {
      this.siteService.getById(local.IdSite).subscribe({
        next: (site) => {
          // Cast the response to handle uppercase properties
          const transformedSite = {
            ...site,
            name: (site as any).Name ?? site.Name,
            adress: (site as any).Address ?? site.Address
          };
          this.currentSite = transformedSite;
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading site:', error);
          this.showErrorNotification('Erreur lors du chargement du site');
          this.isLoading = false;
        }
      });
    } else {
      this.isLoading = false;
    }
  }

  ngAfterViewInit(): void {
    this.initializeCanvas();
    if (this.local?.Architecture2DImage) {
      this.initializeArchitecturePlan();
    }
  }

  updateToolbar(): void{}
  onCanvasMouseDown(event: any) {
    const pointer = this.canvas.getPointer(event.e);
    const objects = this.canvas.getObjects();

    // Vérifier si on clique sur un capteur
    const clickedObject = this.canvas.findTarget(event.e);
    if (clickedObject && clickedObject.get('type') === 'capteur') {
      const capteurData = clickedObject.get('capteurData');
    }
  }

  initializeCanvas(): void {
    this.canvas = new fabric.Canvas('canvas', {
      backgroundColor: '#FAFAFA',
      width: 900,
      height: 500,
      selection: true
    });

    this.canvasInitialized = true;
    this.canvas.on('selection:created', () => this.updateToolbar());
    this.canvas.on('selection:cleared', () => this.updateToolbar());
    this.canvas.on('mouse:down', (e) => this.onCanvasMouseDown(e));
  }

  private initializeArchitecturePlan(): void {
    if (this.local?.Architecture2DImage) {
      try {
        const jsonData = JSON.parse(this.local.Architecture2DImage);
        this.canvas.loadFromJSON(jsonData, () => {
          // Wait for all objects to load
          setTimeout(() => {
            this.fitCanvasContent();
            this.lockPlanAsBackground();
            this.canvas.renderAll();
            console.log('✅ Plan JSON imported, centered and resized');
          }, 100);
        });
      } catch (error) {
        console.error('Error loading architecture plan:', error);
        this.showErrorNotification('Erreur lors du chargement du plan d\'architecture');
      }
    }
  }


  private getObjectsBounds(objects: fabric.Object[]): { width: number; height: number } {
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    objects.forEach(obj => {
      const bound = obj.getBoundingRect();
      minX = Math.min(minX, bound.left);
      minY = Math.min(minY, bound.top);
      maxX = Math.max(maxX, bound.left + bound.width);
      maxY = Math.max(maxY, bound.top + bound.height);
    });

    return {
      width: maxX - minX,
      height: maxY - minY
    };
  }

  private lockPlanAsBackground(): void {
    this.canvas.getObjects().forEach(obj => {
      obj.selectable = false;
      obj.hoverCursor = 'default';
    });
    this.canvas.renderAll();
  }

  loadLocalDetails(): void {
    this.isLoading = true;

    this.localService.getById(this.localId).subscribe({
      next: (local: Local) => {
        this.local = local;
        if (local.IdSite) {
          this.loadSiteForLocal(local);
        }
        // Initialize architecture plan after canvas is ready
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading local details:', error);
        this.showErrorNotification('Erreur lors du chargement du local');
        this.isLoading = false;
      }
    });
  }

  loadSiteDetails(siteId: string): void {
    this.siteService.getById(siteId).subscribe({
      next: (site: Site) => {
        this.site = site;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading site details:', error);
        this.isLoading = false;
      }
    });
  }

  goBack(): void {
    if (this.local?.IdSite) {

      this.router.navigate(['/site-locals', this.local.IdSite]);
    } else if (this.site?.Id) {
      this.router.navigate(['/site-locals', this.site.Id]);
    } else {
      this.router.navigate(['/accueil']);
    }
  }

  private showSuccessNotification(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 4000,
      panelClass: ['success-snackbar'],
      horizontalPosition: 'right',
      verticalPosition: 'top'
    });
  }

  onImageError(event: any): void {
    event.target.src = 'assets/images/default-image.jpg';
  }

  private showErrorNotification(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar'],
      horizontalPosition: 'right',
      verticalPosition: 'top'
    });
  }

  fitCanvasContent() {
    const objects = this.canvas.getObjects();
 
    if (objects.length === 0) {
      console.log('Aucun objet à redimensionner');
      return;
    }
 
    // Calculer les limites de tous les objets
    let minX = Infinity;
    let minY = Infinity;
    let maxX = -Infinity;
    let maxY = -Infinity;
 
    objects.forEach(obj => {
      const bounds = obj.getBoundingRect();
      minX = Math.min(minX, bounds.left);
      minY = Math.min(minY, bounds.top);
      maxX = Math.max(maxX, bounds.left + bounds.width);
      maxY = Math.max(maxY, bounds.top + bounds.height);
    });
 
    // Dimensions du contenu total
    const contentWidth = maxX - minX;
    const contentHeight = maxY - minY;
    const contentCenterX = minX + contentWidth / 2;
    const contentCenterY = minY + contentHeight / 2;
 
    console.log('Dimensions du contenu:', {
      width: contentWidth,
      height: contentHeight,
      minX, minY, maxX, maxY
    });
 
    // Dimensions du canvas
    const canvasWidth = this.canvas.getWidth();
    const canvasHeight = this.canvas.getHeight();
    const canvasCenterX = canvasWidth / 2;
    const canvasCenterY = canvasHeight / 2;
 
    // Calculer l'échelle pour s'adapter au canvas avec une marge
    const margin = 50;
    const scaleX = (canvasWidth - margin * 2) / contentWidth;
    const scaleY = (canvasHeight - margin * 2) / contentHeight;
    const scale = Math.min(scaleX, scaleY, 1); // Ne pas agrandir
 
    console.log('Échelle calculée:', scale);
 
    // Calculer le décalage pour centrer
    const deltaX = canvasCenterX - contentCenterX;
    const deltaY = canvasCenterY - contentCenterY;
 
    // Appliquer la transformation à tous les objets
    objects.forEach(obj => {
      // Sauvegarder les propriétés actuelles
      const currentLeft = obj.left || 0;
      const currentTop = obj.top || 0;
      const currentScaleX = obj.scaleX || 1;
      const currentScaleY = obj.scaleY || 1;
 
      // Calculer les nouvelles positions
      const newLeft = (currentLeft - contentCenterX) * scale + canvasCenterX;
      const newTop = (currentTop - contentCenterY) * scale + canvasCenterY;
 
      // Appliquer les transformations
      obj.set({
        left: newLeft,
        top: newTop,
        scaleX: currentScaleX * scale,
        scaleY: currentScaleY * scale
      });
 
      // Mettre à jour les coordonnées
      obj.setCoords();
    });
 
    console.log(`✅ ${objects.length} objets repositionnés et redimensionnés`);
  }
 
}
