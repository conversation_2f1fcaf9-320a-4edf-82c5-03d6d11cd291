<div class="container">
  <div class="card">
    <!-- Header -->
    <div class="header">
      <h1 class="title">Gestionnaire de Règles</h1>
      <button mat-icon-button (click)="refreshData()" [disabled]="hasPendingOperations()"
        matTooltip="Actualiser les données">
        <mat-icon [class.loading-icon]="getLoadingState()">
          {{ getLoadingState() ? 'hourglass_empty' : 'refresh' }}
        </mat-icon>
      </button>
    </div>

    <div class="search-create">
      <div class="search-container">
        <input type="text" placeholder="Rechercher une règle par nom, tag ou contenu..." class="input"
          [(ngModel)]="searchTerm" [disabled]="isSearching" (keyup.enter)="submitSearch()" />
        <button mat-icon-button (click)="submitSearch()" [disabled]="isSearching" matTooltip="Rechercher">
          <mat-icon>search</mat-icon>
        </button>
        <button mat-icon-button (click)="clearSearch()" [disabled]="!searchTerm || isSearching"
          matTooltip="Nettoyer la recherche">
          <mat-icon>clear</mat-icon>
        </button>
        <div *ngIf="isSearching" class="search-loading">
          <mat-icon class="loading-icon">hourglass_empty</mat-icon>
        </div>
      </div>

      <div class="create-buttons">
        <button mat-raised-button class="ai-create-button" (click)="openAiRuleGeneratorDialog()"
          [disabled]="hasPendingOperations()" matTooltip="Générer une règle automatiquement avec l'IA">
          <mat-icon>auto_awesome</mat-icon>
          Générer avec IA
        </button>

        <button mat-raised-button class="create-button" (click)="openRuleFormDialog()"
          [disabled]="hasPendingOperations()">
          <mat-icon>add</mat-icon>
          Créer une nouvelle règle
        </button>
      </div>
    </div>

    <!-- Loading and Empty States -->
    <div *ngIf="filteredRules.length === 0 && searchTerm && !isSearching" class="no-data-message">
      <p>Aucune règle trouvée pour "{{ searchTerm }}".
        <button mat-button (click)="clearSearch()" class="clear-search-button">
          <mat-icon>clear</mat-icon>
          Effacer la recherche
        </button>
      </p>
    </div>

    <div *ngIf="filteredRules.length === 0 && !searchTerm && !isSearching && !getLoadingState()"
      class="no-data-message">
      <p>Aucune règle trouvée. Cliquez sur "Créer une nouvelle règle" pour en ajouter une.</p>
    </div>

    <div *ngIf="isSearching" class="search-loading-message">
      <mat-icon class="loading-icon">search</mat-icon>
      <p>Recherche en cours...</p>
    </div>

    <!-- Rules List -->
    <div class="rules-list" *ngIf="filteredRules.length > 0">
      <div *ngFor="let rule of filteredRules" class="rule-item" [ngClass]="{
             'rule-expanded': expandedRuleId === rule.id,
             'simple-rule': isSimpleRule(rule),
             'complex-rule': !isSimpleRule(rule),
             'action-pending': isRuleDeleting(rule.id)
           }">

        <div class="rule-content">
          <div class="rule-details">
            <!-- Rule Header -->
            <div class="rule-header">
              <h3 class="rule-name">{{ rule.name }}</h3>
              <span class="priority">Priorité: {{ rule.priority }}</span>
              <span class="status" [ngClass]="{
                  'status-active': rule.status === 'active',
                  'status-inactive': rule.status === 'inactive'
                }">
                {{ rule.status === "active" ? "Active" : "Inactive" }}
              </span>
            </div>

            <!-- Summary Preview for Simple Rules -->
            <div *ngIf="isSimpleRule(rule) && hasRuleSummary(rule.id)" class="summary-preview">
              <div class="summary-content">
                <mat-icon class="summary-icon">summarize</mat-icon>
                <p class="summary-text">
                  {{ getRuleSummary(rule.id) | slice:0:120 }}{{ getRuleSummary(rule.id).length > 120 ? '...' : '' }}
                </p>
              </div>
            </div>

            <!-- Detailed Stats for Complex Rules or Expanded View -->
            <div *ngIf="!isSimpleRule(rule) || expandedRuleId === rule.id" class="usage-stats">
              <div class="stat-item">
                <mat-icon class="stat-icon">touch_app</mat-icon>
                <div>
                  <div class="stat-value">{{ rule.totalApplications }}</div>
                  <div class="stat-label">Applications</div>
                </div>
              </div>
              <div class="stat-item">
                <mat-icon class="stat-icon">people</mat-icon>
                <div>
                  <div class="stat-value">{{ getTotalClientsForRule(rule) }}</div>
                  <div class="stat-label">Clients</div>
                </div>
              </div>
              <div class="stat-item">
                <mat-icon class="stat-icon">devices</mat-icon>
                <div>
                  <div class="stat-value">{{ getTotalControllersForRule(rule) }}</div>
                  <div class="stat-label">Contrôleurs</div>
                </div>
              </div>
            </div>

            <!-- Tags Section -->
            <div class="tags" *ngIf="!isSimpleRule(rule) || expandedRuleId === rule.id">
              <span *ngFor="let tag of rule.tags" class="tag">#{{ tag }}</span>
              <div *ngIf="rule.tags.length === 0" class="no-tags">Aucun tag disponible</div>
            </div>

            <!-- Actions Section -->
            <div class="actions-triggered" *ngIf="!isSimpleRule(rule) || expandedRuleId === rule.id">
              <div class="actions-list">
                <span class="action-label">Actions:</span>
                <span *ngIf="!rule.actions || rule.actions.length === 0" class="no-actions">
                  Aucune action définie
                </span>
                <span *ngFor="let action of rule.actions" class="action-bubble">
                  {{ action.type }}
                  <span *ngIf="action.action"> ({{ action.action }}</span>
                  <span *ngIf="action.value">: {{ action.value }})</span>
                  <span *ngIf="!action.action && !action.value">)</span>
                  <span *ngIf="action.target"> sur {{ action.target }}</span>
                </span>
              </div>
              <div class="last-triggered">
                <mat-icon style="font-size: 14px;">schedule</mat-icon>
                Dernier déclenchement:
                <span class="timestamp">{{ rule.lastTriggered || "Jamais" }}</span>
              </div>
            </div>
          </div>

          <!-- Rule Actions (Buttons) -->
          <div class="rule-actions">
            <button mat-icon-button (click)="toggleRuleDetails(rule.id)"
              [matTooltip]="expandedRuleId === rule.id ? 'Masquer les détails' : 'Voir les détails'"
              [disabled]="isRuleDeleting(rule.id)">
              <mat-icon>{{ expandedRuleId === rule.id ? "expand_less" : "expand_more" }}</mat-icon>
            </button>

            <button mat-icon-button color="primary" (click)="editRuleById(rule.id)" matTooltip="Modifier la règle"
              [disabled]="isRuleDeleting(rule.id) || hasPendingOperations()">
              <mat-icon>edit</mat-icon>
            </button>

            <button mat-icon-button color="warn" (click)="deleteRule(rule.id)" matTooltip="Supprimer la règle"
              [disabled]="isRuleDeleting(rule.id)" [class.destructive-action]="!isRuleDeleting(rule.id)">
              <mat-icon *ngIf="!isRuleDeleting(rule.id)">delete</mat-icon>
              <mat-icon class="loading-icon" *ngIf="isRuleDeleting(rule.id)">hourglass_empty</mat-icon>
            </button>
          </div>
        </div>

        <!-- Expanded Details Section -->
        <div *ngIf="expandedRuleId === rule.id" class="expanded-details">
          <!-- Tab Navigation -->
          <div class="tab-navigation">
            <button class="tab-button" [ngClass]="{ 'active': activeTab === 'summary' }"
              (click)="setActiveTab('summary')">
              <mat-icon>summarize</mat-icon>
              Résumé IA
            </button>
            <button class="tab-button" [ngClass]="{ 'active': activeTab === 'hierarchy' }"
              (click)="setActiveTab('hierarchy')">
              <mat-icon>account_tree</mat-icon>
              Hiérarchie & Performances
            </button>
            <button class="tab-button" [ngClass]="{ 'active': activeTab === 'rawdata' }"
              (click)="setActiveTab('rawdata')">
              <mat-icon>code</mat-icon>
              Données Brutes (JSON)
            </button>
          </div>

          <!-- Tab Content -->
          <div class="tab-content">
            <!-- Summary Tab -->
            <div *ngIf="activeTab === 'summary'" class="summary-tab">
              <div class="summary-viewer">
                <div class="summary-header">
                  <h4>
                    <mat-icon style="vertical-align: middle; margin-right: 8px;">summarize</mat-icon>
                    Résumé Intelligent de la Règle
                  </h4>
                </div>

                <div class="summary-content-display" *ngIf="hasRuleSummary(rule.id)">
                  <div class="summary-text-content">{{ getRuleSummary(rule.id) }}</div>
                </div>

                <div class="summary-empty-state" *ngIf="!hasRuleSummary(rule.id)">
                  <mat-icon class="empty-icon">smart_toy</mat-icon>
                  <h5>Résumé non disponible</h5>
                  <p>Cette règle n'a pas encore de résumé généré par l'IA. Le résumé sera automatiquement créé lors de
                    la prochaine sauvegarde de la règle.</p>
                </div>

                <div class="summary-info">
                  <div class="info-item">
                    <mat-icon class="info-icon">info</mat-icon>
                    <span>Ce résumé est généré automatiquement par l'IA pour vous aider à comprendre rapidement le but
                      et le fonctionnement de la règle.</span>
                  </div>
                  <div class="info-item" *ngIf="hasRuleSummary(rule.id)">
                    <mat-icon class="info-icon">schedule</mat-icon>
                    <span>Dernière mise à jour: {{ rule.lastTriggered || 'Date de création de la règle' }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Hierarchy Tab -->
            <div *ngIf="activeTab === 'hierarchy'" class="hierarchy-tab">
              <div class="hierarchy">
                <h4>
                  <mat-icon style="vertical-align: middle; margin-right: 8px;">account_tree</mat-icon>
                  Hiérarchie des Clients & Contrôleurs
                </h4>

                <div *ngIf="rule.clients.length === 0" class="hierarchy-empty-state">
                  Aucun client, site, localisation ou contrôleur associé à cette règle.
                </div>

                <div *ngFor="let client of rule.clients" class="client-node">
                  <div class="node-header">
                    <mat-icon>business</mat-icon>
                    <span><strong>Client:</strong> {{ client.name }}</span>
                  </div>
                  <div class="node-children">
                    <div *ngFor="let site of client.sites" class="site-node">
                      <div class="node-header">
                        <mat-icon>location_city</mat-icon>
                        <span><strong>Site:</strong> {{ site.name }} <em>({{ site.address }})</em></span>
                      </div>
                      <div class="node-children">
                        <div *ngFor="let location of site.locations" class="location-node">
                          <div class="node-header">
                            <mat-icon>room</mat-icon>
                            <span><strong>Localisation:</strong> {{ location.name }}</span>
                          </div>
                          <div class="node-children">
                            <div *ngFor="let controller of location.controllers" class="controller-node">
                              <div class="node-header controller-header"
                                [ngClass]="{ 'selected': selectedControllerId === controller.id }"
                                (click)="selectController(controller.id)">
                                <mat-icon class="controller-icon" [ngClass]="getControllerStatusClass(controller)">
                                  {{ getControllerStatusIcon(controller) }}
                                </mat-icon>
                                <span><strong>Contrôleur:</strong> {{ controller.name }}</span>
                                <mat-icon class="arrow-icon">
                                  {{ selectedControllerId === controller.id ? 'keyboard_arrow_up' :
                                  'keyboard_arrow_down' }}
                                </mat-icon>
                              </div>

                              <!-- Controller Details Panel -->
                              <div *ngIf="selectedControllerId === controller.id" class="controller-details-panel">
                                <div class="detail-row">
                                  <strong>Modèle:</strong> {{ controller.model || 'Non spécifié' }}
                                </div>
                                <div class="detail-row">
                                  <strong>Dernière vue:</strong> {{ formatTimestamp(controller.lastSeen) || 'Jamais' }}
                                </div>
                                <div class="detail-row">
                                  <strong>Statut:</strong>
                                  <span [ngClass]="'status-' + getControllerStatusClass(controller)">
                                    {{ getControllerStatusText(controller) }}
                                  </span>
                                </div>

                                <h5>
                                  <mat-icon style="vertical-align: middle; margin-right: 4px;">history</mat-icon>
                                  Historique des Applications (Récentes)
                                </h5>

                                <!-- Applications Table -->
                                <div class="applications-table"
                                  [ngClass]="{ 'empty-state': !controller.applications || controller.applications.length === 0 }">
                                  <div *ngIf="controller.applications && controller.applications.length > 0">
                                    <div class="table-header">
                                      <span>Date/Heure</span>
                                      <span>Succès</span>
                                      <span>Durée (ms)</span>
                                    </div>
                                    <div *ngFor="let app of controller.applications" class="table-row">
                                      <span>{{ formatTimestamp(app.timestamp) }}</span>
                                      <span>
                                        <mat-icon [color]="app.success ? 'primary' : 'warn'">
                                          {{ app.success ? 'check_circle' : 'cancel' }}
                                        </mat-icon>
                                      </span>
                                      <span>{{ app.executionTime > 0 ? (app.executionTime | number:'1.0-0') : 'N/A'
                                        }}</span>
                                    </div>
                                  </div>
                                  <div *ngIf="!controller.applications || controller.applications.length === 0"
                                    class="empty-message">
                                    Aucune application récente enregistrée
                                  </div>
                                </div>

                                <h5>
                                  <mat-icon style="vertical-align: middle; margin-right: 4px;">analytics</mat-icon>
                                  Performance Quotidienne (7 derniers jours)
                                </h5>

                                <!-- Performance Chart -->
                                <div class="performance-chart">
                                  <div
                                    *ngIf="controller.performanceAnalytics && controller.performanceAnalytics.length > 0">
                                    <div class="chart-header">
                                      <span>Jour</span>
                                      <span>Total</span>
                                      <span>Succès</span>
                                      <span>Échecs</span>
                                    </div>
                                    <div class="chart-grid">
                                      <div *ngFor="let perf of controller.performanceAnalytics"
                                        class="chart-bar-column">
                                        <div class="bar-label">{{ perf.ApplicationDayOfWeek }}</div>
                                        <div class="bar-container"
                                          [matTooltip]="perf.SuccessfulApplications + ' succès, ' + perf.FailedApplications + ' échecs sur ' + perf.TotalApplications + ' total'">
                                          <div class="bar-segment success-bar"
                                            [style.height.%]="perf.TotalApplications > 0 ? (perf.SuccessfulApplications / perf.TotalApplications) * 100 : 0">
                                          </div>
                                          <div class="bar-segment failure-bar"
                                            [style.height.%]="perf.TotalApplications > 0 ? (perf.FailedApplications / perf.TotalApplications) * 100 : 0">
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    *ngIf="!controller.performanceAnalytics || controller.performanceAnalytics.length === 0"
                                    class="chart-grid empty-state">
                                    <div class="empty-message">Aucune donnée de performance disponible</div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Raw Data Tab -->
            <div *ngIf="activeTab === 'rawdata'" class="rawdata-tab">
              <div class="rawdata-viewer">
                <div class="rawdata-header">
                  <h4>
                    <mat-icon style="vertical-align: middle; margin-right: 8px;">code</mat-icon>
                    Données Brutes de la Règle
                  </h4>
                  <div class="rawdata-actions">
                    <button mat-button (click)="copyRawData(rule.id)" class="copy-button">
                      <mat-icon>content_copy</mat-icon>
                      Copier
                    </button>
                    <button mat-button (click)="downloadRawData(rule)" class="download-button">
                      <mat-icon>download</mat-icon>
                      Télécharger
                    </button>
                  </div>
                </div>

                <div class="json-viewer">
                  <pre class="json-content">{{ formatRawData(rule.id) }}</pre>
                </div>

                <div class="rawdata-info">
                  <div class="info-item">
                    <mat-icon class="info-icon">info</mat-icon>
                    <span>Cette section affiche la structure JSON brute telle qu'elle est stockée dans la base de
                      données.</span>
                  </div>
                  <div class="info-item">
                    <mat-icon class="info-icon">warning</mat-icon>
                    <span>Modification directe non recommandée. Utilisez l'éditeur de règles pour les
                      modifications.</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination Section -->
    <div *ngIf="totalElements > 0" class="pagination-info" [class.pagination-controls]="shouldShowPagination()"
      [class.simple-info]="!shouldShowPagination()">

      <!-- Navigation buttons only if multiple pages -->
      <ng-container *ngIf="pageCount > 1">
        <button mat-icon-button (click)="previousPage()" [disabled]="currentPage === 1 || hasPendingOperations()"
          matTooltip="Page précédente">
          <mat-icon>chevron_left</mat-icon>
        </button>

        <span *ngFor="let page of getPagesArray()" class="page-number" [class.active]="page === currentPage"
          [class.disabled]="hasPendingOperations()" (click)="!hasPendingOperations() && goToPage(page)">
          {{ page }}
        </span>

        <button mat-icon-button (click)="nextPage()" [disabled]="currentPage === pageCount || hasPendingOperations()"
          matTooltip="Page suivante">
          <mat-icon>chevron_right</mat-icon>
        </button>
      </ng-container>

      <!-- Page info -->
      <span class="page-info">
        <ng-container *ngIf="pageCount > 1">Page {{ currentPage }} sur {{ pageCount }} - </ng-container>
        Total: {{ totalElements }} règle{{ totalElements !== 1 ? 's' : '' }}
        <ng-container *ngIf="searchTerm">
          (filtré{{ totalElements !== 1 ? 's' : '' }} par "{{ searchTerm }}")
        </ng-container>
        <ng-container *ngIf="hasPendingOperations()">
          <span class="operation-indicator">
            <mat-icon class="loading-icon" style="font-size: 14px; margin-left: 8px;">hourglass_empty</mat-icon>
            Opération en cours...
          </span>
        </ng-container>
      </span>
    </div>

    <!-- Global loading overlay for critical operations -->
    <ngx-ui-loader *ngIf="getLoadingState() && !isSearching"></ngx-ui-loader>
    <ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>
  </div>
</div>