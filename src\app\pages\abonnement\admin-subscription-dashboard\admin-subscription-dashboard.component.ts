import { CommonModule, NgIf, Ng<PERSON><PERSON>, Ng<PERSON>lass } from '@angular/common';
import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { LogApiService } from '@app/core/services/administrative/log.service';
import { Subscription } from 'rxjs';
@Component({
  selector: 'app-admin-subscription-dashboard',
  templateUrl: './admin-subscription-dashboard.component.html',
  styleUrls: ['./admin-subscription-dashboard.component.css'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, MatIconModule],
})
export class AdminSubscriptionDashboardComponent implements OnInit {
  logs: any[] = [];
  filteredLogs: any[] = [];
  localId: string | null = null; // will store LocalId from logs
  logForm!: FormGroup;
  isLoading = false;
  selectedLog: any = null;
  isSummaryLoading = false;
  locals: string[] = [];

  localIdFilter: string | null = null;

  private refreshSubscription?: Subscription;
  private formSubscription?: Subscription;
  private lastPageSize = 100;
  private isProgrammaticFormChange = false;

  constructor(private fb: FormBuilder, private logService: LogApiService) {}

  ngOnInit(): void {
    this.initForm();
    this.fetchLogs();
  }

  ngOnDestroy(): void {
    this.refreshSubscription?.unsubscribe();
    this.formSubscription?.unsubscribe();
  }

  private initForm(): void {
    this.logForm = this.fb.group({
      logType: ['Tout'],
      textSearch: [''],
      pageSize: [100],
      localIdFilter: ['Tout'], // Added dropdown for locals filter
    });

    this.formSubscription = this.logForm.valueChanges.subscribe((formValue) => {
      if (this.isProgrammaticFormChange) return;

      if (formValue.pageSize !== this.lastPageSize) {
        this.lastPageSize = formValue.pageSize;
        this.fetchLogs();
      } else {
        this.filterLogs();
      }
    });
  }

  fetchLogs(): void {
    this.isProgrammaticFormChange = true;
    this.isLoading = true;

    const { pageSize } = this.logForm.value;
    this.lastPageSize = pageSize;

    // No filtering on localId when fetching logs - get all to build local filter list
    const payload = {
      pagination: {
        currentPage: 0,
        pageSize: pageSize,
        isLast: true,
        isFirst: true,
        startIndex: 0,
        totalElement: 0,
      },
      filterParams: [], // fetch all locals logs
    };

    this.logService.gatePage(payload).subscribe({
      next: (res: any) => {
        this.logs =
          res.Content?.map((log: any) => {
            const parsed = this.parseLogMessage(log.Message);
            parsed.localId = log.LocalId || 'Unknown'; // store localId from API log
            parsed.capteur = log.Capteur || null; // store capteur info if exists
            return parsed;
          }) || [];

        // Extract unique locals for filter dropdown
        this.locals = Array.from(
          new Set(this.logs.map((log) => log.localId))
        ).sort();

        // If no localIdFilter set, default to 'Tout' (all)
        if (!this.logForm.value.localIdFilter) {
          this.isProgrammaticFormChange = true;
          this.logForm.patchValue({ localIdFilter: 'Tout' });
          this.isProgrammaticFormChange = false;
        }

        this.filterLogs();
        this.isLoading = false;
        this.isProgrammaticFormChange = false;
      },
      error: (err: any) => {
        console.error('Error loading logs', err);
        this.isLoading = false;
        this.isProgrammaticFormChange = false;
      },
    });
  }

  private parseLogMessage(message: string): any {
    try {
      const jsonStart = message.indexOf('{');
      const prefix = message.substring(0, jsonStart);
      const remaining = message.substring(jsonStart);

      let braceCount = 0;
      let endIndex = -1;

      for (let i = 0; i < remaining.length; i++) {
        const char = remaining[i];
        if (char === '{') braceCount++;
        else if (char === '}') braceCount--;

        if (braceCount === 0) {
          endIndex = i;
          break;
        }
      }

      if (endIndex !== -1) {
        const jsonStr = remaining.substring(0, endIndex + 1);
        const jsonData = JSON.parse(jsonStr);

        return {
          level: jsonData.level ?? 'info',
          message: jsonData.message ?? (prefix.trim() || 'Pas de message'),
          timestamp: prefix.trim() || new Date().toISOString(),
          topic: jsonData.topic,
          payload: jsonData,
          summary: null, // init summary as null
        };
      }
    } catch (e) {
      console.warn('Could not parse log message as JSON', e);
    }

    return {
      level: 'info',
      message: message || 'Pas de message',
      timestamp: new Date().toISOString(),
      payload: null,
      summary: null,
    };
  }

  private filterLogs(): void {
    const { logType, textSearch, localIdFilter } = this.logForm.value;

    this.filteredLogs = this.logs.filter((log) => {
      // Filter by LocalId
      if (localIdFilter && localIdFilter !== 'Tout') {
        if (log.localId !== localIdFilter) {
          return false;
        }
      }

      // Filter by logType
      if (logType !== 'Tout') {
        if (log.level.toLowerCase() !== logType.toLowerCase()) {
          return false;
        }
      }

      // Filter by text search
      if (textSearch) {
        const searchText = textSearch.toLowerCase();
        if (
          !log.message.toLowerCase().includes(searchText) &&
          !(
            log.payload &&
            JSON.stringify(log.payload).toLowerCase().includes(searchText)
          )
        ) {
          return false;
        }
      }
      return true;
    });
  }

  resetForm(): void {
    this.isProgrammaticFormChange = true;
    this.logForm.reset({
      logType: 'Tout',
      textSearch: '',
      pageSize: 100,
    });
    this.isProgrammaticFormChange = false;
  }

  openLogDetails(log: any): void {
    this.selectedLog = log;

    // If no summary yet, fetch it from the service
    if (!log.summary) {
      this.isSummaryLoading = true;
      this.logService.summarizeLog(log).subscribe({
        next: (summaryText: String) => {
          log.summary = summaryText.toString();
          this.selectedLog = log; // update selectedLog reference to trigger UI update
          this.isSummaryLoading = false;
        },
        error: (error) => {
          console.error('Error generating summary:', error);
          log.summary = null;
          this.isSummaryLoading = false;
        },
      });
    }
  }

  closeLogDetails(): void {
    this.selectedLog = null;
    this.isSummaryLoading = false;
  }

  getLogLevelClass(level: string): string {
    return level.toLowerCase();
  }

  formatTimestamp(timestamp: string): string {
    try {
      return new Date(timestamp).toLocaleString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      });
    } catch {
      return timestamp;
    }
  }

  getMetaKeys(payload: any): string[] {
    if (!payload || typeof payload !== 'object') return [];
    return Object.keys(payload).filter(
      (key) =>
        key !== 'level' &&
        key !== 'message' &&
        key !== 'timestamp' &&
        payload[key] !== null &&
        payload[key] !== undefined
    );
  }

  getMetaValue(value: any): string {
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    return String(value);
  }

  hasMetaData(payload: any): boolean {
    return this.getMetaKeys(payload).length > 0;
  }
}
