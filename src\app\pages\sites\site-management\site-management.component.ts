import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import {FormsModule,ReactiveFormsModule,FormGroup,FormControl, Validators,} from '@angular/forms';
import { SiteApiService } from '../../../core/services/administrative/site.service';
import { Site } from '../../../core/models/site';
import {trigger,state,style,transition,animate,} from '@angular/animations';
import { GenericTableComponent } from '../../../components/generic-table/generic-table.component';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatIconModule } from '@angular/material/icon';
import { Client } from '@app/core/models/client';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';
import { FormSiteComponent } from '../form-site/form-site.component';
import { NgToastComponent, NgToastService, TOAST_POSITIONS } from 'ng-angular-popup';

interface IndexableSite extends Site {
  [key: string]: any;
}

interface PaginationRequest {
  currentPage: number;
  pageSize: number;
  totalElement: number;
}

interface ApiRequest {
  pagination: PaginationRequest;
  filterParams?: Array<{
    column: string;
    value: string;
    op: string;
    andOr: string;
  }>;
}

interface ApiResponse {
  Content: Site[];
  Lister?: {
    Pagination?: {
      TotalElement?: number;
    };
  };
}

@Component({
  selector: 'app-site-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    GenericTableComponent,
    MatPaginatorModule,
    MatIconModule,
    MatDialogModule,
    NgxUiLoaderModule,
    FormSiteComponent,
    NgToastComponent
  ],
  templateUrl: './site-management.component.html',
  styleUrls: ['./site-management.component.css'],
  animations: [
    trigger('tableRowAnimation', [
      state('void', style({ opacity: 0, transform: 'translateY(20px)' })),
      transition('void => *', animate('300ms ease-in')),
    ]),
    trigger('fadeIn', [
      state('void', style({ opacity: 0 })),
      transition('void => *', animate('400ms 300ms ease-in')),
    ]),
  ],
})
export class SiteManagementComponent implements OnInit {
  sites: Site[] = [];
  filteredSites: any[] = [];
  clients: any[] = [];
  searchTerm: string = '';
  uploadedImages: File[] = [];
  viewMode: string = 'table';
  currentPage: number = 0;
  pageSize: number = 5;
  totalCount: number = 0;
  showEditForm: boolean = false;
  selectedSite: any | null = null;
  editUploadedImages: File[] = [];
  removedImageIds: number[] = [];
  showOptionalFields: boolean = false;
  showImageRemovedMessage: boolean = false;
    TOAST_POSITIONS = TOAST_POSITIONS;
  keys: string[] = [
    'Name',
    'Address',
    'PhoneNumber',
    'Contact',
    'Status',
    'Client.Name',
  ];
  headers: string[] = [
    'Nom',
    'Adresse',
    'Téléphone',
    'Contact',
    'Statut',
    'Client',
  ];

  editSiteForm = new FormGroup({
    Id: new FormControl(''),
    Name: new FormControl('', [Validators.required]),
    Adress: new FormControl('', [Validators.required]),
    AddressComplement: new FormControl(''),
    PhoneNumber: new FormControl('', [Validators.required]),
    Description: new FormControl(''),
    Image: new FormControl(),
    Contact: new FormControl('', [Validators.required]),
    Manager: new FormControl('', [Validators.required]),
    Email: new FormControl('', [
      Validators.required,
      Validators.email,
      Validators.pattern('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$'),
    ]),
    Status: new FormControl('', [Validators.required]),
    Grade: new FormControl('A'),
    Latitude: new FormControl(0),
    Longtitude: new FormControl(0),
    Surface: new FormControl(0),
    ClientId: new FormControl(''),
  });

  constructor(
    private readonly clientApiService: ClientApiService,
    private readonly siteService: SiteApiService,
    private readonly router: Router,
    private readonly dialog: MatDialog,
    private readonly spinner: NgxUiLoaderService,
    private readonly toast: NgToastService
  ) {}

  ngOnInit(): void {
    this.currentPage = 0;
    this.pageSize = 5;
    this.totalCount = 0;
    this.loadClients();
    this.loadSites();
  }

  private scrollToElement(elementId: string): void {
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  }

  loadSiteForEdit(siteId: string): void {
    this.spinner.start(); // Changed from show()
    console.log('Spinner show called');

    this.siteService.getById(siteId).subscribe({
      next: (site: any) => {
        if (site) {
          console.log('Loading site for edit:', site);
          this.selectedSite = site;
          this.editSiteForm.patchValue({
            Id: site.Id ?? site.Id ?? '',
            Name: site.Name ?? site.Name ?? '',
            Adress: site.Address ?? site.Address ?? '',
            AddressComplement:
              site.AddressComplement ?? site.addressComplement ?? '',
            PhoneNumber: site.PhoneNumber ?? site.phoneNumber ?? '',
            Description: site.Description ?? site.description ?? '',
            Contact: site.Contact ?? site.contact ?? '',
            Manager: site.Manager ?? site.manager ?? '',
            Email: site.Email ?? site.email ?? '',
            Status: site.Status ?? site.status ?? '',
            Grade: site.Grade ?? site.grade ?? 'A',
            Image: site.Image ?? site.image ?? null,
            Latitude: site.Latitude ?? site.latitude ?? 0,
            Longtitude: site.Longtitude ?? site.longtitude ?? 0,
            Surface: site.Surface ?? site.surface ?? 0,
            ClientId: site.ClientId ?? site.clientId ?? '',
          });
          this.showEditForm = true;
          this.scrollToElement('editForm');
        }
        this.spinner.stop(); // ✅ Hide spinner
      },
      error: (error) => {
        console.error('Error loading site for edit:', error);
        this.spinner.stop(); // ✅ Hide spinner
        // Optionally show an error message to the user
        alert('Erreur lors du chargement du site pour modification');
      },
    });
  }

  loadClients(): void {
    this.spinner.start();
    this.clientApiService.getAll().subscribe({
      next: (clients) => {
        console.log('Clients:', clients);
        this.clients = clients;
        this.spinner.stop(); // ✅ Hide spinner
      },
      error: (error) => {
        console.error('Error loading clients:', error);
        this.spinner.stop(); // ✅ Hide spinner
      },
    });
  }

  loadSites(): void {
    this.spinner.start();
    console.log('Spinner show called');
  
  const apiPage = this.currentPage + 1;
  const pagination: PaginationRequest = {
    currentPage: apiPage,
    pageSize: this.pageSize,
    totalElement: 0
  };

  const request: ApiRequest = {
    pagination: pagination
  };

  // Add filter parameters if searchTerm is not empty
  if (this.searchTerm.trim()) {
    request.filterParams = [{
      column: 'Name',
      value: this.searchTerm,
      op: 'contains',
      andOr: 'AND'
    }];
  }

  this.siteService.gatePage(request).subscribe({
    next: (response: any) => {
      const sites = response?.Content ?? [];
      console.log('Sites:', sites);
      this.filteredSites = sites

      if (response?.Lister?.pagination?.totalElement !== undefined) {
        this.totalCount = response.Lister.pagination.totalElement;
      } else if (response?.Lister?.Pagination?.TotalElement !== undefined) {
        this.totalCount = response.Lister.Pagination.TotalElement;
      } else {
        this.totalCount = Math.max(8, this.filteredSites.length);
      }
      
      this.spinner.stop(); // ✅ Hide spinner
    },
    error: (error) => {
      console.error('Error loading sites:', error);
      this.filteredSites = [];
      this.totalCount = 0;
      this.spinner.stop(); // ✅ Hide spinner
this.toast.warning('Échec du chargement des sites', 'Error', 3000, false);
    }
  });
}

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadSites();
  }

  filterSites(): void {
    this.currentPage = 0; // Reset to first page when searching
    this.loadSites();
  }


  onImagesSelected(event: any): void {
    if (event.target.files && event.target.files.length > 0) {
      this.uploadedImages = Array.from(event.target.files);
    } else {
      this.uploadedImages = [];
    }
  }

  generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  editSite(id: string): void {
    const site = this.filteredSites.find((s) => s.Id === id);
    console.log('Editing site:', site);
    if (site) {
      this.selectedSite = site;
      this.editSiteForm.patchValue({
        Id: site.Id ?? '',
        Name: site.Name ?? '',
        Adress: site.Address ?? '',
        AddressComplement: site.AddressComplement ?? '',
        PhoneNumber: site.PhoneNumber ?? '',
        Description: site.Description ?? '',
        Contact: site.Contact ?? '',
        Manager: site.Manager ?? '',
        Email: site.Email ?? '',
        Status: site.Status ?? '',
        Grade: site.Grade ?? 'A',
        Latitude: site.Latitude ?? 0,
        Longtitude: site.Longtitude ?? 0,
        Surface: site.Surface ?? 0,
        ClientId: site.ClientId ?? '',
      });
      this.showEditForm = true;
      this.scrollToElement('editForm');
    } else {
      console.warn('Site not found for editing, id:', id);
    }
  }

deleteSite(id: string): void {
  console.log('Deleting site, id:', id);

  const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
    width: '400px',
    data: {
      title: 'Confirmation de suppression',
      message: 'Êtes-vous sûr de vouloir supprimer ce site ?',
      icon: 'warning'
    }
  });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.siteService.delete(id).subscribe({
          next: () => {
this.toast.info('Site supprimé avec succès', 'Success', 3000, false);
            this.loadSites();
          },
          error: (error) => {
            console.error('Error deleting site:', error);
this.toast.warning('Échec de la suppression du site', 'Error', 3000, false);
          },
        });
      }
    });
  }

  viewDetails(id: string): void {
    this.router.navigate(['/site-locals/', id]);
  }

  hideEditForm(): void {
    this.showEditForm = false;
    this.selectedSite = null;
    this.editSiteForm.reset();
    this.editUploadedImages = [];
  }

  onEditImagesSelected(event: any): void {
    if (event.target.files && event.target.files.length > 0) {
      this.editUploadedImages = Array.from(event.target.files);
    } else {
      this.editUploadedImages = [];
    }
  }

submitEditForm(): void {
  this.confirmEditSite();
}

  private confirmEditSite(): void {
    if (!this.selectedSite) return;

    const formValues = this.editSiteForm.value;
    const updatedSite: any = {
      Id: formValues.Id ?? this.selectedSite.id,
      Name: formValues.Name ?? this.selectedSite.name,
      Address: formValues.Adress ?? this.selectedSite.adress,
      AddressComplement:
        formValues.AddressComplement ?? this.selectedSite.addressComplement,
      PhoneNumber: formValues.PhoneNumber ?? this.selectedSite.phoneNumber,
      Description: formValues.Description ?? this.selectedSite.description,
      Contact: formValues.Contact ?? this.selectedSite.contact,
      Manager: formValues.Manager ?? this.selectedSite.manager,
      Email: formValues.Email ?? this.selectedSite.email,
      Status: formValues.Status ?? this.selectedSite.status,
      Grade: formValues.Grade ?? this.selectedSite.grade,
      Latitude: formValues.Latitude ?? this.selectedSite.latitude,
      Longtitude: formValues.Longtitude ?? this.selectedSite.longtitude,
      Surface: formValues.Surface ?? this.selectedSite.surface,
      ClientId: formValues.ClientId ?? this.selectedSite.clientId,
      CreatedAt: this.selectedSite.createdAt,
      Image: formValues.Image ?? null,
      CreatedBy: this.selectedSite.createdBy,
      LastUpdatedAt: new Date().toISOString(),
      lastUpdatedBy: '',
      DeletedAt: this.selectedSite.deletedAt,
      DeletedBy: this.selectedSite.deletedBy,
      EmployeesCount: this.selectedSite.employeesCount ?? 0,
      LocalsCount: this.selectedSite.localsCount ?? 0,
    };

  if (this.editUploadedImages && this.editUploadedImages.length > 0) {
    const file = this.editUploadedImages[0];
    const reader = new FileReader();

    reader.onload = () => {
      const result = reader.result as string;
      updatedSite.Image = result.includes('base64,')
        ? result.split('base64,')[1]
        : result;
      this.updateSite(updatedSite);
    };

    reader.onerror = (error) => {
      console.error('Error reading file:', error);
      this.updateSite(updatedSite);
    };

    reader.readAsDataURL(file);
  } else {
    this.updateSite(updatedSite);
  }
}

  private updateSite(updatedSite: Site): void {
    this.siteService.update(updatedSite).subscribe({
      next: () => {
this.toast.success('Site modifié avec succès', 'Success', 3000, false);
        this.loadSites();
        this.hideEditForm();
      },
      error: (error) => {
        console.error('Error updating site:', error);
this.toast.warning('Échec de la modification du site', 'Error', 3000, false);
      },
    });
  }

handleAction(event: { action: string; row: any }): void {
  const { action, row } = event;

  if (action === 'edit') {
    this.editSite(row.Id ?? row.id);
  } else if (action === 'delete') {
    this.deleteSite(row.Id ?? row.id); // Just call deleteSite directly
  } else if (action === 'view') {
    this.viewDetails(row.Id ?? row.id);
  }
}

  toggleOptionalFields($event: any): void {
    $event.preventDefault();
    this.showOptionalFields = !this.showOptionalFields;
  }

  removeExistingImage(): void {
    if (this.selectedSite) {
      this.selectedSite.Image = '';
      this.showImageRemovedMessage = false;
    }
  }
}
