<div class="create-form-card">
  <div class="form-container">
    <form>
      <div class="form-grid">
        <div class="form-group">
          <label for="name">Nom <span class="required">*</span></label>
          <input
            id="name"
            type="text"
            formControlName="name"
            value="{{ controllerServer?.Name }}"
            disabled
          />
        </div>
        <div class="form-group">
          <label for="idlicence">Licence <span class="required">*</span></label>

          <input
            id="name"
            type="text"
            formControlName="name"
            value="{{ selectedLicenceName }}"
            disabled
          />
        </div>
        <div class="form-group">
          <label for="geographicZone">Zone Géographique</label>
          <input
            id="geographicZone"
            formControlName="geographicZone"
            value="{{ controllerServer?.GeographicZone }}"
            disabled
          />
        </div>

        <div class="form-group">
          <label for="commercialCondition">Condition Commerciale</label>
          <input
            id="commercialCondition"
            formControlName="commercialCondition"
            value="{{ controllerServer?.CommercialCondition }}"
            disabled
          />
        </div>
        <div class="form-group">
          <label for="actionType">Type d'action</label>
          <input
            id="actionType"
            formControlName="actionType"
            disabled
            value="{{ controllerServer?.ActionType }}"
          />
        </div>
        <div class="form-group">
          <label for="eventType">Type d'événement</label>
          <input
            id="eventType"
            formControlName="eventType"
            disabled
            value="{{ controllerServer?.EventType }}"
          />
        </div>

        <div class="form-group">
          <label for="status">Statut <span class="required">*</span></label>
          <input
            id="eventType"
            formControlName="eventType"
            disabled
            value="{{ controllerServer?.Status }}"
          />
        </div>
      </div>

      
    </form>
  </div>
</div>
