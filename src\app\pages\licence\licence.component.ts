import { Component, HostListener, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { Client } from '@app/core/models/client';
import { LicenceApiService } from '@app/core/services/administrative/licence.service';
import { LicenceOptionApiService } from '@app/core/services/administrative/licenceOption.service';
import { OptionApiService } from '@app/core/services/administrative/option.service';
import { Licence } from '@app/core/models/licence';
import { LicenceOption } from '@app/core/models/licenceOption';
import { Option } from '@app/core/models/option';
import { Subscription } from '@app/core/models/subscription';
import { SubscriptionApiService } from '@app/core/services/administrative/subscription.service';
import { SubscribedOptionsApiService } from '@app/core/services/administrative/subscribedOptions.service';
import { SubscribedOptions } from '@app/core/models/subscribedoptions';
import { NgxSpinnerService, NgxSpinnerModule } from 'ngx-spinner';
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';

@Component({
  selector: 'app-licence',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NgxSpinnerModule,
    GenericTableComponent
  ],
  templateUrl: './licence.component.html',
  styleUrls: ['./licence.component.css'],
  animations: [
    trigger('slideInOut', [
      state('void', style({
        transform: 'translateY(-10px)',
        opacity: 0
      })),
      state('*', style({
        transform: 'translateY(0)',
        opacity: 1
      })),
      transition('void <=> *', animate('200ms ease-in-out'))
    ]),
    trigger('growIn', [
      state('void', style({
        transform: 'scale(0.8)',
        opacity: 0
      })),
      state('*', style({
        transform: 'scale(1)',
        opacity: 1
      })),
      transition('void <=> *', animate('150ms ease-in-out'))
    ]),
    trigger('fadeInOut', [
      state('void', style({
        opacity: 0,
        transform: 'scale(0.9)'
      })),
      state('*', style({
        opacity: 1,
        transform: 'scale(1)'
      })),
      transition('void <=> *', animate('300ms ease-in-out'))
    ]),
    trigger('slideDown', [
      state('void', style({
        transform: 'translateY(-20px)',
        opacity: 0
      })),
      state('*', style({
        transform: 'translateY(0)',
        opacity: 1
      })),
      transition('void <=> *', animate('400ms ease-out'))
    ]),
    trigger('cardSlide', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateX({{enterX}}%) scale(0.95)' }),
        animate('400ms cubic-bezier(.4,2,.6,1)', style({ opacity: 1, transform: 'translateX(0) scale(1)' }))
      ], { params: { enterX: 100 } }),
      transition(':leave', [
        animate('400ms cubic-bezier(.4,2,.6,1)', style({ opacity: 0, transform: 'translateX({{leaveX}}%) scale(0.95)' }))
      ], { params: { leaveX: -100 } })
    ])
  ]
})
export class LicenceComponent implements OnInit {
  constructor(
    private clientApiService: ClientApiService,
    private licenceApiService: LicenceApiService,
    private licenceOptionApiService: LicenceOptionApiService,
    private optionApiService: OptionApiService,
    private subscriptionApiService: SubscriptionApiService,
    private subscribedOptionsApiService: SubscribedOptionsApiService,
    private spinner: NgxSpinnerService
  ) {}

  billingCycle: 'monthly' | 'yearly' = 'monthly';
  searchQuery = '';
  showDropdown = false;
  tablePageSize = 10;
  tableCurrentPage = 0;
  filteredTableRows: any[] = [];
  subscriptionTableRows: any[] = [];
  // Payment frequency properties
  selectedPaymentFrequency: string = 'Mensuel';
  customDateFin: string = '';
  customMonths: number = 1;
  // Table actions
  tableActionType: any = null;
  tableActionRow: any = null;
  showTableActionPopup: boolean = false;
  showAddOptionPopup = false;
  showDeleteOptionPopup = false;
  showDeleteConfirmationPopup = false;
  selectedLicenceForOption: string = '';
  newOptionName = '';
  showCard = false;
  activeTableRows: any[] = [];
  showTable = false;
  newOptionPrice: number | null = null;
  selectedLicenceForDeletion: string = '';
  optionSearchQuery = '';
  filteredOptionsForDeletion: Option[] = [];
  optionsForDeletion: Option[] = [];
  selectedOptionForDeletion: Option | null = null;
  proratedDiferenceReturn: number = 0;
  proratedOldForUpgrade: number = 0;
  proratedDiferencePay: number = 0;
  proratedNewForUpgrade: number = 0;
  proratedTotalForUpgrade: number = 0;
  selectedClient: Client | null = null;
  showConfirmationPopup = false;
  selectedLicenseForConfirmation: Licence | null = null;
  showSuccessNotification = false;
  showCancelPopup = false;
  licenceToCancel: Licence | null = null;
  showCancelNotification = false;
  showChooseClientError = false;
  showUpgradePopup = false;
  clients: Client[] = [];
  filteredClients: Client[] = [];
  licences: Licence[] = [];
  options: Option[] = [];
  licenceOptions: LicenceOption[] = []; 
  assignedLicences: { [idClient: string]: string } = {};
  subscriptions: Subscription[] = [];
  clientSubscription: Subscription | null = null;
  checkedOptions: { [licenceId: string]: Set<string> } = {};
  oldLicence: Licence | null = null;
  modifyingLicence: Licence | null = null;
  showModifyPopup = false;
  showSaveNotification = false;
  oldOptions: Option[] = [];
  oldOptionsLoaded: boolean = false;
  initialCheckedOptions: { [licenceId: string]: Set<string> } = {};
  assignedLicenceChanges: { [licenceId: string]: boolean } = {};
  showSaveOrDiscardPopup = false;
  licenceWithUnsavedChanges: Licence | null = null;
  showNoOptionCheckedPopup = false;
  _pendingRestoreLicence: Licence | null = null;
  _pendingRestoreType: 'modify' | 'affecter' | 'upgrade' | null = null;

  // Pagination state
  pageSize = 3;
  currentPage = 0;
  get totalPages() {
    return Math.ceil(this.licences.length / this.pageSize);
  }
  get pagedLicences() {
    const start = this.currentPage * this.pageSize;
    return this.licences.slice(start, start + this.pageSize);
  }
  cardAnimDirection: 'next' | 'prev' = 'next';
  viewMode: 'add' | 'modify' = 'add';
  private swipeStartX: number | null = null;
  dropdownOpenRow: any = null;
  cardMode: 'add' | 'modify' = 'add';

  get tableTotalPages(): number {
    return Math.ceil(this.activeTableRows.length / this.tablePageSize);
  }

  get tablePageNumbers(): number[] {
    return Array.from({length: this.tableTotalPages}, (_, i) => i);
  }

    get pagedTableRows(): any[] {
      const start = this.tableCurrentPage * this.tablePageSize;
      return this.activeTableRows.slice(start, start + this.tablePageSize);
    }

    get tableFirstItem(): number {
      if (this.activeTableRows.length === 0) return 0;
      return this.tableCurrentPage * this.tablePageSize + 1;
    }

    get tableLastItem(): number {
      return Math.min((this.tableCurrentPage + 1) * this.tablePageSize, this.activeTableRows.length);
    }

  onTablePageSizeChange(): void {
    this.tableCurrentPage = 0;
    this.updatePagedTableRows();
  }

  prevTablePage(): void {
    if (this.tableCurrentPage > 0) {
      this.tableCurrentPage--;
      this.updatePagedTableRows();
    }
  }

  nextTablePage(): void {
    if (this.tableCurrentPage < this.tableTotalPages - 1) {
      this.tableCurrentPage++;
      this.updatePagedTableRows();
    }
  }

  goToTablePage(page: number): void {
    this.tableCurrentPage = page;
    this.updatePagedTableRows();
  }

  updatePagedTableRows(): void {
    // No assignment needed; pagedTableRows is a getter and updates automatically
  }

  updateSubscriptionTableRows(): void {
  this.subscriptionTableRows = this.subscriptions.map(sub => {
    const client = this.clients.find(c => c.Id === sub.ClientId);
    const licence = this.licences.find(l => l.Id === sub.LicenceId);
    return {
      ClientName: client ? client.Name : '',
      LicenceName: licence ? licence.Name : '',
      DateDebut: this.formatDate(sub.DateDebut),
      DateFin: this.formatDate(sub.DateFin),
      Status: sub.Status,
      price: sub.Price,
      PaymentFrequency: sub.PaymentFrequency,
      ClientId: sub.ClientId,
      LicenceId: sub.LicenceId,
      SubscriptionId: sub.Id
    };
  });
  this.filteredTableRows = [...this.subscriptionTableRows];
  // Update active rows after filtering
  this.getActiveTableRows();
}

    filterClientsTable(): void {
      if (!this.searchQuery.trim()) {
        this.filteredTableRows = [...this.subscriptionTableRows];
      } else {
        const query = this.searchQuery.toLowerCase().trim();
        this.filteredTableRows = this.subscriptionTableRows.filter(row =>
          (row.ClientName || '').toLowerCase().includes(query) ||
          (row.LicenceName || '').toLowerCase().includes(query) ||
          (row.Status || '').toLowerCase().includes(query)
        );
      }
      
      // Update active rows after filtering and reset pagination
      this.getActiveTableRows();
      this.tableCurrentPage = 0;
    }

  fetchSubscriptions(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.subscriptionApiService.getAll().subscribe({
        next: (data: Subscription[]) => {
          this.subscriptions = data;
          this.updateSubscriptionTableRows();
          resolve();
        },
        error: (error) => {
          console.error('Error fetching subscriptions:', error);
          reject(error);
        }
      });
    });
  }

  fetchClients(): Promise<void> {
    return new Promise((resolve) => {
      this.clientApiService.getAll().subscribe({
        next: (data: Client[]) => {
          this.clients = data.map(clients => ({
            ...clients,
            Name: clients.Name,
            ClientLogo: clients.ClientLogo
          }));
          this.filteredClients = [...this.clients];
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  fetchLicences(): Promise<void> {
    return new Promise((resolve) => {
      this.licenceApiService.getAll().subscribe({
        next: (data: Licence[]) => {
          this.licences = data.map(licence => ({
            ...licence,
            name: licence.Name,
            description: licence.Description
          }));
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  fetchOptions(): Promise<void> {
    return new Promise((resolve) => {
      this.optionApiService.getAll().subscribe({
        next: (data: Option[]) => {
          this.options = data.map(options => ({
            ...options,
            name: options.Name,
            price: options.Price
          }));
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  fetchLicenceOptions(): Promise<void> {
    return new Promise((resolve) => {
      this.licenceOptionApiService.getAll().subscribe({
        next: (data: LicenceOption[]) => {
          this.licenceOptions = data;
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  private fetchCheckedOptionsForClientSubscription(subscriptionId: string, licenceId: string) {
    this.subscribedOptionsApiService.getAll().subscribe({
      next: (subscribedOptions: SubscribedOptions[]) => {
        const checkedOptionIds = subscribedOptions
          .filter((so: SubscribedOptions) =>
            so.SubscriptionId === subscriptionId &&
            this.isOptionLinkedToLicence(licenceId, so.OptionId))
          .map((so: SubscribedOptions) => so.OptionId);
        this.checkedOptions[licenceId] = new Set(checkedOptionIds);
      }
    });
  }

  ShowLicenceCard() {
    this.cardMode = 'add';
    this.showCard = true;
    this.showTable = false;
    this.searchQuery = '';
    this.selectedClient = null;
    this.filteredClients = [];
    this.clientSubscription = null;
    
    // Reset to default values for add mode
    this.selectedPaymentFrequency = 'Mensuel';
    this.customDateFin = '';

    this.licences.forEach(licence => {
      const allOptionIds = this.getOptionsForLicence(licence).map(opt => opt.Id);
      this.checkedOptions[licence.Id] = new Set(allOptionIds);
      this.initialCheckedOptions[licence.Id] = new Set(allOptionIds);
    });
  }

  public showAssignedActions(licence: Licence): boolean {
    if (!this.showCard) {
      return this.isLicenceAssignedToClient(licence);
    }
    return false;
  }

  showTableView(){
    this.showTable = true;
    this.showCard = false;
  }

  toggleOption(licence: Licence, optionId: string, event: Event) {
    const licenceId = licence.Id;
    if (!this.checkedOptions[licenceId]) {
      this.checkedOptions[licenceId] = new Set();
    }
    if (this.checkedOptions[licenceId].has(optionId)) {
      this.checkedOptions[licenceId].delete(optionId);
    } else {
      this.checkedOptions[licenceId].add(optionId);
    }
    this.assignedLicenceChanges[licenceId] = true;
  }

  
selectClient(client: Client) {
  console.log('selectClient called with:', client.Name, 'Mode:', this.cardMode);
  
  this.selectedClient = client;
  this.searchQuery = '';
  this.showDropdown = false;
  this.filteredClients = [];

  // Clear all previous states after 1 second delay
  setTimeout(() => {
    this.checkedOptions = {};
    this.initialCheckedOptions = {};
  }, 1000);

  if (this.cardMode === 'add') {
    this.clientSubscription = null;
    // Reset to default values for add mode
    this.selectedPaymentFrequency = 'Mensuel';
    this.customDateFin = '';
    
    // For add mode: check ALL options by default (after delay)
    setTimeout(() => {
      this.licences.forEach(licence => {
        const allOptionIds = this.getOptionsForLicence(licence).map(opt => opt.Id);
        this.checkedOptions[licence.Id] = new Set(allOptionIds);
        this.initialCheckedOptions[licence.Id] = new Set(allOptionIds);
        console.log(`Add mode - Licence ${licence.Name}: checked ${allOptionIds.length} options`);
      });
    }, 1000);
    return;
  }

  // Rest of the method remains the same...
  // For modify mode
  this.clientSubscription = this.subscriptions.find(sub =>
    sub.ClientId === client.Id && sub.Status !== 'Resilié'
  ) || null;

  if (this.clientSubscription) {
    const assignedLicenceId = this.clientSubscription.LicenceId;
    console.log('Found subscription for licence:', assignedLicenceId);

    // Load payment frequency and dates from subscription
    this.selectedPaymentFrequency = this.clientSubscription.PaymentFrequency || 'Mensuel';
    
    if (this.selectedPaymentFrequency === 'custom') {
      this.customDateFin = typeof this.clientSubscription.DateFin === 'string'
        ? this.formatDateForInput(this.clientSubscription.DateFin)
        : this.formatDateForInput(this.clientSubscription.DateFin || '');
    } else {
      this.customDateFin = '';
    }

    // Initialize all licences with empty sets first (after delay)
    setTimeout(() => {
      this.licences.forEach(licence => {
        this.checkedOptions[licence.Id] = new Set();
        this.initialCheckedOptions[licence.Id] = new Set();
      });

      // Load ONLY the actually subscribed options from the database
      this.subscribedOptionsApiService.getAll().subscribe({
        next: (subscribedOptions: SubscribedOptions[]) => {
          console.log('All subscribed options from DB:', subscribedOptions);
          
          // Filter to get only options for this specific subscription
          const thisSubscriptionOptions = subscribedOptions.filter(so =>
            so.SubscriptionId === this.clientSubscription!.Id
          );
          
          console.log('Options for this subscription:', thisSubscriptionOptions);
          
          // Get the option IDs that are actually subscribed AND linked to the assigned licence
          const actuallySubscribedOptionIds = thisSubscriptionOptions
            .filter(so => this.isOptionLinkedToLicence(assignedLicenceId, so.OptionId))
            .map(so => so.OptionId);

          console.log('Actually subscribed option IDs:', actuallySubscribedOptionIds);

          // Set ONLY these options as checked for the assigned licence
          this.checkedOptions[assignedLicenceId] = new Set(actuallySubscribedOptionIds);
          this.initialCheckedOptions[assignedLicenceId] = new Set(actuallySubscribedOptionIds);

          console.log(`Modify mode - Assigned licence ${assignedLicenceId}: checked ${actuallySubscribedOptionIds.length} options`);
          
          // Log the final state for verification
          this.licences.forEach(licence => {
            const checkedCount = this.checkedOptions[licence.Id].size;
            console.log(`Final state - Licence ${licence.Name} (${licence.Id}): ${checkedCount} options checked`);
          });
        },
        error: (error) => {
          console.error('Error loading subscribed options:', error);
        }
      });
    }, 1000);
  } else {
    // No subscription found - all options remain unchecked (empty sets) after delay
    console.log('No active subscription found - all options unchecked');
    this.selectedPaymentFrequency = 'Mensuel';
    this.customDateFin = '';
    
    // All licences will have empty sets after the timeout above
  }
}

  getSelectedOptionsForConfirmation(): Option[] {
  if (!this.selectedLicenseForConfirmation) return [];
  
  const licenceId = this.selectedLicenseForConfirmation.Id;
  const checkedSet = this.checkedOptions[licenceId] || new Set();
  
  // Return only options that are both linked to the licence AND checked
  return this.getOptionsForLicence(this.selectedLicenseForConfirmation)
    .filter(option => checkedSet.has(option.Id));
}

  openAddOptionPopup() {
    this.showAddOptionPopup = true;
    this.selectedLicenceForOption = this.licences.length > 0 ? this.licences[0].Id : '';
    this.newOptionName = '';
    this.newOptionPrice = null;
  }

  closeAddOptionPopup() {
    this.showAddOptionPopup = false;
  }

  confirmAddOption() {
    if (!this.selectedLicenceForOption || !this.newOptionName || this.newOptionPrice === null) {
      return;
    }

    const newOption: Partial<Option> = {
      Name: this.newOptionName,
      Price: this.newOptionPrice
    };

    this.optionApiService.create(newOption).subscribe({
      next: (createdOption) => {
        const licenceOption: Partial<LicenceOption> = {
          LicenceId: this.selectedLicenceForOption,
          OptionId: createdOption.Id
        };

        this.licenceOptionApiService.create(licenceOption).subscribe({
          next: () => {
            this.fetchOptions();
            this.fetchLicenceOptions();
            this.closeAddOptionPopup();
            this.showSuccessNotification = true;
            setTimeout(() => this.showSuccessNotification = false, 3000);
          },
          error: (error) => {
            console.error('Error creating licence option link:', error);
          }
        });
      },
      error: (error) => {
        console.error('Error creating option:', error);
      }
    });
  }

  openDeleteOptionPopup() {
    this.showDeleteOptionPopup = true;
    this.selectedLicenceForDeletion = this.licences.length > 0 ? this.licences[0].Id : '';
    this.optionSearchQuery = '';
    this.filteredOptionsForDeletion = [];
    this.selectedOptionForDeletion = null;
    this.loadOptionsForDeletion();
  }

  closeDeleteOptionPopup() {
    this.showDeleteOptionPopup = false;
  }

  onOptionSelectChange() {
    const selectedId = typeof this.selectedOptionForDeletion === 'string'
      ? this.selectedOptionForDeletion
      : (this.selectedOptionForDeletion && (this.selectedOptionForDeletion as any).Id)
        ? (this.selectedOptionForDeletion as any).Id
        : '';
    this.selectedOptionForDeletion = this.optionsForDeletion.find(opt => opt.Id === selectedId) || null;
  }

  isOptionDisabled(option: Option): boolean {
    return false;
  }

  loadOptionsForDeletion() {
    if (this.selectedLicenceForDeletion) {
      this.optionsForDeletion = this.options.filter(opt =>
        this.isOptionLinkedToLicence(this.selectedLicenceForDeletion, opt.Id)
      );
    } else {
      this.optionsForDeletion = [];
    }
  }

  licenseTypes = ['Basic', 'Standard', 'Premium'];

  filterOptionsForDeletion() {
    if (!this.selectedLicenceForDeletion) return;
    
    const query = this.optionSearchQuery.toLowerCase();
    const licenceId = this.selectedLicenceForDeletion;
    const allOptions = this.getOptionsForLicence({ Id: licenceId } as Licence);
    
    this.filteredOptionsForDeletion = allOptions
      .filter(option => option.Name.toLowerCase().includes(query))
      .slice(0, 5);
  }

  selectOptionForDeletion(option: Option) {
    this.selectedOptionForDeletion = option;
  }

  confirmDeleteOption() {
    if (!this.selectedOptionForDeletion) return;
    this.showDeleteConfirmationPopup = true;
  }

  closeDeleteConfirmationPopup() {
    this.showDeleteConfirmationPopup = false;
  }

  finalizeOptionDeletion() {
    if (!this.selectedOptionForDeletion || !this.selectedLicenceForDeletion) return;

    const licenceOption = this.licenceOptions.find(lo => 
      lo.LicenceId === this.selectedLicenceForDeletion && 
      lo.OptionId === this.selectedOptionForDeletion?.Id);

    if (licenceOption && this.selectedOptionForDeletion) {
      this.licenceOptionApiService.delete(licenceOption.Id).subscribe({
        next: () => {
          if (!this.selectedOptionForDeletion) return;
          this.optionApiService.delete(this.selectedOptionForDeletion.Id).subscribe({
            next: () => {
              this.fetchOptions();
              this.fetchLicenceOptions();
              this.showDeleteOptionPopup = false;
              this.showDeleteConfirmationPopup = false;
              this.showSuccessNotification = true;
              setTimeout(() => this.showSuccessNotification = false, 3000);
            },
            error: (error) => {
              console.error('Error deleting option:', error);
            }
          });
        },
        error: (error) => {
          console.error('Error deleting licence option link:', error);
        }
      });
    }
  }

  clearSelection() {
    if (this.selectedClient && this.clientSubscription) {
      const assignedLicenceId = this.clientSubscription.LicenceId;
      if (this.hasOptionChanges({ Id: assignedLicenceId } as Licence)) {
        this.licenceWithUnsavedChanges = this.licences.find(l => l.Id === assignedLicenceId) || null;
        this.showSaveOrDiscardPopup = true;
        return;
      }
    }

    this.licences.forEach(licence => {
      const allOptionIds = this.getOptionsForLicence(licence).map(opt => opt.Id);
      this.checkedOptions[licence.Id] = new Set(allOptionIds);
    });
    
    this.initialCheckedOptions = {};
    this.selectedClient = null;
    
    // Reset payment frequency and dates when clearing selection
    this.selectedPaymentFrequency = 'Mensuel';
    this.customDateFin = '';
  }

  confirmSaveAndClearSelection() {
    if (this.licenceWithUnsavedChanges) {
      this.modifyingLicence = this.licenceWithUnsavedChanges;
      const licenceId = this.modifyingLicence ? this.modifyingLicence.Id : '';
      this.oldOptions = this.getOptionsForLicence(this.modifyingLicence!).filter(opt =>
        licenceId && this.initialCheckedOptions[licenceId]?.has(opt.Id)
      );
      this.showModifyPopup = true;
      this.showSaveOrDiscardPopup = false;
      this.licenceWithUnsavedChanges = null;
    }
  }

 public selectLicense(licence: Licence) {
  if (!this.selectedClient) {
    this.showChooseClientError = true;
    setTimeout(() => { this.showChooseClientError = false; }, 2500);
    return;
  }
  
  const checked = this.checkedOptions[licence.Id] || new Set();
  console.log('selectLicense - Checked options:', Array.from(checked));
  console.log('selectLicense - Available options:', this.getOptionsForLicence(licence).map(o => o.Id));
  
  if (checked.size === 0) {
    this.showNoOptionCheckedPopup = true;
    this._pendingRestoreLicence = licence;
    this._pendingRestoreType = 'affecter';
    return;
  }
  
  this.selectedLicenseForConfirmation = licence;
  
  // Log what will be shown in confirmation
  console.log('Will show these options in confirmation:', this.getSelectedOptionsForConfirmation());
  
  this.showConfirmationPopup = true;
}

clearAllOptionStates(): void {
  // Add 1 second delay before clearing the options
  setTimeout(() => {
    this.checkedOptions = {};
    this.initialCheckedOptions = {};
    console.log('Cleared all option states after 1 second delay');
  }, 2000);
}

  public discardChangesAndClearSelection() {
    Object.keys(this.initialCheckedOptions).forEach(licenceId => {
      this.checkedOptions[licenceId] = new Set<string>(Array.from(this.initialCheckedOptions[licenceId]));
    });
    this.showSaveOrDiscardPopup = false;
    this.modifyingLicence = null;
    this.selectedClient = null;
    setTimeout(() => {
      window.location.reload();
    }, 500);
  }

  showModifyView(client: Client, licenceId?: string) {
  this.cardMode = 'modify';
  this.showCard = true;
  this.showTable = false;
  this.selectedClient = client;
  this.searchQuery = '';
  this.showDropdown = false;
  this.filteredClients = [];

  // Find all active subscriptions for this client (not "Resilié")
  const clientSubscriptions = this.subscriptions.filter(
    sub => sub.ClientId === client.Id && sub.Status !== 'Resilié'
  );

  // If licenceId is provided (from table row), pick the subscription for that licence
  let targetSubscription: Subscription | null = null;
  if (licenceId) {
    targetSubscription = clientSubscriptions.find(sub => sub.LicenceId === licenceId) || null;
  }
  // If not found, fallback to first subscription
  if (!targetSubscription && clientSubscriptions.length > 0) {
    targetSubscription = clientSubscriptions[0];
  }

  // Set the clientSubscription to the found subscription (or null)
  this.clientSubscription = targetSubscription;

  // Reset checkedOptions and initialCheckedOptions for all licences to empty sets
  this.licences.forEach(licence => {
    this.checkedOptions[licence.Id] = new Set();
    this.initialCheckedOptions[licence.Id] = new Set();
  });

  // If we have a subscription, load its options and payment info
  if (this.clientSubscription) {
    const assignedLicenceId = this.clientSubscription.LicenceId;
    this.selectedPaymentFrequency = this.clientSubscription.PaymentFrequency || 'Mensuel';
    if (this.selectedPaymentFrequency === 'custom') {
      this.customDateFin = typeof this.clientSubscription.DateFin === 'string'
        ? this.formatDateForInput(this.clientSubscription.DateFin)
        : this.formatDateForInput(this.clientSubscription.DateFin || '');
    } else {
      this.customDateFin = '';
    }

    // Load subscribed options for this subscription
    this.subscribedOptionsApiService.getAll().subscribe({
      next: (subscribedOptions: SubscribedOptions[]) => {
        const checkedOptionIds = subscribedOptions
          .filter(so =>
            so.SubscriptionId === this.clientSubscription!.Id &&
            this.isOptionLinkedToLicence(assignedLicenceId, so.OptionId))
          .map(so => so.OptionId);

        this.checkedOptions[assignedLicenceId] = new Set(checkedOptionIds);
        this.initialCheckedOptions[assignedLicenceId] = new Set(checkedOptionIds);

        // FIXED: For other licences, keep them empty (unchecked) for modify mode
        // Don't auto-check all options for non-assigned licences
      }
    });
  } else {
    // No subscription - reset to defaults for modify mode (all empty)
    this.selectedPaymentFrequency = 'Mensuel';
    this.customDateFin = '';
    // checkedOptions and initialCheckedOptions are already set to empty sets above
  }
}

  // Fixed: Check if licence is assigned by comparing with clientSubscription
  isLicenceAssignedToClient(licence: Licence): boolean {
    if (!this.selectedClient || !this.clientSubscription) return false;
    return this.clientSubscription.LicenceId === licence.Id && this.clientSubscription.Status !== 'Resilié';
  }

  closeNoOptionCheckedPopup() {
    this.showNoOptionCheckedPopup = false;
    if (this._pendingRestoreLicence) {
      const licence = this._pendingRestoreLicence;
      const initial = this.initialCheckedOptions[licence.Id] || new Set();
      this.checkedOptions[licence.Id] = new Set(Array.from(initial));
      this._pendingRestoreLicence = null;
      this._pendingRestoreType = null;
    }
  }

  getOldOptionsForModifying(): Option[] {
    return this.oldOptions;
  }

  getNewOptionsForModifying(): Option[] {
    if (!this.modifyingLicence) return [];
    return this.getOptionsForLicence(this.modifyingLicence).filter(opt =>
      this.checkedOptions[this.modifyingLicence!.Id]?.has(opt.Id)
    );
  }

  getOldTotalForModifying(): number {
    if (
      this.modifyingLicence &&
      this.clientSubscription &&
      this.clientSubscription.LicenceId === this.modifyingLicence.Id &&
      typeof this.clientSubscription.Price === 'number'
    ) {
      return this.clientSubscription.Price;
    }
    // Fallback: recalculate based on frequency
    if (!this.modifyingLicence) return 0;
    const months = this.getMonthsForPaymentFrequency(this.selectedPaymentFrequency);
    return this.getOldOptionsForModifying().reduce((sum, opt) => sum + ((opt.Price || 0) * months), 0);
  }

  getPriceToPayForModification(): number {
    if (!this.modifyingLicence) return 0;
    const oldSet = this.initialCheckedOptions[this.modifyingLicence.Id] || new Set();
    const newSet = this.checkedOptions[this.modifyingLicence.Id] || new Set();
    let price = 0;

    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const currentDay = today.getDate();
    const daysRemaining = daysInMonth - currentDay + 1;

    for (const id of newSet) {
      if (!oldSet.has(id)) {
        const option = this.options.find(opt => opt.Id === id);
        if (option && typeof option.Price === 'number') {
          price += (option.Price / daysInMonth) * daysRemaining;
        }
      }
    }
    return price;
  }

  getPriceToRefundForModification(): number {
    if (!this.modifyingLicence) return 0;
    const oldSet = this.initialCheckedOptions[this.modifyingLicence.Id] || new Set();
    const newSet = this.checkedOptions[this.modifyingLicence.Id] || new Set();
    let refund = 0;

    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const currentDay = today.getDate();
    const daysRemaining = daysInMonth - currentDay + 1;

    for (const id of oldSet) {
      if (!newSet.has(id)) {
        const option = this.options.find(opt => opt.Id === id);
        if (option && typeof option.Price === 'number') {
          refund += (option.Price / daysInMonth) * daysRemaining;
        }
      }
    }
    return refund;
  }

  getNewTotalWithOldForModifying(): number {
    // If DateFin is finished, return the normal value (all checked options for the full period)
    if (this.modifyingLicence && this.clientSubscription) {
      const today = new Date();
      const dateFinRaw = this.clientSubscription.DateFin;
      if (dateFinRaw) {
        // Fix: Safely convert dateFinRaw to string for Date constructor
        const dateFin = new Date(typeof dateFinRaw === 'string' ? dateFinRaw : dateFinRaw.toString());
        today.setHours(0,0,0,0);
        dateFin.setHours(0,0,0,0);
        if (today >= dateFin) {
          const months = this.getMonthsForPaymentFrequency(this.selectedPaymentFrequency);
          return this.getNewOptionsForModifying().reduce((sum, opt) => sum + ((opt.Price || 0) * months), 0);
        }
      }
    }
    // Otherwise, use: old + to pay - to refund
    return (
      this.getOldTotalForModifying() +
      this.getPriceToPayForModification() -
      this.getPriceToRefundForModification()
    );
  }

  getModificationType(): 'add' | 'remove' | 'both' | null {
    if (!this.modifyingLicence) return null;
    const oldSet = this.initialCheckedOptions[this.modifyingLicence.Id] || new Set();
    const newSet = this.checkedOptions[this.modifyingLicence.Id] || new Set();
    let added = false, removed = false;
    for (const id of newSet) {
      if (!oldSet.has(id)) added = true;
    }
    for (const id of oldSet) {
      if (!newSet.has(id)) removed = true;
    }
    if (added && removed) return 'both';
    if (added) return 'add';
    if (removed) return 'remove';
    return null;
  }

  confirmSaveModifiedLicenceOptions() {
    if (
      !this.modifyingLicence ||
      !this.selectedClient ||
      !this.clientSubscription ||
      this.clientSubscription.LicenceId !== this.modifyingLicence.Id
    ) {
      this.showModifyPopup = false;
      this.modifyingLicence = null;
      this.showCard = false;
      return;
    }

    // Find the subscription to update by its Id (the one being modified)
    const subscriptionId = this.clientSubscription.Id;
    const updatedSubscription = {
      ...this.clientSubscription,
      Price: this.getNewTotalWithOldForModifying(),
      DateDebut: this.clientSubscription.DateDebut,
      DateFin: this.clientSubscription.DateFin,
      Status: 'En attente' // always set to "En attente" on modification
    };

    // Only use checked options (those shown in popup)
    const licenceId = this.modifyingLicence.Id;
    const checkedSet = this.checkedOptions[licenceId] || new Set();
    const checkedOptionIds = Array.from(checkedSet);

    // Update the subscription by its Id
    this.subscriptionApiService.update({ ...updatedSubscription, Id: subscriptionId }).subscribe({
      next: () => {
        // Save only the checked options for this subscriptionId
        this.saveSubscribedOptionsForLicence(subscriptionId, checkedOptionIds).then(() => {
          this.fetchSubscriptions();
          this.fetchClients().then(() => {
            const refreshedClient = this.clients.find(c => c.Id === this.selectedClient?.Id);
            if (refreshedClient) {
              this.selectClient(refreshedClient);
            }
          });

          this.showModifyPopup = false;
          this.modifyingLicence = null;
          this.showSaveNotification = true;
          this.showCard = false;
          setTimeout(() => this.showSaveNotification = false, 3000);
        });
      },
      error: (error) => {
        console.error('Error updating subscription:', error);
        this.showModifyPopup = false;
        this.modifyingLicence = null;
        this.showCard = false;
      }
    });
  }

  cancelModifyLicence() {
    Object.keys(this.initialCheckedOptions).forEach(licenceId => {
      this.checkedOptions[licenceId] = new Set<string>(Array.from(this.initialCheckedOptions[licenceId]));
    });
    this.showModifyPopup = false;
    this.modifyingLicence = null;
  }

  saveOptionsForLicence(licence: Licence) {
    if (!this.selectedClient || !this.clientSubscription) return;

    const subscriptionId = this.clientSubscription.Id;
    const optionIds = Array.from(this.checkedOptions[licence.Id] || []);

    this.subscribedOptionsApiService.getAll().subscribe({
      next: (allSubscribedOptions) => {
        const toDelete = allSubscribedOptions.filter(
          so => so.SubscriptionId === subscriptionId
        );
        let deleteCount = 0;
        if (toDelete.length === 0) {
          this.createCheckedOptions(subscriptionId, optionIds);
        } else {
          toDelete.forEach(so => {
            this.subscribedOptionsApiService.delete(so.Id).subscribe({
              next: () => {
                deleteCount++;
                if (deleteCount === toDelete.length) this.createCheckedOptions(subscriptionId, optionIds);
              },
              error: () => {
                deleteCount++;
                if (deleteCount === toDelete.length) this.createCheckedOptions(subscriptionId, optionIds);
              }
            });
          });
        }
        const self = this;
        function createAll() {
          optionIds.forEach(optionId => {
            const subscribedOption: Partial<SubscribedOptions> = {
              SubscriptionId: subscriptionId,
              OptionId: optionId,
              checked: true
            };
            self.subscribedOptionsApiService.create(subscribedOption).subscribe();
          });
          
          self.initialCheckedOptions[licence.Id] = new Set(optionIds);
          self.assignedLicenceChanges[licence.Id] = false;
          self.showSaveNotification = true;
          setTimeout(() => self.showSaveNotification = false, 5000);
        }
      }
    });
  }

  async confirmLicenseApplication() {
  if (!this.selectedClient || !this.selectedLicenseForConfirmation) {
    return;
  }
  
  // Get the actual selected options
  const selectedOptions = this.getSelectedOptionsForConfirmation();
  
  if (selectedOptions.length === 0) {
    console.error('No options selected for licence');
    this.showNoOptionCheckedPopup = true;
    return;
  }
  
  this.spinner.show();
  try {
    let dateDebut: Date, dateFin: Date;
    if (this.selectedPaymentFrequency === 'custom' || this.selectedPaymentFrequency === 'Personnalisé') {
      dateDebut = new Date();
      dateFin = this.customDateFin ? new Date(this.customDateFin) : new Date();
    } else {
      dateDebut = new Date();
      dateFin = this.calculateDateFin(dateDebut, this.selectedPaymentFrequency, this.customMonths);
    }
    const dateDebutStr = this.formatDate(dateDebut);
    const dateFinStr = this.formatDate(dateFin);

    const clientId = this.selectedClient.Id;
    const licenceId = this.selectedLicenseForConfirmation.Id;

    // Use only the IDs of actually selected options
    const selectedOptionIds = selectedOptions.map(option => option.Id);

    // Calculate total based on frequency using only selected options
    const totalPrice = selectedOptions.reduce((sum, opt) => {
      const months = this.getMonthsForPaymentFrequency(this.selectedPaymentFrequency);
      return sum + ((opt.Price || 0) * months);
    }, 0);

    const subscription = {
      DateDebut: dateDebutStr,
      DateFin: dateFinStr,
      ClientId: clientId,
      LicenceId: licenceId,
      Price: totalPrice,
      Status: 'En attente',
      PaymentFrequency: this.selectedPaymentFrequency
    };
    
    const createdSub = await this.subscriptionApiService.create(subscription).toPromise();
    if (createdSub) {
      // Save only the actually selected options
      await this.saveSubscribedOptionsForLicence(createdSub.Id, selectedOptionIds);
    }

    this.showSuccessNotification = true;
    this.showConfirmationPopup = false;
    this.selectedLicenseForConfirmation = null;
    await Promise.all([
      this.fetchSubscriptions(),
      this.fetchClients()
    ]);
    const refreshedClient = this.clients.find(c => c.Id === this.selectedClient?.Id);
    if (refreshedClient) {
      this.selectClient(refreshedClient);
    }
  } catch (error) {
    console.error('Error applying license:', error);
  } finally {
    this.spinner.hide();
  }
}

  // Helper to get number of months for a payment frequency
  getMonthsForPaymentFrequency(paymentFrequency: string): number {
    const freq = this.paymentFrequencies.find(f => f.value === paymentFrequency);
    if (!freq) return 1;
    if (freq.value === 'Personnalisé') {
      // Calculate months between now and customDateFin
      if (!this.customDateFin) return 1;
      const now = new Date();
      const end = new Date(this.customDateFin);
      let months = (end.getFullYear() - now.getFullYear()) * 12 + (end.getMonth() - now.getMonth());
      if (end.getDate() >= now.getDate()) months += 1;
      return Math.max(months, 1);
    }
    return freq.months;
  }

  // Calculate total options price based on PaymentFrequency
  getOptionsTotalForFrequency(licence: Licence, checkedSet: Set<string>, paymentFrequency: string): number {
    const months = this.getMonthsForPaymentFrequency(paymentFrequency);
    return this.getOptionsForLicence(licence)
      .filter(opt => checkedSet.has(opt.Id))
      .reduce((sum, opt) => sum + ((opt.Price || 0) * months), 0);
  }


private calculateUpgradePrices() {
  if (!this.selectedLicenseForConfirmation || !this.oldLicence || !this.clientSubscription) {
    this.proratedOldForUpgrade = 0;
    this.proratedNewForUpgrade = 0;
    this.proratedDiferencePay = 0;
    this.proratedDiferenceReturn = 0;
    this.proratedTotalForUpgrade = 0;
    return;
  }

  let dateFin: Date;
  if (this.selectedPaymentFrequency === 'custom' || this.selectedPaymentFrequency === 'Personnalisé') {
    dateFin = this.customDateFin ? new Date(this.customDateFin) : new Date();
  } else {
    dateFin = this.calculateDateFin(new Date(), this.selectedPaymentFrequency, this.customMonths);
  }
  const today = new Date();
  today.setHours(0,0,0,0);
  dateFin.setHours(0,0,0,0);

  // Get the actual old price from subscription.Price
  const oldPriceFromSubscription = this.clientSubscription.Price || 0;
  
  const checkedSet = this.checkedOptions[this.selectedLicenseForConfirmation.Id] || new Set();
  const newOptions = this.getOptionsForLicence(this.selectedLicenseForConfirmation).filter(opt => checkedSet.has(opt.Id));

  const months = this.getMonthsForPaymentFrequency(this.selectedPaymentFrequency);
  let newTotalPrice = newOptions.reduce((sum, opt) => sum + ((opt.Price || 0) * months), 0);

  let isFinished = today >= dateFin;
  let priceToPay = 0;
  let priceToReturn = 0;

  if (isFinished) {
    // When dateFin is finished, compare full prices
    if (oldPriceFromSubscription < newTotalPrice) {
      priceToPay = newTotalPrice - oldPriceFromSubscription;
      priceToReturn = 0;
    } else if (oldPriceFromSubscription > newTotalPrice) {
      priceToPay = 0;
      priceToReturn = oldPriceFromSubscription - newTotalPrice;
    } else {
      priceToPay = 0;
      priceToReturn = 0;
    }
    this.addedOptionsProrata = [];
  } else {
    // Calculate prorata for remaining days in the current month
    const daysInMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0).getDate();
    const currentDay = today.getDate();
    const daysRemaining = daysInMonth - currentDay + 1;

    // Calculate new total with prorata
    const newTotalWithProrata = newOptions.reduce((sum, opt) => {
      const pricePerMonth = opt.Price || 0;
      const prorata = pricePerMonth * (daysRemaining / daysInMonth);
      return sum + prorata;
    }, 0);

    if (oldPriceFromSubscription < newTotalWithProrata) {
      priceToPay = newTotalWithProrata - oldPriceFromSubscription;
      priceToReturn = 0;
    } else if (oldPriceFromSubscription > newTotalWithProrata) {
      priceToPay = 0;
      priceToReturn = oldPriceFromSubscription - newTotalWithProrata;
    } else {
      priceToPay = 0;
      priceToReturn = 0;
    }

    // For HTML: expose the new options and their prorata
    this.addedOptionsProrata = newOptions.map(opt => {
      const pricePerMonth = opt.Price || 0;
      const prorata = pricePerMonth * (daysRemaining / daysInMonth);
      return {
        ...opt,
        prorata: prorata,
        days: daysRemaining
      };
    });

    newTotalPrice = newTotalWithProrata;
  }

  this.proratedOldForUpgrade = oldPriceFromSubscription;
  this.proratedNewForUpgrade = newTotalPrice;
  this.proratedDiferencePay = priceToPay;
  this.proratedDiferenceReturn = priceToReturn;
  this.proratedTotalForUpgrade = newTotalPrice;
}

  // For HTML: expose addedOptionsProrata
  addedOptionsProrata: Array<{ Id: string, Name: string, prorata: number, days: number }> = [];

  // Cancel licence for client
  cancelLicenceForClient(licence: Licence) {
    this.licenceToCancel = licence;
    this.showCancelPopup = true;
  }

  // Open modify popup for licence
  openModifyPopup(licence: Licence) {
    if (!this.selectedClient || !this.clientSubscription) return;
    const checked = this.checkedOptions[licence.Id] || new Set();
    if (checked.size === 0) {
      this.showNoOptionCheckedPopup = true;
      this._pendingRestoreLicence = licence;
      this._pendingRestoreType = 'modify';
      return;
    }
    this.modifyingLicence = licence;
    this.oldOptions = this.getOptionsForLicence(licence).filter(opt =>
      this.initialCheckedOptions[licence.Id]?.has(opt.Id)
    );
    this.showModifyPopup = true;
  }

  // Upgrade licence for client
  upgradeLicenceForClient(licence: Licence) {
    if (!this.selectedClient || !this.clientSubscription) return;
    const checked = this.checkedOptions[licence.Id] || new Set();
    if (checked.size === 0) {
      this.showNoOptionCheckedPopup = true;
      this._pendingRestoreLicence = licence;
      this._pendingRestoreType = 'upgrade';
      return;
    }
    this.selectedLicenseForConfirmation = licence;
    this.oldLicence = this.licences.find(l => l.Id === this.clientSubscription?.LicenceId) || null;
    if (this.clientSubscription) {
      this.subscribedOptionsApiService.getAll().subscribe({
        next: (subscribedOptions: SubscribedOptions[]) => {
          const checkedOptionIds = subscribedOptions
            .filter(so =>
              so.SubscriptionId === this.clientSubscription!.Id &&
              this.isOptionLinkedToLicence(this.clientSubscription!.LicenceId, so.OptionId))
            .map(so => so.OptionId);
          this.oldOptions = this.getOptionsForLicence(this.oldLicence!).filter(opt =>
            checkedOptionIds.includes(opt.Id)
          );
          this.oldOptionsLoaded = true;
          this.calculateUpgradePrices();
          this.showUpgradePopup = true;
        }
      });
    }
  }

  // Cancel/close popups and notifications
  cancelLicenseApplication() {
    this.showConfirmationPopup = false;
    this.selectedLicenseForConfirmation = null;
  }
  closeCancelPopup() {
    this.showCancelPopup = false;
    this.licenceToCancel = null;
    this.showCancelNotification = false;
  }
 confirmCancelLicenceForClient() {
  if (!this.licenceToCancel || !this.selectedClient) return;

  // Find the subscription for this client and licence
  const subscription = this.subscriptions.find(
    sub => sub.ClientId === this.selectedClient!.Id && sub.LicenceId === this.licenceToCancel!.Id
  );
  
  if (!subscription) {
    console.error('No subscription found for client and licence');
    this.showCancelPopup = false;
    return;
  }

  console.log('Found subscription to cancel:', subscription);
  console.log('Current status:', subscription.Status);

  this.spinner.show();
  
  // Create updated subscription object with explicit Id
  const updatedSubscription = {
    Id: subscription.Id, // Explicitly set the Id first
    ClientId: subscription.ClientId,
    LicenceId: subscription.LicenceId,
    DateDebut: subscription.DateDebut,
    DateFin: subscription.DateFin,
    Price: subscription.Price,
    PaymentFrequency: subscription.PaymentFrequency,
    Status: 'Resilié' // Change status to "Resilié"
  };
  
  console.log('Updating subscription with:', updatedSubscription);
  
  this.subscriptionApiService.update(updatedSubscription).subscribe({
    next: (response) => {
      console.log('Subscription update response:', response);
      console.log('Subscription status updated to Resilié successfully');
      
      // Close the cancel popup
      this.showCancelPopup = false;
      this.licenceToCancel = null;
      
      // Show success notification
      this.showCancelNotification = true;
      setTimeout(() => this.showCancelNotification = false, 3000);
      
      // Refresh data and update UI
      Promise.all([
        this.fetchSubscriptions(),
        this.fetchClients()
      ]).then(() => {
        console.log('Data refreshed after cancellation');
        
        // Update the local subscriptions array immediately to reflect the change
        const localSubIndex = this.subscriptions.findIndex(s => s.Id === subscription.Id);
        if (localSubIndex !== -1) {
          this.subscriptions[localSubIndex].Status = 'Resilié';
        }
        
        // Update table rows
        this.updateSubscriptionTableRows();
        
        // Close the card view
        this.showCard = false;
        
        // Clear selected client and reset state
        this.selectedClient = null;
        this.clientSubscription = null;
        this.checkedOptions = {};
        this.initialCheckedOptions = {};
        
        console.log('UI updated after cancellation');
      }).catch(error => {
        console.error('Error refreshing data after cancellation:', error);
        this.showCard = false; // Still close the card even if refresh fails
      });
    },
    error: (error) => {
      console.error('Error cancelling licence:', error);
      console.error('Error details:', error);
      this.showCancelPopup = false;
      this.licenceToCancel = null;
      // Don't close showCard on error so user can retry
    },
    complete: () => {
      this.spinner.hide();
    }
  });
}

  closeSuccessNotification() {
    this.showSuccessNotification = false;
  }
  cancelUpgradePopup() {
    this.showUpgradePopup = false;
    this.selectedLicenseForConfirmation = null;
    this.oldLicence = null;
    this.oldOptions = [];
    this.oldOptionsLoaded = false;
  }
  getLicenceOptionsTotal(licence: Licence): number {
  if (!licence) return 0;
  
  const licenceId = licence.Id;
  const checkedSet = this.checkedOptions[licenceId] || new Set();
  
  // Only calculate total for options that are both linked to licence AND checked
  return this.getOptionsForLicence(licence)
    .filter(opt => checkedSet.has(opt.Id))
    .reduce((sum, opt) => sum + (opt.Price || 0), 0);
  }
  getNewOptionsForUpgrade(): Option[] {
    if (!this.selectedLicenseForConfirmation) return [];
    const checkedSet = this.checkedOptions[this.selectedLicenseForConfirmation.Id] || new Set();
    return this.getOptionsForLicence(this.selectedLicenseForConfirmation).filter(opt => checkedSet.has(opt.Id));
  }

  debugCheckedOptions(licence: Licence): void {
  console.log('=== DEBUG CHECKED OPTIONS ===');
  console.log('Licence:', licence.Name, licence.Id);
  console.log('Checked Options Set:', this.checkedOptions[licence.Id]);
  console.log('Initial Checked Options Set:', this.initialCheckedOptions[licence.Id]);
  console.log('All Options for Licence:', this.getOptionsForLicence(licence));
  console.log('Selected Options:', this.getSelectedOptionsForConfirmation());
  console.log('Card Mode:', this.cardMode);
  console.log('=== END DEBUG ===');
}

  // Card pagination
  prevPage() {
    if (this.currentPage > 0) {
      this.cardAnimDirection = 'prev';
      this.currentPage--;
      this.scrollToTop();
    }
  }
  nextPage() {
    if (this.currentPage < this.totalPages - 1) {
      this.cardAnimDirection = 'next';
      this.currentPage++;
      this.scrollToTop();
    }
  }
  scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  // Add missing loadAssignedLicences (used in ngOnInit)
  private loadAssignedLicences() {
    const data = localStorage.getItem('assignedLicences');
    if (data) {
      try { this.assignedLicences = JSON.parse(data); }
      catch { this.assignedLicences = {}; }
    }
  }

  // Add missing methods and fix implicit any for template and TS errors

  // Touch/drag handlers for card pagination
  onTouchStart(event: TouchEvent | MouseEvent) {
    if (event instanceof TouchEvent) {
      this.swipeStartX = event.touches[0].clientX;
    } else if (event instanceof MouseEvent) {
      this.swipeStartX = event.clientX;
    }
  }
  onTouchEnd(event: TouchEvent | MouseEvent) {
    if (this.swipeStartX === null) return;
    let endX: number;
    if (event instanceof TouchEvent) {
      endX = event.changedTouches[0].clientX;
    } else if (event instanceof MouseEvent) {
      endX = event.clientX;
    } else {
      return;
    }
    const deltaX = endX - this.swipeStartX;
    if (Math.abs(deltaX) > 60) {
      if (deltaX > 0) {
        this.prevPage();
      } else {
        this.nextPage();
      }
    }
  }

  // Card mode helpers for template
  isAffecterCard(): boolean {
    return this.cardMode === 'add';
  }
  isModifyCard(): boolean {
    return this.cardMode === 'modify';
  }

  // Checkbox checked state for options
  isOptionChecked(licence: Licence, optionId: string): boolean {
    return this.checkedOptions[licence.Id]?.has(optionId) ?? false;
  }

  // Subscription cancelled state
  isSubscriptionCancelled(licence: Licence): boolean {
    if (!this.selectedClient || !this.clientSubscription) return false;
    if (this.clientSubscription.LicenceId !== licence.Id) return false;
    const status = this.clientSubscription.Status;
    return typeof status === 'string' && status.toLowerCase() === 'resilié';
  }

  // Get options for a licence
  getOptionsForLicence(licence: Licence): Option[] {
    if (!licence) return [];
    const linkedOptionIds = this.licenceOptions
      .filter((lo: LicenceOption) => lo.LicenceId === licence.Id)
      .map((lo: LicenceOption) => lo.OptionId);
    return this.options.filter((opt: Option) => linkedOptionIds.includes(opt.Id));
  }

  // Check if option is linked to licence
  isOptionLinkedToLicence(licenceId: string, optionId: string): boolean {
    return this.licenceOptions.some((lo: LicenceOption) => lo.LicenceId === licenceId && lo.OptionId === optionId);
  }

  // Has option changes
  hasOptionChanges(licence: Licence): boolean {
    const checked = this.checkedOptions[licence.Id] || new Set();
    const initial = this.initialCheckedOptions[licence.Id] || new Set();
    if (checked.size !== initial.size) return true;
    for (const id of checked) {
      if (!initial.has(id)) return true;
    }
    return false;
  }

  // Format date for display
  formatDate(date: Date | string | null): string {
    if (!date) return '';
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    return `${day}/${month}/${year}`;
  }

  // Format date for input[type=date]
  formatDateForInput(date: Date | string | null): string {
    if (!date) return '';
    const d = new Date(date);
    if (isNaN(d.getTime())) return '';
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // Format date for backend (yyyy-MM-dd)
  formatDateForBackend(date: Date | string | null): string {
    if (!date) return '';
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // Calculate date fin based on frequency
  calculateDateFin(dateDebut: Date, paymentFrequency: string, customMonths: number): Date {
    let monthsToAdd = 1;
    switch (paymentFrequency) {
      case 'Mensuel':
        monthsToAdd = 1;
        break;
      case 'Trimestriel':
        monthsToAdd = 3;
        break;
      case 'Semestriel':
        monthsToAdd = 6;
        break;
      case 'Annuel':
        monthsToAdd = 12;
        break;
      case 'Biannuel':
        monthsToAdd = 24;
        break;
      case 'Quateriel':
        monthsToAdd = 4;
        break;
      case 'custom':
      case 'Personnalisé':
        monthsToAdd = customMonths > 0 ? customMonths : 1;
        break;
      default:
        monthsToAdd = 1;
    }
    const result = new Date(dateDebut);
    const originalDay = result.getDate();
    let newMonth = result.getMonth() + monthsToAdd;
    let newYear = result.getFullYear();
    newYear += Math.floor(newMonth / 12);
    newMonth = newMonth % 12;
    result.setFullYear(newYear, newMonth, 1);
    const lastDay = new Date(result.getFullYear(), result.getMonth() + 1, 0).getDate();
    result.setDate(Math.min(originalDay, lastDay));
    return result;
  }

  // Save only the checked options for a subscription
  private async saveSubscribedOptionsForLicence(subscriptionId: string, checkedOptionIds: string[]) {
    return new Promise<void>((resolve) => {
      this.subscribedOptionsApiService.getAll().subscribe({
        next: (allSubscribedOptions: SubscribedOptions[]) => {
          const toDelete = allSubscribedOptions.filter(
            (so: SubscribedOptions) => so.SubscriptionId === subscriptionId
          );
          let deleteCount = 0;
          if (toDelete.length === 0) {
            checkedOptionIds.forEach(optionId => {
              const subscribedOption: Partial<SubscribedOptions> = {
                SubscriptionId: subscriptionId,
                OptionId: optionId,
                checked: true
              };
              this.subscribedOptionsApiService.create(subscribedOption).subscribe();
            });
            resolve();
          } else {
            toDelete.forEach((so: SubscribedOptions) => {
              this.subscribedOptionsApiService.delete(so.Id).subscribe({
                next: () => {
                  deleteCount++;
                  if (deleteCount === toDelete.length) {
                    checkedOptionIds.forEach(optionId => {
                      const subscribedOption: Partial<SubscribedOptions> = {
                        SubscriptionId: subscriptionId,
                        OptionId: optionId,
                        checked: true
                      };
                      this.subscribedOptionsApiService.create(subscribedOption).subscribe();
                    });
                    resolve();
                  }
                },
                error: () => {
                  deleteCount++;
                  if (deleteCount === toDelete.length) {
                    checkedOptionIds.forEach(optionId => {
                      const subscribedOption: Partial<SubscribedOptions> = {
                        SubscriptionId: subscriptionId,
                        OptionId: optionId,
                        checked: true
                      };
                      this.subscribedOptionsApiService.create(subscribedOption).subscribe();
                    });
                    resolve();
                  }
                }
              });
            });
          }
        }
      });
    });
  }

  // Helper to create checked options (used in saveOptionsForLicence)
  private createCheckedOptions(subscriptionId: string, optionIds: string[]) {
    optionIds.forEach(optionId => {
      const subscribedOption: Partial<SubscribedOptions> = {
        SubscriptionId: subscriptionId,
        OptionId: optionId,
        checked: true
      };
      this.subscribedOptionsApiService.create(subscribedOption).subscribe();
    });
  }

  // Only keep "Mensuel" and "Annuel" in paymentFrequencies
  paymentFrequencies: { label: string, value: string, months: number }[] = [
    { label: 'Mensuel', value: 'Mensuel', months: 1 },
    // { label: 'Trimestriel', value: 'Trimestriel', months: 3 },
    // { label: 'Semestriel', value: 'Semestriel', months: 6 },
    { label: 'Annuel', value: 'Annuel', months: 12 },
    // { label: 'Biannuel', value: 'Biannuel', months: 24 },
    // { label: 'Quateriel', value: 'Quateriel', months: 4 },
    // { label: 'Personnalisé', value: 'Personnalisé', months: 0 }
  ];

  // Add missing getActiveTableRows for table data
 getActiveTableRows(): any[] {
  this.activeTableRows = this.filteredTableRows.filter(row => row.Status !== 'Resilié');
  return this.activeTableRows;
}

  // Add missing onTableAction for generic table actions
  // Add missing onTableAction for generic table actions
  onTableAction(event: { action: string; row: any }) {
    if (event.action === 'Annuler') {
      // Find the subscription using the SubscriptionId from the row
      const subscription = this.subscriptions.find(sub => sub.Id === event.row.SubscriptionId);
      
      if (!subscription) {
        console.error('No subscription found for ID:', event.row.SubscriptionId);
        return;
      }

      console.log('Cancelling subscription:', subscription);
      this.spinner.show();
      
      // Create updated subscription object with "Resilié" status
      const updatedSubscription = {
        Id: subscription.Id,
        ClientId: subscription.ClientId,
        LicenceId: subscription.LicenceId,
        DateDebut: subscription.DateDebut,
        DateFin: subscription.DateFin,
        Price: subscription.Price,
        PaymentFrequency: subscription.PaymentFrequency,
        Status: 'Resilié'
      };
      
      this.subscriptionApiService.update(updatedSubscription).subscribe({
        next: (response) => {
          console.log('Subscription cancelled successfully:', response);
          
          // Show success notification
          this.showCancelNotification = true;
          setTimeout(() => this.showCancelNotification = false, 3000);
          
          // Refresh data to update the table
          Promise.all([
            this.fetchSubscriptions(),
            this.fetchClients()
          ]).then(() => {
            // Update the local subscriptions array immediately
            const localSubIndex = this.subscriptions.findIndex(s => s.Id === subscription.Id);
            if (localSubIndex !== -1) {
              this.subscriptions[localSubIndex].Status = 'Resilié';
            }
            
            // Update table rows to reflect the change
            this.updateSubscriptionTableRows();
            console.log('Table updated after cancellation');
          }).catch(error => {
            console.error('Error refreshing data after cancellation:', error);
          });
        },
        error: (error) => {
          console.error('Error cancelling subscription:', error);
        },
        complete: () => {
          this.spinner.hide();
        }
      });
      return;
    }
    
    if (event.action === 'Modifier') {
      const client = this.clients.find(c => c.Name === event.row.ClientName || c.Id === event.row.ClientId);
      const licence = this.licences.find(l =>
        l.Name === event.row.LicenceName || l.Id === event.row.LicenceId
      );
      if (client && licence) {
        this.showModifyView(client, licence.Id);
      }
      return;
    }
    
    // Handle other actions
    this.tableActionType = event.action as any;
    this.tableActionRow = event.row;
    this.showTableActionPopup = true;
  }

  // Add missing filterClients for client search
  filterClients() {
    if (!this.searchQuery.trim()) {
      this.filteredClients = [];
      this.showDropdown = false;
      return;
    }
    const query = this.searchQuery.toLowerCase().trim();
    this.filteredClients = this.clients
      .filter(client => client.Name && client.Name.toLowerCase().includes(query))
      .slice(0, 5);
    this.showDropdown = this.filteredClients.length > 0;
  }

  // Add missing onClearSelection for template
  onClearSelection() {
    this.clearSelection();
  }

  // Add missing confirmUpgradeLicence for upgrade popup
  async confirmUpgradeLicence() {
    if (!this.selectedClient || !this.selectedLicenseForConfirmation) {
      return;
    }
    this.spinner.show();
    try {
      let dateDebut: Date, dateFin: Date;
      if (this.selectedPaymentFrequency === 'custom' || this.selectedPaymentFrequency === 'Personnalisé') {
        dateDebut = new Date();
        dateFin = this.customDateFin ? new Date(this.customDateFin) : new Date();
      } else {
        dateDebut = new Date();
        dateFin = this.calculateDateFin(dateDebut, this.selectedPaymentFrequency, this.customMonths);
      }
      const dateFinStr = this.formatDateForBackend(dateFin);

      const clientId = this.selectedClient.Id;
      const licenceId = this.selectedLicenseForConfirmation.Id;

      // If the date is finished, use full price for all checked options
      const checkedSet = this.checkedOptions[licenceId] || new Set();
      let totalPrice: number;
      const today = new Date();
      today.setHours(0,0,0,0);
      dateFin.setHours(0,0,0,0);
      if (today >= dateFin) {
        totalPrice = this.getOptionsForLicence(this.selectedLicenseForConfirmation)
          .filter(opt => checkedSet.has(opt.Id))
          .reduce((sum, opt) => sum + ((opt.Price || 0) * this.getMonthsForPaymentFrequency(this.selectedPaymentFrequency)), 0);
      } else {
        totalPrice = Math.abs(this.proratedTotalForUpgrade);
      }

      // Find the current subscription for this client (should be the one being upgraded)
      const subscriptions = await this.subscriptionApiService.getAll().toPromise() ?? [];
      // Find the subscription for this client (regardless of licence)
      const existing = subscriptions.find(sub => sub.ClientId === clientId && sub.Status !== 'Resilié');

      const checkedOptionIds = Array.from(checkedSet);

      if (existing) {
        // Update the existing subscription (by its Id)
        const updated = {
          ...existing,
          LicenceId: licenceId,
          DateDebut: this.formatDateForBackend(new Date()),
          DateFin: dateFinStr,
          Price: totalPrice,
          Status: 'En attente', // always set to "En attente" on upgrade
          PaymentFrequency: this.selectedPaymentFrequency
        };
        await this.subscriptionApiService.update(updated).toPromise();
        await this.saveSubscribedOptionsForLicence(updated.Id, checkedOptionIds);
        this.showSuccessNotification = true;
        this.showUpgradePopup = false;
        this.selectedLicenseForConfirmation = null;
        this.oldLicence = null;
        this.oldOptions = [];
        this.oldOptionsLoaded = false;
        await Promise.all([
          this.fetchSubscriptions(),
          this.fetchClients()
        ]);
        const refreshedClient = this.clients.find(c => c.Id === this.selectedClient?.Id);
        if(refreshedClient) {
          this.selectClient(refreshedClient);
        }
      }
    } catch (error) {
      console.error('Error upgrading licence:', error);
    } finally {
      this.spinner.hide();
    }
  }

  // Add missing ngOnInit for OnInit interface
  ngOnInit(): void {
    this.spinner.show();
    Promise.all([
      this.loadAssignedLicences(),
      this.fetchClients(),
      this.fetchLicences(),
      this.fetchOptions(),
      this.fetchLicenceOptions(),
      this.fetchSubscriptions()
    ]).then(() => {
      this.initialCheckedOptions = {};
      this.updateSubscriptionTableRows();
      this.spinner.hide();
    }).catch(() => {
      this.spinner.hide();
    });
  }
}