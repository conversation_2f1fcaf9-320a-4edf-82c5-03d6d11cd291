// licence.component.ts - FINAL CORRECTED VERSION

import { Component, HostListener, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { Client } from '@app/core/models/client';
import { LicenceApiService } from '@app/core/services/administrative/licence.service';
import { LicenceOptionApiService } from '@app/core/services/administrative/licenceOption.service';
import { OptionApiService } from '@app/core/services/administrative/option.service';
import { Licence } from '@app/core/models/licence';
import { LicenceOption } from '@app/core/models/licenceOption';
import { Option } from '@app/core/models/option';
import { Subscription } from '@app/core/models/subscription';
import { SubscriptionApiService } from '@app/core/services/administrative/subscription.service';
import { SubscribedOptionsApiService } from '@app/core/services/administrative/subscribedOptions.service';
import { SubscribedOptions } from '@app/core/models/subscribedoptions';



@Component({
  selector: 'app-licence',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './licence.component.html',
  styleUrls: ['./licence.component.css'],
  animations: [
    trigger('slideInOut', [
      state('void', style({
        transform: 'translateY(-10px)',
        opacity: 0
      })),
      state('*', style({
        transform: 'translateY(0)',
        opacity: 1
      })),
      transition('void <=> *', animate('200ms ease-in-out'))
    ]),
    trigger('growIn', [
      state('void', style({
        transform: 'scale(0.8)',
        opacity: 0
      })),
      state('*', style({
        transform: 'scale(1)',
        opacity: 1
      })),
      transition('void <=> *', animate('150ms ease-in-out'))
    ]),
    trigger('fadeInOut', [
      state('void', style({
        opacity: 0,
        transform: 'scale(0.9)'
      })),
      state('*', style({
        opacity: 1,
        transform: 'scale(1)'
      })),
      transition('void <=> *', animate('300ms ease-in-out'))
    ]),
    trigger('slideDown', [
      state('void', style({
        transform: 'translateY(-20px)',
        opacity: 0
      })),
      state('*', style({
        transform: 'translateY(0)',
        opacity: 1
      })),
      transition('void <=> *', animate('400ms ease-out'))
    ])
  ]
})
export class LicenceComponent implements OnInit {
  constructor(
    private clientApiService: ClientApiService,
    private licenceApiService: LicenceApiService,
    private licenceOptionApiService: LicenceOptionApiService,
    private optionApiService: OptionApiService,
    private subscriptionApiService: SubscriptionApiService,
    private subscribedOptionsApiService: SubscribedOptionsApiService
  ) {}

  billingCycle: 'monthly' | 'yearly' = 'monthly';
  searchQuery = '';
  showDropdown = false;
  selectedClient: Client | null = null;
  showConfirmationPopup = false;
  selectedLicenseForConfirmation: Licence | null = null;
  showSuccessNotification = false;
  showCancelPopup = false;
  licenceToCancel: Licence | null = null;
  showCancelNotification = false;
  showChooseClientError = false;
  showUpgradePopup = false;

  clients: Client[] = [];
  filteredClients: Client[] = [];
  licences: Licence[] = [];
  options: Option[] = [];
  licenceOptions: LicenceOption[] = []; 

  // Track assigned licence per client (simulate backend, but now persistent in localStorage)
  assignedLicences: { [idClient: string]: string } = {};
  subscriptions: Subscription[] = [];
  clientSubscription: Subscription | null = null;

  // Track checked options for the selected licence
  checkedOptions: { [licenceId: string]: Set<string> } = {};

  // For upgrade functionality
  oldLicence: Licence | null = null;

  ngOnInit() {
    this.loadAssignedLicences();
    this.fetchClients();
    this.fetchLicences();
    this.fetchOptions();
    this.fetchLicenceOptions();
    this.fetchSubscriptions();
  }

  fetchClients() {
    this.clientApiService.getAll().subscribe({
      next: (data: Client[]) => {
        this.clients = data.map(clients => ({
          ...clients,
          Name: clients.Name,
          ClientLogo: clients.ClientLogo
        }) );
        this.filteredClients = [...this.clients];
        console.log('Clients loaded:', this.clients);
      },
      error: (error) => {
        console.error('Error fetching clients:', error);
      }
    });
  }

  fetchLicences() {
    this.licenceApiService.getAll().subscribe({
      next: (data: Licence[]) => {
        this.licences = data.map(licence => ({
          ...licence,          
          name: licence.Name ,       
          description: licence.Description 
        }));
        console.log('Licences loaded:', this.licences);
      },
      error: (error) => {
        console.error('Error fetching licences:', error);
      }
    });
  }

  fetchOptions() {
    this.optionApiService.getAll().subscribe({
      next: (data: Option[]) => {
        this.options = data.map(options => ({
          ...options,
          name: options.Name,
          price: options.Price
        }));
        console.log('Options loaded:', this.options);
      },
      error: (error) => {
        console.error('Error fetching options:', error);
      }
    });
  }

  fetchLicenceOptions() {
    this.licenceOptionApiService.getAll().subscribe({
      next: (data: LicenceOption[]) => {
        this.licenceOptions = data;
        console.log('LicenceOptions loaded:', this.licenceOptions);
      },
      error: (error) => {
        console.error('Error fetching licence options:', error);
      }
    });
  }

  fetchSubscriptions() {
    this.subscriptionApiService.getAll().subscribe({
      next: (data: Subscription[]) => {
        this.subscriptions = data;
      },
      error: (error) => {
        // handle error
      }
    });
  }

  toggleBillingCycle(cycle: 'monthly' | 'yearly') {
    this.billingCycle = cycle;
  }

  // Helper to fetch checked options for a subscription from backend
  private fetchCheckedOptionsForClientSubscription(subscriptionId: string, licenceId: string) {
    this.subscribedOptionsApiService.getAll().subscribe({
      next: (subscribedOptions: SubscribedOptions[]) => {
        // Get all checked OptionIds for this subscription
        const checkedOptionIds = subscribedOptions
          .filter((so: SubscribedOptions) => so.SubscriptionId === subscriptionId && so.checked)
          .map((so: SubscribedOptions) => so.OptionId);
        // Set checked options for this licence
        this.checkedOptions[licenceId] = new Set(checkedOptionIds);
      }
    });
  }

  selectClient(client: Client) {
    this.selectedClient = client;
    this.searchQuery = '';
    this.showDropdown = false;
    this.filteredClients = [];
    const clientId = client.Id;
    this.clientSubscription = this.subscriptions.find(sub => sub.ClientId === clientId) || null;

    if (this.clientSubscription) {
      this.fetchCheckedOptionsForClientSubscription(this.clientSubscription.Id, this.clientSubscription.LicenceId);
    }
  }

  clearSelection() {
    this.selectedClient = null;
  }

  // When selecting a licence for confirmation, all options are checked by default (uncheckedOptions is empty)
  selectLicense(licence: Licence) {
    if (!this.selectedClient) {
      this.showChooseClientError = true;
      setTimeout(() => { this.showChooseClientError = false; }, 2500);
      return;
    }
    this.selectedLicenseForConfirmation = licence;
    // Do NOT reset checkedOptions here; keep previous selection or default to all checked
    if (!this.checkedOptions[licence.Id]) {
      // If not already set, initialize with all options checked
      const allOptionIds = this.getOptionsForLicence(licence).map(opt => opt.Id);
      this.checkedOptions[licence.Id] = new Set(allOptionIds);
    }
    this.showConfirmationPopup = true;
  }

  // Use checkedOptions for option selection
  isOptionChecked(licence: Licence, optionId: string): boolean {
    return this.checkedOptions[licence.Id]?.has(optionId) ?? false;
  }

  toggleOption(licence: Licence, optionId: string, event: Event) {
    if (!this.checkedOptions[licence.Id]) {
      this.checkedOptions[licence.Id] = new Set();
    }
    if ((event.target as HTMLInputElement).checked) {
      this.checkedOptions[licence.Id].add(optionId);
    } else {
      this.checkedOptions[licence.Id].delete(optionId);
    }
  }

  // Calculate the sum of option prices for a given licence
  getLicenceOptionsTotal(licence: Licence): number {
    // Only sum checked options
    const checkedSet = this.checkedOptions[licence.Id] || new Set();
    return this.getOptionsForLicence(licence)
      .filter(opt => checkedSet.has(opt.Id))
      .reduce((sum, opt) => sum + (opt.Price || 0), 0);
  }

  private createSubscribedOptionsForLicence(subscriptionId: string, licence: Licence) {
    // Store all options as checked in subscribedoptions table
    const allOptionIds = this.getOptionsForLicence(licence).map(opt => opt.Id);
    this.subscribedOptionsApiService.getAll().subscribe({
      next: (allSubscribedOptions: SubscribedOptions[]) => {
        // Delete all previous options for this subscription
        const toDelete = allSubscribedOptions.filter(
          (so: SubscribedOptions) => so.SubscriptionId === subscriptionId
        );
        toDelete.forEach((so: SubscribedOptions) => {
          this.subscribedOptionsApiService.delete(so.Id).subscribe();
        });

        // Add all options as checked
        allOptionIds.forEach(optionId => {
          const subscribedOption: Partial<SubscribedOptions> = {
            SubscriptionId: subscriptionId,
            OptionId: optionId,
            checked: true
          };
          this.subscribedOptionsApiService.create(subscribedOption).subscribe();
        });
      }
    });
  }

  private saveAssignedLicences() {
    localStorage.setItem('assignedLicences', JSON.stringify(this.assignedLicences));
  }

  confirmLicenseApplication() {
    if (!this.selectedClient || !this.selectedLicenseForConfirmation) {
      return;
    }
    const today = new Date();
    const dateFin = new Date(today);
    dateFin.setMonth(today.getMonth() + 1);

    const clientId = this.selectedClient.Id;
    const licenceId = this.selectedLicenseForConfirmation.Id;
    const checkedSet = this.checkedOptions[licenceId] || new Set();

    // Only take checked options and their prices
    const totalPrice = this.getOptionsForLicence(this.selectedLicenseForConfirmation)
      .filter(opt => checkedSet.has(opt.Id))
      .reduce((sum, opt) => sum + (opt.Price || 0), 0);

    this.subscriptionApiService.getAll().subscribe({
      next: (subscriptions: Subscription[]) => {
        const existing = subscriptions.find(
          sub => sub.ClientId === clientId
        );
        if (existing) {
          const updated = {
            ...existing,
            LicenceId: licenceId,
            DateDebut: today,
            DateFin: dateFin,
            Price: totalPrice
          };
          this.subscriptionApiService.update(updated).subscribe({
            next: (updatedSub: Subscription) => {
              this.createSubscribedOptionsForLicence(updatedSub.Id, this.selectedLicenseForConfirmation!);
              this.showSuccessNotification = true;
              this.showConfirmationPopup = false;
              // Reset client search and data
              this.selectedClient = null;
              this.searchQuery = '';
              this.filteredClients = [...this.clients];
              this.showDropdown = false;
              this.selectedLicenseForConfirmation = null;
              this.fetchSubscriptions();
              this.fetchClients();
              setTimeout(() => {
                this.showSuccessNotification = false;
              }, 5000);
            },
            error: (err) => {
              // Handle error
            }
          });
        } else {
          const subscription = {
            DateDebut: today,
            DateFin: dateFin,
            ClientId: clientId,
            LicenceId: licenceId,
            Price: totalPrice
          };
          this.subscriptionApiService.create(subscription).subscribe({
            next: (createdSub: Subscription) => {
              this.createSubscribedOptionsForLicence(createdSub.Id, this.selectedLicenseForConfirmation!);
              this.showSuccessNotification = true;
              this.showConfirmationPopup = false;
              // Reset client search and data
              this.selectedClient = null;
              this.searchQuery = '';
              this.filteredClients = [...this.clients];
              this.showDropdown = false;
              this.selectedLicenseForConfirmation = null;
              this.fetchSubscriptions();
              this.fetchClients();
              setTimeout(() => {
                this.showSuccessNotification = false;
              }, 5000);
            },
            error: (err) => {
              // Handle error
            }
          });
        }
      },
      error: (err) => {
        // Handle error
      }
    });
  }

  cancelLicenseApplication() {
    this.showConfirmationPopup = false;
    this.selectedLicenseForConfirmation = null;
  }

  closeSuccessNotification() {
    this.showSuccessNotification = false;
  }

  @HostListener('document:click', ['$event'])
  onClick(event: MouseEvent) {
    if (!(event.target as HTMLElement).closest('.search-input-container')) {
      this.showDropdown = false;
    }
  }

  // FIXED: Use correct property names from backend
  getOptionsForLicence(licence: Licence): Option[] {
    if (!licence) {
      return [];
    }

    // Use licence.LicenceOptions if present and valid
    if (licence.LicenceOptions && Array.isArray(licence.LicenceOptions)) {
      return licence.LicenceOptions
        .map(lo => lo.Option)
        .filter(opt => opt !== null && opt !== undefined) as Option[];
    }

    // Fallback: Find options using the global licenceOptions array
    const licenceOptionLinks = this.licenceOptions.filter(lo => lo.LicenceId === licence.Id);

    const options = licenceOptionLinks
      .map(lo => {
        if (lo.Option) return lo.Option;
        // fallback: find by idoption in global options
        return this.options.find(opt => opt.Id === lo.OptionId);
      })
      .filter(opt => opt !== undefined && opt !== null) as Option[];

    return options;
  }

  // Add a method to get all licenceOptions for a given licence, including option and licence details
  getLicenceOptionsForCard(licence: Licence): Array<{
    licenceId: string;
    licenceName: string;
    licenceDescription: string;
    optionId: string;
    optionName: string;
    optionPrice: number;
  }> {
    let licenceOptionLinks: LicenceOption[] = [];

    // If licence.Name equals licence.Id, show only options linked to this licence.Id
    if (licence.Name === licence.Id) {
      licenceOptionLinks = this.licenceOptions.filter(lo => lo.LicenceId === licence.Id);
    } else if (licence.LicenceOptions && Array.isArray(licence.LicenceOptions)) {
      licenceOptionLinks = licence.LicenceOptions;
    } else {
      licenceOptionLinks = this.licenceOptions.filter(lo => lo.LicenceId === licence.Id);
    }

    return licenceOptionLinks
      .map(lo => {
        let option = lo.Option || this.options.find(opt => opt.Id === lo.OptionId);
        if (!option) return null;
        return {
          licenceId: licence.Id,
          licenceName: licence.Name,
          licenceDescription: licence.Description ?? '',
          optionId: option.Id,
          optionName: option.Name,
          optionPrice: option.Price
        };
      })
      .filter(item => !!item);
  }


  allOptionsIncluded(licence: Licence): boolean {
    // Button enabled only if at least one option exists for this licence
    const options = this.getOptionsForLicence(licence);
    return options.length > 0;
  }

  public filterClients() {
    if (!this.searchQuery.trim()) {
      this.filteredClients = [];
      this.showDropdown = false;
      return;
    }

    const query = this.searchQuery.toLowerCase().trim();
    this.filteredClients = this.clients
      .filter(client => 
        client.Name && client.Name.toLowerCase().includes(query)
      )
      .slice(0, 5);
    
    this.showDropdown = this.filteredClients.length > 0;
  }

  get imageUrl(): string | null {
    if (this.selectedClient && this.selectedClient.ClientLogo) {
      return `data:image/png;base64,${this.selectedClient.ClientLogo}`;
    }
    return null;
  }

  // Helper: is this licence assigned to the selected client?
  isLicenceAssignedToClient(licence: Licence): boolean {
    if (!this.selectedClient || !this.clientSubscription) return false;
    return this.clientSubscription.LicenceId === licence.Id;
  }

  // Helper: should Affecter button be disabled for this client/licence?
  isAffecterDisabled(licence: Licence): boolean {
    if (!this.selectedClient) return true;
    // If client has a subscription and it's not this licence, disable Affecter
    return !!this.clientSubscription && this.clientSubscription.LicenceId !== licence.Id;
  }

  // Helper: should show "Annuler la licence" for this client/licence?
  showAssignedActions(licence: Licence): boolean {
    return this.isLicenceAssignedToClient(licence);
  }

  // Helper: should show "Upgrade" for this client/licence?
  showUpgradeButton(licence: Licence): boolean {
    if (!this.selectedClient || !this.clientSubscription) return false;
    return this.clientSubscription.LicenceId !== licence.Id;
  }

  // Remove licence assignment for client
  cancelLicenceForClient(licence: Licence) {
    this.licenceToCancel = licence;
    this.showCancelPopup = true;
  }

  // Called when user confirms cancellation in the popup
  confirmCancelLicenceForClient() {
    if (!this.selectedClient || !this.licenceToCancel) return;
    const idClient = this.selectedClient.Id ?? '';
    // Remove the subscription for this client and licence
    this.subscriptionApiService.getAll().subscribe({
      next: (subscriptions: Subscription[]) => {
        const sub = subscriptions.find(
          s => s.ClientId === idClient && s.LicenceId === this.licenceToCancel!.Id
        );
        if (sub) {
          this.subscriptionApiService.delete(sub.Id).subscribe({
            next: () => {
              if (idClient && this.assignedLicences[idClient] === this.licenceToCancel!.Id) {
                delete this.assignedLicences[idClient];
                this.saveAssignedLicences();
              }
              this.showCancelPopup = false;
              this.licenceToCancel = null;
              this.showCancelNotification = true;
              // Reset client search and data
              this.selectedClient = null;
              this.searchQuery = '';
              this.filteredClients = [...this.clients];
              this.showDropdown = false;
              this.fetchSubscriptions();
              this.fetchClients();
              setTimeout(() => {
                this.showCancelNotification = false;
              }, 5000);
            },
            error: () => {
              if (idClient && this.assignedLicences[idClient] === this.licenceToCancel!.Id) {
                delete this.assignedLicences[idClient];
                this.saveAssignedLicences();
              }
              this.showCancelPopup = false;
              this.licenceToCancel = null;
              this.showCancelNotification = true;
              // Reset client search and data
              this.selectedClient = null;
              this.searchQuery = '';
              this.filteredClients = [...this.clients];
              this.showDropdown = false;
              this.fetchSubscriptions();
              this.fetchClients();
              setTimeout(() => {
                this.showCancelNotification = false;
              }, 5000);
            }
          });
        } else {
          if (idClient && this.assignedLicences[idClient] === this.licenceToCancel!.Id) {
            delete this.assignedLicences[idClient];
            this.saveAssignedLicences();
          }
          this.showCancelPopup = false;
          this.licenceToCancel = null;
          this.showCancelNotification = true;
          // Reset client search and data
          this.selectedClient = null;
          this.searchQuery = '';
          this.filteredClients = [...this.clients];
          this.showDropdown = false;
          this.fetchSubscriptions();
          this.fetchClients();
          setTimeout(() => {
            this.showCancelNotification = false;
          }, 5000);
        }
      },
      error: () => {
        if (idClient && this.assignedLicences[idClient] === this.licenceToCancel!.Id) {
          delete this.assignedLicences[idClient];
          this.saveAssignedLicences();
        }
        this.showCancelPopup = false;
        this.licenceToCancel = null;
        this.showCancelNotification = true;
        // Reset client search and data
        this.selectedClient = null;
        this.searchQuery = '';
        this.filteredClients = [...this.clients];
        this.showDropdown = false;
        this.fetchSubscriptions();
        this.fetchClients();
        setTimeout(() => {
          this.showCancelNotification = false;
        }, 5000);
      }
    });
  }

  upgradeLicenceForClient(licence: Licence) {
    if (!this.selectedClient) return;
    this.oldLicence = this.licences.find(l => l.Id === this.clientSubscription?.LicenceId) || null;
    this.selectedLicenseForConfirmation = licence;
    if (this.clientSubscription) {
      this.fetchCheckedOptionsForClientSubscription(
        this.clientSubscription.Id, 
        licence.Id
      );
    } else {
      this.checkedOptions[licence.Id] = new Set<string>();
    }
    this.showUpgradePopup = true;
  }

  cancelUpgradePopup() {
    this.showUpgradePopup = false;
    this.selectedLicenseForConfirmation = null;
    this.oldLicence = null;
  }

  confirmUpgradeLicence() {
    // Same logic as confirmLicenseApplication, but for upgrade
    if (!this.selectedClient || !this.selectedLicenseForConfirmation) {
      return;
    }
    const today = new Date();
    const dateFin = new Date(today);
    dateFin.setMonth(today.getMonth() + 1);

    const clientId = this.selectedClient.Id;
    const licenceId = this.selectedLicenseForConfirmation.Id;
    const checkedSet = this.checkedOptions[licenceId] || new Set();

    const totalPrice = this.getOptionsForLicence(this.selectedLicenseForConfirmation)
      .filter(opt => checkedSet.has(opt.Id))
      .reduce((sum, opt) => sum + (opt.Price || 0), 0);

    this.subscriptionApiService.getAll().subscribe({
      next: (subscriptions: Subscription[]) => {
        const existing = subscriptions.find(
          sub => sub.ClientId === clientId
        );
        if (existing) {
          const updated = {
            ...existing,
            LicenceId: licenceId,
            DateDebut: today,
            DateFin: dateFin,
            Price: totalPrice
          };
          this.subscriptionApiService.update(updated).subscribe({
            next: (updatedSub: Subscription) => {
              this.createSubscribedOptionsForLicence(updatedSub.Id, this.selectedLicenseForConfirmation!);
              this.showSuccessNotification = true;
              this.showUpgradePopup = false;
              // Reset client search and data
              this.selectedClient = null;
              this.searchQuery = '';
              this.filteredClients = [...this.clients];
              this.showDropdown = false;
              this.selectedLicenseForConfirmation = null;
              this.oldLicence = null;
              this.fetchSubscriptions();
              this.fetchClients();
              setTimeout(() => {
                this.showSuccessNotification = false;
              }, 5000);
            },
            error: (err) => {
              // Handle error
            }
          });
        }
      },
      error: (err) => {
        // Handle error
      }
    });
  }

  private loadAssignedLicences() {
    const data = localStorage.getItem('assignedLicences');
    if (data) {
      try {
        this.assignedLicences = JSON.parse(data);
      } catch {
        this.assignedLicences = {};
      }
    }
  }

  closeCancelPopup() {
    this.showCancelPopup = false;
    this.licenceToCancel = null;
    this.showCancelNotification = false;
  }
}