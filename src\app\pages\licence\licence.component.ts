import { Component, HostListener, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { Client } from '@app/core/models/client';
import { LicenceApiService } from '@app/core/services/administrative/licence.service';
import { LicenceOptionApiService } from '@app/core/services/administrative/licenceOption.service';
import { OptionApiService } from '@app/core/services/administrative/option.service';
import { Licence } from '@app/core/models/licence';
import { LicenceOption } from '@app/core/models/licenceOption';
import { Option } from '@app/core/models/option';
import { Subscription } from '@app/core/models/subscription';
import { SubscriptionApiService } from '@app/core/services/administrative/subscription.service';
import { SubscribedOptionsApiService } from '@app/core/services/administrative/subscribedOptions.service';
import { SubscribedOptions } from '@app/core/models/subscribedoptions';

@Component({
  selector: 'app-licence',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './licence.component.html',
  styleUrls: ['./licence.component.css'],
  animations: [
    trigger('slideInOut', [
      state('void', style({
        transform: 'translateY(-10px)',
        opacity: 0
      })),
      state('*', style({
        transform: 'translateY(0)',
        opacity: 1
      })),
      transition('void <=> *', animate('200ms ease-in-out'))
    ]),
    trigger('growIn', [
      state('void', style({
        transform: 'scale(0.8)',
        opacity: 0
      })),
      state('*', style({
        transform: 'scale(1)',
        opacity: 1
      })),
      transition('void <=> *', animate('150ms ease-in-out'))
    ]),
    trigger('fadeInOut', [
      state('void', style({
        opacity: 0,
        transform: 'scale(0.9)'
      })),
      state('*', style({
        opacity: 1,
        transform: 'scale(1)'
      })),
      transition('void <=> *', animate('300ms ease-in-out'))
    ]),
    trigger('slideDown', [
      state('void', style({
        transform: 'translateY(-20px)',
        opacity: 0
      })),
      state('*', style({
        transform: 'translateY(0)',
        opacity: 1
      })),
      transition('void <=> *', animate('400ms ease-out'))
    ])
  ]
})
export class LicenceComponent implements OnInit {
  constructor(
    private clientApiService: ClientApiService,
    private licenceApiService: LicenceApiService,
    private licenceOptionApiService: LicenceOptionApiService,
    private optionApiService: OptionApiService,
    private subscriptionApiService: SubscriptionApiService,
    private subscribedOptionsApiService: SubscribedOptionsApiService
  ) {}
  billingCycle: 'monthly' | 'yearly' = 'monthly';
  searchQuery = '';
  showDropdown = false;
  showAddOptionPopup = false;
  showDeleteOptionPopup = false;
  showDeleteConfirmationPopup = false;
  selectedLicenceForOption: string = '';
  newOptionName = '';
  newOptionPrice: number | null = null;
  selectedLicenceForDeletion: string = '';
  optionSearchQuery = '';
  filteredOptionsForDeletion: Option[] = [];
  optionsForDeletion: Option[] = [];
  selectedOptionForDeletion: Option | null = null;
  proratedDiferenceReturn: number = 0;
  proratedOldForUpgrade: number = 0;
  proratedDiferencePay: number = 0;
  proratedNewForUpgrade: number = 0;
  proratedTotalForUpgrade: number = 0;
  selectedClient: Client | null = null;
  showConfirmationPopup = false;
  selectedLicenseForConfirmation: Licence | null = null;
  showSuccessNotification = false;
  showCancelPopup = false;
  licenceToCancel: Licence | null = null;
  showCancelNotification = false;
  showChooseClientError = false;
  showUpgradePopup = false;
  clients: Client[] = [];
  filteredClients: Client[] = [];
  licences: Licence[] = [];
  options: Option[] = [];
  licenceOptions: LicenceOption[] = []; 
  assignedLicences: { [idClient: string]: string } = {};
  subscriptions: Subscription[] = [];
  clientSubscription: Subscription | null = null;
  checkedOptions: { [licenceId: string]: Set<string> } = {};
  oldLicence: Licence | null = null;
  modifyingLicence: Licence | null = null;
  showModifyPopup = false;
  showSaveNotification = false;
  oldOptions: Option[] = [];
  oldOptionsLoaded: boolean = false;
  initialCheckedOptions: { [licenceId: string]: Set<string> } = {};
  assignedLicenceChanges: { [licenceId: string]: boolean } = {};
  showSaveOrDiscardPopup = false;
  licenceWithUnsavedChanges: Licence | null = null;
  showNoOptionCheckedPopup = false;
  // Add a property to track pending restore for openModifyPopup, affecter, and upgrade
  _pendingRestoreLicence: Licence | null = null;
  _pendingRestoreType: 'modify' | 'affecter' | 'upgrade' | null = null;

  ngOnInit() {
    this.loadAssignedLicences();
    this.fetchClients();
    this.fetchLicences();
    this.fetchOptions();
    this.fetchLicenceOptions();
    this.fetchSubscriptions();
    this.initialCheckedOptions = {};
  }

  fetchClients() {
    return new Promise<void>((resolve) => {
      this.clientApiService.getAll().subscribe({
        next: (data: Client[]) => {
          this.clients = data.map(clients => ({
            ...clients,
            Name: clients.Name,
            ClientLogo: clients.ClientLogo
          }));
          this.filteredClients = [...this.clients];
          resolve();
        },
        error: (error) => {
          resolve();
        }
      });
    });
  }

  fetchLicences() {
    this.licenceApiService.getAll().subscribe({
      next: (data: Licence[]) => {
        this.licences = data.map(licence => ({
          ...licence,          
          name: licence.Name ,       
          description: licence.Description
        }));
      },
      error: (error) => {}
    });
  }

  fetchOptions() {
    this.optionApiService.getAll().subscribe({
      next: (data: Option[]) => {
        this.options = data.map(options => ({
          ...options,
          name: options.Name,
          price: options.Price
        }));
      },
      error: (error) => {}
    });
  }

  fetchLicenceOptions() {
    this.licenceOptionApiService.getAll().subscribe({
      next: (data: LicenceOption[]) => {
        this.licenceOptions = data;
      },
      error: (error) => {}
    });
  }

  fetchSubscriptions() {
  return this.subscriptionApiService.getAll().subscribe({
    next: (data: Subscription[]) => {
      this.subscriptions = data;
    },
    error: (error) => {}
  });
}

  toggleBillingCycle(cycle: 'monthly' | 'yearly') {
    this.billingCycle = cycle;
  }

  private fetchCheckedOptionsForClientSubscription(subscriptionId: string, licenceId: string) {
    this.subscribedOptionsApiService.getAll().subscribe({
      next: (subscribedOptions: SubscribedOptions[]) => {
        const checkedOptionIds = subscribedOptions
          .filter((so: SubscribedOptions) =>
            so.SubscriptionId === subscriptionId &&
            this.isOptionLinkedToLicence(licenceId, so.OptionId))
          .map((so: SubscribedOptions) => so.OptionId);
        this.checkedOptions[licenceId] = new Set(checkedOptionIds);
      }
    });
  }

  toggleOption(licence: Licence, optionId: string, event: Event) {
    const licenceId = licence.Id;
    // Only update the local checkedOptions, never call backend here
    if (!this.checkedOptions[licenceId]) {
      this.checkedOptions[licenceId] = new Set();
    }
    if (this.checkedOptions[licenceId].has(optionId)) {
      this.checkedOptions[licenceId].delete(optionId);
    } else {
      this.checkedOptions[licenceId].add(optionId);
    }
    this.assignedLicenceChanges[licenceId] = true;
  }

  selectClient(client: Client) {
    this.selectedClient = client;
    this.searchQuery = '';
    this.showDropdown = false;
    this.filteredClients = [];
    const clientId = client.Id;
    this.clientSubscription = this.subscriptions.find(sub => sub.ClientId === clientId) || null;

    // Always reload checkedOptions from DB for the selected client
    if (this.clientSubscription) {
      // Clear all checkedOptions except for the assigned licence
      Object.keys(this.checkedOptions).forEach(licenceId => {
        this.checkedOptions[licenceId] = new Set();
      });
      this.subscribedOptionsApiService.getAll().subscribe({
        next: (subscribedOptions: SubscribedOptions[]) => {
          const checkedOptionIds = subscribedOptions
            .filter(so =>
              so.SubscriptionId === this.clientSubscription!.Id &&
              this.isOptionLinkedToLicence(this.clientSubscription!.LicenceId, so.OptionId))
            .map(so => so.OptionId);
          this.checkedOptions[this.clientSubscription!.LicenceId] = new Set(checkedOptionIds);
          this.initialCheckedOptions[this.clientSubscription!.LicenceId] = new Set(checkedOptionIds);
        }
      });
    } else {
      this.licences.forEach(licence => {
        const allOptionIds = this.getOptionsForLicence(licence).map(opt => opt.Id);
        this.checkedOptions[licence.Id] = new Set(allOptionIds);
        this.initialCheckedOptions[licence.Id] = new Set(allOptionIds);
      });
    }
  }

    openAddOptionPopup() {
    this.showAddOptionPopup = true;
    this.selectedLicenceForOption = this.licences.length > 0 ? this.licences[0].Id : '';
    this.newOptionName = '';
    this.newOptionPrice = null;
    }

    closeAddOptionPopup() {
      this.showAddOptionPopup = false;
    }

    confirmAddOption() {
      if (!this.selectedLicenceForOption || !this.newOptionName || this.newOptionPrice === null) {
        // Show error or validation message
        return;
      }

      const newOption: Partial<Option> = {
        Name: this.newOptionName,
        Price: this.newOptionPrice
      };

      this.optionApiService.create(newOption).subscribe({
        next: (createdOption) => {
          // Create the licence-option link
          const licenceOption: Partial<LicenceOption> = {
            LicenceId: this.selectedLicenceForOption,
            OptionId: createdOption.Id
          };

          this.licenceOptionApiService.create(licenceOption).subscribe({
            next: () => {
              // Refresh data
              this.fetchOptions();
              this.fetchLicenceOptions();
              this.closeAddOptionPopup();
              this.showSuccessNotification = true;
              setTimeout(() => this.showSuccessNotification = false, 3000);
            },
            error: (error) => {
              console.error('Error creating licence option link:', error);
            }
          });
        },
        error: (error) => {
          console.error('Error creating option:', error);
        }
      });
    }

    openDeleteOptionPopup() {
    this.showDeleteOptionPopup = true;
    this.selectedLicenceForDeletion = this.licences.length > 0 ? this.licences[0].Id : '';
    this.optionSearchQuery = '';
    this.filteredOptionsForDeletion = [];
    this.selectedOptionForDeletion = null;
    this.loadOptionsForDeletion();
    }

    closeDeleteOptionPopup() {
      this.showDeleteOptionPopup = false;
    }

    onOptionSelectChange() {
      const selectedId = typeof this.selectedOptionForDeletion === 'string'
      ? this.selectedOptionForDeletion
      : (this.selectedOptionForDeletion && (this.selectedOptionForDeletion as any).Id)
        ? (this.selectedOptionForDeletion as any).Id
        : '';
    this.selectedOptionForDeletion = this.optionsForDeletion.find(opt => opt.Id === selectedId) || null;
    }

    isOptionDisabled(option: Option): boolean {
      // Add logic here if you need to disable certain options
      return false;
    }

    loadOptionsForDeletion() {
      if (this.selectedLicenceForDeletion) {
        this.optionsForDeletion = this.options.filter(opt =>
          this.isOptionLinkedToLicence(this.selectedLicenceForDeletion, opt.Id)
        );
      } else {
        this.optionsForDeletion = [];
      }
    }

  licenseTypes = ['Basic', 'Standard', 'Premium'];

    filterOptionsForDeletion() {
      if (!this.selectedLicenceForDeletion) return;
      
      const query = this.optionSearchQuery.toLowerCase();
      const licenceId = this.selectedLicenceForDeletion;
      const allOptions = this.getOptionsForLicence({ Id: licenceId } as Licence);
      
      this.filteredOptionsForDeletion = allOptions
        .filter(option => option.Name.toLowerCase().includes(query))
        .slice(0, 5);
    }

    selectOptionForDeletion(option: Option) {
      this.selectedOptionForDeletion = option;
    }

    confirmDeleteOption() {
      if (!this.selectedOptionForDeletion) return;
      this.showDeleteConfirmationPopup = true;
    }

    closeDeleteConfirmationPopup() {
      this.showDeleteConfirmationPopup = false;
    }

  finalizeOptionDeletion() {
    if (!this.selectedOptionForDeletion || !this.selectedLicenceForDeletion) return;

    // First delete the licence-option link
    const licenceOption = this.licenceOptions.find(lo => 
      lo.LicenceId === this.selectedLicenceForDeletion && 
      lo.OptionId === this.selectedOptionForDeletion?.Id);

    if (licenceOption && this.selectedOptionForDeletion) {
      this.licenceOptionApiService.delete(licenceOption.Id).subscribe({
        next: () => {
          // Then delete the option itself
          if (!this.selectedOptionForDeletion) return; // Additional null check
          this.optionApiService.delete(this.selectedOptionForDeletion.Id).subscribe({
            next: () => {
              // Refresh data
              this.fetchOptions();
              this.fetchLicenceOptions();
              this.showDeleteOptionPopup = false;
              this.showDeleteConfirmationPopup = false;
              this.showSuccessNotification = true;
              setTimeout(() => this.showSuccessNotification = false, 3000);
            },
            error: (error) => {
              console.error('Error deleting option:', error);
            }
          });
        },
        error: (error) => {
          console.error('Error deleting licence option link:', error);
        }
      });
    }
  }

  clearSelection() {
    // Check if there are unsaved changes for the assigned licence
    if (this.selectedClient && this.clientSubscription) {
      const assignedLicenceId = this.clientSubscription.LicenceId;
      if (this.hasOptionChanges({ Id: assignedLicenceId } as Licence)) {
        this.licenceWithUnsavedChanges = this.licences.find(l => l.Id === assignedLicenceId) || null;
        this.showSaveOrDiscardPopup = true;
        return;
      }
    }

    this.licences.forEach(licence => {
    // If no client is selected, check all options by default
    const allOptionIds = this.getOptionsForLicence(licence).map(opt => opt.Id);
    this.checkedOptions[licence.Id] = new Set(allOptionIds);
    });
    
    // Clear initial checked options
    this.initialCheckedOptions = {};
    
    this.selectedClient = null;

    // Restore checkedOptions for all licences to their initial DB state
    // Object.keys(this.initialCheckedOptions).forEach(licenceId => {
    //   this.checkedOptions[licenceId] = new Set<string>(Array.from(this.initialCheckedOptions[licenceId]));
    // });
    // this.selectedClient = null;
    // this.searchQuery = '';
    // this.filteredClients = [...this.clients]; 
  }

  

  // Called when user chooses to save changes in the popup
  confirmSaveAndClearSelection() {
    if (this.licenceWithUnsavedChanges) {
      this.modifyingLicence = this.licenceWithUnsavedChanges;
      const licenceId = this.modifyingLicence ? this.modifyingLicence.Id : '';
      this.oldOptions = this.getOptionsForLicence(this.modifyingLicence!).filter(opt =>
        licenceId && this.initialCheckedOptions[licenceId]?.has(opt.Id)
      );
      this.showModifyPopup = true;
      this.showSaveOrDiscardPopup = false;
      // Do not clear selectedClient or reload here; wait for user confirmation in the modify popup
      this.licenceWithUnsavedChanges = null;
    }
  }

  // Called when user chooses to discard changes in the popup
  discardChangesAndClearSelection() {
    // Restore checkedOptions for all licences to their initial DB state (front only)
    Object.keys(this.initialCheckedOptions).forEach(licenceId => {
      this.checkedOptions[licenceId] = new Set<string>(Array.from(this.initialCheckedOptions[licenceId]));
    });
    this.showSaveOrDiscardPopup = false;
    this.modifyingLicence = null;
    this.selectedClient = null;
    setTimeout(() => {
      window.location.reload();
    }, 500);
  }

  selectLicense(licence: Licence) {
    if (!this.selectedClient) {
      this.showChooseClientError = true;
      setTimeout(() => { this.showChooseClientError = false; }, 2500);
      return;
    }
    // Check if at least one option is checked
    const checked = this.checkedOptions[licence.Id] || new Set();
    if (checked.size === 0) {
      this.showNoOptionCheckedPopup = true;
      this._pendingRestoreLicence = licence;
      this._pendingRestoreType = 'affecter';
      return;
    }
    this.selectedLicenseForConfirmation = licence;
    const allOptionIds = this.getOptionsForLicence(licence).map(opt => opt.Id);
    if (!this.checkedOptions[licence.Id] || this.checkedOptions[licence.Id].size === 0) {
      this.checkedOptions[licence.Id] = new Set(allOptionIds);
    }
    this.showConfirmationPopup = true;
  }

  isOptionChecked(licence: Licence, optionId: string): boolean {
    if (this.isLicenceAssignedToClient(licence)) {
      return this.checkedOptions[licence.Id]?.has(optionId) ?? false;
    }
    return this.checkedOptions[licence.Id]?.has(optionId) ?? true;
  }

  hasOptionChanges(licence: Licence): boolean {
    const checked = this.checkedOptions[licence.Id] || new Set();
    const initial = this.initialCheckedOptions[licence.Id] || new Set();
    if (checked.size !== initial.size) return true;
    for (const id of checked) {
      if (!initial.has(id)) return true;
    }
    return false;
  }

  openModifyPopup(licence: Licence) {
    if (!this.selectedClient || !this.clientSubscription) return;
    // Check if at least one option is checked
    const checked = this.checkedOptions[licence.Id] || new Set();
    if (checked.size === 0) {
      this.showNoOptionCheckedPopup = true;
      this._pendingRestoreLicence = licence;
      this._pendingRestoreType = 'modify';
      return;
    }
    this.modifyingLicence = licence;
    this.oldOptions = this.getOptionsForLicence(licence).filter(opt =>
      this.initialCheckedOptions[licence.Id]?.has(opt.Id)
    );
    this.showModifyPopup = true;
  }

  closeNoOptionCheckedPopup() {
    this.showNoOptionCheckedPopup = false;
    if (this._pendingRestoreLicence) {
      const licence = this._pendingRestoreLicence;
      const initial = this.initialCheckedOptions[licence.Id] || new Set();
      this.checkedOptions[licence.Id] = new Set(Array.from(initial));
      this._pendingRestoreLicence = null;
      this._pendingRestoreType = null;
    }
  }

  getOldOptionsForModifying(): Option[] {
    return this.oldOptions;
  }

  getNewOptionsForModifying(): Option[] {
    if (!this.modifyingLicence) return [];
    return this.getOptionsForLicence(this.modifyingLicence).filter(opt =>
      this.checkedOptions[this.modifyingLicence!.Id]?.has(opt.Id)
    );
  }

  getOldTotalForModifying(): number {
    if (
      this.modifyingLicence &&
      this.clientSubscription &&
      this.clientSubscription.LicenceId === this.modifyingLicence.Id &&
      typeof this.clientSubscription.Price === 'number'
    ) {
      return this.clientSubscription.Price;
    }
    return this.getOldOptionsForModifying().reduce((sum, opt) => sum + (opt.Price || 0), 0);
  }

  getPriceToPayForModification(): number {
    if (!this.modifyingLicence) return 0;
    const oldSet = this.initialCheckedOptions[this.modifyingLicence.Id] || new Set();
    const newSet = this.checkedOptions[this.modifyingLicence.Id] || new Set();
    let price = 0;

    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const currentDay = today.getDate();
    const daysRemaining = daysInMonth - currentDay + 1;

    for (const id of newSet) {
      if (!oldSet.has(id)) {
        const option = this.options.find(opt => opt.Id === id);
        if (option && typeof option.Price === 'number') {
          price += (option.Price / daysInMonth) * daysRemaining;
        }
      }
    }
    return price;
  }

  getPriceToRefundForModification(): number {
    if (!this.modifyingLicence) return 0;
    const oldSet = this.initialCheckedOptions[this.modifyingLicence.Id] || new Set();
    const newSet = this.checkedOptions[this.modifyingLicence.Id] || new Set();
    let refund = 0;

    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const currentDay = today.getDate();
    const daysRemaining = daysInMonth - currentDay + 1;

    for (const id of oldSet) {
      if (!newSet.has(id)) {
        const option = this.options.find(opt => opt.Id === id);
        if (option && typeof option.Price === 'number') {
          refund += (option.Price / daysInMonth) * daysRemaining;
        }
      }
    }
    return refund;
  }

  getNewTotalWithOldForModifying(): number {
    const modType = this.getModificationType();
    if (modType === 'remove') {
      return this.getOldTotalForModifying() - this.getPriceToRefundForModification();
    }
    if (modType === 'add') {
      return this.getOldTotalForModifying() + this.getPriceToPayForModification();
    }
    if (modType === 'both') {
      
      return this.getOldTotalForModifying() + this.getPriceToPayForModification() - this.getPriceToRefundForModification();
    }
    return this.getOldTotalForModifying();
  }

  getModificationType(): 'add' | 'remove' | 'both' | null {
    if (!this.modifyingLicence) return null;
    const oldSet = this.initialCheckedOptions[this.modifyingLicence.Id] || new Set();
    const newSet = this.checkedOptions[this.modifyingLicence.Id] || new Set();
    let added = false, removed = false;
    for (const id of newSet) {
      if (!oldSet.has(id)) added = true;
    }
    for (const id of oldSet) {
      if (!newSet.has(id)) removed = true;
    }
    if (added && removed) return 'both';
    if (added) return 'add';
    if (removed) return 'remove';
    return null;
  }

  confirmSaveModifiedLicenceOptions() {
    if (
      !this.modifyingLicence ||
      !this.selectedClient ||
      !this.clientSubscription ||
      this.clientSubscription.LicenceId !== this.modifyingLicence.Id
    ) {
      this.showModifyPopup = false;
      this.modifyingLicence = null;
      return;
    }

    const dateDebut = this.clientSubscription.DateDebut
    ? this.formatDateForBackend(this.clientSubscription.DateDebut)
    : this.formatDateForBackend(new Date());
    // Only update Price, do not change DateDebut or DateFin
    const updatedSubscription = {
      ...this.clientSubscription,
      Price: this.getNewTotalWithOldForModifying(),
      DateDebut: this.clientSubscription.DateDebut,
      DateFin: this.clientSubscription.DateFin,
      Status: this.clientSubscription.Status,
      PayementFrequency: this.clientSubscription.PaymentFrequency
    };
    this.subscriptionApiService.update(updatedSubscription).subscribe({
      next: () => {
        if (this.modifyingLicence) {
          this.saveOptionsForLicence(this.modifyingLicence);
        }
        this.fetchClients().then(() => {
          const refrehedClient = this.clients.find(c => c.Id === this.selectedClient?.Id);
          if(refrehedClient) {
            this.selectClient(refrehedClient);
          }
        });
        this.fetchSubscriptions();
        this.showModifyPopup = false;
        this.modifyingLicence = null;
      },
      error: () => {
        if (this.modifyingLicence) {
          this.saveOptionsForLicence(this.modifyingLicence);
        }
        this.fetchClients().then(() => {
          const refrehedClient = this.clients.find(c => c.Id === this.selectedClient?.Id);
          if(refrehedClient) {
            this.selectClient(refrehedClient);
          }
        });
        this.fetchSubscriptions();
        this.showModifyPopup = false;
        this.modifyingLicence = null;
      }
    });
  }

  cancelModifyLicence() {
    // Restore checkedOptions for all licences to their initial DB state (front only)
    Object.keys(this.initialCheckedOptions).forEach(licenceId => {
      this.checkedOptions[licenceId] = new Set<string>(Array.from(this.initialCheckedOptions[licenceId]));
    });
    this.showModifyPopup = false;
    this.modifyingLicence = null;
  }

  saveOptionsForLicence(licence: Licence) {
    if (!this.selectedClient || !this.clientSubscription) return;

    const subscriptionId = this.clientSubscription.Id;
    const optionIds = Array.from(this.checkedOptions[licence.Id] || []);

    this.subscribedOptionsApiService.getAll().subscribe({
      next: (allSubscribedOptions) => {
        const toDelete = allSubscribedOptions.filter(
          so => so.SubscriptionId === subscriptionId
        );
        let deleteCount = 0;
        if (toDelete.length === 0) {
          createAll();
        } else {
          toDelete.forEach(so => {
            this.subscribedOptionsApiService.delete(so.Id).subscribe({
              next: () => {
                deleteCount++;
                if (deleteCount === toDelete.length) createAll();
              },
              error: () => {
                deleteCount++;
                if (deleteCount === toDelete.length) createAll();
              }
            });
          });
        }
        const self = this;
        function createAll() {
          optionIds.forEach(optionId => {
            const subscribedOption: Partial<SubscribedOptions> = {
              SubscriptionId: subscriptionId,
              OptionId: optionId,
              checked: true
            };
            self.subscribedOptionsApiService.create(subscribedOption).subscribe();
          });
          
          self.initialCheckedOptions[licence.Id] = new Set(optionIds);
          self.assignedLicenceChanges[licence.Id] = false;
          self.showSaveNotification = true;
          setTimeout(() => self.showSaveNotification = false, 5000);
        }
      }
    });
  }

  confirmLicenseApplication() {
    if (!this.selectedClient || !this.selectedLicenseForConfirmation) {
      return;
    }
    const today = new Date();
    const dateFin = new Date(today);
    dateFin.setMonth(today.getMonth() + 1);

    const dateDebutStr = this.formatDate(today);
    const dateFinStr = this.formatDate(dateFin);

    const clientId = this.selectedClient.Id;
    const licenceId = this.selectedLicenseForConfirmation.Id;
    const checkedSet = this.checkedOptions[licenceId] || new Set();

    const totalPrice = this.getOptionsForLicence(this.selectedLicenseForConfirmation)
      .filter(opt => checkedSet.has(opt.Id))
      .reduce((sum, opt) => sum + (opt.Price || 0), 0);

    this.subscriptionApiService.getAll().subscribe({
      next: (subscriptions: Subscription[]) => {
        const existing = subscriptions.find(
          sub => sub.ClientId === clientId
        );
        if (existing) {
          const updated = {
            ...existing,
            LicenceId: licenceId,
            DateDebut: dateDebutStr,
            DateFin: dateFinStr,
            Price: totalPrice,
            Status: 'Payé',
            PaymentFrequency: 'Mensuel'
          };
          this.subscriptionApiService.update(updated).subscribe({
            next: (updatedSub: Subscription) => {
              this.createSubscribedOptionsForLicence(updatedSub.Id, this.selectedLicenseForConfirmation!);
              this.showSuccessNotification = true;
              this.showConfirmationPopup = false;
              this.selectedLicenseForConfirmation = null;
              this.fetchSubscriptions();
              this.fetchClients().then(() => {
                const refrehedClient = this.clients.find(c => c.Id === this.selectedClient?.Id);
                if(refrehedClient) {
                  this.selectClient(refrehedClient);
                }
              });
              setTimeout(() => {
                this.showSuccessNotification = false;
              }, 500);
            },
            error: (err) => {}
          });
        } else {
          const subscription = {
            DateDebut: dateDebutStr,
            DateFin: dateFinStr,
            ClientId: clientId,
            LicenceId: licenceId,
            Price: totalPrice,
            Status: 'Payé',
            PaymentFrequency: 'Mensuel'
          };
          this.subscriptionApiService.create(subscription).subscribe({
            next: (createdSub: Subscription) => {
              this.createSubscribedOptionsForLicence(createdSub.Id, this.selectedLicenseForConfirmation!);
              this.showSuccessNotification = true;
              this.showConfirmationPopup = false;
              this.selectedLicenseForConfirmation = null;
              this.fetchSubscriptions();
              this.fetchClients().then(() => {
                const refrehedClient = this.clients.find(c => c.Id === this.selectedClient?.Id);
                if(refrehedClient) {
                  this.selectClient(refrehedClient);
                }
              });
              setTimeout(() => {
                this.showSuccessNotification = false;
              }, 1500);
            },
            error: (err) => {}
          });
        }
      },
      error: (err) => {}
    });
  }

  cancelLicenseApplication() {
    this.showConfirmationPopup = false;
    
    this.selectedLicenseForConfirmation = null;
  }

  closeSuccessNotification() {
    this.showSuccessNotification = false;
  }

  @HostListener('document:click', ['$event'])
  onClick(event: MouseEvent) {
    if (!(event.target as HTMLElement).closest('.search-input-container')) {
      this.showDropdown = false;
    }
  }

  getOptionsForLicence(licence: Licence): Option[] {
    if (!licence) {
      return [];
    }

    if (licence.LicenceOptions && Array.isArray(licence.LicenceOptions)) {
      return licence.LicenceOptions
        .map(lo => lo.Option)
        .filter(opt => opt !== null && opt !== undefined) as Option[];
    }

    const licenceOptionLinks = this.licenceOptions.filter(lo => lo.LicenceId === licence.Id);

    const options = licenceOptionLinks
      .map(lo => {
        if (lo.Option) return lo.Option;
        return this.options.find(opt => opt.Id === lo.OptionId);
      })
      .filter(opt => opt !== undefined && opt !== null) as Option[];

    return options;
  }

  getLicenceOptionsForCard(licence: Licence): Array<{
    licenceId: string;
    licenceName: string;
    licenceDescription: string;
    optionId: string;
    optionName: string;
    optionPrice: number;
  }> {
    let licenceOptionLinks: LicenceOption[] = [];

    if (licence.Name === licence.Id) {
      licenceOptionLinks = this.licenceOptions.filter(lo => lo.LicenceId === licence.Id);
    } else if (licence.LicenceOptions && Array.isArray(licence.LicenceOptions)) {
      licenceOptionLinks = licence.LicenceOptions;
    } else {
      licenceOptionLinks = this.licenceOptions.filter(lo => lo.LicenceId === licence.Id);
    }

    return licenceOptionLinks
      .map(lo => {
        let option = lo.Option || this.options.find(opt => opt.Id === lo.OptionId);
        if (!option) return null;
        return {
          licenceId: licence.Id,
          licenceName: licence.Name,
          licenceDescription: licence.Description ?? '',
          optionId: option.Id,
          optionName: option.Name,
          optionPrice: option.Price
        };
      })
      .filter(item => !!item);
  }

  allOptionsIncluded(licence: Licence): boolean {
    const options = this.getOptionsForLicence(licence);
    return options.length > 0;
  }

  public filterClients() {
    if (!this.searchQuery.trim()) {
      this.filteredClients = [];
      this.showDropdown = false;
      return;
    }

    const query = this.searchQuery.toLowerCase().trim();
    this.filteredClients = this.clients
      .filter(client => 
        client.Name && client.Name.toLowerCase().includes(query)
      )
      .slice(0, 5);
    
    this.showDropdown = this.filteredClients.length > 0;
  }

  get imageUrl(): string | null {
    if (this.selectedClient && this.selectedClient.ClientLogo) {
      return `data:image/png;base64,${this.selectedClient.ClientLogo}`;
    }
    return null;
  }

  isLicenceAssignedToClient(licence: Licence): boolean {
    if (!this.selectedClient || !this.clientSubscription) return false;
    return this.clientSubscription.LicenceId === licence.Id;
  }

  isAffecterDisabled(licence: Licence): boolean {
    if (!this.selectedClient) return true;
    return !!this.clientSubscription && this.clientSubscription.LicenceId !== licence.Id;
  }

  showAssignedActions(licence: Licence): boolean {
    return this.isLicenceAssignedToClient(licence);
  }

  showUpgradeButton(licence: Licence): boolean {
    if (!this.selectedClient || !this.clientSubscription) return false;
    return this.clientSubscription.LicenceId !== licence.Id;
  }

  cancelLicenceForClient(licence: Licence) {
    this.licenceToCancel = licence;
    this.showCancelPopup = true;
  }

  confirmCancelLicenceForClient() {
  if (!this.selectedClient || !this.licenceToCancel) return;
  const idClient = this.selectedClient.Id ?? '';

  this.subscriptionApiService.getAll().subscribe({
    next: (subscriptions: Subscription[]) => {
      const sub = subscriptions.find(
        s => s.ClientId === idClient && s.LicenceId === this.licenceToCancel!.Id
      );
      if (sub) {
        const today = new Date();
        const updatedSubscription = {
          ...sub,
          Status: 'Annuler',
          PaymentFrequency: null,
          DateFin: this.formatDateForBackend(today)
        };

        // Correctly pass both ID and updated subscription object
        this.subscriptionApiService.update(updatedSubscription).subscribe({
          next: () => {
            if (idClient && this.assignedLicences[idClient] === this.licenceToCancel!.Id) {
              delete this.assignedLicences[idClient];
              this.saveAssignedLicences();
            }
            
            this.fetchSubscriptions();
            this.clientSubscription = null;
            
            this.initialCheckedOptions = {};
            // Reset UI state
            this.showCancelPopup = false;
            this.licenceToCancel = null;
            this.showCancelNotification = true;
            
            // Reset checked options for all licenses
            this.licences.forEach(licence => {
              const allOptionIds = this.getOptionsForLicence(licence).map(opt => opt.Id);
              this.checkedOptions[licence.Id] = new Set(allOptionIds);
            });
            
            // Hide notification after delay
            setTimeout(() => {
              this.showCancelNotification = false;
            }, 1500);
          },
          error: (error) => {
            console.error('Error cancelling subscription:', error);
            this.showCancelPopup = false;
            this.showCancelNotification = true;
            setTimeout(() => this.showCancelNotification = false, 1500);
          }
        });
      } else {
        // Handle case where subscription wasn't found
        this.showCancelPopup = false;
        this.showCancelNotification = true;
        setTimeout(() => this.showCancelNotification = false, 1500);
      }
    },
    error: (error) => {
      console.error('Error fetching subscriptions:', error);
      this.showCancelPopup = false;
      this.showCancelNotification = true;
      setTimeout(() => this.showCancelNotification = false, 1500);
    }
  });
}

  upgradeLicenceForClient(licence: Licence) {
    // Check if at least one option is checked
    const checked = this.checkedOptions[licence.Id] || new Set();
    if (checked.size === 0) {
      this.showNoOptionCheckedPopup = true;
      this._pendingRestoreLicence = licence;
      this._pendingRestoreType = 'upgrade';
      return;
    }
    if (!this.selectedClient) return;
    this.oldLicence = this.licences.find(l => l.Id === this.clientSubscription?.LicenceId) || null;
    this.selectedLicenseForConfirmation = licence;
    this.oldOptionsLoaded = false;
    if (this.clientSubscription) {
      this.subscribedOptionsApiService.getAll().subscribe({
        next: (subscribedOptions: SubscribedOptions[]) => {
          const oldSubscribedOptions = subscribedOptions.filter(
            so => so.SubscriptionId === this.clientSubscription!.Id
          );
          const oldOptionIds = oldSubscribedOptions.map(so => so.OptionId);
          this.oldOptions = this.getOptionsForLicence(this.oldLicence!).filter(opt => oldOptionIds.includes(opt.Id));
          if (this.oldLicence) {
            this.checkedOptions[this.oldLicence.Id] = new Set(oldOptionIds);
          }
          this.oldOptionsLoaded = true;
        }
      });
      if (!this.checkedOptions[licence.Id] || this.checkedOptions[licence.Id].size === 0) {
        const allOptionIds = this.getOptionsForLicence(licence).map(opt => opt.Id);
        this.checkedOptions[licence.Id] = new Set(allOptionIds);
      }
    } else {
      const allOptionIds = this.getOptionsForLicence(licence).map(opt => opt.Id);
      this.checkedOptions[licence.Id] = new Set(allOptionIds);
      this.oldOptions = [];
      this.oldOptionsLoaded = true;
    }
    this.showUpgradePopup = true;
    this.calculateUpgradePrices();
  }

  getNewOptionsForUpgrade(): Option[] {
    if (!this.selectedLicenseForConfirmation) return [];
    const checkedSet = this.checkedOptions[this.selectedLicenseForConfirmation.Id] || new Set();
    return this.getOptionsForLicence(this.selectedLicenseForConfirmation).filter(opt => checkedSet.has(opt.Id));
  }

  getNewTotalForUpgrade(): number {
    return this.getNewOptionsForUpgrade().reduce((sum, opt) => sum + (opt.Price || 0), 0);
  }

  getOldTotalForUpgrade(): number {
    return this.oldOptions.reduce((sum, opt) => sum + (opt.Price || 0), 0);
  }

  private formatDateForBackend(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

  confirmUpgradeLicence() {
    if (!this.selectedClient || !this.selectedLicenseForConfirmation) {
      return;
    }
    const today = new Date();
    const dateFin = new Date(today);
    dateFin.setMonth(today.getMonth() + 1);

    const dateFinStr = this.formatDateForBackend(dateFin);

    const clientId = this.selectedClient.Id;
    const licenceId = this.selectedLicenseForConfirmation.Id;
    const checkedSet = this.checkedOptions[licenceId] || new Set();

    // const totalPrice = this.getOptionsForLicence(this.selectedLicenseForConfirmation)
    //   .filter(opt => checkedSet.has(opt.Id))
    //   .reduce((sum, opt) => sum + (opt.Price || 0), 0);

      const totalPrice = Math.abs(this.proratedTotalForUpgrade);
      

    this.subscriptionApiService.getAll().subscribe({
      next: (subscriptions: Subscription[]) => {
        const existing = subscriptions.find(
          sub => sub.ClientId === clientId
        );
        if (existing) {
          const updated = {
            ...existing,
            LicenceId: licenceId,
            DateDebut: this.clientSubscription?.DateDebut,
            DateFin: this.clientSubscription?.DateFin,
            Price: totalPrice,
            Status: this.clientSubscription?.Status,
            PayementFrequency: this.billingCycle
          };
          this.subscriptionApiService.update(updated).subscribe({
            next: (updatedSub: Subscription) => {
              this.createSubscribedOptionsForLicence(updatedSub.Id, this.selectedLicenseForConfirmation!);
              this.showSuccessNotification = true;
              this.showUpgradePopup = false;
              this.selectedLicenseForConfirmation = null;
              this.oldLicence = null;
              this.oldOptions = [];
              this.oldOptionsLoaded = false;
              this.fetchSubscriptions();
              this.fetchClients().then(() => {
                const refrehedClient = this.clients.find(c => c.Id === this.selectedClient?.Id);
                if(refrehedClient) {
                  this.selectClient(refrehedClient);
                }
              });
              setTimeout(() => {
                this.showSuccessNotification = false;
              }, 1500);
            },
            error: (err) => {}
          });
        }
      },
      error: (err) => {}
    });
  }

  formatDate(date: Date | string | null): string {
    if (!date) return '';
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    return `${day}/${month}/${year}`;
  }

  parseDate(dateStr: string): Date {
    const [day, month, year] = dateStr.split('/');
    return new Date(Number(year), Number(month) - 1, Number(day));
  }

  private loadAssignedLicences() {
    const data = localStorage.getItem('assignedLicences');
    if (data) {
      try { this.assignedLicences = JSON.parse(data); }
      catch { this.assignedLicences = {}; }
    }
  }

  closeCancelPopup() {
    this.showCancelPopup = false;
    this.licenceToCancel = null;
    this.showCancelNotification = false;
  }

  cancelUpgradePopup() {
    this.showUpgradePopup = false;
    this.selectedLicenseForConfirmation = null;
    this.oldLicence = null;
    this.oldOptions = [];
    this.oldOptionsLoaded = false;
  }

  getLicenceOptionsTotal(licence: Licence): number {
    const checkedSet = this.checkedOptions[licence?.Id] || new Set();
    return this.getOptionsForLicence(licence)
      .filter(opt => checkedSet.has(opt.Id))
      .reduce((sum, opt) => sum + (opt.Price || 0), 0);
  }

  createSubscribedOptionsForLicence(subscriptionId: string, licence: Licence) {
    const checkedOptionIds = Array.from(this.checkedOptions[licence.Id] || []);
    this.subscribedOptionsApiService.getAll().subscribe({
      next: (allSubscribedOptions: SubscribedOptions[]) => {
        const toDelete = allSubscribedOptions.filter(
          (so: SubscribedOptions) => so.SubscriptionId === subscriptionId
        );
        let deleteCount = 0;
        if (toDelete.length === 0) {
          this.createCheckedOptions(subscriptionId, checkedOptionIds);
        } else {
          toDelete.forEach((so: SubscribedOptions) => {
            this.subscribedOptionsApiService.delete(so.Id).subscribe({
              next: () => {
                deleteCount++;
                if (deleteCount === toDelete.length) {
                  this.createCheckedOptions(subscriptionId, checkedOptionIds);
                }
              },
              error: () => {
                deleteCount++;
                if (deleteCount === toDelete.length) {
                  this.createCheckedOptions(subscriptionId, checkedOptionIds);
                }
              }
            });
          });
        }
      }
    });
  }

  private createCheckedOptions(subscriptionId: string, checkedOptionIds: string[]) {
    if (checkedOptionIds.length === 0) {
      return;
    }
    checkedOptionIds.forEach(optionId => {
      const subscribedOption: Partial<SubscribedOptions> = {
        SubscriptionId: subscriptionId,
        OptionId: optionId,
        checked: true
      };
      this.subscribedOptionsApiService.create(subscribedOption).subscribe();
    });
  }

  resetCheckedOptionsForLicence(licenceId: string) {
    const licence = this.licences.find(l => l.Id === licenceId);
    if (licence) {
      const allOptionIds = this.getOptionsForLicence(licence).map(opt => opt.Id);
      this.checkedOptions[licenceId] = new Set(allOptionIds);
    }
  }

  isOptionLinkedToLicence(licenceId: string, optionId: string): boolean {
    return this.licenceOptions.some(lo => lo.LicenceId === licenceId && lo.OptionId === optionId);
  }

  saveAssignedLicences() {
    localStorage.setItem('assignedLicences', JSON.stringify(this.assignedLicences));
  }

  modifieLicenceForClient(licence: Licence) {
    this.modifyingLicence = licence;
    this.showModifyPopup = false; 
    if (this.clientSubscription && this.clientSubscription.LicenceId === licence.Id) {
      this.fetchCheckedOptionsForClientSubscription(this.clientSubscription.Id, licence.Id);
    }
  }

  getProratedPrice(price: number): number {
  const today = new Date();
  const year = today.getFullYear();
  const month = today.getMonth();
  const daysInMonth = new Date(year, month + 1, 0).getDate();
  const currentDay = today.getDate();
  const daysRemaining = daysInMonth - currentDay + 1;
  return (price / daysInMonth) * daysRemaining;
  }

calculateUpgradePrices() {
  if (!this.selectedLicenseForConfirmation || !this.selectClient || !this.clientSubscription) return;

  const oldTotal = this.clientSubscription.Price || 0;
  
  const today = new Date();
  const year = today.getFullYear();
  const month = today.getMonth();
  const daysInMonth = new Date(year, month + 1, 0).getDate();
  const currentDay = today.getDate();
  const daysRemaining = daysInMonth - currentDay + 1;
  
  this.proratedOldForUpgrade = oldTotal;

  const newBasePrice = this.clientSubscription.Price || 0;
  const newOptionsTotal = this.getNewTotalForUpgrade();
  const newTotalPrice = newBasePrice + newOptionsTotal;


  this.proratedNewForUpgrade = (newTotalPrice / daysInMonth) * daysRemaining;

  const difference = this.proratedNewForUpgrade - this.proratedOldForUpgrade;

  if (difference > 0) {

    this.proratedDiferencePay = difference;
    this.proratedDiferenceReturn = 0;

  } else {

    this.proratedDiferencePay = 0;
    this.proratedDiferenceReturn = Math.abs(difference);

  }

  this.proratedTotalForUpgrade = newTotalPrice;
  } 
  onClearSelection() {
    this.clearSelection();
  }

}