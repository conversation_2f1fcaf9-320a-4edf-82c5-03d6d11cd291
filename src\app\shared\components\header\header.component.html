<!-- src/app/shared/components/header/header.component.html -->
<header class="header"> 
    <div class="profile" (click)="toggleProfileMenu()" matTooltip="Profile">
      <mat-icon>account_circle</mat-icon>
      <span class="user-name" *ngIf="!isSidebarCollapsed">{{ userName }}</span>
    </div>
    <div class="profile-dropdown" *ngIf="showProfileMenu" [@dropdownAnimation]>
      <div class="profile-item">
        <mat-icon>person</mat-icon>
        <span>{{ userRole }}</span>
      </div>
      <div class="profile-item" (click)="logout()">
        <mat-icon>logout</mat-icon>
        <span>Logout</span>
      </div>
    </div>
</header>