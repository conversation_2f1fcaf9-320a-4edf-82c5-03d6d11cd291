// menu.model.ts
export interface Menu {
  name?: string;
  icon?: string;
  starts?: string[];
  url?: string;
  badgeName?: string;
  badgeClass?: string;
  subMenus?: Menu[];
  authority?: string[];
}

export const menu: Menu[] = [
  {
    name: "Accueil",
    icon: "dashboard",
    url: "/accueil",
    authority: ["USER", "ADMIN", "SuperADMIN"]
  },
  {
    name: "Paramétrage",
    icon: "settings",
    starts: ["/organisation-management", "/site-management", "/local-management"],
    url: "#",
    authority: ["ADMIN", "SuperADMIN"],
    subMenus: [
      {
        name: "Client",
        url: "/organisation-management",
        icon: "business",
        authority: ["ADMIN", "SuperADMIN"],
        },
        {
            name: "Sites",
            url: "/site-management",
            icon: "location_on",
            authority: ["ADMIN", "SuperADMIN"],
            },
            {
                name: "<PERSON><PERSON><PERSON>",
                url: "/local-management",
                icon: "meeting_room",
                authority: ["ADMIN", "SuperADMIN"]
              }
            
          
        
      
    ]
  },
  {
    name: "Rapports Énergétiques",
    icon: "assessment",
    url: "/energy-report",
    authority: ["USER", "ADMIN", "SuperADMIN"]
  },
  {
    name: "Network Monitoring",
    icon: "network_check",
    url: "/devices",
    authority: ["ADMIN", "SuperADMIN"]
  },
  {
    name: "Licence",
    icon: "assignment",
    url: "#",
    authority: ["ADMIN", "SuperADMIN"],
    subMenus: [
      {
        name: "Affectation",
        url: "/licence",
        icon: "how_to_reg",
        authority: ["ADMIN", "SuperADMIN"]
      },
      {
        name: "Liste",
        url: "/licence-liste",
        icon: "list_alt",
        authority: ["ADMIN", "SuperADMIN"]
      }
    ]
  },
  {
    name: "IoT",
    icon: "devices",
    url: "/iot",
    authority: ["ADMIN", "SuperADMIN"]
  },
  {
    name: "Facturation",
    icon: "receipt_long",
    url: "/facture",
    authority: ["ADMIN", "SuperADMIN"]
  },
  {
    name: "Gestion des Règles",
    icon: "rule",
    url: "/generator-regles-ia",
    authority: ["ADMIN", "SuperADMIN"]
  },
  {
    name: "Règles en IA",
    icon: "rule",
    url: "/reglesIA",
    authority: ["ADMIN", "SuperADMIN"]
  },
  {
    name: "Installation Capteur",
    icon: "sensors",
    url: "/installCapteur",
    authority: ["ADMIN", "SuperADMIN"]
  },
  {
    name: "Editeur de Plan",
    icon: "draw",
    url: "/planeditor",
    authority: ["ADMIN", "SuperADMIN"]
  }
];