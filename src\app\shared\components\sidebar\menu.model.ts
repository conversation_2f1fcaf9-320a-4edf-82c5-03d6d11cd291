// menu.model.ts
export interface Menu {
  name?: string;
  icon?: string;
  starts?: string[];
  url?: string;
  badgeName?: string;
  badgeClass?: string;
  subMenus?: Menu[];
  authority?: string[];
}

export const menu: Menu[] = [
  {
    name: 'Accueil',
    icon: 'dashboard',
    url: '/accueil',
    authority: [ 'ADMINISTRATEUR', 'INTEGRATEUR'],
  },

  {
    name: 'Paramétrage',
    icon: 'settings',
    starts: [
      '/organisation-management',
      '/site-management',
      '/local-management',
    ],
    url: '#',
    authority: ['ADMINISTRATEUR', 'INTEGRATEUR'],
    subMenus: [
      {
        name: 'Client',
        url: '/organisation-management',
        icon: 'business',
        authority: ['ADMINISTRATEUR', 'INTEGRATEUR'],
      },
      {
        name: 'Sites',
        url: '/site-management',
        icon: 'location_on',
        authority: ['ADMINISTRATEUR', 'INTEGRATEUR'],
      },
      {
        name: '<PERSON><PERSON><PERSON>',
        url: '/local-management',
        icon: 'meeting_room',
        authority: ['ADMINISTRATEUR', 'INTEGRATEUR'],
      },
      {
        name: '<PERSON>tr<PERSON><PERSON><PERSON>',
        url: '/controllers',
        icon: 'device_hub',
        authority: ['ADMINISTRATEUR', 'INTEGRATEUR'],
      },
    ],
  },
  {
    name: 'Rapports Énergétiques',
    icon: 'assessment',
    url: '/energy-report',
    authority: ['ADMINISTRATEUR', 'INTEGRATEUR'],
  },
  {
    name: 'Network Monitoring',
    icon: 'network_check',
    url: '/devices',
    authority: ['ADMINISTRATEUR', 'INTEGRATEUR'],
  },
  {
    name: 'Licence',
    icon: 'assignment',
    url: '#',
    authority: ['ADMINISTRATEUR', 'INTEGRATEUR'],
    subMenus: [
      {
        name: 'Affectation',
        url: '/licence',
        icon: 'how_to_reg',
        authority: ['ADMINISTRATEUR', 'INTEGRATEUR'],
      },
      {
        name: 'Liste',
        url: '/licence-liste',
        icon: 'list_alt',
        authority: ['ADMINISTRATEUR', 'INTEGRATEUR'],
      },
    ],
  },
  {
    name: 'IoT',
    icon: 'devices',
    url: '/iot',
    authority: ['ADMINISTRATEUR', 'INTEGRATEUR'],
  },
  {
    name: 'Facturation',
    icon: 'receipt_long',
    url: '/facture',
    authority: ['ADMINISTRATEUR', 'INTEGRATEUR'],
  },
  {
    name: 'Gestion des Règles',
    icon: 'rule',
    url: '/generator-regles-ia',
    authority: ['ADMINISTRATEUR', 'INTEGRATEUR'],
  },
  {
    name: 'Règles en IA',
    icon: 'rule',
    url: '/reglesIA',
    authority: ['ADMINISTRATEUR', 'INTEGRATEUR'],
  },
  {
    name: 'Installation Capteur',
    icon: 'sensors',
    url: '/installCapteur',
    authority: ['ADMINISTRATEUR', 'INTEGRATEUR'],
  },
  {
    name: 'Editeur de Plan',
    icon: 'draw',
    url: '/planeditor',
    authority: ['ADMINISTRATEUR', 'INTEGRATEUR'],
  },
  {
    name: 'Gestion des Utilisateurs',
    icon: 'people',
    url: '/user-management',
    authority: ['ADMINISTRATEUR', 'INTEGRATEUR'],
  },
  {
    name: 'Gestion des Tags',
    icon: 'local_offer',
    url: '/tag-management',
    authority: ['ADMINISTRATEUR', 'INTEGRATEUR'],
  },
];
