<!-- User Management Component -->
<div class="user-management-container">
  <!-- Header Section -->
  <div class="breadcrumb-nav">
    <span class="breadcrumb-text">
      <mat-icon class="title-icon">people</mat-icon>
      Gestion des Utilisateurs
    </span>
  </div>

  <!-- Users Section -->
  <div class="users-section">
    <!-- Search Section -->
    <div class="search-section">
      <div class="search-container">
        <input
          type="text"
          [(ngModel)]="searchParam"
          placeholder="Rechercher par nom, email ou rôle..."
          class="search-input"
          (keyup)="onSearchKeyup($event)"
        />
        <button class="search-button" (click)="searchUsers()">
          <i class="material-icons">search</i>
        </button>
      </div>
    </div>

    <!-- Section Header -->
    <div class="section-header">
      <h3 class="section-title">Utilisateurs</h3>
      <button class="btn btn-primary add-user-btn" (click)="addNewUser()">
        <i class="material-icons">add</i> Ajouter un utilisateur
      </button>
    </div>

    <!-- Loading Container -->
    <div class="loading-container" *ngIf="isLoadingUsers">
      <div class="spinner"></div>
      <p>Chargement des utilisateurs...</p>
    </div>

    <!-- Table View -->
    <div class="table-container" *ngIf="!isLoadingUsers && filteredUsers.length > 0">
      <app-generic-table
        [data]="paginatedUsers"
        [headers]="tableHeaders"
        [keys]="tableKeys"
        [actions]="tableActions"
        (actionTriggered)="handleTableAction($event)"
      >
      </app-generic-table>

      <!-- Pagination -->
      <div class="pagination-container">
        <mat-paginator
          [length]="totalUsers"
          [pageSize]="pageSize"
          [pageIndex]="currentPage"
          [pageSizeOptions]="[5, 10, 25, 50]"
          (page)="onPageChange($event)"
          aria-label="Select page"
        >
        </mat-paginator>
      </div>
    </div>

    <!-- No Users Message -->
    <div class="no-users-message" *ngIf="!isLoadingUsers && filteredUsers.length === 0">
      <i class="material-icons">people_outline</i>
      <p *ngIf="!hasSearchFilter">Aucun utilisateur trouvé. Commencez par créer votre premier utilisateur.</p>
      <p *ngIf="hasSearchFilter">Aucun résultat pour votre recherche "{{ searchParam }}"</p>
    </div>
  </div>
</div>

<!-- User Form Popup -->
<div class="popup-overlay" *ngIf="showUserForm" (click)="closeUserForm()">
  <div class="popup-form" (click)="$event.stopPropagation()">
    <div class="popup-header">
      <h3>
        <mat-icon>{{ isEditMode ? "edit" : "person_add" }}</mat-icon>
        {{ isEditMode ? "Modifier l'Utilisateur" : "Ajouter un Utilisateur" }}
      </h3>
      <button class="close-btn" (click)="closeUserForm()">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <form [formGroup]="userForm" (ngSubmit)="submitUserForm()" class="user-form">
      <!-- Validation errors section -->
      <div class="validation-errors" *ngIf="showValidationErrors">
        <div class="error-header">
          <mat-icon>warning</mat-icon>
          <span>{{ validationErrorMessage }}</span>
        </div>
        <ul class="validation-errors-list">
          <li *ngIf="userForm.get('userName')?.invalid && userForm.get('userName')?.touched">
            <mat-icon>error</mat-icon>
            {{ getFieldError('userName') }}
          </li>
          <li *ngIf="userForm.get('email')?.invalid && userForm.get('email')?.touched">
            <mat-icon>error</mat-icon>
            {{ getFieldError('email') }}
          </li>
          <li *ngIf="userForm.get('phoneNumber')?.invalid && userForm.get('phoneNumber')?.touched">
            <mat-icon>error</mat-icon>
            {{ getFieldError('phoneNumber') }}
          </li>
          <li *ngIf="userForm.get('roles')?.invalid && userForm.get('roles')?.touched">
            <mat-icon>error</mat-icon>
            {{ getFieldError('roles') }}
          </li>
          <li *ngIf="userForm.get('newPassword')?.invalid && userForm.get('newPassword')?.touched && !isEditMode">
            <mat-icon>error</mat-icon>
            {{ getFieldError('newPassword') }}
          </li>
          <li *ngIf="userForm.get('confirmPassword')?.invalid && userForm.get('confirmPassword')?.touched && !isEditMode">
            <mat-icon>error</mat-icon>
            {{ getFieldError('confirmPassword') }}
          </li>
          <li *ngIf="userForm.hasError('passwordMismatch') && !isEditMode">
            <mat-icon>error</mat-icon>
            Les mots de passe ne correspondent pas
          </li>
        </ul>
      </div>

      <div class="form-grid">
        <!-- Username Field -->
        <mat-form-field appearance="outline" class="form-field">
          <mat-label>Nom d'utilisateur <span class="required">*</span></mat-label>
          <input
            matInput
            formControlName="userName"
            placeholder="Entrez le nom d'utilisateur"
            required
          />
          <mat-icon matSuffix>person</mat-icon>
          <mat-error *ngIf="userForm.get('userName')?.invalid">
            {{ getFieldError('userName') }}
          </mat-error>
        </mat-form-field>

        <!-- Email Field -->
        <mat-form-field appearance="outline" class="form-field">
          <mat-label>Adresse Email <span class="required">*</span></mat-label>
          <input 
            matInput
            type="email" 
            formControlName="email" 
            placeholder="<EMAIL>"
            required 
          />
          <mat-icon matSuffix>email</mat-icon>
          <mat-error *ngIf="userForm.get('email')?.invalid">
            {{ getFieldError('email') }}
          </mat-error>
        </mat-form-field>

        <!-- Phone Field -->
        <mat-form-field appearance="outline" class="form-field">
          <mat-label>Téléphone <span class="required">*</span></mat-label>
          <input 
            matInput
            type="tel" 
            formControlName="phoneNumber" 
            placeholder="+33 1 23 45 67 89"
            required
          />
          <mat-icon matSuffix>phone</mat-icon>
          <mat-error *ngIf="userForm.get('phoneNumber')?.invalid">
            {{ getFieldError('phoneNumber') }}
          </mat-error>
        </mat-form-field>

        <!-- Role Field -->
        <mat-form-field appearance="outline" class="form-field">
          <mat-label>Rôles <span class="required">*</span></mat-label>
          <mat-select formControlName="roles" multiple required>
            <mat-option *ngFor="let role of userRoles" [value]="role.value">
              <div class="role-option">
                <span class="role-label">{{ role.label }}</span>
                <span class="role-description">{{ role.description }}</span>
              </div>
            </mat-option>
          </mat-select>
          <mat-icon matSuffix>admin_panel_settings</mat-icon>
          <mat-error *ngIf="userForm.get('roles')?.invalid">
            {{ getFieldError('roles') }}
          </mat-error>
          <mat-hint>Vous pouvez sélectionner plusieurs rôles</mat-hint>
        </mat-form-field>

        <!-- Password Fields (only for create mode) -->
        <mat-form-field appearance="outline" class="form-field" *ngIf="!isEditMode">
          <mat-label>Mot de passe <span class="required">*</span></mat-label>
          <input
            matInput
            type="password"
            formControlName="newPassword"
            placeholder="Minimum 6 caractères"
            required
          />
          <mat-icon matSuffix>lock</mat-icon>
          <mat-error *ngIf="userForm.get('newPassword')?.invalid">
            {{ getFieldError('newPassword') }}
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="form-field" *ngIf="!isEditMode">
          <mat-label>Confirmer le mot de passe <span class="required">*</span></mat-label>
          <input
            matInput
            type="password"
            formControlName="confirmPassword"
            placeholder="Répétez le mot de passe"
            required 
          />
          <mat-icon matSuffix>lock_outline</mat-icon>
          <mat-error *ngIf="userForm.get('confirmPassword')?.invalid || userForm.hasError('passwordMismatch')">
            {{ getFieldError('confirmPassword') }}
          </mat-error>
        </mat-form-field>

        <!-- Password Change Fields (only for edit mode) -->
        <mat-form-field appearance="outline" class="form-field full-width" *ngIf="isEditMode">
          <mat-label>Nouveau mot de passe (optionnel)</mat-label>
          <input
            matInput
            type="password"
            formControlName="newPassword"
            placeholder="Laissez vide pour ne pas changer"
          />
          <mat-icon matSuffix>lock</mat-icon>
          <mat-hint>Laissez vide si vous ne souhaitez pas changer le mot de passe</mat-hint>
        </mat-form-field>
      </div>

      <div class="form-actions">
        <button type="button" class="btn-cancel" (click)="closeUserForm()">
          Annuler
        </button>
        <button type="submit" class="btn-submit" [disabled]="userForm.invalid">
          {{ isEditMode ? "Enregistrer" : "Créer" }}
        </button>
      </div>
    </form>
  </div>
</div>