// Fixed controller-server.component.ts
import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  OnInit,
  OnChanges,
  OnDestroy,
  SimpleChanges,
  EventEmitter,
  Output,
  HostListener,
} from '@angular/core';
import { ControllerServeur } from '@app/core/models/controllerServeur';
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';
import { PageEvent, MatPaginatorModule } from '@angular/material/paginator';
import { ControllerServeurApiService } from '@app/core/services/administrative/controllerserveur.service';
import { Client } from '@app/core/models/client';
import { Licence } from '@app/core/models/licence';
import { FormCsComponent } from '../form-cs/form-cs.component';
import { DetailsCsComponent } from '../details-cs/details-cs.component';
import { HierarchyComponent } from '../hierarchy/hierarchy.component';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { FilterParam, Lister, Pagination } from '@app/core/models/util/page';
import { SubscriptionApiService } from '@app/core/services/administrative/subscription.service';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { GroupedControllerServer } from '@app/shared/models/ControllerServerHierarchyDto';
import { HierarchyService } from '@app/shared/services/cs-hierarchy.service';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-controller-server',
  imports: [
    CommonModule,
    GenericTableComponent,
    MatPaginatorModule,
    FormCsComponent,
    DetailsCsComponent,
    HierarchyComponent,
    MatIconModule,
    MatTooltipModule,
    FormsModule,
  ],
  templateUrl: './controller-server.component.html',
  styleUrl: './controller-server.component.css',
})
export class ControllerServerComponent implements OnInit, OnChanges, OnDestroy {
  @Input() controllerServeurs: ControllerServeur[] = [];
  @Input() clientId: string = '';
  @Input() client: Client | null = null;
  @Input() licences: Licence[] = [];
  @Output() controllerServerDeleted = new EventEmitter<string>();
  @Output() allControllerServeurs: GroupedControllerServer[] = [];

  // Properties
  displayedControllerServeurs: ControllerServeur[] = [];
  allControllerServeursData: ControllerServeur[] = []; // Store all data for search
  isLoading: boolean = false;
  showCreateForm: boolean = false;
  showDetailsModal: boolean = false;
  showHierarchyModal: boolean = false;
  isEditMode: boolean = false;
  selectedControllerServer: ControllerServeur | null = null;
  searchParam: string = '';
  hasSearchFilter: boolean = false;

  csHeaders: string[] = [
    'Nom',
    'Licence',
    'Nbr Contrôleurs',
    'Nbr Capteurs',
    'Zone Géographic',
    'Conditions Commerciales',
    'Déclencheur',
    'Action',
    'Evenement',
    'Statut',
  ];
  csKeys: string[] = [
    'Name',
    `Licence.Name`,
    'MaxControllers',
    'MaxSensors',
    'GeographicZone',
    'CommercialCondition',
    'TriggerType',
    'ActionType',
    'EventType',
    'Status',
  ];

  pageSize: number = 5;
  currentPage: number = 0;
  totalControllerServeurs: number = 0;

  constructor(
    private readonly controllerServerService: ControllerServeurApiService,
    private readonly subscriptionApiService: SubscriptionApiService,
    private readonly hierarchyService: HierarchyService,
    private dialog: MatDialog
  ) {}

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;

    if (
      (this.showCreateForm ||
        this.showDetailsModal ||
        this.showHierarchyModal) &&
      target &&
      target.classList.contains('modal-overlay')
    ) {
      this.closeAllModals();
    }
  }

  @HostListener('document:keydown.escape', ['$event'])
  onEscapeKey(event: KeyboardEvent): void {
    if (
      this.showCreateForm ||
      this.showDetailsModal ||
      this.showHierarchyModal
    ) {
      this.closeAllModals();
    }
  }

  ngOnInit(): void {
    console.log('Controller Server Component initialized');
    console.log('Licences received:', this.licences);
    this.getControllerServers();
    this.updateMaxValues();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['controllerServeurs']) {
      console.log(
        'Controller serveurs changed:',
        changes['controllerServeurs'].currentValue
      );
      this.updateDisplayedData();
    }

    if (changes['licences']) {
      console.log('Licences changed:', changes['licences'].currentValue);
    }
  }

  ngOnDestroy(): void {
    document.body.classList.remove('modal-open');
  }

  private updateDisplayedData(): void {
    this.controllerServeurs = this.controllerServeurs || [];
    this.totalControllerServeurs = this.controllerServeurs.length;
    this.updatePaginatedData();
  }

  getControllerServers() {
    console.log('Loading controller servers for client:', this.clientId);
    this.isLoading = true;

    // Remove search filter from backend call since we'll handle it on frontend
    const pagination: Pagination = {
      CurrentPage: this.currentPage,
      PageSize: this.pageSize,
      totalElement: 0,
    };

    const filterParams: FilterParam[] = [
      {
        Column: 'ClientId',
        Value: this.clientId,
        Op: 'eq',
        AndOr: 'AND',
      },
    ];

    // Add search filter if searchParam is not empty
    if (this.searchParam && this.searchParam.trim() !== '') {
      this.hasSearchFilter = true;
      filterParams.push({
        Column: 'Name',
        Value: this.searchParam.trim(),
        Op: 'Contains',
        AndOr: 'AND',
      });
    } else {
      this.hasSearchFilter = false;
    }

    const lister: Lister = {
      pagination: pagination,
      filterParams: filterParams,
    };

    // Get subscriptions for table data and licences
    this.subscriptionApiService.gatePage(lister).subscribe({
      next: (response) => {
        console.log('Subscriptions fetched:', response);

        const allControllerServeurs: ControllerServeur[] = [];
        const availableLicences: Licence[] = [];

        if (response?.Content?.length) {
          for (const subscription of response.Content) {
            const licence = subscription.Licence;
            console.log('Processing licence:', licence);

            if (
              licence &&
              !availableLicences.find((l) => l.Id === licence.Id)
            ) {
              availableLicences.push(licence);
            }

            if (licence?.ControllerServeurs?.length) {
              const controllerServersWithLicence =
                licence.ControllerServeurs.map((cs) => ({
                  ...cs,
                  Licence: {
                    Id: licence.Id,
                    id: licence.Id,
                    Name: licence.Name,
                    Description: licence.Description,
                  },
                }));

              allControllerServeurs.push(...controllerServersWithLicence);
            }
          }
        }

        const uniqueControllerServeurs = allControllerServeurs.filter(
          (cs, index, self) =>
            index === self.findIndex((item) => item.Id === cs.Id)
        );

        this.licences = availableLicences;
        
        // Store all data for search functionality
        this.allControllerServeursData = uniqueControllerServeurs;
        
        // Apply search filter if exists
        this.applySearchFilter();

        console.log('All controller serveurs loaded:', uniqueControllerServeurs);

        // Load hierarchy data separately
        this.loadHierarchyData();
      },
      error: (err) => {
        console.error('Error fetching subscriptions:', err);
        this.isLoading = false;
      },
    });
  }

  private applySearchFilter(): void {
    let filteredData = [...this.allControllerServeursData];

    if (this.searchParam && this.searchParam.trim() !== '') {
      const keyword = this.searchParam.trim().toLowerCase();
      this.hasSearchFilter = true;
      
      filteredData = filteredData.filter((cs) => {
        return (
          cs.Name?.toLowerCase().includes(keyword) 
        );
      });
      
      console.log('Search applied with keyword:', keyword);
      console.log('Filtered results:', filteredData.map(cs => cs.Name));
    } else {
      this.hasSearchFilter = false;
    }

    this.controllerServeurs = filteredData;
    this.totalControllerServeurs = filteredData.length;
    this.currentPage = 0; // Reset to first page
    this.updatePaginatedData();
  }

  // New method to load hierarchy data
  loadHierarchyData(): void {
    console.log('Loading hierarchy data for client:', this.clientId);

    this.hierarchyService.getHierarchyByClient(this.clientId).subscribe({
      next: (hierarchyData) => {
        console.log('Hierarchy data loaded successfully:', hierarchyData);
        this.allControllerServeurs = hierarchyData;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading hierarchy data:', error);
        this.isLoading = false;
      },
    });
  }

  private updatePaginatedData(): void {
    this.controllerServeurs = this.controllerServeurs || [];
    this.totalControllerServeurs = this.controllerServeurs.length;

    const startIndex = this.currentPage * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.displayedControllerServeurs = this.controllerServeurs.slice(
      startIndex,
      endIndex
    );
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.updatePaginatedData();
  }

  // Search methods
  onSearch(): void {
    this.currentPage = 1; // Reset to first page when searching
    this.getControllerServers();
  }

  handleTableAction(event: { action: string; row: any }): void {
    const { action, row } = event;

    switch (action) {
      case 'view':
        this.viewControllerServerDetails(row);
        break;
      case 'edit':
        this.editControllerServer(row);
        break;
      case 'delete':
        this.deleteControllerServer(row.Id);
        break;
      default:
        console.warn('Unknown action:', action);
    }
  }

  updateMaxValues(): void {
    this.controllerServerService.updateMaxValues().subscribe({
      next: (response) => {
        console.log('Max values updated successfully:', response);
      },
      error: (error) => {
        console.error('Error updating max values:', error);
      },
    });
  }

  showHierarchy(): void {
    console.log(
      'Opening hierarchy modal with data:',
      this.allControllerServeurs
    );

    if (
      !this.allControllerServeurs ||
      this.allControllerServeurs.length === 0
    ) {
      // If no hierarchy data, try to reload it
      this.loadHierarchyData();
      return;
    }

    this.showHierarchyModal = true;
    document.body.classList.add('modal-open');
  }

  onHierarchyClosed(): void {
    this.showHierarchyModal = false;
    document.body.classList.remove('modal-open');
  }

  viewControllerServerDetails(controllerServer: ControllerServeur): void {
    console.log('Viewing controller server details:', controllerServer);
    this.selectedControllerServer = controllerServer;
    this.showDetailsModal = true;
    document.body.classList.add('modal-open');
  }

  editControllerServer(controllerServer: ControllerServeur): void {
    this.selectedControllerServer = controllerServer;
    this.isEditMode = true;
    this.showCreateForm = true;
    document.body.classList.add('modal-open');
  }

  deleteControllerServer(controllerServerId: string): void {
    const controllerServer =
      this.controllerServeurs.find((cs) => cs.Id === controllerServerId) ||
      this.displayedControllerServeurs.find(
        (cs) => cs.Id === controllerServerId
      );

    const serverName = controllerServer?.Name || 'ce contrôleur serveur';

    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirmation de suppression',
        message: `Êtes-vous sûr de vouloir supprimer "${serverName}" ?`,
        type: 'danger',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.controllerServerService.delete(controllerServerId).subscribe({
          next: () => {
            // Remove from all data arrays
            this.allControllerServeursData = this.allControllerServeursData.filter(
              (cs) => cs.Id !== controllerServerId
            );
            
            // Reapply search filter
            this.applySearchFilter();

            this.controllerServerDeleted.emit(controllerServerId);

            // Reload hierarchy data after deletion
            this.loadHierarchyData();

            this.dialog.open(ConfirmationDialogComponent, {
              width: '400px',
              data: {
                title: 'Succès',
                message: 'Contrôleur serveur supprimé avec succès',
                confirmText: 'OK',
                cancelText: '',
                type: 'success',
              },
            });
          },
          error: (err) => {
            console.error('Error deleting controller server:', err);

            this.dialog.open(ConfirmationDialogComponent, {
              width: '400px',
              data: {
                title: 'Erreur',
                message: 'Erreur lors de la suppression du contrôleur serveur',
                confirmText: 'OK',
                cancelText: '',
                type: 'danger',
              },
            });
          },
        });
      }
    });
  }

  addNewControllerServer(): void {
    this.selectedControllerServer = null;
    this.isEditMode = false;
    this.showCreateForm = true;
    document.body.classList.add('modal-open');
  }

  closeAllModals(): void {
    this.showCreateForm = false;
    this.showDetailsModal = false;
    this.showHierarchyModal = false;
    this.isEditMode = false;
    this.selectedControllerServer = null;
    document.body.classList.remove('modal-open');
  }

  onFormClosed(): void {
    this.showCreateForm = false;
    this.isEditMode = false;
    this.selectedControllerServer = null;
    document.body.classList.remove('modal-open');
  }

  onDetailsClosed(): void {
    this.showDetailsModal = false;
    this.selectedControllerServer = null;
    document.body.classList.remove('modal-open');
  }

  onControllerServerCreated(newControllerServer: ControllerServeur): void {
    this.allControllerServeursData.push(newControllerServer);
    this.applySearchFilter();
    this.onFormClosed();

    // Reload hierarchy data after creation
    this.loadHierarchyData();
  }

  onControllerServerUpdated(updatedControllerServer: ControllerServeur): void {
    const id = updatedControllerServer.Id || updatedControllerServer.Id;
    const index = this.allControllerServeursData.findIndex(
      (cs) => (cs.Id || cs.Id) === id
    );

    if (index !== -1) {
      this.allControllerServeursData[index] = updatedControllerServer;
      this.applySearchFilter();
    }

    this.onFormClosed();

    // Reload hierarchy data after update
    this.loadHierarchyData();
  }

  searchControllerServers(): void {
    console.log('Search triggered with param:', this.searchParam);
    this.applySearchFilter();
  }

  clearSearch(): void {
    console.log('Clearing search');
    this.searchParam = '';
    this.hasSearchFilter = false;
    this.applySearchFilter();
  }

  // Helper methods for template
  getTotalControllers(): number {
    return this.allControllerServeurs.reduce((total, server) => {
      return total + (server.ControllerServerControllers?.length || 0);
    }, 0);
  }

  getActiveControllers(): number {
    return this.allControllerServeurs.reduce((total, server) => {
      const activeCount = server.ControllerServerControllers?.filter(
        (csc) => csc.Controller?.State === true
      ).length || 0;
      return total + activeCount;
    }, 0);
  }
}