import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  OnInit,
  OnChanges,
  OnDestroy,
  SimpleChanges,
  EventEmitter,
  Output,
  HostListener,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ControllerServeur } from '@app/core/models/controllerServeur';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';
import { PageEvent, MatPaginatorModule } from '@angular/material/paginator';
import { ControllerServeurApiService } from '@app/core/services/administrative/controllerserveur.service';
import { Client } from '@app/core/models/client';
import { Licence } from '@app/core/models/licence';
import { FormCsComponent } from '../form-cs/form-cs.component';
import { DetailsCsComponent } from '../details-cs/details-cs.component'; // Import the new details component
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { FilterParam, Lister, Pagination } from '@app/core/models/util/page';
import { LicenceApiService } from '@app/core/services/administrative/licence.service';
import { SubscriptionApiService } from '@app/core/services/administrative/subscription.service';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-controller-server',
  imports: [
    CommonModule,
    GenericTableComponent,
    MatPaginatorModule,
    FormCsComponent,
    DetailsCsComponent,
    MatIconModule
  ],
  templateUrl: './controller-server.component.html',
  styleUrl: './controller-server.component.css',
})
export class ControllerServerComponent implements OnInit, OnChanges, OnDestroy {
  @Input() controllerServeurs: ControllerServeur[] = [];
  @Input() clientId: string = '';
  @Input() client: Client | null = null;
  @Input() licences: Licence[] = [];
  @Output() controllerServerDeleted = new EventEmitter<string>();

  // Properties
  displayedControllerServeurs: ControllerServeur[] = [];
  isLoading: boolean = false;
  showCreateForm: boolean = false;
  showDetailsModal: boolean = false; // Add details modal state
  isEditMode: boolean = false;
  selectedControllerServer: ControllerServeur | null = null;

  csHeaders: string[] = [
    'Nom',
    'Licence',
    'Nbr Contrôleurs',
    'Nbr Capteurs',
    'Zone Géographic',
    'Conditions Commerciales',
    'Déclencheur',
    'Action',
    'Evenement',
    'Statut',
  ];
  csKeys: string[] = [
    'Name',
    `Licence.Name`,
    'MaxControllers',
    'MaxSensors',
    'GeographicZone',
    'CommercialCondition',
    'TriggerType',
    'ActionType',
    'EventType',
    'Status',
  ];

  pageSize: number = 5;
  currentPage: number = 0;
  totalControllerServeurs: number = 0;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly clientService: ClientApiService,
    private readonly licenceApiService: LicenceApiService,
    private readonly controllerServerService: ControllerServeurApiService,
    private readonly subscriptionApiService: SubscriptionApiService,
    private dialog: MatDialog
  ) {}

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;

    if (
      (this.showCreateForm || this.showDetailsModal) &&
      target &&
      target.classList.contains('modal-overlay')
    ) {
      this.closeAllModals();
    }
  }

  @HostListener('document:keydown.escape', ['$event'])
  onEscapeKey(event: KeyboardEvent): void {
    if (this.showCreateForm || this.showDetailsModal) {
      this.closeAllModals();
    }
  }

  ngOnInit(): void {
    console.log('Controller Server Component initialized');
    console.log('Licences received:', this.licences);
    // this.updateDisplayedData();
    this.getControllerServers();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['controllerServeurs']) {
      console.log(
        'Controller serveurs changed:',
        changes['controllerServeurs'].currentValue
      );
      this.updateDisplayedData();
    }

    if (changes['licences']) {
      console.log('Licences changed:', changes['licences'].currentValue);
    }
  }

  ngOnDestroy(): void {
    // Clean up modal state
    document.body.classList.remove('modal-open');
  }

  private updateDisplayedData(): void {
    // Ensure controllerServeurs is always an array
    this.controllerServeurs = this.controllerServeurs || [];
    this.totalControllerServeurs = this.controllerServeurs.length;
    this.updatePaginatedData();
  }

  getControllerServers() {
    const pagination: Pagination = {
      CurrentPage: this.currentPage,
      PageSize: this.pageSize,
      totalElement: 0,
    };

    const filterParams: FilterParam[] = [
      {
        Column: 'ClientId',
        Value: this.clientId,
        Op: 'eq',
        AndOr: 'AND',
      },
    ];

    const lister: Lister = {
      pagination: pagination,
      filterParams: filterParams,
    };

    this.subscriptionApiService.gatePage(lister).subscribe({
      next: (response) => {
        console.log('Subscriptions fetched:', response);

        const allControllerServeurs: ControllerServeur[] = [];
        const availableLicences: Licence[] = [];

        if (response?.Content?.length) {
          for (const subscription of response.Content) {
            const licence = subscription.Licence;
            console.log('Processing licence:', licence);

            // Collect unique licences for the form dropdown
            if (
              licence &&
              !availableLicences.find(
                (l) => (l.Id ) === (licence.Id)
              )
            ) {
              availableLicences.push(licence);
            }

            if (licence?.ControllerServeurs?.length) {
              // Attach licence information to each controller server
              const controllerServersWithLicence =
                licence.ControllerServeurs.map((cs) => ({
                  ...cs,
                  Licence: {
                    Id: licence.Id,
                    id: licence.Id,
                    Name: licence.Name,
                    Description: licence.Description,
                  },
                }));

              allControllerServeurs.push(...controllerServersWithLicence);
            }
          }
        }

        // Remove duplicates based on controller server ID
        const uniqueControllerServeurs = allControllerServeurs.filter(
          (cs, index, self) =>
            index === self.findIndex((item) => item.Id === cs.Id)
        );

        // Update the licences array for the form
        this.licences = availableLicences;
        console.log('Available licences for form:', this.licences);

        // Update both arrays to maintain consistency
        this.controllerServeurs = uniqueControllerServeurs;
        this.totalControllerServeurs = uniqueControllerServeurs.length;

        // Update displayed data for current page
        this.updatePaginatedData();

        console.log('Flattened controller serveurs:', uniqueControllerServeurs);
      },
      error: (err) => {
        console.error('Error fetching subscriptions:', err);
      },
    });
  }

  private updatePaginatedData(): void {
    // Ensure controllerServeurs is always an array
    this.controllerServeurs = this.controllerServeurs || [];
    this.totalControllerServeurs = this.controllerServeurs.length;

    const startIndex = this.currentPage * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.displayedControllerServeurs = this.controllerServeurs.slice(
      startIndex,
      endIndex
    );
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;

    // Fetch new data for the new page if needed, or just update pagination
    this.updatePaginatedData();
  }

  handleTableAction(event: { action: string; row: any }): void {
    const { action, row } = event;

    switch (action) {
      case 'view':
        this.viewControllerServerDetails(row);
        break;
      case 'edit':
        this.editControllerServer(row);
        break;
      case 'delete':
        this.deleteControllerServer(row.Id);
        break;
      default:
        console.warn('Unknown action:', action);
    }
  }

  viewControllerServerDetails(controllerServer: ControllerServeur): void {
    console.log('Viewing controller server details:', controllerServer);
    this.selectedControllerServer = controllerServer;
    this.showDetailsModal = true;
    document.body.classList.add('modal-open');
  }

  editControllerServer(controllerServer: ControllerServeur): void {
    this.selectedControllerServer = controllerServer;
    this.isEditMode = true;
    this.showCreateForm = true;
    document.body.classList.add('modal-open');
  }

  deleteControllerServer(controllerServerId: string): void {
    // Get server name for better UX - check both arrays
    const controllerServer =
      this.controllerServeurs.find(
        (cs) => (cs.Id ) === controllerServerId
      ) ||
      this.displayedControllerServeurs.find(
        (cs) => (cs.Id) === controllerServerId
      );

    const serverName = controllerServer?.Name || 'ce contrôleur serveur';

    // Confirmation dialog
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirmation de suppression',
        message: `Êtes-vous sûr de vouloir supprimer "${serverName}" ?`,
        type: 'danger',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Proceed with deletion
        this.controllerServerService.delete(controllerServerId).subscribe({
          next: () => {
            // Remove from both arrays
            this.controllerServeurs = this.controllerServeurs.filter(
              (cs) => (cs.Id) !== controllerServerId
            );

            // Update pagination and displayed data
            this.updatePaginatedData();

            this.controllerServerDeleted.emit(controllerServerId);

            // Success dialog instead of alert
            this.dialog.open(ConfirmationDialogComponent, {
              width: '400px',
              data: {
                title: 'Succès',
                message: 'Contrôleur serveur supprimé avec succès',
                confirmText: 'OK',
                cancelText: '',
                type: 'success',
              },
            });
          },
          error: (err) => {
            console.error('Error deleting controller server:', err);

            // Error dialog instead of alert
            this.dialog.open(ConfirmationDialogComponent, {
              width: '400px',
              data: {
                title: 'Erreur',
                message: 'Erreur lors de la suppression du contrôleur serveur',
                confirmText: 'OK',
                cancelText: '',
                type: 'danger',
              },
            });
          },
        });
      }
    });
    this.getControllerServers();
  }

  addNewControllerServer(): void {
    this.selectedControllerServer = null;
    this.isEditMode = false;
    this.showCreateForm = true;
    document.body.classList.add('modal-open');
  }

  // Close all modals
  closeAllModals(): void {
    this.showCreateForm = false;
    this.showDetailsModal = false;
    this.isEditMode = false;
    this.selectedControllerServer = null;
    document.body.classList.remove('modal-open');
  }

  // Form-specific close handler
  onFormClosed(): void {
    this.showCreateForm = false;
    this.isEditMode = false;
    this.selectedControllerServer = null;
    document.body.classList.remove('modal-open');
  }

  // Details-specific close handler
  onDetailsClosed(): void {
    this.showDetailsModal = false;
    this.selectedControllerServer = null;
    document.body.classList.remove('modal-open');
  }

  onControllerServerCreated(newControllerServer: ControllerServeur): void {
    this.controllerServeurs.push(newControllerServer);
    this.updateDisplayedData();
    this.onFormClosed();
  }

  onControllerServerUpdated(updatedControllerServer: ControllerServeur): void {
    // Use consistent property naming
    const id = updatedControllerServer.Id || updatedControllerServer.Id;
    const index = this.controllerServeurs.findIndex(
      (cs) => (cs.Id || cs.Id) === id
    );

    if (index !== -1) {
      this.controllerServeurs[index] = updatedControllerServer;
      this.updateDisplayedData();
    }

    this.onFormClosed();
  }
}