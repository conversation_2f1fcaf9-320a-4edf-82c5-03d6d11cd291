<ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>
<div id="section" class="header-section">
  <div class="page-title">
    <h1 class="title">
      <i class="material-icons title-icon">business</i> {{ selectedType ? "Clients de " + selectedType : "Gestion des Organisations" }}
      <button
        *ngIf="selectedType"
        class="reset-filter-btn"
        (click)="resetFilter()"
        title="Afficher tous les clients"
      >
        <i class="material-icons">clear</i>
        Tous les clients
      </button>
    </h1>
  </div>

  <div class="actions">
    <button class="view-toggle" (click)="toggleViewMode()">
      <i class="material-icons action-icon">{{
        viewMode === "cards" ? "view_list" : "grid_view"
        }}</i>
      {{ viewMode === "cards" ? "Vue Tableau" : "Vue Cartes" }}
    </button>
  </div>
</div>
<div class="search-bar">
  <div class="search-field">
    <i class="material-icons search-icon">search</i>
    <input type="text" [(ngModel)]="searchTerm" (keyup)="onSearchChange()" placeholder="Rechercher" />
    <button *ngIf="searchTerm" (click)="searchTerm = ''; onSearchChange()" class="clear-button">
      <i class="material-icons">close</i>
    </button>
  </div>
  <!-- <div class="active-filters" *ngIf="searchOrganisationType">
      <span class="active-filter">
        Afficher Tout
        <button (click)="clearTypeFilter()" class="clear-filter">
          <i class="material-icons">close</i>
        </button>
      </span>
    </div> -->
</div>

<div class="loading-container" *ngIf="isLoading">
  <p>Chargement...</p>
  <ngx-ui-loader></ngx-ui-loader>
</div>

 <div class="no-data" *ngIf="!isLoading && filteredClients.length === 0">
   <p>no Client found</p>
 </div>

<!-- Cards View -->
<div class="cards-view-container" *ngIf="viewMode === 'cards' && !isLoading && filteredClients.length > 0">
  <div class="cards-container">

    <app-card *ngFor="let client of filteredClients" [client]="client" (view)="viewClientDetailsById($event)"
      (edit)="editClientById($event)" (delete)="deleteClientById($event)"></app-card>
  </div>
  <div class="pagination-controls" *ngIf="viewMode === 'cards'">
    <button class="pagination-button" [disabled]="currentPage === 1" (click)="onPageChange(currentPage - 1)">
      <i class="material-icons">chevron_left</i>
    </button>

    <div class="page-numbers">
      <button *ngFor="let page of getPageNumbers()" class="page-number-button" [class.active]="page === currentPage"
        [class.separator]="page === -1" [disabled]="page === -1" (click)="page !== -1 && onPageChange(page)">
        {{ page === -1 ? "..." : page }}
      </button>
    </div>

    <button class="pagination-button" [disabled]="currentPage === totalPages" (click)="onPageChange(currentPage + 1)">
      <i class="material-icons">chevron_right</i>
    </button>
  </div>
</div>

<div *ngIf="viewMode === 'table' && !isLoading && filteredClients.length > 0">
  <app-generic-table [data]="filteredClients" [headers]="headers" [keys]="keys" [actions]="['edit', 'view', 'delete']"
    (actionTriggered)="handleAction($event)"></app-generic-table>
</div>