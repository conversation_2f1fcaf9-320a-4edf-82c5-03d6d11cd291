/* src/app/shared/components/layout/layout.component.css */
:host {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1100;
}

app-sidebar {
  position: fixed;
  top: 60px; /* Match header height */
  bottom: 0;
  left: 0;
  z-index: 1200;
}

.main-content {
  margin-left: 250px; /* Match sidebar expanded width */
  margin-top: 60px; /* Match header height */
  padding: 20px;
  flex: 1;
  overflow: auto;
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: calc(100% - 250px);
}

.main-content.sidebar-collapsed {
  margin-left: 70px; /* Match sidebar collapsed width */
  width: calc(100% - 70px);
}

@media (max-width: 768px) {
  .main-content {
    margin-left: 200px;
    margin-top: 50px;
    width: calc(100% - 200px);
  }

  .main-content.sidebar-collapsed {
    margin-left: 0;
    width: 100%;
  }

  app-sidebar {
    top: 50px; /* Match header height on mobile */
  }
}
