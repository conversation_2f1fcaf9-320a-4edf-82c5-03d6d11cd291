<div class="plan-editor">
  <!-- 🎯 Header avec sélecteur de type de plan -->
  <header class="editor-header">
    <div class="header-left">
      <h1 class="editor-title">
        <span class="title-icon">📐</span>
        Éditeur de Plan 2D
      </h1>



 <!-- Sélecteurs de contexte -->
      <div class="context-selectors">
        <!-- Sélecteur Client -->
        <div class="selector-group">
          <label class="selector-label">Client</label>
          <div class="dropdown" [class.open]="isClientDropdownOpen">
            <button class="dropdown-trigger" (click)="toggleClientDropdown()">
              <span class="selected-text">
                {{ selectedClient ? selectedClient.Name : 'Sélectionner un client' }}
              </span>
              <span class="dropdown-icon">{{ isClientDropdownOpen ? '▲' : '▼' }}</span>
            </button>
            <div class="dropdown-menu" *ngIf="isClientDropdownOpen">
              <div class="search-box">
                <input 
                  type="text" 
                  placeholder="Rechercher un client..." 
                  [(ngModel)]="clientSearchTerm"
                  class="search-input"
                  (click)="$event.stopPropagation()">
              </div>
              <div class="dropdown-options">
                <div 
                  *ngFor="let client of filteredClients" 
                  class="dropdown-option"
                  (click)="selectClient(client)">
                  <div class="option-main">
                    <span class="option-name">{{ client.Name }}</span>
                    
                  </div>
                  <div class="option-meta">{{ client.BusinessSector }}</div>
                </div>
                <div *ngIf="filteredClients.length === 0" class="no-results">
                  Aucun client trouvé
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sélecteur Site -->
        <div class="selector-group">
          <label class="selector-label">Site</label>
          <div class="dropdown" [class.open]="isSiteDropdownOpen" [class.disabled]="!selectedClient">
            <button class="dropdown-trigger" (click)="toggleSiteDropdown()" [disabled]="!selectedClient">
              <span class="selected-text">
                {{ selectedSite ? selectedSite.Name : (selectedClient ? 'Sélectionner un site' : 'Choisir un client d\'abord') }}
              </span>
              <span class="dropdown-icon">{{ isSiteDropdownOpen ? '▲' : '▼' }}</span>
            </button>
            <div class="dropdown-menu" *ngIf="isSiteDropdownOpen && selectedClient">
              <div class="search-box">
                <input 
                  type="text" 
                  placeholder="Rechercher un site..." 
                  [(ngModel)]="siteSearchTerm"
                  class="search-input"
                  (click)="$event.stopPropagation()">
              </div>
              <div class="dropdown-options">
                <div 
                  *ngFor="let site of sites" 
                  class="dropdown-option"
                  (click)="selectSite(site)">
                  <div class="option-main">
                    <span class="option-name">{{ site.Name }}</span>
                    
                  </div>
                  <div class="option-meta">{{ site.Address }}</div>
                </div>
                <div *ngIf="filteredSites.length === 0" class="no-results">
                  Aucun site trouvé
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sélecteur Local -->
        <div class="selector-group">
          <label class="selector-label">Local</label>
          <div class="dropdown" [class.open]="isLocalDropdownOpen" [class.disabled]="!selectedSite">
            <button class="dropdown-trigger" (click)="toggleLocalDropdown()" [disabled]="!selectedSite">
              <span class="selected-text">
                {{ selectedLocal ? selectedLocal.Name : (selectedSite ? 'Sélectionner un local' : 'Choisir un site d\'abord') }}
              </span>
              <span class="dropdown-icon">{{ isLocalDropdownOpen ? '▲' : '▼' }}</span>
            </button>
            <div class="dropdown-menu" *ngIf="isLocalDropdownOpen && selectedSite">
              <div class="search-box">
                <input 
                  type="text" 
                  placeholder="Rechercher un local..." 
                  [(ngModel)]="localSearchTerm"
                  class="search-input"
                  (click)="$event.stopPropagation()">
              </div>
              <div class="dropdown-options">
                <div 
                  *ngFor="let local of locals" 
                  class="dropdown-option"
                  (click)="selectLocal(local)">
                  <div class="option-main">
                    <span class="option-name">{{ local.Name }}</span>
                    <span class="option-floor">{{ local.floor }}</span>
                  </div>
                  <div class="option-meta">
                    <span class="option-area">{{ local.area }}m²</span>
                    <span class="option-type">{{ local.type }}</span>
                  </div>
                </div>
                <div *ngIf="filteredLocals.length === 0" class="no-results">
                  Aucun local trouvé
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


      <div class="export-section">
      <button class="export-btn primary" (click)="addPlanToLocal()">
        <span class="btn-icon">💾</span>
        Enregistrer
      </button>
      <button class="export-btn secondary" (click)="exportToPNG()">
        <span class="btn-icon">🖼️</span>
        Exporter PNG
      </button>

      <div class="import-section">
      <label class="file-input-label" for="file-input">
        <span class="btn-icon">📁</span>
        Importer un fichier
        <input 
          id="file-input"
          type="file" 
          accept=".json,.svg"
          (change)="importFile($event)"
          style="display: none;">
      </label>
      <span class="file-hint">Formats: JSON, SVG</span>
    </div>

    </div>
    </div>
    
    <div class="header-actions">
      <button class="action-btn secondary" (click)="toggleGrid()">
        <span class="btn-icon">{{ isGridVisible ? '⚏' : '⚏' }}</span>
        Grille
      </button>
      <button class="action-btn secondary" (click)="clearCanvas()">
        <span class="btn-icon">🗑️</span>
        Effacer
      </button>
    </div>
  </header>

  <div class="editor-body">
    <!-- 🎨 Palette d'outils -->
    <aside class="tools-palette">
      <!-- Section Structure -->
      <div class="palette-section">
        <h3 class="section-title">
          <span class="section-icon">🏗️</span>
          Structure
        </h3>
        <div class="tool-group">
          <button 
            class="tool-btn" 
            [class.selected]="selectedTool === element.type"
            *ngFor="let element of elements"
            (click)="addShape(element.type)"
            [title]="element.description">
            <span class="tool-icon">{{ element.icon }}</span>
            <span class="tool-name">{{ element.name }}</span>
          </button>
        </div>
      </div>

      <!-- Section Mobilier SVG -->
      <div class="palette-section">
        <h3 class="section-title">
          <span class="section-icon">🪑</span>
          Mobilier SVG
        </h3>
        <div class="tool-group mobilier-grid">
          <button 
            class="tool-btn mobilier-btn" 
            [class.selected]="selectedTool === element.type"
            *ngFor="let element of mobilierElements"
            (click)="addShape(element.type)"
            [title]="element.description">
            <span class="tool-icon">{{ element.icon }}</span>
            <span class="tool-name">{{ element.name }}</span>
            <span class="svg-badge">SVG</span>
          </button>
        </div>
      </div>

      <!-- Section Actions rapides -->
      <div class="palette-section">
        <h3 class="section-title">
          <span class="section-icon">⚡</span>
          Actions rapides
        </h3>
        <div class="quick-actions">
          <button 
            class="quick-btn" 
            *ngFor="let element of elements.slice(0, 6)" 
            (click)="addShape(element.type)"
            [style.background-color]="element.color"
            [title]="'Ajouter ' + element.name">
            {{ element.icon }}
          </button>
        </div>
        <div class="quick-actions" style="margin-top: 0.5rem;">
          <button 
            class="quick-btn" 
            *ngFor="let element of mobilierElements.slice(0, 6)" 
            (click)="addShape(element.type)"
            [style.background-color]="element.color"
            [title]="'Ajouter ' + element.name">
            {{ element.icon }}
          </button>
        </div>
      </div>

      <!-- Section Palette Couleurs -->
      <div class="palette-section">
        <h3 class="section-title">
          <span class="section-icon">🎨</span>
          Palette Couleurs
        </h3>
        
        <!-- Couleur de remplissage -->
        <div class="color-group">
          <label class="color-label">Remplissage</label>
          <div class="color-grid">
            <div 
              *ngFor="let colorItem of colorPalette" 
              class="color-swatch"
              [class.selected]="selectedColor === colorItem.color"
              [style.background-color]="colorItem.color"
              [title]="colorItem.name"
              (click)="selectColor(colorItem.color)">
            </div>
          </div>
          <input 
            type="color" 
            class="color-picker" 
            [value]="selectedColor"
            (change)="onColorChange($event)"
            title="Couleur personnalisée">
        </div>

        <!-- Couleur de contour -->
        <div class="color-group">
          <label class="color-label">Contour</label>
          <div class="stroke-controls">
            <div class="stroke-colors">
              <div 
                *ngFor="let strokeItem of strokeColors" 
                class="stroke-swatch"
                [class.selected]="selectedStrokeColor === strokeItem.color"
                [style.background-color]="strokeItem.color"
                [style.border]="strokeItem.color === '#FFFFFF' ? '1px solid #ccc' : 'none'"
                [title]="strokeItem.name"
                (click)="selectStrokeColor(strokeItem.color)">
              </div>
            </div>
            <div class="stroke-width-control">
              <label class="stroke-width-label">Épaisseur</label>
              <input 
                type="range" 
                min="0" 
                max="10" 
                [value]="selectedStrokeWidth"
                (input)="onStrokeWidthChange($event)"
                class="stroke-slider">
              <span class="stroke-value">{{ selectedStrokeWidth }}px</span>
            </div>
          </div>
        </div>

        <!-- Pipette -->
        <button class="pipette-btn" (click)="pickColorFromSelected()" title="Récupérer la couleur de l'objet sélectionné">
          <span class="btn-icon">🎨</span>
          Pipette
        </button>
      </div>

      <!-- Section Édition -->
      <div class="palette-section">
        <h3 class="section-title">
          <span class="section-icon">✏️</span>
          Édition
        </h3>
        <div class="edit-actions">
          <button class="edit-btn" (click)="duplicateSelected()" title="Dupliquer">
            <span class="btn-icon">📄</span>
            Dupliquer
          </button>
          <button class="edit-btn delete" (click)="deleteSelected()" title="Supprimer">
            <span class="btn-icon">🗑️</span>
            Supprimer
          </button>
        </div>
      </div>
    </aside>

    <!-- 🖼️ Zone de dessin -->
    <main class="canvas-area">
      <div class="canvas-header">
        <div class="canvas-info">
          <span class="canvas-title">Zone de Dessin</span>
         
        </div>
        <div class="canvas-controls">
          <button class="control-btn" (click)="bringAllToFront()" title="Tout au premier plan">
            ⬆️
          </button>
          <button class="control-btn" (click)="sendAllToBack()" title="Tout en arrière-plan">
            ⬇️
          </button>
        </div>
      </div>
      
      <div class="canvas-wrapper">
        <canvas id="canvas" width="1000" height="700"></canvas>
        <div class="canvas-overlay" [class.selecting]="selectedTool">
          <div class="selection-hint" *ngIf="selectedTool">
            Cliquez pour placer: {{ getSelectedToolName() }}
          </div>
        </div>
      </div>
    </main>
  </div>

  <!-- 📊 Footer avec actions d'import/export -->
  
</div>