.local-card {
  position: relative;
  width: 320px;
  max-width: 320px;
  min-width: 320px;
  min-height: 280px;
  cursor: pointer;
  border-radius: 10px;
  transition: all 0.3s ease;
  border-top: 4px solid #4CAF50;
  background-color: #fff;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.card-header {
  padding: 16px 16px 8px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  color: #4CAF50;
  font-weight: 500;
  margin: 0;
  font-size: 1.2rem;
}

.local-id {
  color: #666;
  font-size: 0.9rem;
  padding: 4px 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.card-content {
  padding: 16px;
  flex-grow: 1;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  align-items: center;
}

.info-label {
  color: #666;
  font-size: 0.9rem;
}

.info-value {
  font-weight: 500;
  color: #333;
  font-size: 0.85rem;
}

.tags-container {
  padding: 0 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.tag {
  background-color: #e3f2fd;
  color: #4CAF50;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 500;
}

.card-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: transparent;
}

.action-btn mat-icon {
  font-size: 20px;
  color: #666;
}

.action-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.action-btn:hover mat-icon {
  color: #4CAF50;
}

.action-btn.delete:hover {
  background-color: #fee;
}

.action-btn.delete:hover mat-icon {
  color: #f44336;
}

/* Animation for hover */
@keyframes cardPulse {
  0% { box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
  50% { box-shadow: 0 8px 16px rgba(0,0,0,0.2); }
  100% { box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
}

.local-card:hover {
  transform: translateY(-5px);
  animation: cardPulse 2s infinite;
}

/* Image section styles */
.image-section {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  background-color: #f5f5f5;
  display: flex;
  gap: 8px;
  padding: 8px;
}

.image-section img {
  flex: 1;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
  transition: transform 0.3s ease;
}

.image-section img:hover {
  transform: scale(1.05);
  cursor: pointer;
}

/* Add responsive behavior */
@media (max-width: 480px) {
  .local-card {
    max-width: 100%;
  }
  
  .card-actions {
    justify-content: space-around;
  }
  
  .image-section {
    height: 160px;
    flex-direction: column;
    gap: 4px;
  }
  
  .image-section img {
    width: 100%;
    height: 50%;
  }
}

/* Add loading placeholder */
.image-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  display: none;
}

.image-section.loading::before {
  display: block;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.clickable-card {
  cursor: pointer;
  transition: box-shadow 0.2s, transform 0.2s;
}
.clickable-card:hover {
  box-shadow: 0 8px 24px rgba(76,175,80,0.18), 0 4px 8px rgba(0,0,0,0.10);
  transform: translateY(-3px) scale(1.01);
}