.local-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
  display: flex;
  flex-direction: column;
  min-width: 300px;  /* Add minimum width */
  width: 100%;       /* Make it flexible */
  max-width: 400px;  /* Limit maximum width */
  margin: 0 auto;    /* Center the card */
}

.card-header {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.local-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
}

.local-type {
  display: inline-block;
  background-color: #b2d6b4c7;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  margin-top: 8px;
}

/* Different styles based on local type */
.local-type[data-type="Salle de réunion"] {
  background-color: #d4edda;
  color: #155724;
}

.local-type[data-type="Bureau"] {
  background-color: #cce5ff;
  color: #004085;
}

.local-type[data-type="Laboratoire"] {
  background-color: #e2e3e5;
  color: #383d41;
}

.local-type[data-type="Amphithéâtre"] {
  background-color: #fff3cd;
  color: #856404;
}

.local-type[data-type="Espace détente"] {
  background-color: #e0f2f1;
  color: #00695c;
}

.local-type[data-type="Salle de formation"] {
  background-color: #f3e5f5;
  color: #6a1b9a;
}

.local-id {
  color: #666;
  font-size: 0.9rem;
  padding: 4px 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.card-image-container {
  position: relative;
  width: 100%;
  height: 200px;
  background-color: #f5f5f5;
  overflow: hidden;
}

.image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.local-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-content {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.local-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #555;
  font-size: 0.9rem;
}

.detail-item i {
  color: #4CAF50;
  font-size: 1.1rem;
  min-width: 24px;
  text-align: center;
}

.tags-container {
  padding: 0 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.tag {
  background-color: #e3f2fd;
  color: #4CAF50;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 500;
}

.card-actions {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  margin-top: auto;
  background-color: #fafafa;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: transparent;
  color: #4a5568;
}

.action-btn mat-icon {
  font-size: 20px;
}

.action-btn:hover {
  background-color: #e8f5e9;
  transform: translateY(-2px);
}

.action-btn:hover mat-icon {
  color: #4CAF50;
}

.action-btn.delete:hover {
  background-color: #fee2e2;
}

.action-btn.delete:hover mat-icon {
  color: #ef4444;
}

.btn {
  width: 90%;
  min-height: 36px;
  padding: 8px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: clamp(0.75rem, 2vw, 0.875rem);
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn i {
  font-size: clamp(18px, 2.5vw, 24px);
}

.btn-primary {
  background: #4CAF50;
  color: white;
}

.btn-accent {
  background: #2196F3;
  color: white;
}

.btn-danger {
  background: #f44336;
  color: white;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary:hover {
  background: #43a047;
}

.btn-accent:hover {
  background: #1976D2;
}

.btn-danger:hover {
  background: #e53935;
}

/* Animation for hover */
@keyframes cardPulse {
  0% { box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
  50% { box-shadow: 0 8px 16px rgba(0,0,0,0.2); }
  100% { box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
}

.local-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.image-section img {
  flex: 1;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
  transition: transform 0.3s ease;
}

.image-section img:hover {
  transform: scale(1.05);
  cursor: pointer;
}

/* Add responsive behavior */
@media (max-width: 480px) {
  .local-card {
    min-width: 280px;
    max-width: 100%;
  }
  
  .card-actions {
    justify-content: space-around;
  }
  
  .image-section {
    height: 160px;
    flex-direction: column;
    gap: 4px;
  }
  
  .image-section img {
    width: 100%;
    height: 50%;
  }
}

@media (max-width: 600px) {
  .card-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
    padding: 12px;
  }
  
  .card-actions .btn {
    width: 100%;
    justify-content: center;
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .card-actions {
    grid-template-columns: 1fr;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (min-width: 1200px) {
  .local-card {
    max-width: 420px; /* Slightly larger on bigger screens */
  }
}

/* Add loading placeholder */
.image-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  display: none;
}

.image-section.loading::before {
  display: block;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.clickable-card {
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
}

.clickable-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.no-image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f8f0;
}

.no-image-container i {
  font-size: 64px;
  color: #9e9e9e;
  opacity: 0.6;
}