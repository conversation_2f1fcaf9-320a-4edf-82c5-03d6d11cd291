{"name": "energy-monitoring-app", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^20.0.5", "@angular/cdk": "^19.2.8", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.6", "@angular/forms": "^19.2.0", "@angular/material": "^19.2.8", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/router": "^19.2.0", "@asymmetrik/ngx-leaflet": "^2.5.0", "@fortawesome/fontawesome-free": "^6.7.2", "@ngneat/overview": "6.1.1", "@ngxpert/hot-toast": "^4.2.0", "@swimlane/ngx-charts": "^22.0.0-alpha.1", "@types/file-saver": "^2.0.7", "@types/jspdf": "^1.3.3", "chart.js": "^4.4.9", "fabric": "^6.7.0", "file-saver": "^2.0.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "leaflet": "^1.9.4", "lottie-web": "^5.13.0", "lucide-angular": "^0.511.0", "mqtt": "^5.13.1", "ng-angular-popup": "^1.0.0", "ng2-charts": "^4.1.1", "ng2-filter-pipe": "^0.1.9", "ngx-mqtt": "^17.0.0", "ngx-spinner": "^19.0.0", "ngx-ui-loader": "^13.0.0", "primeicons": "^7.0.0", "primeng": "^19.1.2", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.5", "@angular/cli": "^19.2.5", "@angular/compiler-cli": "^19.2.0", "@tailwindcss/forms": "^0.5.10", "@types/jasmine": "~5.1.0", "@types/leaflet": "^1.9.19", "autoprefixer": "^10.4.21", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.5.3", "postcss-custom-properties": "^14.0.4", "postcss-import": "^16.1.0", "tailwindcss": "^3.4.17", "typescript": "~5.7.2"}}