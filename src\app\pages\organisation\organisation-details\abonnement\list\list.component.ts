import { CommonModule } from '@angular/common';
import { Component, HostListener, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { lastValueFrom } from 'rxjs';

import { Client } from '@app/core/models/client';
import { Licence } from '@app/core/models/licence';
import { Subscription } from '@app/core/models/subscription';

import { ClientApiService } from '@app/core/services/administrative/client.service';
import { LicenceApiService } from '@app/core/services/administrative/licence.service';
import { SubscriptionApiService } from '@app/core/services/administrative/subscription.service';
import { DetailComponent } from '../detail/detail.component';
import { ActivatedRoute } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-list',
  standalone: true,
  imports: [CommonModule, FormsModule, DetailComponent, MatIconModule],
  templateUrl: './list.component.html',
  styleUrls: ['./list.component.css'],
})
export class ListComponent implements OnInit {
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.dropdown-container')) {
      if (this.showDropdown) {
        console.log('Click outside dropdown - closing');
      }
      this.showDropdown = false;
    }
  }

  subscriptions: Subscription[] = [];
  clients: Client[] = [];
  licences: Licence[] = [];
  isLoading: boolean = false;
  clientId: string | null = null;

  selectedSubscription: Subscription | null = null;

  showDropdown: boolean = false;
  showDetailModal: boolean = false;
  showFormModal: boolean = false;

  constructor(
    private subscriptionApiService: SubscriptionApiService,
    private clientApiService: ClientApiService,
    private licenceApiService: LicenceApiService,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    const hash = window.location.hash;
    const match = hash.match(/organisation-details\/([a-f0-9\-]+)/i);
    if (match && match[1]) {
      this.clientId = match[1];
    }
    console.log('Extracted clientId:', this.clientId);
    this.loadAllData();
  }

  async loadAllData(): Promise<void> {
    this.isLoading = true;
    try {
      const [clients, licences, subscriptions] = await Promise.all([
        lastValueFrom(this.clientApiService.getAll()),
        lastValueFrom(this.licenceApiService.getAll()),
        lastValueFrom(this.subscriptionApiService.getAll()),
      ]);
      this.clients = clients ?? [];
      this.licences = licences ?? [];
      this.subscriptions =
        subscriptions?.filter((sub) => sub.ClientId === this.clientId) ?? [];
      this.subscriptions =
        subscriptions?.filter((sub) => sub.ClientId === this.clientId) ?? [];

      console.log('Extracted clientId:', this.clientId);
      console.log('All subscriptions:', subscriptions);

      console.log('Extracted clientId:', this.clientId);
      console.log('All subscriptions:', subscriptions);

      this.selectedSubscription =
        this.subscriptions.length > 0 ? this.subscriptions[0] : null;
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      this.isLoading = false;
    }
  }

  selectSubscription(sub: Subscription) {
    // Toggle: hide if same subscription is clicked again
    this.selectedSubscription = this.selectedSubscription === sub ? null : sub;
  }

  getClientName(clientId?: string): string {
    const client = this.clients.find((c) => c.Id === clientId);
    return client ? client.Name : 'Unknown Client';
  }

  getLicenceName(licenceId?: string): string {
    const licence = this.licences.find((l) => l.Id === licenceId);
    return licence ? licence.Name : 'Unknown Licence';
  }

  onFormSubmit(updatedSubscription: Subscription): void {
    console.log('Form submitted:', updatedSubscription);
    this.selectedSubscription = { ...updatedSubscription };

    // Optionally update the subscriptions array here or call API to save changes
    const index = this.subscriptions.findIndex(
      (sub) =>
        sub.ClientId === updatedSubscription.ClientId &&
        sub.LicenceId === updatedSubscription.LicenceId
    );
    if (index !== -1) {
      this.subscriptions[index] = { ...updatedSubscription };
    } else {
      this.subscriptions.push(updatedSubscription);
    }
  }

  toggleDropdown(sub: Subscription): void {
    console.log('Toggle dropdown called for', sub);
    if (sub) {
      // If clicking the same subscription, toggle dropdown visibility
      if (this.selectedSubscription === sub) {
        this.showDropdown = !this.showDropdown;
      } else {
        // Different subscription clicked: set selected and open dropdown
        this.selectedSubscription = sub;
        this.showDropdown = true;
      }
      console.log('showDropdown:', this.showDropdown);
    }
  }

  // Modal methods
  openDetailModal(): void {
    if (this.selectedSubscription) {
      this.showDetailModal = true;
      this.showDropdown = false;
      document.body.classList.add('modal-open');
    }
  }

  openFormModal(): void {
    if (this.selectedSubscription) {
      this.showFormModal = true;
      this.showDropdown = false;
      document.body.classList.add('modal-open');
    }
  }

  closeDetailModal(): void {
    this.showDetailModal = false;
    document.body.classList.remove('modal-open');
  }

  closeFormModal(): void {
    this.showFormModal = false;
    document.body.classList.remove('modal-open');
  }
}
