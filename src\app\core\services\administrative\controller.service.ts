import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { Controller } from '@app/core/models/controller';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class ControllerApiService extends ApiService<Controller> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint('controller');
  }

  setBaseTopic(id: string): Observable<any> {
    return this.http.put<any>(
      `${this.baseUrl}controller/set-base-topic/${id}`,
      {}
    );
  }

  getAllLightByClient(clientId: string): Observable<any> {
    return this.http.get<any>(
      `${this.baseUrl}controller/get-light-by-client/${clientId}`,
      {}
    );
  }

  canAddController(subscriptionId: string) {
    return this.http.get<boolean>(
      `${this.baseUrl}controller/subscription/${subscriptionId}/can-add`,
      {}
    );
  }
  getControllersByLocal(localId: string): Observable<any> {
    return this.http.get<any>(
      `${this.baseUrl}controller/local/${localId}`,
      {}
    );
  }
}
