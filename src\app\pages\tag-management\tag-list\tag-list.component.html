<!-- Tag List Component -->
<div class="tag-list-container">

  <!-- Create Tag Popup -->
  <div class="popup-overlay" *ngIf="showCreateForm" (click)="hideCreateTagForm()">
    <div class="popup-form" (click)="$event.stopPropagation()">
      <div class="popup-header">
        <h3>
          <mat-icon>add</mat-icon>
          Créer un Tag
        </h3>
        <button class="close-btn" (click)="hideCreateTagForm()">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <form [formGroup]="createTagForm" (ngSubmit)="submitCreateForm()" class="popup-content">
        <div class="form-group">
          <label for="nom">Nom du Tag <span class="required">*</span></label>
          <input
            id="nom"
            type="text"
            formControlName="nom"
            placeholder="Entrez le nom du tag"
            required
          />
          <div class="error-message" *ngIf="createTagForm.get('nom')?.invalid && createTagForm.get('nom')?.touched">
            Le nom du tag est requis (minimum 2 caractères)
          </div>
        </div>

        <div class="popup-actions">
          <button type="button" class="btn-cancel" (click)="hideCreateTagForm()">
            Annuler
          </button>
          <button type="submit" class="btn-submit" [disabled]="isLoading || createTagForm.invalid">
            <mat-icon>add</mat-icon>
            Créer
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Edit Tag Popup -->
  <div class="popup-overlay" *ngIf="showEditForm" (click)="hideEditTagForm()">
    <div class="popup-form" (click)="$event.stopPropagation()">
      <div class="popup-header">
        <h3>
          <mat-icon>edit</mat-icon>
          Modifier le Tag
        </h3>
        <button class="close-btn" (click)="hideEditTagForm()">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <form [formGroup]="editTagForm" (ngSubmit)="submitEditForm()" class="popup-content">
        <div class="form-group">
          <label for="editNom">Nom du Tag <span class="required">*</span></label>
          <input
            id="editNom"
            type="text"
            formControlName="nom"
            placeholder="Entrez le nom du tag"
            required
          />
          <div class="error-message" *ngIf="editTagForm.get('nom')?.invalid && editTagForm.get('nom')?.touched">
            Le nom du tag est requis (minimum 2 caractères)
          </div>
        </div>

        <div class="popup-actions">
          <button type="button" class="btn-cancel" (click)="hideEditTagForm()">
            Annuler
          </button>
          <button type="submit" class="btn-submit" [disabled]="isLoading || editTagForm.invalid">
            <mat-icon>save</mat-icon>
            Modifier
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Header Section -->
  <div class="header-card-container">
    <div class="header-section">
      <div class="page-title">
        <h1 class="title">
          <mat-icon class="title-icon">local_offer</mat-icon>
          Liste des Tags
        </h1>
      </div>
      <div class="actions">
        <button class="create-button" (click)="showAddTagForm()">
          <mat-icon class="action-icon">add</mat-icon>
          Créer Tag
        </button>
      </div>
    </div>
  </div>

  <!-- Search Section -->
  <div class="search-section">
    <div class="search-container">
      <input
        type="text"
        [(ngModel)]="searchTerm"
        placeholder="Rechercher un tag..."
        class="search-input"
      />
      <button class="search-button" (click)="onSearch()">
        <mat-icon>search</mat-icon>
      </button>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div class="loading-spinner" *ngIf="isLoading">Chargement...</div>

  <!-- Table Section -->
  <div class="table-section" *ngIf="!isLoading">
    <app-generic-table
      [headers]="headers"
      [keys]="keys"
      [data]="paginatedTags"
      [actions]="['edit', 'delete']"
      (actionTriggered)="handleAction($event)"
    >
    </app-generic-table>

    <!-- Pagination -->
    <mat-paginator
      [length]="totalCount"
      [pageSize]="pageSize"
      [pageIndex]="currentPage"
      [pageSizeOptions]="[5, 10, 25, 50]"
      (page)="onPageChange($event)"
      aria-label="Select page"
      *ngIf="totalCount > 0"
    >
    </mat-paginator>

    <!-- No Data Message -->
    <div class="no-data-message" *ngIf="filteredTags.length === 0 && !isLoading">
      <mat-icon class="no-data-icon">local_offer</mat-icon>
      <p>Aucun tag trouvé</p>
      <button class="create-button" (click)="showAddTagForm()">
        <mat-icon>add</mat-icon>
        Créer votre premier tag
      </button>
    </div>
  </div>
</div>

