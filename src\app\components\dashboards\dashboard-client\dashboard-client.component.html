<div class="dashboard-container">
  <!-- Header -->
  <div class="org-name">
    <h1>Statistique:</h1>
  </div>

  <div class="dashboard-content">
    <!-- All Cards Section - Unified grid for all cards -->
    <div class="all-cards-section">
      <!-- Main metrics cards -->
      <div class="card" *ngFor="let sale of salesData">
        <div class="card-header">
          <div class="card-icon">
            <i [class]="sale.icon" [style.color]="sale.iconColor"></i>
          </div>
          <div class="card-title">{{ sale.title }}</div>
        </div>
        <div class="card-content">
          <div class="card-amount">
            <span class="amount-text">{{ sale.amount }}</span>
          </div>
          <div class="card-progress">
            <div class="progress-bar">
              <div
                class="progress-fill"
                [style.width.%]="sale.progress"
                [style.background-color]="sale.color"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="charts-section">
      <!-- Top row with two side-by-side charts -->
      <div class="top-charts">
        <div class="chart-container">
          <canvas id="energyLineChart"></canvas>
        </div>
        <div class="chart-container">
          <canvas id="expenseChart"></canvas>
        </div>
      </div>

      <!-- Full-width chart below -->
      <div class="chart-container consommation-chart full-width-chart">
        <canvas #chartCanvas></canvas>
      </div>
    </div>

    <!-- Map Section -->
    <div class="map-container">
      <div class="map-header">Map - Sites</div>
      <div id="map" style="height: 500px; width: 100%"></div>
    </div>
  </div>
</div>
