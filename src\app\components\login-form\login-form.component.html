<div class="login-container">
  <div class="login-header">
    <h1>Admin Login</h1>
  </div>

  <form #loginForm="ngForm" (ngSubmit)="onSubmit(loginForm)">
    <div class="form-group">
      <label for="email">Email Address</label>
      <input
        type="email"
        id="email"
        name="email"
        ngModel
        required
      />
    </div>

    <div class="form-group">
      <label for="password">Password</label>
      <input
        type="password"
        id="password"
        name="password"
        ngModel
        required
      />
    </div>

    <div class="form-options">
      <div class="remember-me">
        <input type="checkbox" id="remember" name="remember" ngModel />
        <label for="remember">Remember me</label>
      </div>
      <a href="#" class="forgot-password">Forgot password?</a>
    </div>

    <button type="submit" class="login-button">Login</button>
  </form>
</div>
