/* 🎨 Variables CSS pour la cohérence */
:root {
  --primary-color: #3B82F6;
  --secondary-color: #6B7280;
  --success-color: #49b38d;
  --danger-color: #DC2626;
  --warning-color: #D97706;
  --background-light: #F9FAFB;
  --background-white: #FFFFFF;
  --text-primary: #111827;
  --text-secondary: #6B7280;
  --border-color: #E5E7EB;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --border-radius: 8px;
  --transition: all 0.2s ease-in-out;
}

/* 📱 Container principal */
.plan-editor {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--background-light);
  font-family: 'Se<PERSON>e UI', <PERSON><PERSON><PERSON>, Geneva, Verdana, sans-serif;
  color: var(--text-primary);
}

/* 🎯 Header */
.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: var(--background-white);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.editor-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.title-icon {
  font-size: 1.75rem;
}

.plan-type-selector {
  display: flex;
  background: var(--background-light);
  border-radius: var(--border-radius);
  padding: 0.25rem;
  gap: 0.25rem;
}

.type-btn {
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  border-radius: calc(var(--border-radius) - 2px);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  color: var(--text-secondary);
}

.type-btn:hover {
  background: var(--background-white);
  color: var(--text-primary);
}

.type-btn.active {
  background: var(--primary-color);
  color: white;
  box-shadow: var(--shadow-sm);
}

.header-actions {
 display: flex;
  align-items: center;
  flex-wrap: wrap; /* pour éviter le débordement sur petits écrans */
  gap: 1rem;
  padding: 0.5rem 1rem;
  background: #f9f9f9;
  border-bottom: 1px solid #ddd;
  font-family: Arial, sans-serif;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  padding: 0.4rem 0.8rem;
  border: none;
  background: #e9ecef;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s;
  font-size: 0.85rem;
}

.action-btn:hover {
  background: #dee2e6;
}
.action-btn .btn-icon {
  font-size: 1rem;
}
.action-btn.secondary {
  color: var(--text-secondary);
}

/* 📐 Corps de l'éditeur */
.editor-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 🎨 Palette d'outils */
.tools-palette {
  width: 280px;
  background: var(--background-white);
  border-right: 1px solid var(--border-color);
  overflow-y: auto;
  padding: 1rem;
}

.palette-section {
  margin-bottom: 2rem;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.section-icon {
  font-size: 1.125rem;
}

.tool-group {
  display: grid;
  gap: 0.5rem;
}

.tool-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 2px solid var(--border-color);
  background: var(--background-white);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  text-align: left;
}

.tool-btn:hover {
  border-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.05);
}

.tool-btn.selected {
  border-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.1);
  box-shadow: var(--shadow-md);
}

.tool-icon {
  font-size: 1.25rem;
  min-width: 1.5rem;
}

.tool-name {
  font-weight: 500;
  color: var(--text-primary);
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
}

.quick-btn {
  aspect-ratio: 1;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1.5rem;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
  color: white;
}

.quick-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.quick-btn:active {
  transform: translateY(0);
}

.edit-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.edit-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  background: var(--background-white);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.edit-btn:hover {
  background: var(--background-light);
}

.edit-btn.delete {
  color: var(--danger-color);
  border-color: rgba(220, 38, 38, 0.3);
}

.edit-btn.delete:hover {
  background: rgba(220, 38, 38, 0.05);
  border-color: var(--danger-color);
}

/* 🖼️ Zone de canvas */
.canvas-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--background-light);
}

.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: var(--background-white);
  border-bottom: 1px solid var(--border-color);
}

.canvas-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.canvas-title {
  font-weight: 600;
  color: var(--text-primary);
}

.canvas-type {
  padding: 0.25rem 0.75rem;
  background: var(--background-light);
  border-radius: 999px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.canvas-controls {
  display: flex;
  gap: 0.5rem;
}

.control-btn {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  background: var(--background-white);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 1rem;
}

.control-btn:hover {
  background: var(--background-light);
  border-color: var(--primary-color);
}

.canvas-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  overflow: auto;
}

#canvas {
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  background: white;
  max-width: 100%;
  max-height: 100%;
}

.canvas-overlay {
  position: absolute;
  inset: 0;
  pointer-events: none;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.canvas-overlay.selecting {
  background: rgba(59, 130, 246, 0.05);
  border: 2px dashed var(--primary-color);
}

.selection-hint {
  position: absolute;
  top: 1rem;
  left: 50%;
  transform: translateX(-50%);
  padding: 0.5rem 1rem;
  background: var(--primary-color);
  color: white;
  border-radius: var(--border-radius);
  font-weight: 500;
  box-shadow: var(--shadow-md);
  pointer-events: none;
}

.selection-hint::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: var(--primary-color);
}

/* 📊 Footer */
.editor-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: var(--background-white);
  border-top: 1px solid var(--border-color);
  box-shadow: 0 -1px 2px 0 rgba(0, 0, 0, 0.05);
}

.import-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.file-input-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
  color: var(--text-secondary);
}

.file-input-label:hover {
  border-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.05);
  color: var(--primary-color);
}

.file-hint {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.export-section {
  display: flex;
  gap: 0.75rem;
}

.export-btn {
  display: flex;
  align-items: center;
  gap: 8px;

  border: 2px solid var(--border-color);
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
 
  font-size: 14px;
  font-weight: 500;
}

.export-btn:hover {
 
  box-shadow: var(--shadow-md);
}

.export-btn.primary {
  background: #49b38d;
  border-color: var(--primary-color);
  color: white;
}

.export-btn.primary:hover {
  background: #3db186;
  border-color: #3db186;
}

.export-btn.secondary {
  color: var(--text-secondary);
}

.export-btn.secondary:hover {
  background: var(--background-light);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.footer-info {
  display: flex;
  align-items: center;
}

.status-indicator {
  padding: 0.25rem 0.75rem;
  border-radius: 999px;
  font-size: 0.875rem;
  font-weight: 500;
  background: var(--background-light);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.status-indicator.ready {
  background: rgba(5, 150, 105, 0.1);
  color: var(--success-color);
  border-color: rgba(5, 150, 105, 0.3);
}

/* 🎨 Icônes et éléments visuels */
.btn-icon {
  font-size: 1rem;
  min-width: 1rem;
}

/* 📱 Responsive Design */
@media (max-width: 1024px) {
  .editor-body {
    flex-direction: column;
  }
  
  .tools-palette {
    width: 100%;
    max-height: 200px;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
    padding: 0.75rem;
  }
  
  .palette-section {
    margin-bottom: 1rem;
  }
  
  .tool-group {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
  
  .quick-actions {
    grid-template-columns: repeat(6, 1fr);
  }
  
  .canvas-wrapper {
    padding: 1rem;
  }
  
  #canvas {
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 400px);
  }
}

@media (max-width: 768px) {
  .editor-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .header-left {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .plan-type-selector {
    justify-content: center;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .editor-footer {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .import-section,
  .export-section {
    justify-content: center;
  }
  
  .canvas-header {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }
  
  .canvas-info,
  .canvas-controls {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .editor-title {
    font-size: 1.25rem;
  }
  
  .tools-palette {
    padding: 0.5rem;
  }
  
  .tool-btn {
    padding: 0.5rem;
  }
  
  .tool-name {
    font-size: 0.875rem;
  }
  
  .quick-actions {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .canvas-wrapper {
    padding: 0.5rem;
  }
  
  #canvas {
    max-width: calc(100vw - 1rem);
    max-height: calc(100vh - 350px);
  }
}

/* 🎭 Animations et transitions */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.tool-btn.selected {
  animation: fadeInUp 0.3s ease-out;
}

.status-indicator.ready {
  animation: pulse 2s infinite;
}

/* 🎨 Palette de couleurs */
.color-group {
  display:flex;
  flex-direction: row;
  margin-bottom: 1.5rem;
}

.color-label,
.stroke-width-label {
  font-weight: 500;
  font-size: 0.9rem;
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.color-swatch {
  aspect-ratio: 1;
  border-radius: var(--border-radius);
  cursor: pointer;
  border: 2px solid transparent;
  transition: var(--transition);
  position: relative;
}

.color-swatch:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

.color-swatch.selected {
  border-color: var(--text-primary);
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.color-swatch.selected::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
}

.color-picker {
 max-width: 90px;
  height: 2.5rem;
  border: 2px solid var(--border-color);
  border-radius: 50px;
  cursor: pointer;
  transition: var(--transition);
}

.color-picker:hover {
  border-color: var(--primary-color);
}

.stroke-controls {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stroke-colors {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.stroke-swatch {
    width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid transparent;
  box-shadow: 0 0 0 1px #ccc;
  transition: transform 0.2s, border-color 0.2s;
}

.stroke-swatch:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

.stroke-swatch.selected {
  border-color: #333;
  transform: scale(1.1);
}

.stroke-swatch.selected::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--primary-color);
  font-weight: bold;
  font-size: 0.75rem;
}

.stroke-width-control {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stroke-width-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.stroke-slider {
  max-width: 80px;
accent-color: #007bff;
  height: 4px;
  cursor: pointer;

}

.stroke-slider::-webkit-slider-thumb {
  appearance: none;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  box-shadow: var(--shadow-sm);
}

.stroke-slider::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: none;
  box-shadow: var(--shadow-sm);
}

.stroke-value {
   font-size: 0.85rem;
  min-width: 30px;
  text-align: center;
}

.pipette-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem;
  border: 2px dashed var(--border-color);
  background: var(--background-white);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
  color: var(--text-secondary);
}

.pipette-btn:hover {
  border-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.05);
  color: var(--primary-color);
}

.pipette-btn:active {
  transform: scale(0.98);
}

/* 🎨 Animations pour la palette */
.color-swatch,
.stroke-swatch {
  animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 📱 Responsive pour la palette */
@media (max-width: 768px) {
  .color-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .stroke-colors {
    justify-content: center;
  }
  
  .stroke-width-control {
    align-items: center;
  }
}

/* 🌟 Effets visuels améliorés pour les couleurs */
.color-swatch:active,
.stroke-swatch:active {
  transform: scale(0.95);
}

/* 🎯 Indicateur de couleur active dans l'interface */
.selected-color-indicator {
  position: fixed;
  top: 1rem;
  right: 1rem;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  border: 3px solid var(--background-white);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  pointer-events: none;
}

/* 🎨 États de survol et focus améliorés pour la palette */
button:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.tool-btn:focus-visible,
.quick-btn:focus-visible,
.edit-btn:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* 📐 Améliorations canvas */
.canvas-wrapper:hover #canvas {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 🎯 Indicateurs visuels pour les actions */
.export-btn:active {
  transform: translateY(1px);
}

.quick-btn:active {
  transform: scale(0.95);
}
.section-title {
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toggle-icon {
  margin-left: auto;
  font-size: 0.8em;
}


.export-btn.primary:hover::before {
  left: 100%;
}

/* 🎨 Styles pour les tooltips */
[title] {
  position: relative;
}

/* 📱 Amélioration de l'accessibilité */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 🌙 Support du mode sombre (optionnel) */
@media (prefers-color-scheme: dark) {
  :root {
    --primary-color: #60A5FA;
    --secondary-color: #9CA3AF;
    --success-color: #10B981;
    --danger-color: #F87171;
    --warning-color: #FBBF24;
    --background-light: #1F2937;
    --background-white: #111827;
    --text-primary: #F9FAFB;
    --text-secondary: #9CA3AF;
    --border-color: #374151;
  }
}




/* 🎯 Header amélioré avec sélecteurs */
.editor-header {
  display: flex;
  flex-direction: column;
  padding: 1rem 1.5rem;
  background: var(--background-white);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  z-index: 10;
  gap: 1rem;
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 2rem;
  flex-wrap: wrap;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  justify-content: space-between;
}

/* Sélecteurs de contexte */
.context-selectors {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  min-width: 0;
  flex: 1;
}

.selector-group {
  position: relative;
  min-width: 0;
}

.selector-label {
  display: block;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Dropdown styles */
.dropdown {
  position: relative;
  width: 100%;
}

.dropdown.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.dropdown-trigger {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  background: var(--background-white);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.875rem;
  text-align: left;
  min-height: 3rem;
}

.dropdown-trigger:hover:not(:disabled) {
  border-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.02);
}

.dropdown.open .dropdown-trigger {
  border-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.05);
}

.dropdown-trigger:disabled {
  background: var(--background-light);
  color: var(--text-secondary);
  cursor: not-allowed;
}

.selected-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

.dropdown-icon {
  margin-left: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
  transition: transform 0.2s ease;
}

.dropdown.open .dropdown-icon {
  transform: rotate(180deg);
}

/* Menu dropdown */
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--background-white);
  border: 2px solid var(--primary-color);
  border-top: none;
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  max-height: 300px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Recherche */
.search-box {
  padding: 0.75rem;
  border-bottom: 1px solid var(--border-color);
  background-color: #FFFFFF;
}

.search-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: calc(var(--border-radius) - 2px);
  font-size: 0.875rem;
  background: var(--background-white);
  transition: var(--transition);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* Options du dropdown */
.dropdown-options {
  flex: 1;
  overflow-y: auto;
  max-height: 220px;
}

.dropdown-option {
  padding: 0.75rem;
  cursor: pointer;
  transition: var(--transition);
  border-bottom: 1px solid rgba(255, 1, 1, 0.05);
  background-color: #FFFFFF;
}

.dropdown-option:hover {
 background-color: #d6d6d6;
}

.dropdown-option:last-child {
  border-bottom: none;
}

.option-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.25rem;
}

.option-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.option-code,
.option-type,
.option-floor {
  font-size: 0.75rem;
  padding: 0.125rem 0.375rem;
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
  border-radius: 999px;
  font-weight: 500;
}

.option-meta {
  font-size: 0.75rem;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.option-area {
  font-weight: 600;
  color: var(--success-color);
}

.no-results {
  padding: 1rem;
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
  font-size: 0.875rem;
}

/* Responsive pour les sélecteurs */
@media (max-width: 1200px) {
  .context-selectors {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .header-left {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .header-right {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .editor-header {
    padding: 0.75rem 1rem;
  }
  
  .context-selectors {
    grid-template-columns: 1fr;
  }
  
  .dropdown-trigger {
    padding: 0.5rem 0.75rem;
    min-height: 2.5rem;
    font-size: 0.8rem;
  }
  
  .dropdown-menu {
    max-height: 250px;
  }
  
  .dropdown-option {
    padding: 0.5rem 0.75rem;
  }
  
  .option-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .plan-type-selector {
    flex-direction: column;
    width: 100%;
  }
  
  .header-actions {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .context-selectors {
    gap: 0.5rem;
  }
  
  .selector-label {
    font-size: 0.7rem;
  }
  
  .dropdown-trigger {
    padding: 0.5rem;
    min-height: 2.25rem;
    font-size: 0.75rem;
  }
  
  .search-input {
    padding: 0.375rem 0.5rem;
    font-size: 0.8rem;
  }
  
  .option-name {
    font-size: 0.8rem;
  }
  
  .option-code,
  .option-type,
  .option-floor {
    font-size: 0.7rem;
    padding: 0.1rem 0.25rem;
  }
}

/* Animation pour l'ouverture des dropdowns */
.dropdown-menu {
  animation: dropdownOpen 0.2s ease-out;
}

@keyframes dropdownOpen {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Styles pour les états de sélection */
.dropdown-option.selected {
  background: rgba(59, 130, 246, 0.1);
  border-left: 3px solid var(--primary-color);
}

.dropdown-option.selected .option-name {
  color: var(--primary-color);
  font-weight: 700;
}

/* Indicateur de hiérarchie */
.selector-group::before {
  content: '';
  position: absolute;
  top: 0;
  left: -0.5rem;
  width: 2px;
  height: 100%;
  background: var(--border-color);
  opacity: 0;
  transition: var(--transition);
}

.selector-group:nth-child(2)::before,
.selector-group:nth-child(3)::before {
  opacity: 1;
}

.selector-group:nth-child(2)::before {
  background: var(--primary-color);
  opacity: 0.3;
}

.selector-group:nth-child(3)::before {
  background: var(--success-color);
  opacity: 0.3;
}

/* États de validation */
.dropdown.has-selection .dropdown-trigger {
  border-color: var(--success-color);
  background: rgba(5, 150, 105, 0.02);
}

.dropdown.has-selection .selected-text {
  color: var(--success-color);
  font-weight: 600;
}

/* Overlay pour fermer les dropdowns */
.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: transparent;
}

/* Styles pour les badges de statut */
.context-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: var(--background-light);
  border-radius: var(--border-radius);
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.status-indicator.complete {
  background: rgba(5, 150, 105, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(5, 150, 105, 0.3);
}

.status-indicator.incomplete {
  background: rgba(217, 119, 6, 0.1);
  color: var(--warning-color);
  border: 1px solid rgba(217, 119, 6, 0.3);
}

/* Scrollbar pour les options */
.dropdown-options::-webkit-scrollbar {
  width: 6px;
}

.dropdown-options::-webkit-scrollbar-track {
  background: var(--background-light);
}

.dropdown-options::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.dropdown-options::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* Effets de focus pour l'accessibilité */
.dropdown-trigger:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.search-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.dropdown-option:focus {
  outline: none;
  background: rgba(59, 130, 246, 0.15);
}

/* Styles pour l'état loading */
.dropdown-loading {
  padding: 1rem;
  text-align: center;
  color: var(--text-secondary);
}

.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 🏠 Styles pour la construction de plans architecturaux */

/* Mode de vue */
.view-mode-selector {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.view-btn {
  flex: 1;
  padding: 0.5rem;
  border: 2px solid var(--border-color);
  background: var(--background-white);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
  font-size: 0.875rem;
}

.view-btn:hover {
  border-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.05);
}

.view-btn.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

/* Types de pièces */
.room-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
}

.room-btn {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem 0.5rem;
  border: 2px solid var(--border-color);
  background: var(--background-white);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  min-height: 4rem;
}

.room-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.room-icon {
  font-size: 1.25rem;
  margin-bottom: 0.25rem;
}

.room-name {
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

.room-color {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  border: 1px solid rgba(255,255,255,0.8);
}

/* Templates de plans */
.template-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
}

.template-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem 0.5rem;
  border: 2px dashed var(--border-color);
  background: var(--background-white);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  min-height: 3.5rem;
}

.template-btn:hover {
  border-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.05);
  border-style: solid;
}

.template-icon {
  font-size: 1.125rem;
  margin-bottom: 0.25rem;
}

.template-name {
  font-size: 0.8rem;
  font-weight: 500;
}

/* Onglets de catégories de mobilier */
.category-tabs {
  display: flex;
  margin-bottom: 0.75rem;
  background: var(--background-light);
  border-radius: var(--border-radius);
  padding: 0.25rem;
}

.category-tab {
  flex: 1;
  padding: 0.5rem 0.25rem;
  border: none;
  background: transparent;
  border-radius: calc(var(--border-radius) - 2px);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.category-tab:hover {
  background: var(--background-white);
  color: var(--text-primary);
}

.category-tab.active {
  background: var(--primary-color);
  color: white;
  box-shadow: var(--shadow-sm);
}

/* Catégories de mobilier */
.furniture-category {
  display: none;
}

.furniture-category.active {
  display: block;
}

.furniture-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
}

.furniture-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  color: white;
  font-weight: 500;
  min-height: 3rem;
  box-shadow: var(--shadow-sm);
}

.furniture-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.furniture-icon {
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.furniture-name {
  font-size: 0.7rem;
  text-align: center;
  line-height: 1.1;
}

/* Outils avancés */
.advanced-tools {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.advanced-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  background: var(--background-white);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
  color: var(--text-secondary);
}

.advanced-btn:hover {
  background: var(--background-light);
  border-color: var(--primary-color);
  color: var(--text-primary);
}

.advanced-btn.active {
  background: rgba(59, 130, 246, 0.1);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.grid-controls {
  padding-top: 0.75rem;
  border-top: 1px solid var(--border-color);
}

.control-label {
  display: block;
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.grid-slider {
  width: 100%;
  height: 0.375rem;
  border-radius: 0.2rem;
  background: var(--background-light);
  outline: none;
  cursor: pointer;
}

.grid-slider::-webkit-slider-thumb {
  appearance: none;
  width: 0.875rem;
  height: 0.875rem;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  box-shadow: var(--shadow-sm);
}

.grid-slider::-moz-range-thumb {
  width: 0.875rem;
  height: 0.875rem;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: none;
  box-shadow: var(--shadow-sm);
}

/* Canvas amélioré pour les plans architecturaux */
.canvas-wrapper.architectural {
  background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%), 
              linear-gradient(-45deg, #f8f9fa 25%, transparent 25%), 
              linear-gradient(45deg, transparent 75%, #f8f9fa 75%), 
              linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

/* États spéciaux pour la construction */
.canvas-overlay.room-drawing {
  background: rgba(34, 197, 94, 0.05);
  border: 2px dashed #22C55E;
}

.canvas-overlay.wall-drawing {
  background: rgba(107, 114, 128, 0.05);
  border: 2px dashed #6B7280;
}

/* Styles pour les hints de construction */
.construction-hint {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  padding: 0.5rem 1rem;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  font-weight: 500;
  pointer-events: none;
  z-index: 100;
}

.construction-hint::before {
  content: '';
  position: absolute;
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-bottom-color: rgba(0, 0, 0, 0.8);
}

/* Animations pour les éléments de construction */
@keyframes roomPreview {
  0% { opacity: 0; transform: scale(0.95); }
  100% { opacity: 0.3; transform: scale(1); }
}

.room-preview {
  animation: roomPreview 0.3s ease-out;
}

@keyframes wallConnect {
  0% { stroke-dasharray: 0, 100; }
  100% { stroke-dasharray: 100, 0; }
}

.wall-connecting {
  animation: wallConnect 0.5s ease-in-out;
}

/* Styles pour les labels de pièces */
.room-label {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Mode 3D isométrique */
.canvas-3d {
  transform: perspective(1000px) rotateX(30deg) rotateY(-15deg);
  transform-style: preserve-3d;
}

.canvas-3d .room-3d {
  box-shadow: 
    0 0 0 3px rgba(0, 0, 0, 0.1),
    0 6px 12px rgba(0, 0, 0, 0.15),
    inset 0 -3px 6px rgba(0, 0, 0, 0.1);
}

.canvas-3d .wall-3d {
  box-shadow: 
    3px 0 0 rgba(0, 0, 0, 0.2),
    0 3px 6px rgba(0, 0, 0, 0.3);
}

/* Responsive pour les nouvelles fonctionnalités */
@media (max-width: 768px) {
  .room-grid,
  .template-grid,
  .furniture-grid {
    grid-template-columns: 1fr;
  }
  
  .category-tabs {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .category-tab {
    padding: 0.75rem;
    text-align: left;
  }
  
  .view-mode-selector {
    flex-direction: column;
  }
  
  .advanced-tools {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .room-btn,
  .template-btn,
  .furniture-btn {
    min-height: 2.5rem;
    padding: 0.5rem 0.25rem;
  }
  
  .room-name,
  .template-name,
  .furniture-name {
    font-size: 0.7rem;
  }
  
  .room-icon,
  .template-icon,
  .furniture-icon {
    font-size: 1rem;
  }
}

/* Effets visuels pour l'interactivité */
.tool-btn:active,
.room-btn:active,
.template-btn:active,
.furniture-btn:active {
  transform: scale(0.95);
}

/* Indicateurs d'état */
.building-mode .canvas-wrapper {
  border-color: var(--success-color);
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.1);
}

.room-mode .canvas-wrapper {
  border-color: var(--warning-color);
  box-shadow: 0 0 0 2px rgba(217, 119, 6, 0.1);
}

/* 🎨 États de survol et focus améliorés pour la palette *//* 🎨 Variables CSS pour la cohérence */
:root {
  --primary-color: #3B82F6;
  --secondary-color: #6B7280;
  --success-color: #059669;
  --danger-color: #DC2626;
  --warning-color: #D97706;
  --background-light: #F9FAFB;
  --background-white: #FFFFFF;
  --text-primary: #111827;
  --text-secondary: #6B7280;
  --border-color: #E5E7EB;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --border-radius: 8px;
  --transition: all 0.2s ease-in-out;
}

/* 📱 Container principal */
.plan-editor {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--background-light);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--text-primary);
}

/* 🎯 Header */
.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: var(--background-white);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.editor-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.title-icon {
  font-size: 1.75rem;
}

.plan-type-selector {
  display: flex;
  background: var(--background-light);
  border-radius: var(--border-radius);
  padding: 0.25rem;
  gap: 0.25rem;
}

.type-btn {
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  border-radius: calc(var(--border-radius) - 2px);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  color: var(--text-secondary);
}

.type-btn:hover {
  background: var(--background-white);
  color: var(--text-primary);
}

.type-btn.active {
  background: var(--primary-color);
  color: white;
  box-shadow: var(--shadow-sm);
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  background: var(--background-white);
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  color: var(--text-primary);
}

.action-btn:hover {
  background: var(--background-light);
  border-color: var(--primary-color);
}

.action-btn.secondary {
  color: var(--text-secondary);
}

/* 📐 Corps de l'éditeur */
.editor-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 🎨 Palette d'outils */
.tools-palette {
  width: 280px;
  background: var(--background-white);
  border-right: 1px solid var(--border-color);
  overflow-y: auto;
  padding: 1rem;
}

.palette-section {
  margin-bottom: 2rem;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.section-icon {
  font-size: 1.125rem;
}

.tool-group {
  display: grid;
  gap: 0.5rem;
}

.tool-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 2px solid var(--border-color);
  background: var(--background-white);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  text-align: left;
}

.tool-btn:hover {
  border-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.05);
}

.tool-btn.selected {
  border-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.1);
  box-shadow: var(--shadow-md);
}

.tool-icon {
  font-size: 1.25rem;
  min-width: 1.5rem;
}

.tool-name {
  font-weight: 500;
  color: var(--text-primary);
}

/* 🪑 Styles spécifiques pour le mobilier SVG */
.mobilier-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
}

.mobilier-btn {
  position: relative;
  padding: 0.75rem 0.5rem;
  min-height: 4rem;
  flex-direction: column;
  text-align: center;
}

.mobilier-btn .tool-icon {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

.mobilier-btn .tool-name {
  font-size: 0.8rem;
  line-height: 1.2;
}

.svg-badge {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  background: var(--success-color);
  color: white;
  font-size: 0.6rem;
  padding: 0.1rem 0.3rem;
  border-radius: 0.25rem;
  font-weight: 600;
  text-transform: uppercase;
}

.mobilier-btn.selected .svg-badge {
  background: var(--primary-color);
}

.mobilier-btn:hover .svg-badge {
  background: var(--primary-color);
  transform: scale(1.1);
}

.mobilier-btn {
  transition: all 0.3s ease;
}

.mobilier-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.mobilier-btn.selected {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.2));
  border-color: var(--primary-color);
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
}

.quick-btn {
  aspect-ratio: 1;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1.5rem;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
  color: white;
}

.quick-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.quick-btn:active {
  transform: translateY(0);
}

/* 🎨 Palette de couleurs */
.color-group {
  margin-bottom: 1.5rem;
}

.color-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.color-swatch {
  aspect-ratio: 1;
  border-radius: var(--border-radius);
  cursor: pointer;
  border: 2px solid transparent;
  transition: var(--transition);
  position: relative;
}

.color-swatch:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

.color-swatch.selected {
  border-color: var(--text-primary);
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.color-swatch.selected::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
}

.color-picker {
   width: 40px;
  height: 24px;
  padding: 0;
  border: none;
  cursor: pointer;
}

.color-picker:hover {
  border-color: var(--primary-color);
}

.stroke-controls {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stroke-colors {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.stroke-swatch {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid transparent;
  transition: var(--transition);
  position: relative;
}

.stroke-swatch:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

.stroke-swatch.selected {
  border-color: var(--primary-color);
  transform: scale(1.1);
}

.stroke-swatch.selected::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--primary-color);
  font-weight: bold;
  font-size: 0.75rem;
}

.stroke-width-control {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stroke-width-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.stroke-slider {
  width: 100%;
  height: 0.5rem;
  border-radius: 0.25rem;
  background: var(--background-light);
  outline: none;
  cursor: pointer;
}

.stroke-slider::-webkit-slider-thumb {
  appearance: none;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  box-shadow: var(--shadow-sm);
}

.stroke-slider::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: none;
  box-shadow: var(--shadow-sm);
}

.stroke-value {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
  padding: 0.25rem;
  background: var(--background-light);
  border-radius: 0.25rem;
}

.pipette-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem;
  border: 2px dashed var(--border-color);
  background: var(--background-white);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
  color: var(--text-secondary);
}

.pipette-btn:hover {
  border-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.05);
  color: var(--primary-color);
}

.pipette-btn:active {
  transform: scale(0.98);
}

.edit-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.edit-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  background: var(--background-white);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.edit-btn:hover {
  background: var(--background-light);
}

.edit-btn.delete {
  color: var(--danger-color);
  border-color: rgba(220, 38, 38, 0.3);
}

.edit-btn.delete:hover {
  background: rgba(220, 38, 38, 0.05);
  border-color: var(--danger-color);
}

/* 🖼️ Zone de canvas */
.canvas-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--background-light);
}

.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: var(--background-white);
  border-bottom: 1px solid var(--border-color);
}

.canvas-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.canvas-title {
  font-weight: 600;
  color: var(--text-primary);
}

.canvas-type {
  padding: 0.25rem 0.75rem;
  background: var(--background-light);
  border-radius: 999px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.canvas-controls {
  display: flex;
  gap: 0.5rem;
}

.control-btn {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  background: var(--background-white);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 1rem;
}

.control-btn:hover {
  background: var(--background-light);
  border-color: var(--primary-color);
}

.canvas-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  overflow: auto;
}

#canvas {
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  background: white;
  max-width: 100%;
  max-height: 100%;
}

.canvas-overlay {
  position: absolute;
  inset: 0;
  pointer-events: none;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.canvas-overlay.selecting {
  background: rgba(59, 130, 246, 0.05);
  border: 2px dashed var(--primary-color);
}

.selection-hint {
  position: absolute;
  top: 1rem;
  left: 50%;
  transform: translateX(-50%);
  padding: 0.5rem 1rem;
  background: var(--primary-color);
  color: white;
  border-radius: var(--border-radius);
  font-weight: 500;
  box-shadow: var(--shadow-md);
  pointer-events: none;
}

.selection-hint::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: var(--primary-color);
}

/* 📊 Footer */
.editor-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: var(--background-white);
  border-top: 1px solid var(--border-color);
  box-shadow: 0 -1px 2px 0 rgba(0, 0, 0, 0.05);
}

.import-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.file-input-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
  color: var(--text-secondary);
}

.file-input-label:hover {
  border-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.05);
  color: var(--primary-color);
}

.file-hint {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.export-section {
  display: flex;
  gap: 0.75rem;
}





.export-btn.secondary {
  color: var(--text-secondary);
}

.export-btn.secondary:hover {
  background: var(--background-light);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.footer-info {
  display: flex;
  align-items: center;
}

.status-indicator {
  padding: 0.25rem 0.75rem;
  border-radius: 999px;
  font-size: 0.875rem;
  font-weight: 500;
  background: var(--background-light);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.status-indicator.ready {
  background: rgba(5, 150, 105, 0.1);
  color: var(--success-color);
  border-color: rgba(5, 150, 105, 0.3);
}

/* 🎨 Icônes et éléments visuels */
.btn-icon {
  font-size: 1rem;
  min-width: 1rem;
}

/* 📱 Responsive Design */
@media (max-width: 1024px) {
  .editor-body {
    flex-direction: column;
  }
  
  .tools-palette {
    width: 100%;
    max-height: 200px;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
    padding: 0.75rem;
    overflow-x: auto;
    display: flex;
    gap: 1rem;
  }
  
  .palette-section {
    margin-bottom: 0;
    min-width: 200px;
  }
  
  .tool-group {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
  
  .mobilier-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    grid-template-columns: repeat(6, 1fr);
  }
  
  .canvas-wrapper {
    padding: 1rem;
  }
  
  #canvas {
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 400px);
  }
}

@media (max-width: 768px) {
  .editor-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .header-left {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .plan-type-selector {
    justify-content: center;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .editor-footer {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .import-section,
  .export-section {
    justify-content: center;
  }
  
  .canvas-header {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }
  
  .canvas-info,
  .canvas-controls {
    justify-content: center;
  }
  
  .tools-palette {
    flex-direction: column;
    max-height: 300px;
  }
  
  .mobilier-btn {
    flex-direction: row;
    text-align: left;
    min-height: auto;
    padding: 0.75rem;
  }
  
  .mobilier-btn .tool-icon {
    font-size: 1.25rem;
    margin-bottom: 0;
    margin-right: 0.5rem;
  }
  
  .svg-badge {
    position: static;
    margin-left: auto;
  }
}

