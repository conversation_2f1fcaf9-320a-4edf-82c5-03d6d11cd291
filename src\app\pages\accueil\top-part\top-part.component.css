.organisation-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  padding: 20px;
}
.category-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: linear-gradient(145deg, #ffffff, #f5f5f5);
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  animation: fadeIn 0.5s ease-out;
}
.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  background: linear-gradient(145deg, #f5f5f5, #ffffff);
}

/* Ripple effect on click */
.category-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at center,
    rgba(76, 175, 80, 0.2) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.5s ease;
}

.category-card:active::before {
  opacity: 1;
}

.icon-container {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.category-icon {
  font-size: 32px;
  transition: all 0.3s ease;
}

/* Hover effects */
.category-card:hover .icon-container {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.category-card:hover .category-icon {
  transform: scale(1.1);
}

/* Category info */
.category-info {
  text-align: center;
}

.category-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 5px;
  letter-spacing: 0.3px;
}

.category-count {
  font-size: 14px;
  font-weight: 400;
  color: #666;
  margin: 0;
}


.title-icon {
  font-size: 30px;
  color: #4CAF50;
  background: linear-gradient(45deg, #4CAF50, #81C784);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Add these styles to your existing CSS */
.details-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(-5px);
}

.category-card:hover .details-button {
  opacity: 1;
  transform: translateY(0);
}

.details-button:hover {
  background: #f8f9fa;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.details-icon {
  font-size: 18px;
  color: #666;
}

.show-more-container {
  display: flex;
  justify-content: center;
  margin: 1rem 0 2rem;
}

.show-more-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: 2px solid #4CAF50;
  border-radius: 8px;
  background: transparent;
  color: #4CAF50;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.show-more-button:hover {
  background: #e8f5e9;
  transform: translateY(-2px);
}

.show-more-button .material-icons {
  font-size: 20px;
  transition: transform 0.3s ease;
}

.show-more-button:hover .material-icons {
  transform: translateY(2px);
}
